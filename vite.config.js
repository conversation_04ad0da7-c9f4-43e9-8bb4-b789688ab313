import react from '@vitejs/plugin-react-swc'
import path from 'path'
import { defineConfig } from 'vite'

export default defineConfig({
  plugins: [react()],
  server:{
    cors: true,
    port: 3000,
    headers: {
      'Service-Worker-Allowed': '/'
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@shardcn': path.resolve(__dirname, './src/components/ui'),
      '@hooks': path.resolve(__dirname, './src/lib/hooks'),
      '@lib': path.resolve(__dirname, './src/lib'),
      '@context': path.resolve(__dirname, './src/lib/context'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@constants': path.resolve(__dirname, './src/lib/constants'),
      '@api': path.resolve(__dirname, './src/services/api'),
      '@query': path.resolve(__dirname, './src/services/query'),
      '@store': path.resolve(__dirname, './src/services/store'),
      "@public": path.resolve(__dirname, "/images"),
    },
  },
  esbuild: {
    // jsxInject: import React from 'react',
  },
  lintOnSave: true, // Enable linting during development
  eslint: {
    // ESLint options, if any
    fix: true, // Automatically fix ESLint errors on save
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes("node_modules")) {
            return id.toString().split("node_modules/")[1].split("/")[0].toString()
          }
        },
      },
    },
    chunkSizeWarningLimit: 500,
  },
})