const path = require("path")

module.exports = {
  root: true, // Lint this project and only subprojects that have their own config
  env: {
    browser: true, // Browser global variables like `window` etc.
    es2021: true, // Adds all ECMAScript 2021 globals and automatically sets the `ecmaVersion` parser option to `12`.
  },
  extends: [
    "airbnb",
    // 'eslint:recommended',
    "plugin:react/recommended",
    "plugin:react/jsx-runtime",
    "plugin:react-hooks/recommended",
    "plugin:jsx-a11y/recommended",
    "plugin:sonarjs/recommended-legacy",
    "plugin:storybook/recommended",
    "plugin:@tanstack/eslint-plugin-query/recommended",
    "plugin:prettier/recommended",
  ],
  ignorePatterns: [
    "dist",
    ".eslintrc.cjs",
    "vite.config.js",
    "tailwind.config.js",
    "postcss.config.js",
    "src/components/ui", // Ignore shardcn components
    "src/tests",
    "src/services/mock"
  ],
  parserOptions: {
    ecmaVersion: 2021,
    ecmaFeatures: {
      jsx: true,
    },
    // ecmaVersion: "latest", // Allows parsing of modern ECMAScript features
    sourceType: "module", // Allows for the use of imports
  },
  plugins: ["react-refresh", "react-hooks", "jsx-a11y", "sonarjs", "prettier"],
  rules: {
    // "import/named": "off", // If import/named continues to cause issues, turn it off
    "import/no-unresolved": ["error", { ignore: ["^graphql-request"] }],
    // 'import/no-unresolved': 'off',
    "import/named": "off",
    "no-console": "off",
    camelcase: "off",
    "prefer-const": "error",
    "prettier/prettier": "error", // Show errors for Prettier violations as ESLint issues
    "react/react-in-jsx-scope": "off", // React 17+ doesn't require this in JSX files
    "react/jsx-props-no-spreading": "off",
    "react/function-component-definition": "off",
    semi: 0,
    "import/no-cycle": "off",
    "react/jsx-filename-extension": [1, { extensions: [".js", ".jsx"] }],
    "sonarjs/no-extra-arguments": "off",
    // 'require-jsdoc': 0,
    // Disables the requirement of defaultProps for functional components
    "react/require-default-props": [
      2,
      { forbidDefaultForRequired: true, ignoreFunctionalComponents: true },
    ],
    // Disables the matching of default props and prop types for functional components
    "react/default-props-match-prop-types": [2, { allowRequiredDefaults: false }],
    // Disables exhaustive-deps
    "react-hooks/exhaustive-deps": "off",
    "prettier/prettier": ["error", { endOfLine: "auto" }],
  },
  settings: {
    react: {
      version: "detect", // Automatically detect React version
    },
    "import/ignore": ["node_modules/graphql-request"],
    "import/resolver": {
      alias: {
        map: [
          ["@", path.resolve(__dirname, "./src")],
          // we will follow shardcn documentation
          // as its use @/components/ui/<com>
          // ['@components', path.resolve(__dirname, './src/components')],
          // ['@shardcn', path.resolve(__dirname, './src/components/ui')],
          ["@hooks", path.resolve(__dirname, "./src/lib/hooks")],
          ["@lib", path.resolve(__dirname, "./src/lib")],
          ["@context", path.resolve(__dirname, "./src/lib/context")],
          ["@pages", path.resolve(__dirname, "./src/pages")],
          ["@constants", path.resolve(__dirname, "./src/lib/constants")],
          ["@api", path.resolve(__dirname, "./src/services/api")],
          ["@query", path.resolve(__dirname, "./src/services/query")],
          ["@store", path.resolve(__dirname, "./src/services/store")],
          ["@public", path.resolve(__dirname, "./public/images")],
          // this is mainly for eslint
        ],
        extensions: [".js", ".jsx"],
      },
      node: {
        extensions: [".js", ".jsx"],
      },
    },
  },
  overrides: [
    {
      files: ["**/*.js", "**/*.jsx"],
      rules: {
        "no-param-reassign": [
          "error",
          {
            props: true,
            ignorePropertyModificationsFor: [
              "state", // for Redux Toolkit state mutations
              "acc", // for reduce accumulators
              "e", // for e.returnvalue
              "ctx", // for Koa routing
              "req", // for Express requests
              "request", // for Express requests
              "res", // for Express responses
              "response", // for Express responses
              "$scope", // for Angular 1 scopes
              "staticContext", // for ReactRouter context
            ],
          },
        ],
        "import/no-extraneous-dependencies": "off",
      },
    },
    {
      files: ["**/*.js", "**/*.jsx"],
      rules: {
        "import/prefer-default-export": "off",
      },
    },
    {
      files: ["src/tests/**/*.{js,jsx}"],
      rules: {
        "react/prop-types": "off", // Disable 'prop-types' validations rule
        "no-unused-vars": "off", // Disable 'no-unused-vars' rule
        "no-shadow": "off", // Disable 'no-shadow' rule
        "sonarjs/no-duplicate-string": "off", // Disable 'no-duplicate-string' rule
      },
    },
  ],
}
