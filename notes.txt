// /routes/shop.products.tsx

import { z } from 'zod'

const productSearchSchema = z.object({
  page: z.number().catch(1),
  filter: z.string().catch(''),
  sort: z.enum(['newest', 'oldest', 'price']).catch('newest'),
})

type ProductSearch = z.infer<typeof productSearchSchema>

export const Route = createFileRoute('/shop/products')({
  validateSearch: (search) => productSearchSchema.parse(search),
})


import LoginPage from '@pages/auth/login'
import { Provider } from 'react-redux'
import { expect, fn, mocked, userEvent, within, spyOn } from '@storybook/test'
import { mockStore } from '@store/'
import ct from '@constants/'
// import * as router from 'react-router'
import * as router from 'react-router-dom'
import { MemoryRouter, RouterProvider } from 'react-router-dom'
// import {
//   createRootRoute,
//   createRoute,
//   createRouter,
//   Outlet,
//   RouterProvider,
// } from '@tanstack/react-router'
// import { render } from 'react-dom'

// function fancyRender(component) {
//   const rootRoute = createRootRoute({
//     component: Outlet,
//   })
//
//   const indexRoute = createRoute({
//     getParentRoute: () => rootRoute,
//     path: '/',
//     component,
//   })
//
//   const routerTree = rootRoute.addChildren([indexRoute])
//   const router = createRouter({ routerTree })
//
//   // this is rtl's render
//   return <RouterProvider router={router} />
// }

const mockedUsedNavigate = fn()

mocked('react-router-dom', () => ({
  useNavigate: () => mockedUsedNavigate,
}))

export default {
  title: 'Pages/Auth/LoginPage',
  component: LoginPage,
  // decorators: [withRouter],
}

const Template = (args) => {
  return (
    <Provider store={mockStore}>
      <MemoryRouter>
        <LoginPage {...args} />
      </MemoryRouter>
    </Provider>
  )
}

export const Default = Template.bind({})

Default.play = async ({ canvasElement }) => {
  const canvas = within(canvasElement)

  // const navigate = fn()
  // tanstackRouter.useNavigate.mockReturnValue(navigate)

  // mocked('@tanstack/react-router', () => ({
  //   useNavigate: () => fn(),
  // }))

  // spyOn(router, 'useNavigate').mockImplementation(mockedUsedNavigate)

  // Find the email input and button
  const emailInput = canvas.getByPlaceholderText('email')
  const submitButton = canvas.getByRole('button', { name: /submit/i })

  // Mock toast function to test the success message
  // toast.success = jest.fn()

  // Simulate user input
  await userEvent.type(emailInput, '<EMAIL>')
  await userEvent.click(submitButton)
  spyOn(router, 'useNavigate')
  await userEvent.click(submitButton)
  await expect(mockedUsedNavigate).toHaveBeenCalled()

  const userState = mockStore.getState()[ct.store.USER_STORE]
  console.log(userState)

  await expect(userState.userName).toEqual('<EMAIL>')
  await expect(userState.userRole).toEqual('user')

  // await expect(mockedUsedNavigate).toBeCalled()
}
