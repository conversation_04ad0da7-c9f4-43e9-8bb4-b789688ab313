import 'tailwindcss/tailwind.css'
import '@/main.css'
import { initialize, mswLoader } from 'msw-storybook-addon'
import { themes } from '@storybook/theming'
import i18 from '@/lib/i18n'
import { Suspense, useEffect } from 'react'
import { I18nextProvider } from 'react-i18next'

/*
 * Initializes MSW
 * See https://github.com/mswjs/msw-storybook-addon#configuring-msw
 * to learn how to customize it
 */
initialize()

/** @type { import('@storybook/react').Preview } */
const preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    options: {
      storySort: {
        order: ['docs', 'component', 'Pages', '*'],
      },
    },
    docs: {
      theme: themes.dark,
    },
  },

  // 👈 Add the MSW loader to all stories
  loaders: [mswLoader],

  tags: ['autodocs']
}

export default preview

export const globalTypes = {
  locale: {
    name: 'Locale',
    description: 'Internationalization locale',
    toolbar: {
      icon: 'globe',
      items: [
        { value: 'en', title: 'English' },
        { value: 'hi', title: 'Hindi' },
      ],
      showName: true,
    },
  },
}

const withI18next = (Story, context) => {
  const { locale } = context.globals

  useEffect(() => {
    i18.changeLanguage(locale)
  }, [locale])

  return (
    // This catches the suspense from components not yet ready (still loading translations)
    // Alternative: set useSuspense to false on i18next.options.react when initializing i18next
    (<Suspense fallback={<div>loading translations...</div>}>
      <I18nextProvider i18n={i18}>
        <Story />
      </I18nextProvider>
    </Suspense>)
  );
}

// export decorators for storybook to wrap your stories in
export const decorators = [withI18next]
