{"name": "frontend-base", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "test": "vitest", "preview": "vite preview", "lint": "eslint . --ext .js,.jsx", "lint:fix": "eslint . --ext .js,.jsx --fix", "format": "prettier --write \"src/**/*.{js,jsx,json,css}\"", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test-storybook": "test-storybook"}, "dependencies": {"@cyntler/react-doc-viewer": "^1.17.0", "@hookform/resolvers": "^3.6.0", "@mux/mux-player-react": "^3.2.4", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.1.4", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^2.2.5", "@stripe/react-stripe-js": "^3.5.1", "@stripe/stripe-js": "^6.1.0", "@tanstack/query-async-storage-persister": "^5.40.0", "@tanstack/react-query": "^5.40.1", "@tanstack/react-query-persist-client": "^5.40.1", "@tanstack/react-table": "^8.20.6", "axios": "^1.7.2", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "file-saver": "^2.0.5", "firebase": "^11.3.1", "framer-motion": "^11.18.2", "graphql-request": "^7.1.2", "graphql-tag": "^2.12.6", "i18next": "^23.11.5", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^2.5.2", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.474.0", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "next-themes": "^0.4.3", "prop-types": "^15.8.1", "react": "19.0.0", "react-day-picker": "^9.5.1", "react-dom": "19.0.0", "react-github-calendar": "^4.5.5", "react-hook-form": "^7.51.5", "react-i18next": "^14.1.2", "react-icons": "^5.4.0", "react-markdown": "^9.1.0", "react-perfect-scrollbar": "^1.5.8", "react-phone-number-input": "^3.4.11", "react-redux": "^9.1.2", "react-router-dom": "^6.23.1", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.1", "redux": "^5.0.1", "redux-persist": "^6.0.0", "remark-gfm": "^4.0.1", "sonner": "^1.7.0", "vaul": "^1.1.2", "video.js": "^8.22.0", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.4", "@playwright/test": "^1.44.1", "@storybook/addon-essentials": "^8.5.2", "@storybook/addon-interactions": "^8.5.2", "@storybook/addon-jest": "^8.5.2", "@storybook/addon-links": "^8.5.2", "@storybook/addon-mdx-gfm": "^8.5.2", "@storybook/addon-storysource": "^8.5.2", "@storybook/blocks": "^8.5.2", "@storybook/react": "^8.5.2", "@storybook/react-vite": "^8.5.2", "@storybook/test": "^8.5.2", "@storybook/test-runner": "^0.21.0", "@storybook/theming": "^8.5.2", "@tailwindcss/typography": "^0.5.13", "@tanstack/eslint-plugin-query": "^5.35.6", "@tanstack/react-query-devtools": "^5.40.1", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/node": "^20.14.0", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.19", "babel-jest": "^29.7.0", "babel-preset-vite": "^1.1.3", "backstopjs": "^6.3.23", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-no-secrets": "^1.0.2", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "eslint-plugin-sonarjs": "^1.0.3", "eslint-plugin-storybook": "^0.11.2", "jest": "^29.7.0", "jsdom": "^24.1.0", "msw": "^2.3.1", "msw-storybook-addon": "^2.0.4", "postcss": "^8.4.38", "prettier": "^3.3.1", "storybook": "^8.5.2", "storybook-dark-mode": "^4.0.2", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.3", "tailwindcss-animate": "^1.0.7", "vite": "^5.2.0", "vite-plugin-eslint": "^1.8.1"}, "msw": {"workerDirectory": ["public"]}}