/* eslint-disable no-restricted-globals */
/* eslint-disable no-undef */
importScripts("https://www.gstatic.com/firebasejs/11.0.0/firebase-app-compat.js")
importScripts("https://www.gstatic.com/firebasejs/11.0.0/firebase-messaging-compat.js")

// // Firebase configuration for the messaging app
// const firebaseMessagingConfig = {
//   apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
//   authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
//   projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
//   storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
//   messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
//   appId: import.meta.env.VITE_FIREBASE_APP_ID,
// }

// firebase.intitializeApp(firebaseMessagingConfig)
// const messaging = firebase.messaging()

// messaging.onBackgroundMessage((payload) => {
//   console.log("Received background message:", payload)

//   // Extracts notification details from payload
//   const notificationTitle = payload.notification.title
//   const notificationOptions = {
//     body: payload.notification.body, // Notification message
//     icon: "/logo192.png", // Notification icon
//   }

//   // Shows the notification to user
//   self.registration.showNotification(notificationTitle, notificationOptions)
// })
