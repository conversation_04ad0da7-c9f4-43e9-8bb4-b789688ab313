# AI-Enhanced Resume Builder for Tailored Job Applications

<div align="center">
  <img src="public/images/hire10x_logo.png" alt="Resume-Builder Logo" width="200"/>
  <p><strong>Next Generation Talent Acquisition Powered by AI</strong></p>
</div>

## Overview

An AI-powered resume builder that analyzes job descriptions, compares them with your resume, and offers smart suggestions to optimize your resume for better job matches.

## Key Features

* **AI-Driven JD Matching**: Automatically compares resumes against job descriptions to highlight gaps and strengths
* **Smart Resume Suggestions**: Provides AI-powered content improvements to better align resumes with job roles
* **Real-Time Resume Editing**: Instantly apply AI suggestions while customizing your resume
* **ATS Optimization**: Ensures resumes are formatted and keyword-optimized to pass through Applicant Tracking Systems
* **Customizable Resume Templates**: Choose from a variety of modern, professional templates
* **Job Description Parsing**: Extracts key skills, responsibilities, and qualifications from any JD for precise targeting

## Tech Stack

### Core Technologies
- **React**: UI framework
- **Vite**: Fast build tool and development server
- **Tailwind CSS**: Utility-first CSS framework
- **Redux Toolkit**: State management
- **React Router**: Navigation and routing
- **Firebase**: Authentication and backend services
- **Tanstack Query**: Data fetching and caching

### Development Tools
- **Vitest**: Unit and integration testing
- **ESLint & Prettier**: Code quality and formatting

## Getting Started

### Prerequisites
- Node.js (v16+)
- npm or yarn

### Installation

```bash
# Clone the repository
git clone [repository-url]

# Navigate to the project directory
cd frontend-hire10x

# Install dependencies
npm install
```

### Available Commands

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Run tests
npm run test

# Lint code
npm run lint

# Format code
npm run format
```

## Project Structure

The project follows a feature-based organization:

```
src/
├── assets/           # Static assets
├── components/       # Reusable UI components
├── docs/             # Project documentation
├── firebase/         # Firebase configuration
├── hooks/            # Custom React hooks
├── lib/              # Utility libraries
├── pages/            # Application pages/routes
│   ├── resume/
│   ├── resume-dashboard/ # Display the uploaded resume alongside the job description
│   └── ...
├── route/            # Routing configuration
├── services/         # API and state management
├── styles/           # Global styles
├── tests/            # Test utilities
└── utils/            # Helper functions
```

## SOLID Principles

The project adheres to SOLID principles:

- **Single Responsibility**: Each component focuses on a specific task
- **Open/Closed**: Code is open for extension but closed for modification
- **Liskov Substitution**: Components maintain expected behavior
- **Interface Segregation**: Interfaces are tailored to specific needs
- **Dependency Inversion**: High-level modules don't depend on low-level modules


## License

This project is proprietary software owned by 10xScale.

---

© 2025 10xScale. All rights reserved.
