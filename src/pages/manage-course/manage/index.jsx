import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader } from "@/components/ui/card"
import { useFilterHooks, useOpenCloseHooks, usePaginationHooks } from "@/hooks/common.hooks"
import { useDebounce } from "@/hooks/useDebounce"
import { useGetCourse } from "@/services/query/live-course.query"
import { setActiveCourseDetails } from "@/services/store/slices/courses.slice"
import { USER_ROLES } from "@/utils/constants"
import ct from "@constants/"
import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useLocation, useNavigate, useParams } from "react-router-dom"

const getCourseLevelStyles = (level) => {
  switch (level) {
    case "BEGINNER":
      return "text-green-600 bg-green-100" // Green for Beginner
    case "INTERMEDIATE":
      return "text-yellow-600 bg-yellow-100" // Yellow for Intermediate
    case "ADVANCED":
      return "text-red-600 bg-red-100" // Red for Advanced
    default:
      return "text-gray-600 bg-gray-100" // Default gray
  }
}

const ManageCoursePage = () => {
  const user = useSelector((st) => st[ct.store.USER_STORE])
  const [listOfBatches, setListOfBatches] = useState([])
  const [searchingValue, setSearchingValue] = useState("")
  const debouncedSearchQuery = useDebounce(searchingValue, 500)
  const { limit, offset, setOffset } = usePaginationHooks()
  const { open, handleClose, handleOpen } = useOpenCloseHooks()
  const { sortBy } = useFilterHooks()

  // router dome
  const navigate = useNavigate()
  const dispatch = useDispatch()

  const { data: coursesData } = useGetCourse({
    userID: user.id,
    courseType: "Live course",
    limit,
    offset,
    order: sortBy,
    search_query: debouncedSearchQuery?.length > 0 ? debouncedSearchQuery : null,
    for_subscribed: user?.userRole === USER_ROLES.TRAINER,
  })

  useEffect(() => {
    if (coursesData?.get_course?.courses?.length === 0) {
      return
    }
    setListOfBatches(coursesData?.get_course?.courses)
  }, [coursesData?.get_course?.courses])

  const handleNavigate = (course) => {
    dispatch(setActiveCourseDetails(course))
    navigate(`${ct.route.LIVE_COURSES}/${course.id}${ct.route.COURSE_OVERVIEW}`)
  }

  return (
    <div className="p-6">
      {/* Upcoming Class Card (Optional - could be a separate component) */}
      {/* <Card className="mb-6 bg-blue-50">
        <CardContent className="pt-6">
          <h4 className="text-xl font-semibold mb-2">Upcoming Class</h4>
          <p className="text-gray-600">Leetcode 55 at 7:00 PM (07/02/2024)</p>
          <a href="#" className="text-blue-600 hover:underline">
            Meet Link
          </a>
        </CardContent>
      </Card> */}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {listOfBatches?.length > 0 &&
          listOfBatches?.map((course) => (
            <Card
              key={course?.id}
              className="group relative overflow-hidden rounded-xl border-l-primary bg-white border-l-4 transition-all duration-300 hover:shadow-lg  hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-blue-300"
            >
              {/* Decorative background element */}
              <div className="absolute -right-4 -top-4 h-16 w-16 rounded-full bg-blue-200 opacity-30 transition-transform duration-500 group-hover:scale-150" />

              <CardHeader className="pt-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-x-1">
                    <h2 className="font-medium text-lg group-hover:text-primary transition-colors duration-300 mb-0 ">
                      {course?.title}{" "}
                      <span
                        className={`text-xs font-semibold px-2 py-1 rounded-sm ${getCourseLevelStyles(course?.course_level)}`}
                      >
                        {course?.course_level}
                      </span>
                    </h2>
                  </div>
                  <span className="text-xs text-blue-500 font-semibold bg-blue-100 px-2 py-1 rounded-sm">
                    {course?.total_students} Students
                  </span>
                </div>
              </CardHeader>

              <CardContent className="py-2">
                <div className="flex justify-between items-center mb-3">
                  <p className="text-sm font-medium text-gray-600">
                    Duration Days :{" "}
                    <span className="text-sm font-medium text-gray-60">
                      {course?.duration_days}
                    </span>
                  </p>
                  <p className="text-sm font-medium text-gray-600">
                    Total hours :{" "}
                    <span className="text-sm font-medium text-gray-60">{course?.total_hours}</span>
                  </p>
                </div>
                <p className="text-sm mb-1 font-medium w-44 text-primary line-clamp-1">
                  {course?.intro}
                </p>
                <p className="text-sm font-medium text-gray-500 line-clamp-2">
                  {course?.description}
                </p>

                <div className="border-t border-gray-100 pt-3 mt-2">
                  {/* <div className="mb-2">
                    <span className="font-semibold text-sm text-gray-600">Trainers: </span>
                    <span className="text-sm">{course?.trainers?.join(", ")}</span>
                  </div> */}
                  <div className="flex flex-wrap gap-2">
                    {course?.tags?.map((tag) => (
                      <Badge
                        key={tag}
                        variant="secondary"
                        className="text-xs bg-blue-50text-primary hover:bg-blue-100 transition-colors duration-200"
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>

              <CardFooter className="flex justify-end pt-1 pb-4">
                <Button
                  variant="secondary"
                  onClick={() => handleNavigate(course)}
                  className="max-w-36 text-sm bg-blue-100 text-primary hover:bg-blue-700 hover:text-white transition-all duration-300 transform group-hover:scale-105 group-hover:shadow-md w-full"
                  size="sm"
                >
                  Open Course
                </Button>
              </CardFooter>
            </Card>
          ))}
      </div>
    </div>
  )
}

export default ManageCoursePage
