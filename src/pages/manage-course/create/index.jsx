import FormComboBox from "@/components/custom/custom-forms/combo-box.form"
import FormTextArea from "@/components/custom/custom-forms/form-textarea"
import { Button } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { useAvailableJDs } from "@/services/query/jd.query"
import { useGetUsersStudents } from "@/services/query/user.query"
import PropTypes from "prop-types"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"

const CreateJobApplicationForm = ({ onCreateClassForm }) => {
  const { data: fetchStudents } = useGetUsersStudents()
  const [students, setStudents] = useState([])
  const [jobTitles, setJobTitles] = useState([])

  const { data: jdData } = useAvailableJDs({
    jobType: "ALL",
    searchQuery: "",
    offset: 0,
    limit: 100,
  })

  useEffect(() => {
    if (fetchStudents?.data) {
      setStudents(fetchStudents.data.map(({ id, user_name }) => ({ value: id, label: user_name })))
    }

    if (jdData?.data?.length > 0) {
      setJobTitles(jdData.data.map(({ id, title }) => ({ value: id.toString(), label: title })))
    }
  }, [fetchStudents, jdData])

  const methods = useForm({
    defaultValues: {
      studentName: "",
      jobTitle: "",
      notes: "",
    },
  })

  const {
    handleSubmit,
    control,
    formState: { errors },
  } = methods

  return (
    <div className="h-[calc(100vh-200px)] md:h-[calc(100vh-150px)] sm:h-[calc(100vh-100px)] w-full overflow-auto pr-4">
      <Form {...methods}>
        <form onSubmit={handleSubmit(onCreateClassForm)} className="space-y-3 grid gap-y-3">
          <FormComboBox
            fieldControlName="studentName"
            control={control}
            label="Student Name"
            iterateData={students}
            placeholder="Select Student"
            isRequired
            error={errors.studentName?.message}
            width="300px"
          />
          <FormComboBox
            fieldControlName="jobTitle"
            control={control}
            label="Job Title"
            iterateData={jobTitles}
            placeholder="Select Job Title"
            isRequired
            error={errors.jobTitle?.message}
            width="300px"
          />
          <FormTextArea
            fieldControlName="notes"
            control={control}
            label="Notes"
            placeholder="Enter notes"
            isRequired
            error={errors.notes?.message}
          />
          <div className="flex justify-end gap-3">
            <Button variant="secondary" type="button" onClick={() => console.log("Cancel")}>
              Cancel
            </Button>
            <Button variant="primary" type="submit">
              Submit
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
CreateJobApplicationForm.propTypes = {
  onCreateClassForm: PropTypes.func.isRequired,
}

export default CreateJobApplicationForm
