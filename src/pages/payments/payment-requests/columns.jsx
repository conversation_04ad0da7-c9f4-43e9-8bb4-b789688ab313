import { CURRENCY_SYMBOLS } from "@/utils/location-based-helpers"

export const columns = [
  {
    id: "id",
    accessorKey: "id",
    header: "ID",
  },
  {
    id: "student_name",
    accessorKey: "student_name",
    header: "Student Name",
  },
  {
    id: "student_email",
    accessorKey: "student_email",
    header: "Email",
  },
  {
    id: "course_name",
    accessorKey: "course_name",
    header: "Course",
  },
  {
    id: "amount",
    accessorKey: "amount",
    header: "Amount",
    cell: ({ row }) => (
      <span>
        {CURRENCY_SYMBOLS[row?.original?.currency_code]} {row?.original?.amount}
      </span>
    ),
  },
  {
    id: "amount_percentage",
    accessorKey: "amount_percentage",
    header: "Percentage",
    cell: ({ row }) => <span>{row?.original?.amount_percentage}%</span>,
  },
  {
    id: "request_status",
    accessorKey: "request_status",
    header: "Status",
  },
]
