import { Card } from "@/components/ui/card"
import DataTable from "@/components/custom/cutsom-table"
import useTableConfig from "@/hooks/use-table.hooks"
import { flexRender } from "@tanstack/react-table"
import ct from "@constants/"
import { useSelector } from "react-redux"
import { useState } from "react"
import CustomSearchbar from "@/components/custom/custom-search"
import { useFetchPaymentRequests, useRejectPaymentRequest } from "@/services/query/payment.query"
import PayNow from "@/pages/courses/live-courses/course-overview/components/payment-buttons/pay-now"
import { CURRENCY_SYMBOLS } from "@/utils/location-based-helpers"
import { Button } from "@/components/ui/button"
import LoadingSpinner from "@/components/custom/LoadingSpinner"
import { columns } from "./columns"

const StudentPaymentRequest = () => {
  const { userRole, vendorData } = useSelector((st) => st[ct.store.USER_STORE])

  const [searchingValue, setSearchingValue] = useState("")
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })

  const { data: rerquestsData, isLoading } = useFetchPaymentRequests({
    offset: pagination.pageIndex * pagination.pageSize,
    limit: 10,
    search_query: searchingValue,
    vendor_id: vendorData?.id,
  })

  const { mutate: mutateRejectRequest, isPending: isRejectPending } = useRejectPaymentRequest()
  const [pendingRequestID, setPendingRequestID] = useState(null)

  const handleDeclineRequest = (id) => {
    setPendingRequestID(id)
    mutateRejectRequest(
      { id, request_status: "REJECTED" },
      {
        onSettled: () => {
          setPendingRequestID(null)
        },
      }
    )
  }

  const renderCellContent = (cell, row) => {
    console.log("cell?.column.id", cell?.column.id, row?.original)
    if (cell?.column.id === "id")
      return <span>{row.index + 1 + pagination.pageIndex * pagination.pageSize} </span>
    if (cell?.column.id === "request_status" && row?.original.request_status === "PENDING") {
      const { original: data } = row
      console.log("data", data)
      const courseDetails = {
        id: data?.course_id,
        courseName: data?.course_name,
        imageUrl: "",
        pricing: {
          currency: data?.currency_code,
          currencySymbol: CURRENCY_SYMBOLS[data?.currency_code],
          currentPrice: data?.amount,
          originalPrice: data?.amount,
        },
      }
      return (
        <div className="space-x-2">
          <PayNow
            activeCourseDetails={courseDetails}
            requestID={data?.id}
            userRole={userRole}
            isImmediatePayment
          />
          <Button
            variant="outline"
            className="hover:bg-blue-50"
            onClick={() => {
              handleDeclineRequest(row?.original?.id)
            }}
          >
            {isRejectPending && pendingRequestID === row?.original?.id ? (
              <LoadingSpinner className="flex items-center justify-center mx-4" variant="pulse" />
            ) : (
              "Decline"
            )}
          </Button>
        </div>
      )
    }

    return flexRender(cell.column.columnDef.cell, cell.getContext())
  }

  const { table, found, pageCount } = useTableConfig(
    rerquestsData?.data || [],
    columns,
    rerquestsData?.metadata?.total_records,
    setPagination,
    pagination
  )

  return (
    <Card className="p-8 bg-white rounded-2xl">
      <div className="flex justify-between items-center mb-2">
        <h1 className="text-2xl font-medium">Payment Requests</h1>
      </div>
      <div>
        <div className="flex justify-between mb-3">
          <CustomSearchbar
            inputSize="w-[20rem]"
            placeholder="Search by title..."
            searchedValue={searchingValue}
            setSearchedValue={(e) => setSearchingValue(e?.target.value)}
          />
        </div>
        <div className="w-full">
          <DataTable
            table={table}
            found={found}
            columns={columns}
            pageCount={pageCount}
            isLoading={isLoading}
            renderCellContent={renderCellContent}
          />
        </div>
      </div>
    </Card>
  )
}

export default StudentPaymentRequest
