import { Card } from "@/components/ui/card"
import DataTable from "@/components/custom/cutsom-table"
import useTableConfig from "@/hooks/use-table.hooks"
import { flexRender } from "@tanstack/react-table"
import ct from "@constants/"
import { useSelector } from "react-redux"
import { USER_ROLES } from "@/utils/constants"
import { useState } from "react"
import CustomSearchbar from "@/components/custom/custom-search"
import { useFetchPayments } from "@/services/query/payment.query"
import PropTypes from "prop-types"
import { studentColumns, vendorColumns, adminColumns } from "./columns"

const StudentPaymentHistory = ({ pageName = "Payment History" }) => {
  const { userRole, vendorData } = useSelector((st) => st[ct.store.USER_STORE])

  const [searchingValue, setSearchingValue] = useState("")
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })

  // Preapare search params
  const params = {
    offset: pagination.pageIndex * pagination.pageSize,
    limit: 10,
    search_query: searchingValue,
  }
  if (userRole === USER_ROLES.ADMIN) params.payment_user = USER_ROLES.STUDENT
  if (userRole === USER_ROLES.VENDOR) params.vendor_id = vendorData?.id

  const { data: paymentsData, isLoading } = useFetchPayments(params)

  const getColumns = () => {
    if (userRole === USER_ROLES.VENDOR) return vendorColumns
    if (userRole === USER_ROLES.ADMIN) return adminColumns

    return studentColumns
  }

  const renderCellContent = (cell, row) => {
    switch (cell?.column.id) {
      case "id":
        return <span>{row.index + 1 + pagination.pageIndex * pagination.pageSize}</span>
      case "course_name":
        return <span>{row.original?.course_name}</span>
      default:
        return flexRender(cell.column.columnDef.cell, cell.getContext())
    }
  }

  const { table, found, pageCount } = useTableConfig(
    paymentsData?.data || [],
    getColumns(),
    paymentsData?.metadata?.total_records,
    setPagination,
    pagination
  )

  return (
    <Card className="p-8 bg-white rounded-2xl">
      <div className="flex justify-between items-center mb-2">
        <h1 className="text-2xl font-medium">{pageName}</h1>
      </div>
      <div>
        <div className="flex justify-between mb-3">
          <CustomSearchbar
            inputSize="w-[20rem]"
            placeholder="Search"
            searchedValue={searchingValue}
            setSearchedValue={(e) => setSearchingValue(e?.target.value)}
          />
        </div>
        <div className="w-full">
          <DataTable
            table={table}
            found={found}
            columns={getColumns()}
            pageCount={pageCount}
            isLoading={isLoading}
            renderCellContent={renderCellContent}
          />
        </div>
      </div>
    </Card>
  )
}

StudentPaymentHistory.propTypes = {
  pageName: PropTypes.string,
}

export default StudentPaymentHistory
