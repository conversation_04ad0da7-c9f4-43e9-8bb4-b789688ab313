import { DateCell } from "@/components/custom/cutsom-table/table-cells"
import { CURRENCY_SYMBOLS } from "@/utils/location-based-helpers"

export const commonColumns = [
  {
    id: "course_name",
    accessorKey: "course_name",
    header: "Course",
  },
  {
    id: "amount",
    accessorKey: "amount",
    header: "Amount",
    cell: ({ row }) => (
      <span>
        {CURRENCY_SYMBOLS[row?.original?.currency_code]} {row?.original?.amount}
      </span>
    ),
  },
  {
    id: "amount_percentage",
    accessorKey: "amount_percentage",
    header: "Percentage",
    cell: ({ row }) => <span>{row?.original?.amount_percentage}%</span>,
  },
  {
    id: "payment_status",
    accessorKey: "payment_status",
    header: "Status",
  },
  {
    id: "invoice",
    accessorKey: "invoice",
    header: "Invoice",
  },
  {
    id: "created_at",
    accessorKey: "created_at",
    header: "Payment Date",
    cell: ({ row }) => <DateCell value={row?.original?.created_at} />,
  },
]

export const studentColumns = [
  {
    id: "id",
    accessorKey: "id",
    header: "ID",
  },
  {
    id: "vendor_name",
    accessorKey: "vendor_name",
    header: "Vendor Name",
    cell: ({ row }) => {
      const vendor_name = row?.original?.vendor_name
      if (vendor_name) return <span>{row?.original?.vendor_name}</span>

      return <span className="text-xs text-muted">Not By Vendor</span>
    },
  },
  ...commonColumns,
]

export const vendorColumns = [
  {
    id: "id",
    accessorKey: "id",
    header: "ID",
  },
  {
    id: "student_name",
    accessorKey: "student_name",
    header: "Student Name",
  },
  {
    id: "student_email",
    accessorKey: "student_email",
    header: "Email",
  },
  ...commonColumns,
]

export const adminColumns = [
  {
    id: "id",
    accessorKey: "id",
    header: "ID",
  },
  {
    id: "vendor_name",
    accessorKey: "vendor_name",
    header: "Vendor Name",
    cell: ({ row }) => {
      const vendor_name = row?.original?.vendor_name
      if (vendor_name) return <span>{row?.original?.vendor_name}</span>

      return <span className="text-xs text-muted">Not By Vendor</span>
    },
  },
  {
    id: "student_name",
    accessorKey: "student_name",
    header: "Student Name",
  },
  {
    id: "student_email",
    accessorKey: "student_email",
    header: "Student Email",
  },
  ...commonColumns,
]
