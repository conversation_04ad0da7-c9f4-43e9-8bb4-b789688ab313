import { useState } from "react"
import { useSelector } from "react-redux"
import ct from "@constants/"
import CustomTabs from "@/components/custom/custom-tabs"
import { USER_ROLES } from "@/utils/constants"
import StudentPaymentHistory from "./payment-history/student-payment-history"
import StudentPaymentRequest from "./payment-requests/student-payment-request"

const PaymentPages = () => {
  const { userRole } = useSelector((st) => st[ct.store.USER_STORE])
  const [activeTab, setActiveTab] = useState("payment-history")

  if (userRole === USER_ROLES.STUDENT) return <StudentPaymentHistory />
  if (userRole === USER_ROLES.ADMIN) return <StudentPaymentHistory pageName="Student Payments" />

  const tabs = [
    {
      id: "payment-history",
      label: "Payment History",
      content: <StudentPaymentHistory />,
      icon: null,
    },
    {
      id: "payment-request",
      label: "Payment Request",
      content: <StudentPaymentRequest />,
      icon: null,
    },
  ]

  return (
    <CustomTabs
      tabs={tabs}
      defaultTab="payment-history"
      setActiveTab={setActiveTab}
      activeTab={activeTab}
      size="w-full"
      tabsListClassName="justify-start"
    />
  )
}

export default PaymentPages
