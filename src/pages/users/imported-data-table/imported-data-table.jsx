import DataTable from "@/components/custom/cutsom-table"
import useTableConfig from "@/hooks/use-table.hooks"
import { flexRender } from "@tanstack/react-table"
import { useState } from "react"
import PropTypes from "prop-types"
import { Drawer, DrawerContent } from "@/components/ui/drawer"
import { columns } from "./columns"

const ImportedDataTable = ({ usersData, total, isOpen, setIsOpen }) => {
  // Fetch ALL Users
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 5 })

  // Config Table cell
  const renderCellContent = (cell) => {
    // console.log("cell", cell.row.original)
    if (!cell) return null

    if (cell.column.id === "no")
      return cell.row.index + 1 + pagination.pageIndex * pagination.pageSize

    return flexRender(cell.column.columnDef.cell, cell.getContext())
  }
  // Config Table
  const startIdx = pagination.pageIndex * pagination.pageSize
  const endIdx = startIdx + pagination.pageSize
  const { table, pageCount } = useTableConfig(
    usersData.slice(startIdx, endIdx),
    columns,
    total,
    setPagination,
    pagination
  )

  return (
    <Drawer open={isOpen} onOpenChange={setIsOpen}>
      <DrawerContent className="max-h-[75vh] min-h-[50vh] bg-white">
        <div className="w-full flex justify-center items-center rounded-2xl p-8">
          {/* USER TABLE */}
          <div className="w-[75%]">
            <DataTable
              table={table}
              found={total}
              columns={columns}
              pageName="New Users"
              pageCount={pageCount}
              renderCellContent={renderCellContent}
              notFoundPlaceholder="No User Records"
              pageSizeOptions={[5]}
            />
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  )
}

export default ImportedDataTable

ImportedDataTable.propTypes = {
  usersData: PropTypes.arrayOf(
    PropTypes.shape({
      user_name: PropTypes.string,
      email: PropTypes.string,
      password: PropTypes.string,
      role: PropTypes.string,
    })
  ),
  total: PropTypes.number,
  isOpen: PropTypes.bool,
  setIsOpen: PropTypes.func,
}
