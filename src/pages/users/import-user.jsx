import { Button } from "@/components/ui/button"

import { Building2, Plus, User, Users } from "lucide-react"
import { useState } from "react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import CustomTabs from "@/components/custom/custom-tabs"
import { useSelector } from "react-redux"
import ct from "@constants/"
import { USER_ROLES } from "@/utils/constants"
import ImportSingleUser from "./import-single-user"
import ImportMultiUsers from "./import-muti-users"
import ImportVendor from "./import-vendor"

export default function ImportUser() {
  // Get Current User
  const { userRole } = useSelector((state) => state[ct.store.USER_STORE])

  // State to manage dialog's open/close state
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const tabs = [
    {
      id: "single-user",
      label: "Single User",
      content: <ImportSingleUser onDialogClose={() => setIsDialogOpen(false)} />, // Pass the close function as a prop
      icon: <User className="mr-2 h-4 w-4" />,
    },
    {
      id: "multi-user",
      label: "Multiple User",
      content: <ImportMultiUsers onDialogClose={() => setIsDialogOpen(false)} />,
      icon: <Users className="mr-2 h-4 w-4" />,
    },
  ]

  if (userRole === USER_ROLES.ADMIN)
    tabs.push({
      id: "vendor",
      label: "Vendor",
      content: <ImportVendor onDialogClose={() => setIsDialogOpen(false)} />,
      icon: <Building2 className="mr-2 h-4 w-4" />,
    })

  const [activeTab, setActiveTab] = useState(tabs[0]?.id)

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}> {/* Control the dialog's state */}
      <DialogTrigger asChild>
        <Button variant="primary" className="gap-1">
          <Plus size={18} />
          {userRole === USER_ROLES.VENDOR ? "Import Student" : "Import User"}
        </Button>
      </DialogTrigger>

      <DialogContent>
        <DialogHeader className="sr-only">
          <DialogTitle> Import User </DialogTitle>
          <DialogDescription> Importing user into system</DialogDescription>
        </DialogHeader>
        <CustomTabs
          tabs={tabs}
          defaultTab={tabs[0]?.id}
          size="w-full"
          setActiveTab={setActiveTab}
          activeTab={activeTab}
        />
      </DialogContent>
    </Dialog>
  )
}