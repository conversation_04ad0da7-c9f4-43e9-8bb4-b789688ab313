/* eslint-disable prefer-destructuring */
import { Button } from "@/components/ui/button"
import LoadingSpinner from "@/components/custom/LoadingSpinner"
import { useState } from "react"
import { useCreateVendor } from "@/services/query/user.query"
import { toast } from "@/components/ui/use-toast"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Upload, X } from "lucide-react"

export default function ImportVendor({ onDialogClose }) {
  const [vendorName, setVendorName] = useState("")
  const [logo, setLogo] = useState(null)
  const initialErrors = { vendorName: "", logo: "" }
  const [errors, setErrors] = useState(initialErrors)

  const handleVendorNameChange = (value) => {
    setVendorName(value)
    // Clear vendor name error when user starts typing
    if (errors.vendorName) setErrors((prev) => ({ ...prev, vendorName: "" }))
  }

  const handleLogoChange = (e) => {
    const file = e.target.files[0]
    if (file) {
      const allowedTypes = ["image/jpeg", "image/jpg", "image/png"]
      const maxFileSize = 5 * 1024 * 1024 // 5MB in bytes

      if (!allowedTypes.includes(file.type)) {
        setErrors((prev) => ({
          ...prev,
          logo: "Only JPEG, JPG, and PNG files are allowed.",
        }))
        return
      }

      if (file.size > maxFileSize) {
        setErrors((prev) => ({
          ...prev,
          logo: "File size should be less than 5MB.",
        }))
        return
      }

      setLogo(file)
      setErrors((prev) => ({ ...prev, logo: "" }))
    }
  }

  const clearLogo = () => {
    setLogo(null)
    // Clear any previous logo-related errors
    if (errors.logo) setErrors((prev) => ({ ...prev, logo: "" }))
  }

  // Validate form before submission
  const validateForm = () => {
    const newErrors = { ...initialErrors }

    if (!vendorName.trim()) {
      setErrors((prev) => ({ ...prev, vendorName: "Vendor name is required." }))
      return false
    }

    if (!logo) {
      setErrors((prev) => ({ ...prev, logo: "Logo is required." }))
      return false
    }

    setErrors(newErrors)
    return true
  }

  // Reset form method
  const resetForm = () => {
    setVendorName("")
    setLogo(null)
    setErrors(initialErrors)
  }

  // Handle form submission
  const mutateCreateVendor = useCreateVendor()
  const [isLoading, setIsLoading] = useState(false)
  function createVendor() {
    if (!validateForm()) return
    setIsLoading(true)

    // Prepare form data
    const formData = new FormData()
    formData.append("vendor_name", vendorName)
    formData.append("logo", logo)

    // make api call
    mutateCreateVendor.mutate(formData, {
      onSuccess: (responce) => {
        console.log("responce", responce)
        setIsLoading(false)
        toast({
          title: "New Vendor",
          description: "New Vendor added successfully",
          variant: "success",
        })
        // RESET THE FORM
        setTimeout(() => {
          resetForm()
        }, 100)
      },
      onError: (error) => {
        console.log("error", error)
        setIsLoading(false)
        // Extract the exact error message
        let title = error?.response?.data?.error.message ?? "Failed. Please try again in some time"
        if (title.includes("DETAIL")) title = title.split("DETAIL")[1]
        toast({ title, variant: "destructive" })
      },
    })
  }

  return (
    <Card className="w-full max-w-md lg:max-w-lg xl:min-h-[400px]">
      <CardHeader>
        <CardTitle className="mb-2">Import A New Vendor</CardTitle>
        <CardDescription>Add new vendor credential below.</CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Vendor Name */}
        <div>
          <Input
            value={vendorName}
            onChange={(e) => handleVendorNameChange(e.target.value)}
            placeholder="Vendor Name"
          />
          {errors.vendorName && (
            <p className="text-red-500 text-sm font-medium mt-2">{errors.vendorName}</p>
          )}
        </div>

        {/* Logo */}
        <div>
          <div className="border-2 border-dashed border-gray-300 rounded-lg text-center p-4 mt-2">
            <Input
              id="file-upload"
              type="file"
              accept=".jpg,.jpeg,.png"
              onChange={handleLogoChange}
              className="hidden"
              disabled={!!logo}
            />
            <label
              htmlFor="file-upload"
              className="flex flex-col items-center justify-center gap-2 cursor-pointer"
            >
              <Upload />
              <div className="flex items-center gap-x-2">
                <span className="flex-grow text-sm font-medium">
                  {logo ? logo.name : "Click to upload Logo"}
                </span>
                {logo && (
                  <Button
                    variant="icon"
                    className="w-[18px] h-[18px] text-red-500 border border-red-500 rounded-full hover:scale-110"
                    onClick={clearLogo}
                  >
                    <X />
                  </Button>
                )}
              </div>
              <span className="text-xs text-gray-500">
                Only .jpg, .jpeg and .png files are supported
              </span>
            </label>
          </div>
          {errors.logo && <p className="text-red-500 text-sm font-medium mt-2">{errors.logo}</p>}
        </div>
        {/* Add User Button */}
        <CardFooter className="w-full flex justify-end gap-3 p-0">
          <Button
            variant="secondary-outline"
            className="text-base"
            type="button"
            onClick={onDialogClose} // Call the close function on click
          >
            Cancel
          </Button>
          <Button variant="primary" className="text-base text-white" onClick={() => createVendor()}>
            {isLoading ? <LoadingSpinner iconClassName="text-white animate-spin" /> : "Submit"}
          </Button>
        </CardFooter>
      </CardContent>
    </Card>
  )
}
