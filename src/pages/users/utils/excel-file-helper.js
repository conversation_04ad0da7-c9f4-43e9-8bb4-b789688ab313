/* eslint-disable prefer-template */
/* eslint-disable no-plusplus */
/* eslint-disable sonarjs/no-duplicate-string */
import { utils, write } from "xlsx"
import { saveAs } from "file-saver"
import { z } from "zod"
import { isValidPhoneNumber } from "react-phone-number-input"

/**
 * Creates and downloads a sample Excel template for user import
 */
export const downloadUserImportTemplate = () => {
  // Create a new workbook
  const workbook = utils.book_new()

  // Define Headers
  const headers = ["user_name", "email", "password", "user_role"]

  // Add sample row
  const sampleData = [
    {
      user_name: "<PERSON>",
      email: "<EMAIL>",
      password: "Password123",
      user_role: "student",
      phone: "+911234567890",
    },
    {
      user_name: "<PERSON>2",
      email: "<EMAIL>",
      password: "Password123",
      user_role: "student",
      phone: "+911234567890",
    },
  ]

  // Create worksheet with headers and sample data
  const workSheet = utils.json_to_sheet(sampleData, { header: headers })

  // Add dropdown for user_role column
  if (!workSheet["!dataValidation"]) workSheet["!dataValidation"] = []

  // Add dropdown validation to the user_role column (column D starting from row 2)
  workSheet["!dataValidation"].push({
    sqref: "D2:D1000", // Apply to user_role column for multiple rows
    type: "list",
    formula1: '"student"', // Dropdown options
    allowBlank: false,
  })

  // Add worksheet to workbook
  utils.book_append_sheet(workbook, workSheet, "UserImportTemplate")

  // Generate Excel file and download
  const excelBuffer = write(workbook, { bookType: "xlsx", type: "array" })
  const blob = new Blob([excelBuffer], { type: "application/octet-stream" })
  saveAs(blob, "UserImportTemplate.xlsx")
}

/*
 * Validate the extracted users data from the Excel file
 */
export const validateUsersData = (usersData) => {
  // Schema for validating each row
  const userRowSchema = z.object({
    user_name: z.string().min(2, "Name must be at least 2 characters"),
    email: z.string().email("Please enter a valid email"),
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(/[a-z]/, "Password must contain at least one lowercase letter")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
      .regex(/[0-9]/, "Password must contain at least one number"),
    phone: z
      .string()
      .optional()
      .refine(
        (value) => {
          if (!value) return true
          return isValidPhoneNumber(value)
        },
        {
          message: "Please enter a valid phone number",
        },

      ),
    user_role: z.enum(["student"], {
      errorMap: () => ({ message: "Role must be 'student'" }),
    }),
  })

  // Validate each row
  for (let i = 0; i < usersData.length; i++) {
    try {
      console.log("usersData", i, ":", usersData[i])
      userRowSchema.parse(usersData[i])
    } catch (error) {
      console.log("invalid data at row", i + 2)
      return {
        valid: false,
        row: i + 2,
        errors:
          error.errors.map((e) => `${e.path}: ${e.message}`).join(", ") + " at row " + (i + 2),
      }
    }
  }

  return { valid: true, errors: "" }
}
