/* eslint-disable no-case-declarations */
import CustomSearchbar from "@/components/custom/custom-search"
import DataTable from "@/components/custom/cutsom-table"
import { RenderTableData, StatusUpdationCell } from "@/components/custom/cutsom-table/table-cells"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useFilterHooks, usePaginationHooks } from "@/hooks/common.hooks"
import useTableConfig from "@/hooks/use-table.hooks"
import { queryClient } from "@/lib/query-client"
import { useGetAllUsers, useUpdateUser } from "@/services/query/user.query"
import { getOffset } from "@/utils/helper"
import { flexRender } from "@tanstack/react-table"
import { EllipsisVertical, Power } from "lucide-react"
import { useEffect, useRef, useState } from "react"
import { columns } from "./columns"
import ImportUser from "./import-user"

const UserManagementPage = () => {
  // Fetch ALL Users
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 })
  const [searchingValue, setSearchingValue] = useState("")
  const { sortBy, sortByField } = useFilterHooks()
  // const { sortBy, sortByField } = useFilterHooks()
  const { limit } = usePaginationHooks()
  const params = {
    limit,
    search_query: searchingValue,
    offset: getOffset(pagination.pageIndex, pagination.pageSize),
    sort_by: sortBy,
    sort_by_field: sortByField,
  }
  const { data: usersData, isLoading } = useGetAllUsers(params)
  const found = usersData?.metadata?.total_records

  const mutateUpdateUser = useUpdateUser()
  const pageRef = useRef(null)
  const [shouldApplyHeight, setShouldApplyHeight] = useState(false)
  useEffect(() => {
    if (pageRef.current) {
      const pageHeight = pageRef.current.clientHeight // Measure the page height
      const viewportHeight = window.innerHeight // Get the viewport height

      // If the page height exceeds the viewport height, apply the height prop
      if (pageHeight > viewportHeight) {
        setShouldApplyHeight(true)
      } else {
        setShouldApplyHeight(false)
      }
    }
  }, [usersData]) // Re-run when table data changes

  // Config Table cell
  const capitalizeWord = (word) =>
    `${word?.substring(0, 1).toUpperCase()}${word?.substring(1).toLowerCase()}`
  const renderCellContent = (cell) => {
    const { email, role, status, user_name } = cell.row.original
    if (!cell) return null

    switch (cell.column.id) {
      case "no":
        return (
          <RenderTableData
            content={cell.row.index + 1 + pagination.pageIndex * pagination.pageSize}
          />
        )
      case "status":
        return <StatusUpdationCell value={status} />
      case "user_name":
        return <RenderTableData content={user_name} />
      case "role":
        return <RenderTableData content={capitalizeWord(role)} />
      case "email":
        return <RenderTableData content={email} />
      case "actions":
        const user = cell.row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <EllipsisVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent side="right">
              <DropdownMenuItem
                onClick={() => {
                  const formData = new FormData()
                  formData.append(
                    "user_data",
                    JSON.stringify({
                      status: user.status === "ACTIVE" ? "SUSPENDED" : "ACTIVE",
                    })
                  )
                  mutateUpdateUser.mutate(
                    {
                      userID: user?.id,
                      data: formData,
                    },
                    {
                      onSuccess: () => {
                        queryClient.invalidateQueries({ queryKey: ["users"] })
                      },
                      onError: (error) => {
                        console.log("error while uploading profile image", error)
                      },
                    }
                  )
                }}
              >
                <Power className="mr-2 h-4 w-4" />
                <span className="font-semibold text-sm text-gray-600">
                  {" "}
                  {user.status === "ACTIVE" ? "Suspend" : "Enable"} User
                </span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      default:
        return flexRender(cell.column.columnDef.cell, cell.getContext())
    }
  }
  // Config Table
  const { table, pageCount } = useTableConfig(
    usersData?.data || [],
    columns,
    found,
    setPagination,
    pagination
  )
  const dynamicHeight = usersData?.data?.length >= 7 ? "h-[55vh]" : undefined
  return (
    <Card className="bg-white rounded-2xl p-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-lg text-primary font-medium">User Management</h1>
      </div>

      {/* Search & Import */}
      <div className="flex justify-between gap-4">
        <CustomSearchbar
          inputSize="w-[20rem]"
          placeholder="Search by name..."
          searchedValue={searchingValue}
          setSearchedValue={(e) => setSearchingValue(e?.target.value)}
        />
        <ImportUser />
      </div>

      {/* User Table with Scroll */}
      {/* <ScrollArea className="h-[65vh] mt-3"> */}
      <DataTable
        table={table}
        found={found}
        columns={columns}
        height={dynamicHeight}
        pageName="Users"
        pageCount={pageCount}
        isLoading={isLoading}
        renderCellContent={renderCellContent}
        notFoundPlaceholder="No Users Found"
      />
      {/* </ScrollArea> */}
    </Card>
  )
}

export default UserManagementPage
