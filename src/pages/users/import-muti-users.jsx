/* eslint-disable prefer-destructuring */
import { Button } from "@/components/ui/button"
import * as XLSX from "xlsx"
import LoadingSpinner from "@/components/custom/LoadingSpinner"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { useRef, useState } from "react"
import { useImportUser } from "@/services/query/user.query"
import { toast } from "@/components/ui/use-toast"
import { Input } from "@/components/ui/input"
import { Info, Upload, X } from "lucide-react"
import WrapToolTip from "@/components/wrap-tool-tip/wrap-tool-tip"
import { useSelector } from "react-redux"
import ct from "@constants/"
import { downloadUserImportTemplate, validateUsersData } from "./utils/excel-file-helper"
import ImportedDataTable from "./imported-data-table/imported-data-table"

export default function ImportMultiUsers({ onDialogClose }) {
  // Get Current User
  const { id: userID } = useSelector((state) => state[ct.store.USER_STORE])

  const [file, setFile] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)
  const usersDataRef = useRef([])

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0]
    if (!selectedFile) return

    setFile(selectedFile)

    const reader = new FileReader()
    reader.onload = async (fileBuffer) => {
      try {
        const data = new Uint8Array(fileBuffer.target.result)

        // Convert to workbook
        const workbook = XLSX.read(data, { type: "array" })

        // Get the first worksheet
        const worksheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[worksheetName]

        // Convert to JSON
        const users = XLSX.utils.sheet_to_json(worksheet)

        // Check if the file is empty
        if (users.length === 0) {
          toast({
            title: "Empty File",
            description: "The uploaded file doesn't contain any user data",
            variant: "destructive",
          })
          setIsLoading(false)
          return
        }

        usersDataRef.current = users
        console.log("Extracted users data from file", usersDataRef.current)

        // Validate users
        const { valid, errors } = validateUsersData(usersDataRef.current)
        if (!valid) {
          toast({
            title: "Validation Failed",
            description: errors || "Please fix the errors in your Excel file",
            variant: "destructive",
          })
          // Reset file input
          setFile(null)
          document.getElementById("file-upload").value = ""
          usersDataRef.current = []
        }
      } catch (error) {
        setIsLoading(false)
        toast({
          title: "Processing Error",
          description: "Error processing the Excel file",
          variant: "destructive",
        })
      }
    }

    reader.readAsArrayBuffer(selectedFile)
  }

  const clearFile = () => {
    setFile(null)
    usersDataRef.current = []
  }

  // Make API call
  const mutateImportUser = useImportUser(userID)
  const handleUpload = async () => {
    setIsLoading(true)

    mutateImportUser.mutate(
      { userID, data: usersDataRef.current },
      {
        onSuccess: (response) => {
          setIsLoading(false)

          const { created = [], skipped = {} } = response?.data || {}
          const { emails: skippedEmails = [] } = skipped

          // ✅ Show created toast
          if (created.length > 0) {
            const createdCount = created.length
            toast({
              title: `${createdCount} ${createdCount === 1 ? "User" : "Users"} Created`,
              description: `Successfully created ${createdCount} ${createdCount === 1 ? "user" : "users"}.`,
              variant: "success",
            })
            onDialogClose()
          }

          // ✅ Slight delay if both exist to prevent toast override
          if (skippedEmails.length > 0) {
            const skippedCount = skippedEmails.length
            setTimeout(
              () => {
                toast({
                  title: `${skippedCount} ${skippedCount === 1 ? "User" : "Users"} Skipped`,
                  description: `${skippedCount} ${skippedCount === 1 ? "user already exists" : "users already exist"}.`,
                  variant: "warning",
                })
                onDialogClose()
              },
              created.length > 0 ? 100 : 0
            ) // Delay only if both toasts are needed
          }

          // ✅ Clear input
          document.getElementById("file-upload").value = ""
          setFile(null)
        },

        onError: (error) => {
          setIsLoading(false)
          toast({
            title: error?.response?.data?.error?.message ?? "Import Failed",
            description: "Please try again later.",
            variant: "destructive",
          })
        },
      }
    )
  }

  const getHelpIcon = () => (
    <WrapToolTip
      delayDuration={10}
      side="top"
      toolTipContent={
        <>
          <p className="text-[10px] lg:text-[12px] mt-[2px]">Password must:</p>
          <ul className="list-disc text-xs pl-4">
            <li>Contain at least 8 characters</li>
            <li>Contain at least one lowercase, uppercase letter and number</li>
          </ul>
          <p className="text-[10px] lg:text-[12px] mt-[2px]">Roles must be:</p>
          <ul className="list-disc text-xs pl-4">
            <li>student</li>
          </ul>
        </>
      }
    >
      <Info className="h-4 w-4 text-muted-foreground cursor-help mr-2" />
    </WrapToolTip>
  )

  return (
    <>
      <Card className="w-full max-w-md lg:max-w-lg xl:min-h-[400px]">
        <CardHeader>
          <CardTitle className="flex items-center gap-x-2 mb-2">
            Import Mutiple New Users{getHelpIcon()}
          </CardTitle>
          <CardDescription>
            <Button
              variant="link"
              className="text-xs text-primary underline mr-1"
              onClick={() => {
                downloadUserImportTemplate()
              }}
            >
              Download our template
            </Button>
            with the correct headers and format
          </CardDescription>
        </CardHeader>

        <CardContent>
          {/* View File Data */}
          {file && (
            <div className="w-full flex justify-end">
              <Button
                variant="link"
                className="text-xs text-primary underline mr-1"
                onClick={() => setIsDrawerOpen(true)}
              >
                View data
              </Button>
            </div>
          )}
          <div className="border-2 border-dashed border-gray-300 rounded-lg text-center p-4 mx-4 mt-2 mb-6">
            <Input
              id="file-upload"
              type="file"
              accept=".xlsx,.xls"
              onChange={handleFileChange}
              className="hidden"
              disabled={Boolean(file)}
            />
            <label
              htmlFor="file-upload"
              className="flex flex-col items-center justify-center gap-2 cursor-pointer "
            >
              <Upload />
              <div className="flex items-center gap-x-2">
                <span className="text-sm font-medium">
                  {file ? file.name : "Click to upload Excel file"}
                </span>
                {file && (
                  <Button
                    variant="icon"
                    className="w-[18px] h-[18px] text-red-500 border border-red-500 rounded-full hover:scale-110"
                    onClick={clearFile}
                  >
                    <X />
                  </Button>
                )}
              </div>
              <span className="text-xs text-gray-500">Only .xlsx and .xls files are supported</span>
            </label>
          </div>

          {/* Add User Button */}
          <CardFooter className="w-full flex justify-end gap-3 p-0">
            <Button
              variant="secondary-outline"
              className="text-base"
              type="button"
              onClick={onDialogClose}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              className="text-base text-white"
              onClick={handleUpload}
              disabled={!file || isLoading}
            >
              {isLoading ? <LoadingSpinner iconClassName="text-white animate-spin" /> : "Submit"}
            </Button>
          </CardFooter>
        </CardContent>
      </Card>

      {/* Imported Users Table */}
      <div className="w-full flex justify-center items-center">
        <ImportedDataTable
          usersData={usersDataRef.current}
          total={usersDataRef.current.length}
          isOpen={isDrawerOpen}
          setIsOpen={setIsDrawerOpen}
        />
      </div>
    </>
  )
}
