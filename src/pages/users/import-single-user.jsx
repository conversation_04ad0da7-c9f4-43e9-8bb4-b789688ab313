/* eslint-disable no-param-reassign */
/* eslint-disable prefer-destructuring */
import { But<PERSON> } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import LoadingSpinner from "@/components/custom/LoadingSpinner"
import { useState } from "react"
import { useGetAllVendors, useImportUser } from "@/services/query/user.query"
import { toast } from "@/components/ui/use-toast"
import { USER_ROLE_OPTIONS, USER_ROLES } from "@/utils/constants"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import ct from "@constants/"
import { useSelector } from "react-redux"
import { isValidPhoneNumber } from "react-phone-number-input"
import InputF<PERSON><PERSON>ield from "../auth/login/component/input-form-field"
import Pass<PERSON><PERSON><PERSON><PERSON><PERSON> from "../auth/login/component/password-form-field"
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "../auth/login/component/select-form-field"
import PhoneFormField from "../auth/login/component/phone-form-field"

const formSchema = z
  .object({
    user_name: z.string().min(2, "Name must be at least 2 characters"),
    email: z.string().email("Please enter a valid email"),
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(/[a-z]/, "Password must contain at least one lowercase letter")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
      .regex(/[0-9]/, "Password must contain at least one number"),
    phone: z
      .string()
      .optional()
      .refine(
        (value) => {
          if (!value) return true
          return isValidPhoneNumber(value)
        },
        {
          message: "Please enter a valid phone number",
        }
      ),
    role: z.enum(Object.values(USER_ROLES)),
    vendor_name: z.string().optional(),
  })
  .refine(
    (data) => {
      if (data.role === USER_ROLES.VENDOR) return !!data.vendor_name
      return true
    },
    {
      message: "Vendor is required for vendor role",
      path: ["vendor_name"],
    }
  )


export default function ImportSingleUser({ onDialogClose }) {
  const { id: userID, userRole } = useSelector((state) => state[ct.store.USER_STORE])
  const { data: vendorsResponce } = useGetAllVendors()

  // eslint-disable-next-line no-unused-vars
  const [vendorOptions, setVendorOptions] = useState(
    vendorsResponce?.data?.map((vendor) => ({
      value: vendor.vendor_name,
      label: (
        <div className="flex items-center gap-x-2">
          <img src={vendor.logo} alt="" width="20px" height="20px" />
          <span>{vendor.vendor_name}</span>
        </div>
      ),
    })) ?? []
  )
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      user_name: "",
      email: "",
      password: "",
      phone: "",
      role: userRole === USER_ROLES.VENDOR ? USER_ROLES.STUDENT : "",
      vendor_name: "",
    },
  })
  const role = form.watch("role")

  const mutateImportUser = useImportUser(userID)
  async function onSubmit(data) {
    setIsLoading(true)

    if (data.role === USER_ROLES.VENDOR) {
      const selected_vendor = vendorsResponce?.data?.find(
        (vendor) => vendor.vendor_name === data.vendor_name
      )

      data.vendor_id = selected_vendor?.id
    }

    delete data.vendor_name

    mutateImportUser.mutate(
      { userID, data: [data] },
      {
        onSuccess: (responce) => {
          setIsLoading(false)

          const { created, skipped } = responce?.data || {}

          if (created && Array.isArray(created) && created.length > 0) {
            toast({
              title: "User Created Successfully",
              description: "New user created successfully",
              variant: "success",
            })
            onDialogClose() // Close the dialog on success
          }

          if (skipped) {
            const skippedEmails = skipped.emails || []
            const skippedUsernames = skipped.usernames || []
            const totalSkipped = skippedEmails.length + skippedUsernames.length

            if (totalSkipped > 0) {
              toast({
                title: "User Already Exists",
                description: "User already exists and was skipped",
                variant: "warning", // or "default" if warning variant doesn't exist
              })
            }
          }

          setTimeout(() => {
            form.reset()
          }, 100)
        },
        onError: (error) => {
          console.log("error", error)
          setIsLoading(false)

          let title =
            error?.response?.data?.error.message ?? "Failed. Please try again in some time"
          if (title.includes("DETAIL")) title = title.split("DETAIL")[1]
          toast({ title, variant: "destructive" })
        },
      }
    )
  }

  return (
    <Card className="w-full max-w-md lg:max-w-lg xl:min-h-[400px]">
      <CardHeader>
        <CardTitle className="mb-2">Import A New User</CardTitle>
        <CardDescription>Add new user credential below.</CardDescription>
      </CardHeader>

      <CardContent>
        {/* Form */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <InputFormField
              control={form.control}
              uniqueName="user_name"
              placeholder="User Name"
              inputType="text"
              isLabel={false}
            />
            <InputFormField
              control={form.control}
              uniqueName="email"
              placeholder="<EMAIL>"
              inputType="email"
              isLabel={false}
            />
            <PhoneFormField
              control={form.control}
              uniqueName="phone"
              placeholder="Enter phone number"
              isLabel={false}
            />
            <PasswordFormField
              control={form.control}
              uniqueName="password"
              placeholder="Enter Password"
              isNew
              isLabel={false}
            />
            {userRole === USER_ROLES.ADMIN && (
              <SelectFormField
                control={form.control}
                uniqueName="role"
                labelName="User Role"
                options={USER_ROLE_OPTIONS}
                placeholder="Select User Role"
                isLabel={false}
              />
            )}
            {role === USER_ROLES.VENDOR && (
              <SelectFormField
                control={form.control}
                uniqueName="vendor_name"
                labelName="Vendor"
                options={vendorOptions}
                placeholder="Select User's Vendor"
                isLabel={false}
              />
            )}
            {/* Add User Button */}
            <CardFooter className="w-full flex justify-end gap-3 p-0">
              <Button
                variant="secondary-outline"
                className="text-base"
                type="button"
                onClick={onDialogClose} // Call the close function on click
              >
                Cancel
              </Button>
              <Button variant="primary" className="text-base text-white" type="submit">
                {isLoading ? <LoadingSpinner iconClassName="text-white animate-spin" /> : "Submit"}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}