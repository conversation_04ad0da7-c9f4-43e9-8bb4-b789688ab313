/* eslint-disable jsx-a11y/anchor-is-valid */
import { useForm } from "react-hook-form"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { <PERSON>rk<PERSON> } from "lucide-react"
import propTypes from "prop-types"
import { Form } from "@/components/ui/form"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { useNavigate } from "react-router-dom"
import { useDispatch } from "react-redux"
import { loginAC } from "@/services/store/slices/user.slice"
import { toast } from "@/components/ui/use-toast"
import ct from "@constants/"
import { useSignUp } from "@/services/query/auth.query"
import { useState } from "react"
import LoadingSpinner from "@/components/custom/LoadingSpinner"
import { isValidPhoneNumber } from "react-phone-number-input"
import { AUTH_PAGES } from "./utils/constant"
import Input<PERSON><PERSON><PERSON><PERSON> from "./component/input-form-field"
import Password<PERSON><PERSON><PERSON><PERSON> from "./component/password-form-field"
import { getDeviceInfo } from "./utils/device-info"
import Phone<PERSON>orm<PERSON>ield from "./component/phone-form-field"
// Import isValidPhoneNumber

// Define the form validation schema
const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email"),
  // FIX: Corrected chaining of .refine after .optional()
  phone: z
    .string()
    .optional()
    .refine(
      (value) => {
        if (!value) return true // Allows empty string or undefined
        return isValidPhoneNumber(value)
      },
      {
        message: "Please enter a valid phone number",
      }
    ),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[0-9]/, "Password must contain at least one number"),
})

export default function SignUpForm({ setCurrentPage }) {
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      phone: "", // Ensure phone has a default value
    },
  })
  const [isTermsAccepted, setIsTermsAccepted] = useState(false)
  const [isSignupHover, setIsSignupHover] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Handle form submission
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const mutateSignUp = useSignUp()

  async function onSubmit(data) {
    // built payload
    const deviceInfo = getDeviceInfo()
    const payload = {
      signup_data: {
        user_name: data.name,
        email: data.email,
        password: data.password,
        phone: data.phone, // Phone data is now correctly passed
      },
      ...deviceInfo,
    }
    console.log("payload", payload)

    // make api call
    setIsLoading(true)
    mutateSignUp.mutate(
      { ...payload },
      {
        onSuccess: (responce) => {
          console.log("responce", responce)
          setIsLoading(false)
          dispatch(
            loginAC({
              userData: responce?.data?.user_data,
              sessionData: responce?.data?.session_data,
            })
          )
          toast({
            title: "Login",
            description: "Login successful",
            variant: "success",
          })
          navigate(ct.route.ROOT)
        },
        onError: (error) => {
          console.log("error", error)
          setIsLoading(false)
          toast({
            title: "Failed. Please try again in some time",
            variant: "destructive",
          })
        },
      }
    )
  }

  return (
    <div className="w-full max-w-md lg:max-w-lg space-y-4">
      {/* Header Content */}
      <div className="flex flex-col items-start font-medium">
        <p className="flex items-center gap-1 text-xl lg:text-2xl">
          Join us
          <Sparkles className="w-6 h-6 inline text-primary" />
        </p>
        <p className="text-sm lg:text-base">Create Traings10x account</p>
      </div>

      {/* Signup Form */}
      <div className="w-full px-1">
        <Form {...form}>
          <form
            onSubmit={(e) => {
              if (!isTermsAccepted) {
                e.preventDefault()
                toast({
                  title: "Terms & Conditions",
                  description: "Please accept the Terms & Conditions to sign up.",
                  variant: "warning",
                })
                return
              }
              form.handleSubmit(onSubmit)(e)
            }}
            className="space-y-6"
          >
            {/* Name */}
            <InputFormField
              control={form.control}
              uniqueName="name"
              labelName="Name"
              placeholder="Leonardo DiCaprio"
            />
            {/* Email */}
            <InputFormField
              control={form.control}
              uniqueName="email"
              labelName="Email"
              placeholder="<EMAIL>"
              inputType="email"
            />
            {/* Password */}
            <PasswordFormField
              control={form.control}
              uniqueName="password"
              labelName="Password"
              placeholder="Min.8 characters"
              isNew
            />

            {/* Phone */}
            <PhoneFormField
              control={form.control}
              uniqueName="phone"
              labelName="Phone"
              placeholder="Enter phone number"
              className="bg-white"
            />

            <div className="flex items-center space-x-2">
              <Checkbox
                id="terms-condition"
                className={`w-4 h-4 data-[state=checked]:bg-primary ${isSignupHover && !isTermsAccepted ? "scale-110" : ""}`}
                onCheckedChange={(e) => setIsTermsAccepted(e)}
              />
              <label htmlFor="terms-condition" className="text-sm font-medium">
                I agree to the{" "}
                <a href="#" className="text-sm text-primary">
                  Terms & Conditions
                </a>
              </label>
            </div>
            {/* Sign Up Button */}
            <Button
              className={`w-full text-base text-white ${isTermsAccepted ? "hover:cursor-pointer" : "hover:cursor-not-allowed"}`}
              variant="primary"
              type="submit" // Ensure type="submit" is present
              onMouseEnter={() => setIsSignupHover(true)}
              onMouseLeave={() => setIsSignupHover(false)}
            >
              {isLoading ? <LoadingSpinner iconClassName="text-white animate-spin" /> : "Sign Up"}
            </Button>
          </form>
        </Form>
      </div>

      {/* Sign Up Link */}
      <p className="text-sm mt-4">
        Already have an account?{"  "}
        <Button
          className="text-primary text-sm hover:cursor-pointer px-1"
          onClick={() => setCurrentPage(AUTH_PAGES.login)}
          variant="icon"
        >
          Log In
        </Button>
      </p>
    </div>
  )
}

SignUpForm.propTypes = {
  setCurrentPage: propTypes.func,
}
