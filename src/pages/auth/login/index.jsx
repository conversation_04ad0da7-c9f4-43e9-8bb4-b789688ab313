import { useState } from "react"
import ForgetPassword from "./forget-password"
import LogInForm from "./login-form"
import SignUpForm from "./signup-form"
import { AUTH_PAGES } from "./utils/constant"

export default function LoginPage() {
  const [currentPage, setCurrentPage] = useState("login")

  const getCurrentPage = () => {
    switch (currentPage) {
      case AUTH_PAGES.signup:
        return <SignUpForm setCurrentPage={setCurrentPage} />
      case AUTH_PAGES.forgetPassword:
        return <ForgetPassword setCurrentPage={setCurrentPage} />
      default:
        return <LogInForm setCurrentPage={setCurrentPage} />
    }
  }
  console.log(currentPage)
  return (
    <div className="min-h-screen grid grid-cols-1 lg:grid-cols-5">
      {/* Image column - hidden below lg (1024px) */}
      <div className="hidden lg:block lg:col-span-3">
        <img
          src="https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80"
          alt="Left side cover"
          className="w-full h-full object-cover"
        />
      </div>

      {/* Content column - full width below lg, 2/5 on lg and above */}
      <div className="col-span-1 lg:col-span-2">
        <div className="w-full h-full flex flex-col items-center justify-center gap-4 p-4 sm:p-6 lg:py-10">
          {getCurrentPage()}
          {/* {currentPage !== AUTH_PAGES.forgetPassword && <SocialLogin />} */}
          {/* <ResetPassword /> */}
        </div>
      </div>
    </div>
  )
}
