import { useForm } from "react-hook-form"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { Form } from "@/components/ui/form"
import { Button } from "@/components/ui/button"
import propTypes from "prop-types"
import { useNavigate, useSearchParams } from "react-router-dom"
import { useResetPassword } from "@/services/query/auth.query"
import { toast } from "@/components/ui/use-toast"
import PasswordFormField from "./component/password-form-field"

// Define the form validation schema
const formSchema = z
  .object({
    password1: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(/[a-z]/, "Password must contain at least one lowercase letter")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
      .regex(/[0-9]/, "Password must contain at least one number"),
    password2: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(/[a-z]/, "Password must contain at least one lowercase letter")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
      .regex(/[0-9]/, "Password must contain at least one number"),
  })
  .refine((data) => data.password1 === data.password2, {
    message: "Passwords do not match",
    path: ["password2"],
  })

export default function ResetPassword() {
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password1: "",
      password2: "",
    },
  })

  const [searchParam] = useSearchParams()
  const token = searchParam.get("token")
  console.log("token", token)
  const navigate = useNavigate()
  const mutateResetPassword = useResetPassword()
  // Handle form submission
  const onSubmit = (values) => {
    console.log("Form submitted:", values, token)
    mutateResetPassword.mutate(
      { token, new_password: values.password1 },
      {
        onSuccess: (responce) => {
          console.log("responce", responce)
          toast({
            title: "New Password",
            description: "New password set successfully",
            variant: "success",
          })
          navigate("/login")
        },
        onError: (error) => {
          console.log("error", error)
          toast({
            title: "Failed. Please try again in some time",
            variant: "destructive",
          })
        },
      }
    )
  }

  return (
    <div className="min-h-svh grid grid-cols-5">
      <div className="col-span-3">
        <img
          src="https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80"
          alt="Design"
          className="w-full h-full"
        />
      </div>
      <div className="col-span-2">
        <div className="w-full h-full flex flex-col items-center justify-center gap-4 py-6 md:py-10">
          <div className="w-full max-w-md lg:max-w-lg space-y-4">
            {/* Header Content */}
            <p className="text-2xl lg:text-3xl font-medium">Enter New Password</p>

            {/* Form */}
            <div className="w-full px-1">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <PasswordFormField
                    control={form.control}
                    uniqueName="password1"
                    labelName="New Password"
                    placeholder="Min.8 characters"
                    isNew
                  />
                  <PasswordFormField
                    control={form.control}
                    uniqueName="password2"
                    labelName="Confirm Password"
                    placeholder="Password again"
                  />
                  {/* Change Password Button */}
                  <Button className="w-full text-lg text-white" variant="primary">
                    Change Password
                  </Button>
                </form>
              </Form>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

ResetPassword.propTypes = {
  setCurrentPage: propTypes.func,
}
