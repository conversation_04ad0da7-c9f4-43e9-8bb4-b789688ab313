import LoadingSpinner from "@/components/custom/LoadingSpinner"
import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import { Button } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { toast } from "@/components/ui/use-toast"
import { generateFCMToken } from "@/firebase/firebase"
import { useLogIn } from "@/services/query/auth.query"
import { loginAC, makeSessionInvalidAC } from "@/services/store/slices/user.slice"
import { USER_ROLES } from "@/utils/constants"
import { checkTokenExpiry } from "@/utils/helper"
import ct from "@constants/"
import { zodResolver } from "@hookform/resolvers/zod"
import { Sparkles } from "lucide-react"
import propTypes from "prop-types"
import { useEffect, useRef, useState } from "react"
import { useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { useNavigate } from "react-router-dom"
import { z } from "zod"
import Input<PERSON><PERSON><PERSON><PERSON> from "./component/input-form-field"
import Password<PERSON><PERSON><PERSON>ield from "./component/password-form-field"
import { AUTH_PAGES } from "./utils/constant"
import { getDeviceInfo } from "./utils/device-info"

// Define the form validation schema
const formSchema = z.object({
  email: z.string().email("Please enter a valid email"),
  password: z.string().min(8, "Password must be at least 8 characters"),
})

export default function LogInForm({ setCurrentPage }) {
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  })

  const logInBtnRef = useRef(null)

  // Handle form submission
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const mutateLogin = useLogIn()
  const [isLoading, setIsLoading] = useState(false)

  const navigateUser = (role) => {
    if (role === USER_ROLES.ADMIN || role === USER_ROLES.VENDOR) navigate(ct.route.ADMIN_DASHBOARD)
    else if (role === USER_ROLES.STUDENT) navigate(ct.route.LIVE_COURSES)
    else if (role === USER_ROLES.TRAINER) navigate("/course/trainer")
    else if (role === USER_ROLES.MARKETER) navigate(ct.route.JD_PAGE)
  }

  // Check: The user is already logged in
  const userStore = useSelector((st) => st[ct.store.USER_STORE])
  const userRole = userStore?.userRole
  const refresh_token = userStore?.sessionData?.refresh_token
  useEffect(() => {
    if (userRole && refresh_token) {
      // Check: The refresh token validity
      if (checkTokenExpiry(refresh_token)) {
        dispatch(makeSessionInvalidAC())
        return
      }
      console.log("already logged in and token valid")
      navigateUser(userRole)
    }
  }, [])

  async function onSubmit(data) {
    // built payload
    const deviceInfo = getDeviceInfo()
    const payload = {
      login_data: {
        email: data.email,
        password: data.password,
      },
      ...deviceInfo,
    }

    // make api call
    setIsLoading(true)
    mutateLogin.mutate(
      { ...payload },
      {
        onSuccess: (response) => {
          console.log("response", response)

          if (response?.metadata?.message === "OK") {
            setIsLoading(false)
            dispatch(
              loginAC({
                userData: response?.data?.user_data,
                vendorData: response?.data?.vendor_data,
                sessionData: response?.data?.session_data,
              })
            )
            successToast("Login Successful")

            // Landing page based on role
            const role = response?.data?.user_data.role
            navigateUser(role)

            generateFCMToken().then(
              (token) => {
                console.log("token", token)
              },
              (error) => {
                console.log("error", error)
              }
            )
          } else {
            // Handle case where metadata message is not "OK"
            setIsLoading(false)
            failureToast("Login Failed", response?.metadata?.message)
          }
        },
        onError: (error) => {
          console.log("Full error object:", error)
          console.log("Status code:", error?.response?.status)
          setIsLoading(false)
          toast({
            title: error?.response?.data?.error?.message ?? "Failed. Please try again in some time",
            variant: "destructive",
          })
        },
      }
    )
  }

  return (
    <div className="w-full max-w-md lg:max-w-lg space-y-4">
      {/* Header Content */}
      <div className="flex flex-col items-center lg:items-start font-medium">
        <p className="flex items-center justify-center lg:justify-start gap-1 text-xl lg:text-2xl">
          Welcome Back
          <Sparkles className="w-6 h-6 inline text-primary" />
        </p>
        <p className="text-sm lg:text-base text-center lg:text-left">Login to your account</p>
      </div>

      {/* Login Form */}
      <div className="w-full px-1">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <InputFormField
              control={form.control}
              uniqueName="email"
              labelName="Email"
              placeholder="<EMAIL>"
              inputType="email"
            />
            <PasswordFormField
              control={form.control}
              uniqueName="password"
              labelName="Password"
              placeholder="Min.8 characters"
              // If user press enter key then click login button
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault()
                  e.stopPropagation()
                  logInBtnRef.current.click()
                }
              }}
            />
            <div className="flex justify-between text-base">
              <Button
                href="#"
                className="text-primary font-normal p-0 m-0"
                onClick={() => setCurrentPage(AUTH_PAGES.forgetPassword)}
                variant="icon"
              >
                Forgot Password?
              </Button>
            </div>
            {/* Login Button */}
            <Button
              variant="primary"
              className="w-full text-base text-white"
              type="submit"
              ref={logInBtnRef}
            >
              {isLoading ? <LoadingSpinner iconClassName="text-white animate-spin" /> : "Login"}
            </Button>
          </form>
        </Form>
      </div>

      {/* Sign Up Link */}
      <p className="text-sm">
        Not registered yet?{" "}
        <Button
          className="text-primary hover:cursor-pointer"
          onClick={() => setCurrentPage(AUTH_PAGES.signup)}
          variant="icon"
        >
          Create account
        </Button>
      </p>
    </div>
  )
}

LogInForm.propTypes = {
  setCurrentPage: propTypes.func,
}
