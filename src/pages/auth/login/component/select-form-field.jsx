/* eslint-disable react/forbid-prop-types */
import propTypes from "prop-types"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { CLASS_NAMES } from "../utils/constant"

function SelectFormField({
  control,
  uniqueName,
  labelName,
  options = [],
  placeholder,
  isLabel = true,
}) {
  return (
    <FormField
      control={control}
      name={uniqueName}
      render={({ field, fieldState: { error } }) => (
        <FormItem>
          {isLabel && (
            <FormLabel className={CLASS_NAMES.formLabel}>
              {labelName}
              <span className={`${error ? "text-red-600" : "text-primary"}`}>*</span>
            </FormLabel>
          )}
          <FormControl>
            <Select onValueChange={field.onChange}>
              <SelectTrigger
                className={`${field.value ? "" : "text-gray-500"} ${CLASS_NAMES.input} border-gray-300`}
              >
                <SelectValue placeholder={placeholder} />
              </SelectTrigger>
              <SelectContent>
                {options.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormControl>
          <FormMessage className={CLASS_NAMES.formMessage} />
        </FormItem>
      )}
    />
  )
}

SelectFormField.propTypes = {
  control: propTypes.object.isRequired,
  uniqueName: propTypes.string.isRequired,
  labelName: propTypes.string.isRequired,
  placeholder: propTypes.string,
  options: propTypes.arrayOf(
    propTypes.shape({
      label: propTypes.string.isRequired,
      value: propTypes.string.isRequired,
    })
  ).isRequired,
  isLabel: propTypes.bool,
}

export default SelectFormField
