/* eslint-disable react/forbid-prop-types */
import propTypes from "prop-types"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { CLASS_NAMES } from "../utils/constant"

function InputFormField({
  control,
  uniqueName,
  placeholder,
  labelName = "",
  inputType = "text",
  isLabel = true,
}) {
  return (
    <FormField
      control={control}
      name={uniqueName}
      render={({ field, fieldState: { error } }) => (
        <FormItem>
          {isLabel && (
            <FormLabel className={CLASS_NAMES.formLabel}>
              {labelName}
              <span className={`${error ? "text-red-600" : "text-primary"}`}>*</span>
            </FormLabel>
          )}
          <FormControl>
            <Input
              {...field}
              className={CLASS_NAMES.input}
              type={inputType}
              placeholder={placeholder}
            />
          </FormControl>
          <FormMessage className={CLASS_NAMES.formMessage} />
        </FormItem>
      )}
    />
  )
}

InputFormField.propTypes = {
  control: propTypes.object,
  uniqueName: propTypes.string,
  labelName: propTypes.string,
  placeholder: propTypes.string,
  inputType: propTypes.string,
  isLabel: propTypes.bool,
}

export default InputFormField
