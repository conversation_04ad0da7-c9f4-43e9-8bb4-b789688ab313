/* eslint-disable react/forbid-prop-types */
import propTypes from "prop-types"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import PhoneInput from "react-phone-number-input"
import { CLASS_NAMES } from "../utils/constant"
import "react-phone-number-input/style.css"

function PhoneFormField({
    control,
    uniqueName,
    placeholder,
    labelName = "",
    isLabel = true,
    className = "",
}) {
    return (
        <FormField
            control={control}
            name={uniqueName}
            render={({ field, fieldState: { error } }) => (
                <FormItem>
                    {isLabel && (
                        <FormLabel className={CLASS_NAMES.formLabel}>
                            {labelName}
                            <span className={`${error ? "text-red-600" : "text-primary"}`}>*</span>
                        </FormLabel>
                    )}
                    <FormControl>
                        <div className="relative">
                            <PhoneInput
                                {...field}
                                placeholder={placeholder}
                                defaultCountry="IN"
                                international
                                countryCallingCodeEditable={false}
                                className={`
                  flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm  
                  file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground
                 
                  disabled:cursor-not-allowed disabled:opacity-50
                  lg:h-12 lg:text-sm
                  ${error ? "border-red-500 focus-within:ring-red-500" : ""}
                  ${className}
                `}
                            />
                        </div>
                    </FormControl>
                </FormItem>
            )}
        />
    )
}

PhoneFormField.propTypes = {
    control: propTypes.object,
    uniqueName: propTypes.string,
    labelName: propTypes.string,
    placeholder: propTypes.string,
    isLabel: propTypes.bool,
    className: propTypes.string,
}

export default PhoneFormField
