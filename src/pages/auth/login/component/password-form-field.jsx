/* eslint-disable react/forbid-prop-types */
import { <PERSON>, <PERSON>Off, Info } from "lucide-react"
import { useState } from "react"
import propTypes from "prop-types"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import WrapToolTip from "@/components/wrap-tool-tip/wrap-tool-tip"
import { CLASS_NAMES } from "../utils/constant"

function PasswordFormField({
  control,
  uniqueName,
  placeholder,
  labelName = "",
  onKeyDown = null,
  isNew = false,
  isLabel = true,
}) {
  const [show, setShow] = useState(false)

  const getHelpIcon = () => (
    <WrapToolTip
      delayDuration={10}
      side="top"
      toolTipContent={
        <>
          <p className="text-[10px] lg:text-[12px]">Password must:</p>
          <ul className="list-disc text-xs pl-4 mt-1">
            <li>Be at least 8 characters</li>
            <li>Contain at least one lowercase letter</li>
            <li>Contain at least one uppercase letter</li>
            <li>Contain at least one number</li>
          </ul>
        </>
      }
    >
      <Info className="h-4 w-4 text-muted-foreground cursor-help mr-2" />
    </WrapToolTip>
  )

  return (
    <FormField
      control={control}
      name={uniqueName}
      render={({ field, fieldState: { error } }) => (
        <FormItem>
          <div
            className={`flex items-center ${isLabel ? "justify-between" : "justify-end"} gap-2 -mt-2`}
          >
            {isLabel && (
              <FormLabel className={CLASS_NAMES.formLabel}>
                {labelName}
                <span className={`${error ? "text-red-600" : "text-primary"}`}>*</span>
              </FormLabel>
            )}
            {isNew && isLabel && getHelpIcon()}
          </div>
          <FormControl>
            <div className="relative">
              <Input
                {...field}
                className={CLASS_NAMES.input}
                type={show ? "text" : "password"}
                placeholder={placeholder}
                onKeyDown={onKeyDown}
              />
              <Button
                variant="ghost"
                className="absolute top-1/2 -translate-y-1/2 right-3"
                onClick={(e) => {
                  e.preventDefault() // Prevent any form events
                  e.stopPropagation() // Stop event bubbling
                  setShow(!show)
                }}
              >
                {show ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </Button>
            </div>
          </FormControl>
          <FormMessage className={CLASS_NAMES.formMessage} />
        </FormItem>
      )}
    />
  )
}

PasswordFormField.propTypes = {
  control: propTypes.object,
  uniqueName: propTypes.string,
  labelName: propTypes.string,
  placeholder: propTypes.string,
  onKeyDown: propTypes.func,
  isNew: propTypes.bool,
  isLabel: propTypes.bool,
}

export default PasswordFormField
