// Detect device type
const getDeviceType = () => {
  const ua = navigator.userAgent
  if (/(tablet|ipad|playbook|silk)|(android(?!.*mobi))/i.test(ua)) {
    return "tablet"
  }
  if (
    /Mobile|Android|iP(hone|od)|IEMobile|BlackBerry|Kindle|Silk-Accelerated|(hpw|web)OS|Opera M(obi|ini)/.test(
      ua
    )
  ) {
    return "mobile"
  }
  return "web"
}

// deviceInfo.js
export const getDeviceInfo = () => {
  // Get screen details
  const screenResolution = `${window.screen.width}x${window.screen.height}`

  return {
    device_info: {
      user_agent: navigator.userAgent,
      platform: navigator.platform,
      screen_resolution: screenResolution,
      color_depth: window.screen.colorDepth,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: navigator.language,
      device_type: getDeviceType(),
    },
  }
}
