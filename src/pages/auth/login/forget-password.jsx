import { useForm } from "react-hook-form"
import { z } from "zod"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import propTypes from "prop-types"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { useForgetPassword } from "@/services/query/auth.query"
import { toast } from "@/components/ui/use-toast"
import { failureToast } from "@/components/custom/toasts/tosters"
import { AUTH_PAGES } from "./utils/constant"
import InputFormField from "./component/input-form-field"

// Define the form validation schema
const formSchema = z.object({
  email: z.string().email("Please enter a valid email"),
})

function ForgetPassword({ setCurrentPage }) {
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
    },
  })

  // Handle form submission
  const mutateForgetPassword = useForgetPassword()
  const onSubmit = (values) => {
    console.log("Form submitted:", values)
    // make api call
    mutateForgetPassword.mutate(values.email, {
      onSuccess: (responce) => {
        console.log("responce", responce)
        toast({
          title: "Reset Link Sent",
          description: responce?.message,
          variant: "success",
        })
      },
      onError: (error) => {
        console.log("error", error.message)
        failureToast(
          "Login Failed",
          error?.response?.data?.error.message ?? "Failed. Please try again in some time"
        )
      },
    })
  }

  return (
    <div className="w-full max-w-md lg:max-w-lg space-y-4">
      {/* Header Content */}
      <div className="space-y-5">
        <p className="text-2xl lg:text-3xl font-medium">Forgot Password ?</p>
        <p className="text-[16px]">
          No Problem! Enter your email or username below and we will send you an email with
          instructions to reset your password.
        </p>
      </div>

      {/* Login Form */}
      <div className="w-full px-1">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Email Field */}
            <InputFormField
              control={form.control}
              uniqueName="email"
              labelName="Email"
              placeholder="<EMAIL>"
              inputType="email"
            />
            {/* Reset Link Button */}
            <Button className="w-full text-lg text-white" variant="primary">
              Send Reset Link
            </Button>
          </form>
        </Form>
      </div>

      {/* Login Link */}
      <p className="flex justify-center text-base">
        Back to{" "}
        <Button
          className="text-primary hover:cursor-pointer px-1"
          onClick={() => setCurrentPage(AUTH_PAGES.login)}
          variant="icon"
        >
          Login
        </Button>
      </p>
    </div>
  )
}

ForgetPassword.propTypes = {
  setCurrentPage: propTypes.func,
}

export default ForgetPassword
