import Login from "@pages/auth/login"
import { Provider } from "react-redux"
// eslint-disable-next-line import/no-extraneous-dependencies
import { expect, userEvent, within } from "@storybook/test"
import { mockStore } from "@store/"
import ct from "@constants/"
import { MemoryRouter } from "react-router-dom"

export default {
  title: "Pages/Auth/LoginPage",
  component: Login,
  // decorators: [withRouter],
}

function Template() {
  return (
    <Provider store={mockStore}>
      <MemoryRouter>
        <Login />
      </MemoryRouter>
    </Provider>
  )
}

export const Default = Template.bind({})

Default.play = async ({ canvasElement }) => {
  const canvas = within(canvasElement)

  // Find the email input and button
  const emailInput = canvas.getByPlaceholderText("email")
  const submitButton = canvas.getByRole("button", { name: /submit/i })
  // Simulate user input
  await userEvent.type(emailInput, "<EMAIL>")
  await userEvent.click(submitButton)

  const userState = mockStore.getState()[ct.store.USER_STORE]
  await expect(userState.userName).toEqual("<EMAIL>")
  await expect(userState.userRole).toEqual("user")
}
