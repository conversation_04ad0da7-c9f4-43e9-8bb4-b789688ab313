import ct from "@constants/"
import { But<PERSON> } from "@/components/ui/button"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { toast } from "@/components/ui/use-toast"
import { loginAC } from "@store/slices/user.slice"
import { useDispatch } from "react-redux"
import { useNavigate } from "react-router-dom"

const FormSchema = z.object({
  email: z.string().email().min(5, {
    message: "Username must be at least 2 characters.",
  }),
})

export default function Login() {
  const navigate = useNavigate()
  const dispatch = useDispatch()

  const form = useForm({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      email: "",
    },
  })

  async function onSubmit(data) {
    dispatch(
      loginAC({
        userName: data.email,
        userRole: "user",
      })
    )
    toast({
      title: "Login",
      description: "Login successful",
      variant: "success",
    })

    navigate(ct.route.ROOT)
  }
console.log("Test deployment one")
  return (
    <div className="flex justify-center items-center h-screen">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="w-2/3 space-y-6">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Login</FormLabel>
                <FormControl>
                  {/* eslint-disable-next-line react/jsx-props-no-spreading */}
                  <Input placeholder="email" {...field} className="w-62" />
                </FormControl>
                <FormDescription>Use your email address to login.</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit">Submit</Button>
        </form>
      </Form>
    </div>
  )
}
