import { Bell, User } from "lucide-react"
import BaseInfoEditorForm from "../base-info/base-info-editor-form"
import NotificationSettings from "../notification-settings/notification-settings"

export const PROFILE_TABS = [
  {
    id: "basic-info",
    label: "Basic Info",
    icon: <User className="w-5 h-5 hover:text-blue-500" />,
    content: <BaseInfoEditorForm />,
  },
  {
    id: "notifications",
    label: "Notifications",
    icon: <Bell className="w-5 h-5 hover:text-blue-500" />,
    content: <NotificationSettings />,
  },
]
