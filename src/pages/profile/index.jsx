import ProfileImageUpload from "@/components/profile-image-upload/profile-image-upload"
import { Card } from "@/components/ui/card"
import { useEffect, useState } from "react"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { useDispatch, useSelector } from "react-redux"
import ct from "@constants/"
import { useGetUser, useUpdateUser } from "@/services/query/user.query"
import { onApplyChangesAC } from "@/services/store/slices/user.slice"
import { PROFILE_TABS } from "./utils/tabs.contast"

function Profile() {
  // Get User from Redux
  const {
    id: userID,
    full_name,
    user_name,
    image: profileImage,
  } = useSelector((state) => state[ct.store.USER_STORE])
  const dispatch = useDispatch()

  // Fetch user from api
  const { data: userResponce } = useGetUser(userID)
  useEffect(() => {
    const userData = userResponce?.data
    dispatch(
      onApplyChangesAC({
        full_name: userData?.full_name,
        email: userData?.email,
        phone: userData?.phone,
        date_of_birth: userData?.date_of_birth,
        skills: userData?.skills,
        location: userData?.location,
        designation: userData?.designation,
        cv_link: userData?.cv_link,
        linkedin_link: userData?.linkedin_link,
        github_link: userData?.github_link,
        image: userData?.image,
        resume: userData?.resume,
        summary: userData?.summary,
      })
    )
  }, [userResponce?.data])

  const [image, setImage] = useState(profileImage)
  const mutateUpdateUser = useUpdateUser()

  const handleImageChange = (imageData) => {
    // Set the image preview immidiately
    setImage(imageData.preview)

    // mutate
    const formData = new FormData()
    formData.append("profile_image", imageData.file)
    mutateUpdateUser.mutate(
      { userID, data: formData },
      {
        onSuccess: () => {
          console.log("Updated")
        },
        onError: (error) => {
          console.log("error while uploading profile image", error)
          // If api fails remove the image
          setImage(null)
        },
      }
    )
  }

  return (
    <div className="w-full h-screen xl:h-[120vh] relative overflow-hidden">
      {/* Profile Image & Name */}
      <Card className="w-full h-[30%] relative flex items-center bg-gradient-to-r from-blue-300 to-blue-600 z-10">
        <div className="w-full flex items-center gap-x-16 lg:px-52">
          <ProfileImageUpload image={image} handleImageChange={handleImageChange} size="lg" />
          <div>
            <p className="text-2xl text-white font-medium">{full_name}</p>
            <p className="text-base text-gray-100">Username: {user_name}</p>
          </div>
        </div>
      </Card>

      {/* Content */}
      <Tabs
        defaultValue={PROFILE_TABS[0].id}
        className="relative w-full h-full flex"
        orientation="vertical"
      >
        <TabsList
          className="min-h-[50%] absolute flex flex-col justify-start 
          bg-transparent left-[5%] 2xl:left-[15%] mt-10 space-y-3"
        >
          {PROFILE_TABS.map((tab) => (
            <TabsTrigger
              key={tab.id}
              value={tab.id}
              className="w-full flex justify-start gap-x-1 text-base
              data-[state=active]:bg-blue-500 data-[state=active]:text-white 
              data-[state=active]:shadow-lg data-[state=active]:shadow-blue-300 
              data-[state=active]:scale-105 rounded-[8px]
              transition-all duration-300 ease-in-out transforms"
            >
              {tab.icon}
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>
        {PROFILE_TABS.map((tab) => (
          <TabsContent key={tab.id} value={tab.id} className="flex-1">
            <Card
              className="w-[50%] min-h-[50%] h-fit absolute xl:-top-[2%] 2xl:-top-[7%] xl:left-[27%]
              bg-white z-20 rounded-2xl shadow-lg hover:shadow-xl py-3 px-6"
            >
              {tab.content}
            </Card>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  )
}

export default Profile
