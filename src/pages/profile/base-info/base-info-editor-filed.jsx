import { useState } from "react"
import { Edit } from "lucide-react"
import PropTypes from "prop-types"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"

function BaseInfoEditorField({
  keyName,
  labelName,
  children,
  placeholder,
  value,
  onSave,
  onCancel,
}) {
  const [isEdit, setIsEdit] = useState(false)

  const handleSave = () => {
    onSave(keyName)
    setIsEdit(false)
  }

  const handleCancel = () => {
    onCancel()
    setIsEdit(false)
  }

  return (
    <>
      <div className="w-full flex items-start px-2">
        {/* Label */}
        <Label className="w-1/4 text-sm md:text-base text-zinc-700 font-normal">{labelName}</Label>

        {/* Content */}
        <div className="w-2/4 text-sm md:text-base">
          {isEdit ? (
            <>
              {children}
              <div className="space-x-3 pt-3">
                <Button variant="primary" className="bg-blue-500" onClick={handleSave}>
                  Save
                </Button>
                <Button variant="outline" onClick={handleCancel}>
                  Cancel
                </Button>
              </div>
            </>
          ) : (
            <span className={`${value ? "" : "text-slate-400"}`}>{value || placeholder}</span>
          )}
        </div>

        {/* Edit */}
        {!isEdit && (
          <div className="w-1/4 text-end">
            <Button
              className="text-blue-500 text-sm md:text-base  dark:text-[#FFFFFF] font-semibold hover:scale-105 pt-0"
              variant="icon"
              onClick={() => setIsEdit(!isEdit)}
            >
              <Edit className="w-5 h-5 font-normal mr-1" />
              Edit
            </Button>
          </div>
        )}
      </div>
      <hr className="w-full border-gray-200 my-3" />
    </>
  )
}

BaseInfoEditorField.propTypes = {
  keyName: PropTypes.string.isRequired,
  labelName: PropTypes.string.isRequired,
  children: PropTypes.node,
  placeholder: PropTypes.string,
  value: PropTypes.string,
  onSave: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
}

export default BaseInfoEditorField
