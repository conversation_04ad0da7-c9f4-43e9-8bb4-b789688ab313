import { useState } from "react"
import { Textarea } from "@/components/ui/textarea"
import { useDispatch, useSelector } from "react-redux"
import ct from "@constants/"
import { onApplyChangesAC } from "@/services/store/slices/user.slice"
import { useUpdateUser } from "@/services/query/user.query"
import LoadingSpinner from "@/components/custom/LoadingSpinner"
import { toast } from "@/components/ui/use-toast"
import TagInput from "../../../components/tag-input"
import { Input } from "../../../components/ui/input"
import BaseInfoEditorField from "./base-info-editor-filed"
import { FIELD_TYPES } from "../utils/constants"

const FIELD_CONFIGS = [
  {
    section: "Basic Info",
    fields: [
      {
        keyName: "full_name",
        labelName: "Name",
        type: FIELD_TYPES.TEXT,
        placeholder: "Your Name",
      },
      {
        keyName: "date_of_birth",
        labelName: "Date of Birth",
        type: FIELD_TYPES.DATE,
        placeholder: "Your Birth Date",
      },
      {
        keyName: "location",
        labelName: "Location",
        type: FIELD_TYPES.TEXT,
        placeholder: "Your Current Location",
      },
      {
        keyName: "summary",
        labelName: "Summary",
        type: FIELD_TYPES.TEXT_AREA,
        placeholder: "Tell us about yourself (objective, experience, etc.)",
      },
      {
        keyName: "linkedin_link",
        labelName: "LinkedIn",
        type: FIELD_TYPES.TEXT,
        placeholder: "Your LinkedIn url",
      },
      {
        keyName: "github_link",
        labelName: "GitHub",
        type: FIELD_TYPES.TEXT,
        placeholder: "Your GitHub url",
      },
    ],
  },
  {
    section: "Professional Info",
    fields: [
      {
        keyName: "designation",
        labelName: "Designation",
        type: FIELD_TYPES.TEXT,
        placeholder: "Your Current Designation",
      },
      {
        keyName: "skills",
        labelName: "Technical Skills",
        type: FIELD_TYPES.TAGS,
        placeholder: "Your Technical Skills",
      },
      {
        keyName: "resume",
        labelName: "Resume",
        type: FIELD_TYPES.FILE,
        placeholder: "Upload Your Resume(max 5mb)",
      },
    ],
  },
]

function BaseInfoEditorForm() {
  // Get current profile data from Redux store
  const [profileData, skills, resumeUrl] = useSelector((state) => {
    const userStore = state[ct.store.USER_STORE]
    return [
      {
        id: userStore.id,
        full_name: userStore.full_name,
        user_name: userStore.user_name,
        userRole: userStore.userRole,
        date_of_birth: userStore.date_of_birth,
        location: userStore.location,
        designation: userStore.designation,
        linkedin_link: userStore.linkedin_link,
        github_link: userStore.github_link,
        summary: userStore.summary,
      },
      userStore.skills?.split(", ") ?? [],
      userStore.resume,
    ]
  })

  // Local state to track changes
  const [profileDataSt, setProfileDataSt] = useState(profileData)
  const [skillsSt, setSkillsSt] = useState(skills)
  const [resumeSt, setResumeSt] = useState(null)
  const [resumeError, setResumeError] = useState(null)
  const [isLoading, setIsLoading] = useState(false)

  const dispatch = useDispatch()
  const mutateUpdateUser = useUpdateUser()

  // On changes to local state
  const onChangeProfileData = (keyName, value) => {
    console.log("onChangeProfileData", keyName, value)
    setProfileDataSt((prev) => ({ ...prev, [keyName]: value }))
  }

  const onChangeResume = (keyName, file) => {
    if (file) {
      const maxFileSize = 1 * 1024 * 1024
      if (file.size > maxFileSize) {
        setResumeError("File size should be less than 5mb.")
        return
      }
      setResumeSt(file)
      setResumeError(null)
    }
  }

  // Apply changes to store while user clicks save button
  const onSave = () => {
    console.log("onSaveProfileDataStore", profileDataSt)
    const formData = new FormData()
    setIsLoading(true)

    // Add user data as JSON string
    formData.append("user_data", JSON.stringify({ ...profileDataSt, skills: skillsSt?.join(", ") }))
    if (resumeSt) formData.append("resume", resumeSt)

    mutateUpdateUser.mutate(
      { userID: profileDataSt.id, data: formData },
      {
        onSuccess: (responce) => {
          dispatch(onApplyChangesAC(profileDataSt))
          console.log(responce)
        },
        onError: (error) => {
          toast({
            title: "Update failed",
            description: error?.response?.data?.error.message ?? "Please try again in some time",
            variant: "destructive",
          })
          setProfileDataSt(profileData)
          setSkillsSt(skills)
        },
      }
    )
    setResumeError(null)
    setIsLoading(false)
  }

  // Reset form data to current Redux store values
  const onCancel = () => {
    setProfileDataSt(profileData)
    setSkillsSt(skills)
    setResumeError(null)
  }

  const renderField = (fieldConfig) => {
    const { keyName, type, placeholder, labelName } = fieldConfig

    let fieldComponent
    switch (type) {
      case FIELD_TYPES.TEXT_AREA:
        fieldComponent = (
          <Textarea
            id={keyName}
            value={profileDataSt[keyName]}
            onChange={(e) => onChangeProfileData(keyName, e.target.value)}
            placeholder={placeholder}
            className="max-w-[90%] resize-none"
          />
        )
        break

      case FIELD_TYPES.TAGS:
        fieldComponent = (
          <TagInput
            id={keyName}
            values={skillsSt}
            setValues={setSkillsSt}
            className="max-w-[90%] border-2 border-gray-200 rounded-lg"
          />
        )
        break

      case FIELD_TYPES.FILE:
        fieldComponent = (
          <>
            <input
              id={keyName}
              type={type}
              onChange={(e) => onChangeResume(keyName, e.target.files[0])}
              className="max-w-[75%] text-base"
            />
            {resumeError && <p className="text-red-500">{resumeError}</p>}
          </>
        )
        break

      default:
        fieldComponent = (
          <Input
            id={keyName}
            type={type}
            value={profileDataSt[keyName]}
            placeholder={placeholder}
            onChange={(e) => onChangeProfileData(keyName, e.target.value)}
            className={`${type === FIELD_TYPES.DATE ? "max-w-[50%]" : "max-w-[75%]"}`}
          />
        )
    }

    // Formatting value for display
    let value
    if (keyName === "skills") value = skillsSt?.join(", ")
    else if (keyName === "resume" && resumeUrl)
      value = (
        <a href={resumeUrl} target="_blank" rel="noopener noreferrer">
          <span className="underline">{`${profileData.full_name}'s Resume`}</span>
        </a>
      )
    else value = profileDataSt[keyName]

    return (
      <BaseInfoEditorField
        key={keyName}
        keyName={keyName}
        labelName={labelName}
        placeholder={placeholder}
        value={value}
        onSave={onSave}
        onCancel={onCancel}
      >
        {fieldComponent}
      </BaseInfoEditorField>
    )
  }

  return (
    <div className="w-full relative">
      {FIELD_CONFIGS.map(({ section, fields }) => (
        <div key={section}>
          <h2 className="text-base lg:text-lg dark:text-white font-semibold my-5">{section}</h2>
          {fields.map(renderField)}
        </div>
      ))}
      {isLoading && (
        <div className="fixed inset-0 flex justify-center items-center bg-black/30 backdrop-blur-sm z-50">
          <LoadingSpinner />
        </div>
      )}
    </div>
  )
}

export default BaseInfoEditorForm
