import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import Wrap<PERSON><PERSON>T<PERSON> from "@/components/wrap-tool-tip/wrap-tool-tip"
import {
  useGetNotificationSettings,
  useUpdateNotificationSettings,
} from "@/services/query/user.query"
import { CircleHelp } from "lucide-react"
import { useSelector } from "react-redux"
import ct from "@constants/"
import { useEffect, useState } from "react"

function NotificationSettings() {
  const { id: userID } = useSelector((state) => state[ct.store.USER_STORE])
  // Fetching data from api
  const { data, isPending } = useGetNotificationSettings(userID)
  const settingsData = data?.data?.settings
  console.log("NotificationSettings", data, isPending, settingsData)

  const [emailUpdates, setEmailUpdates] = useState(settingsData?.email_updates ?? false)
  const [notificationUpdates, setNotificationUpdates] = useState(
    settingsData?.notification_updates ?? false
  )

  useEffect(() => {
    if (settingsData) {
      setEmailUpdates(!!settingsData?.email_updates)
      setNotificationUpdates(!!settingsData?.notification_updates)
    }
  }, [settingsData])

  // Mutation
  const mutateUpdateSettings = useUpdateNotificationSettings()
  const handleChange = (keyName, setValue, value) => {
    setValue(value)
    mutateUpdateSettings.mutate({
      userID,
      data: {
        user_id: userID,
        settings: { [keyName]: value },
      },
    })
  }

  const labelClassName = "text-sm md:text-base font-normal"
  const switchClassName =
    "w-10 h-[22px] data-[state=checked]:bg-primary data-[state=unchecked]:bg-secondary"
  const descriptionClassName = "text-xs text-slate-400 ml-auto mr-4"

  return (
    <div className="w-full px-2">
      {/* Header */}
      <h2 className="text-base lg:text-lg dark:text-white font-semibold my-5">
        Notification Settings
      </h2>

      {/* Site Updates */}
      <div className="flex justify-between items-center px-2">
        <div className="flex items-center gap-x-1">
          <Label htmlFor="notification-updates" className={labelClassName}>
            App Notification
          </Label>
          <WrapToolTip delayDuration={0} toolTipContent="All activities notified here" side="right">
            <CircleHelp className="w-4 h-4 font-normal text-slate-500 ml-1" />
          </WrapToolTip>
        </div>
        <span className={descriptionClassName}>
          {notificationUpdates ? "You will receive notifications in site" : "No site updates"}
        </span>
        <Switch
          id="notification-updates"
          className={switchClassName}
          checked={notificationUpdates}
          onCheckedChange={(value) =>
            handleChange("notification_updates", setNotificationUpdates, value)
          }
        />
      </div>
      <hr className="w-full border-gray-200 my-3" />

      {/* Email Updates */}
      <div className="flex justify-between items-center px-2">
        <div className="flex items-center gap-x-1">
          <Label htmlFor="email-updates" className={labelClassName}>
            Email Updates
          </Label>
          <WrapToolTip
            delayDuration={0}
            toolTipContent="Important updates notified via email"
            side="right"
          >
            <CircleHelp className="w-4 h-4 font-normal text-slate-500 ml-1" />
          </WrapToolTip>
        </div>
        <span className={descriptionClassName}>
          {emailUpdates ? "You will receive updates in emails" : "No email updates"}
        </span>
        <Switch
          id="email-updates"
          className={switchClassName}
          checked={emailUpdates}
          onCheckedChange={(value) => handleChange("email_updates", setEmailUpdates, value)}
        />
      </div>
      <hr className="w-full border-gray-200 my-3" />
    </div>
  )
}

export default NotificationSettings
