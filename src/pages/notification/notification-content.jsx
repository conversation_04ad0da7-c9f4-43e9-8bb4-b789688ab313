import { ScrollArea } from "@/components/ui/scroll-area"
import { Card } from "@/components/ui/card"
import { useEffect, useRef, useState } from "react"
import { Input } from "@/components/ui/input"
import { Search, SlidersHorizontal } from "lucide-react"
import WrapToolTip from "@/components/wrap-tool-tip/wrap-tool-tip"
import { Button } from "@/components/ui/button"
import NotificationData from "./all-activity/message-data"

const NotificationContent = () => {
  const [limit, setLimit] = useState(10)
  const { data: listOfJDs } = {
    data: [],
  }
  const [searchValue, setSearchValue] = useState("")

  const scrollRef = useRef(null) // Ref for the scrollable container
  console.log("scrollRef", scrollRef)
  useEffect(() => {
    console.log("ScrollRef attached:", scrollRef.current)
  }, [scrollRef])

  console.log("listOfJDs_response ", { listOfJDs, limit })

  const handleSearchChange = (event) => {
    const { value } = event.target
  }

  return (
    <div className="py-2 px-5">
      <h2 className="text-2xl text-primary font-semibold mt-5">Notifications</h2>
      <div className="w-full flex px-10">
        <ScrollArea className="w-full max-h-[45.5rem] pr-4">
          <Card className="w-full p-8 mt-4">
            {/* Search and Apply Filters */}
            <div className="flex gap-7">
              <div className="relative max-w-72">
                <Input
                  placeholder="Search"
                  onChange={handleSearchChange}
                  className="h-12 rounded-lg text-md border-2 pr-10"
                  data-testid="search_input"
                />
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
              </div>
              <WrapToolTip delayDuration={50} toolTipContent="Coming Soon..." side="top">
                <Button
                  data-testid="apply_filters_btn"
                  variant="outline"
                  className="h-12 border-2 font-semibold rounded-lg pr-10 text-primary cursor-not-allowed dark:text-white opacity-50 hover:bg-blue-50 hover:text-primary hover:border-blue-100"
                >
                  <SlidersHorizontal className="mr-1 h-5 w-5" />
                  Apply Filters
                </Button>
              </WrapToolTip>
            </div>

            {/* Notification Data */}
            <NotificationData />
          </Card>
        </ScrollArea>
      </div>
    </div>
  )
}

export default NotificationContent
