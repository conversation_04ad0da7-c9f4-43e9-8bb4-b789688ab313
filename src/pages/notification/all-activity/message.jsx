/* eslint-disable sonarjs/no-small-switch */
/* eslint-disable import/no-absolute-path */
/* eslint-disable import/no-unresolved */
import { AlertCircleIcon, ChevronLeft, ChevronRight } from "lucide-react"
import { useState } from "react"
import { Button } from "@/components/ui/button"
import PropTypes from "prop-types"
import user from "/user.svg"

const Message = ({ data, total }) => {
  const [currentPage, setCurrentPage] = useState(1)

  const moveToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1)
    }
  }

  const moveToNextPage = () => {
    if (currentPage < Math.ceil(total / 10)) {
      setCurrentPage(currentPage + 1)
    }
  }
  console.log(data)

  const getIcon = (iconName) => {
    switch (iconName) {
      case "alert":
        return <AlertCircleIcon className="w-11 h-11 text-red-400" />
      default:
        return <img src={user} alt="user" className="w-10 h-10" />
    }
  }

  return (
    <div className="space-y-6 my-4">
      <div className="abstract flex justify-end items-center text-gray-300 text-medium -mb-11 mr-3">
        Page {currentPage} of {Math.ceil(total / 10)}
        <Button variant="icon" onClick={moveToPreviousPage} disabled={currentPage === 1}>
          <ChevronLeft data-testid="previous_page" />
        </Button>
        <Button
          variant="icon"
          onClick={moveToNextPage}
          disabled={currentPage === Math.ceil(total / 10)}
        >
          <ChevronRight data-testid="next_page" />
        </Button>
      </div>
      {Object.entries(data).map(([month, messageDataList]) => (
        <div key={month} className="space-y-2">
          <p className="text-lg font-medium dark:text-white text-gray-700 ml-3">{month}</p>
          <div className="border-2 rounded-xl bg-white dark:bg-black">
            {messageDataList.map((messageData) => (
              <div
                key={messageData.id}
                className="flex items-center space-x-4 p-4 border-b last:border-b-0"
              >
                <div className="flex items-center space-x-3 flex-1">
                  {getIcon(messageData.icon)}
                  <div>
                    <p className="mt-2 dark:text-white font-normal">{messageData.title}</p>
                    {messageData.message && (
                      <p className="text-sm dark:text-white text-gray-500">{messageData.message}</p>
                    )}
                  </div>
                </div>
                <div className="space-y-1 text-right">
                  <div className="text-sm dark:text-white text-gray-400 font-medium text-right">
                    <p>{messageData.date}</p>
                  </div>
                  <div>{messageData.jobDetails}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}

Message.propTypes = {
  data: PropTypes.objectOf(
    PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.number.isRequired,
        icon: PropTypes.string.isRequired,
        text: PropTypes.string.isRequired,
        message: PropTypes.string,
        date: PropTypes.string.isRequired,
        jobDetails: PropTypes.oneOfType([PropTypes.string, PropTypes.element]).isRequired,
      })
    )
  ).isRequired,
  total: PropTypes.number.isRequired,
}

export default Message
