import { useEffect, useState } from "react"
import { useSelector } from "react-redux"
import ct from "@constants/"
import { useFetchNotificationQuery } from "@/services/query/notifications.query"
import Message from "./message"

const NOTIFICATION_STATUS = {
  ALL: "ALL",
  READ: "READ",
  UNREAD: "UNREAD",
}

const SORT_ORDER = {
  ASC: "ASC",
  DESC: "DESC",
}

const NotificationData = () => {
  const [notifications, setNotifications] = useState([])
  const [totalRecords, setTotalRecords] = useState(0)

  // Filters
  const { id: userID } = useSelector((state) => state[ct.store.USER_STORE])
  const [filters, setFilters] = useState({
    user_id: userID,
    search_query: null,
    date_range: null,
    status: NOTIFICATION_STATUS.ALL,
    sort_by: SORT_ORDER.DESC,
  })

  // Pagination
  const initialPagination = {
    offset: 0,
    limit: 10,
  }
  const [pagination, setPagination] = useState(initialPagination)

  // Fetch notifications data
  const { data, isLoading } = useFetchNotificationQuery(
    filters,
    pagination.offset,
    pagination.limit
  )

  const notificationsResponse = data?.get_notification_activity
  console.log(data, notificationsResponse)

  useEffect(() => {
    if (notificationsResponse?.total_records) {
      setTotalRecords(notificationsResponse.total_records)
    }
    if (!isLoading && notificationsResponse?.notifications) {
      const groupedNotifications = notificationsResponse?.notifications.reduce(
        (acc, notification) => {
          console.log("notification", notification)
          const date = new Date(notification.created_at)
          const monthYear = date.toLocaleString("default", { month: "long", year: "numeric" })

          if (!acc[monthYear]) {
            acc[monthYear] = []
          }
          acc[monthYear].push({
            id: notification.id,
            icon: "doc",
            title: notification.title,
            message: notification.message,
            date: `At ${date.toLocaleTimeString()} ${date.toLocaleDateString()}`,
          })

          return acc
        },
        {}
      )
      setNotifications(groupedNotifications)
    }
  }, [])

  return <Message total={totalRecords} data={notifications} />
}

export default NotificationData
