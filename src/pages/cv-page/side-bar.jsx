import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Sheet,
  Sheet<PERSON>lose,
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON>le,
  She<PERSON><PERSON>rigger,
} from "@/components/ui/sheet"

const CreateNewSidebar = () => {
  const handleSubmit = (e) => {
    e.preventDefault()
    // Add your submission logic here
  }

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="primary" className="rounded-[.4rem] py-4 text-base me-8">
          Create New
        </Button>
      </SheetTrigger>
      <SheetContent className="w-full min-w-[500px] bg-white py-12 px-12">
        <SheetHeader className="space-y-6">
          <div className="flex justify-between items-center">
            <SheetTitle className="text-xl font-medium">Create New</SheetTitle>
          </div>
        </SheetHeader>

        <form onSubmit={handleSubmit} className="space-y-6 pt-6">
          <div className="space-y-2">
            <Label className="block text-sm font-medium mb-2">Job Role</Label>
            <Input placeholder="Write Title here" className="w-full rounded-md border px-3 py-2" />
          </div>

          <div className="flex justify-end gap-3 pt-6">
            <SheetClose asChild>
              <Button variant="primary-outline" className="rounded-[.4rem] py-4 ">
                Cancel
              </Button>
            </SheetClose>
            <Button className="rounded-[.4rem] py-4  " type="submit" variant="primary">
              Save
            </Button>
          </div>
        </form>
      </SheetContent>
    </Sheet>
  )
}

export default CreateNewSidebar
