import { DateCell } from "@/components/custom/cutsom-table/table-cells"

export const StudentColumns = [
  {
    accessorKey: "id",
    header: "Cv.No",
  },
  {
    id: "job_role",
    accessorKey: "job_role",
    header: "Job Role",
  },

  {
    id: "view_resume",
    accessorKey: "view_resume",
    header: "View Resume",
  },
  {
    id: "view_question",
    accessorKey: "view_question",
    header: "View Question",
  },
  {
    id: "feedback",
    accessorKey: "feedback",
    header: "Feedbacks",
  },
  {
    id: "generated_at",
    cell: ({ row }) => <DateCell value={row.getValue("date")} />,
    header: "Generated At",
  },

  {
    id: "actions",
    header: "Actions",
    enableHiding: false,
  },
]

export const TrainerColumns = [
  {
    accessorKey: "id",
    header: "No",
  },
  {
    id: "job_role",
    accessorKey: "job_role",
    header: "Job Role",
  },
  {
    id: "student_name",
    accessorKey: "student_name",
    header: "Student Name",
  },
  {
    id: "total_experience",
    accessorKey: "total_experience",
    header: "Total Exp",
  },
  {
    id: "view_resume",
    accessorKey: "view_resume",
    header: "View Resume",
  },
  {
    id: "feedback",
    accessorKey: "feedback",
    header: "Feedbacks",
  },
  {
    id: "notes",
    accessorKey: "notes",
    header: "Notes",
  },

  {
    id: "generated_at",
    cell: ({ row }) => <DateCell value={row.getValue("date")} />,
    header: "Genarated At",
  },
]
