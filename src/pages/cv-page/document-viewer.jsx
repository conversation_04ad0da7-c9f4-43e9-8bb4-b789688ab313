import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { useGetPublicUrls } from "@/services/query/cv.query"
import { AlertCircle, ArrowLeft, Loader2 } from "lucide-react"
import { useEffect, useState } from "react"
import { useLocation, useNavigate } from "react-router-dom"

const DocumentViewer = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const [iframeUrl, setIframeUrl] = useState("")

  const cloudPath = location.state?.cloudPath
  const documentType = location.state?.documentType

  const {
    data: publicUrl,
    isLoading,
    error,
  } = useGetPublicUrls(cloudPath || "", {
    enabled: !!cloudPath,
  })

  useEffect(() => {
    if (publicUrl?.data) {
      setIframeUrl(publicUrl.data)
    }
  }, [publicUrl])

  if (!cloudPath) return null

  return (
    <Card className="min-h-screen flex flex-col p-8 shadow-xl border border-gray-300 rounded-lg bg-white">
      <div className="flex items-center gap-4 mb-6">
        <Button
          onClick={() => navigate(-1)}
          variant="outline"
          className="flex items-center gap-2 px-5 py-3 text-lg font-medium shadow-sm hover:bg-gray-100 transition"
        >
          <ArrowLeft className="h-5 w-5" />
          Back
        </Button>
        <h1 className="text-3xl mt-2 ms-4 font-bold text-gray-800">
          {documentType === "resume" ? "Resume" : "Questions"} Viewer
        </h1>
      </div>

      {isLoading && (
        <div className="flex items-center justify-center flex-1">
          <Loader2 className="h-10 w-10 animate-spin text-gray-500" />
        </div>
      )}
      {error && (
        <div className="flex flex-col items-center justify-center flex-1 text-center p-6 bg-red-50 rounded-lg shadow-md">
          <AlertCircle className="h-12 w-12 text-red-600 mb-3" />
          <p className="text-xl text-red-700 font-semibold">Error loading document</p>
          <p className="text-gray-600">Please try again later.</p>
        </div>
      )}
      {!isLoading && !error && (
        <div className="flex-1 w-full bg-gray-100 rounded-xl shadow-lg overflow-hidden border border-gray-400">
          {iframeUrl && (
            <iframe
              src={iframeUrl}
              className="w-full h-full min-h-[calc(100vh-160px)] rounded-lg"
              title={documentType === "resume" ? "Resume PDF" : "Questions PDF"}
            />
          )}
        </div>
      )}
    </Card>
  )
}

export default DocumentViewer
