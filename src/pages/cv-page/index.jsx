import { useCallback, useEffect, useMemo, useState } from "react"
import { useSelector } from "react-redux"
import { useNavigate } from "react-router-dom"
import { flexRender } from "@tanstack/react-table"
import { ActionCell, CvLinkCell, DateCell } from "@/components/custom/cutsom-table/table-cells"
import useTableConfig from "@/hooks/use-table.hooks"
import DataTable from "@/components/custom/cutsom-table"
import { useOpenCloseHooks } from "@/hooks/common.hooks"
import { Card } from "@/components/ui/card"
import { useGetResumes } from "@/services/query/cv.query"
import ct from "@constants/"
import { USER_ROLES } from "@/utils/constants"
import { StudentColumns, TrainerColumns } from "./columns"
import CreateNewSidebar from "./side-bar"

const CvPageTable = () => {
  const navigate = useNavigate()
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })
  const userRole = useSelector((st) => st[ct.store.USER_STORE]?.userRole)
  const { data, isLoading } = useGetResumes()
  const { open, handleClose } = useOpenCloseHooks()
  const [resumeData, setResumeData] = useState([])

  const handleViewDocument = useCallback(
    (path, type) => {
      if (path) {
        navigate(ct.route.PUBLIC_URL, {
          state: {
            cloudPath: path,
            documentType: type,
          },
        })
      }
    },
    [navigate]
  )

  const columns = useMemo(
    () =>
      userRole === USER_ROLES.STUDENT || userRole === USER_ROLES.MARKETER
        ? StudentColumns
        : TrainerColumns,
    [userRole]
  )

  const { table, found, pageCount } = useTableConfig(
    resumeData?.data,
    columns,
    data?.data.length,
    setPagination,
    pagination
  )

  useEffect(() => {
    if (data?.data) {
      setResumeData(data)
    }
  }, [data])

  const renderCellContent = useCallback(
    (cell, row) => {
      console.log("renderCellContent", cell, row?.original)

      const { created_at, id, job_role, cv_link, feedback, questions, notes } = row?.original || {}

      switch (cell.column.id) {
        case "date":
        case "generated_at":
          return <DateCell value={created_at} />
        case "id":
          return <p className="text-sm">{id}</p>
        case "job_role":
          return <p className="text-sm">{job_role}</p>
        case "notes":
          return <p className="text-sm">{notes || "No notes yet"}</p>
        case "feedback":
          return <p className="text-sm">{feedback || "No feedback yet"}</p>
        case "view_resume":
          return cv_link ? (
            <CvLinkCell type="resume" onClick={() => handleViewDocument(cv_link, "resume")} />
          ) : null
        case "view_question":
          return questions ? (
            <CvLinkCell
              type="questions"
              onClick={() => handleViewDocument(questions, "questions")}
            />
          ) : null
        case "actions":
          return <ActionCell isDelete isActions={open} toggleRowMenu={handleClose} row={row} />
        default:
          return flexRender(cell.column.columnDef.cell, cell.getContext())
      }
    },
    [open, handleClose, handleViewDocument]
  )

  return (
    <Card className="p-8 bg-white rounded-2xl">
      <div className="flex justify-between gap-9">
        <h1 className="text-2xl font-medium">CV Page</h1>
        {userRole !== "trainer" && <CreateNewSidebar />}
      </div>
      <div className="w-full py-5">
        <DataTable
          renderCellContent={renderCellContent}
          columns={columns}
          table={table}
          found={found}
          pageCount={pageCount}
          pagination={pagination}
          isLoading={isLoading}
        />
      </div>
    </Card>
  )
}

export default CvPageTable
