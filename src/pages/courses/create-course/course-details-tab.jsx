import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Clock, GraduationCap } from "lucide-react"
import PropTypes from "prop-types"
import CourseMultipleSelect from "@/components/custom/course-multiple-select"
import { useState, useEffect } from "react"

const CourseDetailsTab = ({ register, errors = {}, setValue, handleChange, watch, courseList }) => {
  const courseType = watch("course_data.course_type")
  const currentNanoCourseIds = watch("course_data.nano_course_ids") || []

  console.log("Course Type:", courseType)
  console.log("Current nano_course_ids:", currentNanoCourseIds)

  const [selectedCourses, setSelectedCourses] = useState([])

  useEffect(() => {
    const courseIds = Array.isArray(currentNanoCourseIds) ? currentNanoCourseIds : []
    setSelectedCourses(courseIds)
  }, [currentNanoCourseIds])

  const handleCourseSelection = (updatedSelectedCourses) => {
    console.log("Updating nano_course_ids with:", updatedSelectedCourses)

    const coursesArray = Array.isArray(updatedSelectedCourses) ? updatedSelectedCourses : []

    setValue("course_data.nano_course_ids", coursesArray, {
      shouldValidate: true,
      shouldDirty: true,
      shouldTouch: true,
    })

    setSelectedCourses(coursesArray)
  }

  useEffect(() => {
    // If courseType changes to non-NANO, reset selected courses
    if (courseType !== "NANO") {
      setValue("course_data.nano_course_ids", [], {
        shouldValidate: true,
        shouldDirty: true,
      })
      setSelectedCourses([])
    }
  }, [courseType, setValue])

  return (
    <div className="grid grid-cols-2 gap-6">
      <div className="space-y-3">
        <Label className="text-sm font-semibold text-gray-700">
          Course Type <span className="text-red-500 ml-1">*</span>
        </Label>
        <Select
          defaultValue={watch("course_data.course_type")}
          onValueChange={(value) => handleChange("course_type", value)}
        >
          <SelectTrigger className="h-12">
            <SelectValue placeholder="Select course type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="LIVE">
              <div className="flex items-center space-x-2">
                <Badge className="bg-green-100 text-green-800">LIVE</Badge>
                <span>Live Sessions</span>
              </div>
            </SelectItem>
            <SelectItem value="SHORT">
              <div className="flex items-center space-x-2">
                <Badge className="bg-blue-100 text-blue-800">SHORT</Badge>
                <span>Short Course</span>
              </div>
            </SelectItem>
            <SelectItem value="NANO">
              <div className="flex items-center space-x-2">
                <Badge className="bg-yellow-100 text-yellow-700">NANO</Badge>
                <span>Nano Degree</span>
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
        {errors.course_data?.course_type && (
          <span className="text-sm text-red-500">{errors.course_data.course_type.message}</span>
        )}
      </div>

      <div className="space-y-3">
        <Label className="text-sm font-semibold text-gray-700">
          Course Level <span className="text-red-500 ml-1">*</span>
        </Label>
        <Select
          onValueChange={(value) => handleChange("course_level", value)}
          defaultValue={watch("course_data.course_level")}
        >
          <SelectTrigger className="h-12 focus:border-blue-500 focus:ring-2 focus:ring-blue-200">
            <SelectValue placeholder="Select level" />
          </SelectTrigger>
          <SelectContent>
            {[
              {
                value: "BEGINNER",
                color: "text-green-500",
                label: "Beginner",
                icon: GraduationCap,
              },
              {
                value: "INTERMEDIATE",
                color: "text-yellow-500",
                label: "Intermediate",
                icon: GraduationCap,
              },
              {
                value: "ADVANCED",
                color: "text-red-500",
                label: "Advanced",
                icon: GraduationCap,
              },
            ].map((level) => (
              <SelectItem key={level.value} value={level.value}>
                <div className="flex items-center space-x-2">
                  <level.icon className={`w-4 h-4 ${level.color}`} />
                  <span>{level.label}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.course_data?.course_level && (
          <span className="text-sm text-red-500">{errors.course_data.course_level.message}</span>
        )}
      </div>

      {/* Conditional Course Selection for NANO type */}
      {courseType === "NANO" && (
        <div className="col-span-2 space-y-3">
          <Label className="text-sm font-semibold text-gray-700">
            Select Courses <span className="text-red-500 ml-1">*</span>
          </Label>
          <CourseMultipleSelect
            courseList={courseList}
            onChangeValue={handleCourseSelection}
            selectedValues={selectedCourses}
            name="courses"
            placeholder="Select multiple courses for this nano degree..."
          />
          {errors.course_data?.nano_course_ids && (
            <span className="text-sm text-red-500">
              {errors.course_data.nano_course_ids.message}
            </span>
          )}
        </div>
      )}

      <div className="space-y-3">
        <Label className="text-sm font-semibold text-gray-700">
          Duration (Days) <span className="text-red-500 ml-1">*</span>
        </Label>
        <div className="relative">
          <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            {...register("course_data.duration_days")}
            type="number"
            min="1"
            className="pl-10 h-12 focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
            placeholder="Enter duration in days"
          />
        </div>
        {errors.course_data?.duration_days && (
          <span className="text-sm text-red-500">{errors.course_data.duration_days.message}</span>
        )}
      </div>

      <div className="space-y-3">
        <Label className="text-sm font-semibold text-gray-700">
          Total Hours<span className="text-red-500 ml-1">*</span>
        </Label>
        <div className="relative">
          <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            {...register("course_data.total_hours")}
            type="number"
            min="1"
            step="0.5"
            className="pl-10 h-12 focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
            placeholder="Enter total hours"
          />
        </div>
        {errors.course_data?.total_hours && (
          <span className="text-sm text-red-500">{errors.course_data.total_hours.message}</span>
        )}
      </div>
    </div>
  )
}

CourseDetailsTab.propTypes = {
  watch: PropTypes.func,
  register: PropTypes.func,
  errors: PropTypes.shape({}),
  handleChange: PropTypes.func,
  setValue: PropTypes.func,
  courseList: PropTypes.arrayOf(PropTypes.shape({})),
}

export default CourseDetailsTab
