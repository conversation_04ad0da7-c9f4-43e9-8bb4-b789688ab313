import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useToast } from "@/components/ui/use-toast"
import {
  DATA_TYPES,
  RESOURCE_TYPES,
  useCreateCourse,
  useResources,
  useUpdateCourse,
} from "@/services/query/create-course-form.query"
import ct from "@constants/"
import { zodResolver } from "@hookform/resolvers/zod"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { useLocation, useNavigate } from "react-router-dom"
import { useFetchCourseMinInfo } from "@/services/query/live-course.query"
import { courseFormSchema } from "./course-form-schema"
import HeaderSection from "./header-section"
import MainFormContent from "./main-form-content"
import SubmitButtonSection from "./submit-button-section"

const CourseCreationForm = () => {
  const { toast } = useToast()
  const mutation = useCreateCourse()
  const [isForBatch, setIsForBatch] = useState(false)
  const [activeTab, setActiveTab] = useState("basic")
  const [reRender, setRerender] = useState(true)
  const [progress, setProgress] = useState(0)
  const [courseList, setCourseList] = useState([])
  const navigate = useNavigate()
  const location = useLocation()

  const { mutate: updateCourse } = useUpdateCourse()
  const { data: coursesData } = useFetchCourseMinInfo({})
  const { courseDetails } = location.state || {}
  const courseType = location?.state?.courseType
  useEffect(() => {
    if (coursesData) {
      setCourseList(coursesData?.get_course?.courses)
    }
  }, [coursesData])
  console.log("courseDetails", courseDetails)

  // Fetch resources using the custom hooks
  const { data: skillsData, isLoading: loadingSkills } = useResources(
    DATA_TYPES.skills,
    RESOURCE_TYPES.SKILLS
  )

  const { data: tagsData, isLoading: loadingTags } = useResources(
    DATA_TYPES.tags,
    RESOURCE_TYPES.TAGS
  )

  const { data: categoriesData, isLoading: loadingCategories } = useResources(
    DATA_TYPES.categories,
    RESOURCE_TYPES.CATEGORIES
  )

  const defaultValues = {
    for_batch_course: false,
    parent_data: null,
    course_data: {
      category_id: "",
      nano_course_ids: [],
      new_category: "",
      title: "",
      description: "",
      intro: "",
      duration_days: "",
      course_type: "",
      total_hours: "",
      course_level: "",
      course_language: "",
      pricing_type: "",
      course_price: "",
      base_currency: "",
      course_discount: "",
      prerequisites: "",
      syllabus_url: "",
      new_skills: [],
      new_tags: [],
      skill_ids: [],
      tag_ids: [],
      skills: [],
      tags: [],
      syllabus: null,
      intro_image: null,
    },
  }

  const form = useForm({
    resolver: zodResolver(courseFormSchema),
    defaultValues,
  })

  const {
    handleSubmit,
    register,
    formState: { errors },
    watch,
    setValue,
    reset,
    getValues,
  } = form
  const formValues = watch()

  const resetForm = () => {
    reset(defaultValues)
    setProgress(0)
    setActiveTab("basic")
  }

  const handleChange = (name, value) => {

    setValue(`course_data.${name}`, value, { shouldValidate: true })
  }

  const editcourse = (data) => {
    console.log("editcourse", data)

    const formData = new FormData()
    const numericFields = [
      "category_id",
      "nano_course_ids",
      "duration_days",
      "total_hours",
      "course_price",
      "course_discount",
    ]


    const payload = {
      course_type: "LIVE",
      course_data: {
        ...Object.fromEntries(
          Object.entries(data.course_data).map(([key, value]) => {
            if (numericFields.includes(key)) {

              if (key === "category_id" && typeof value === 'string' && value.match(/^\d+$/)) {
                return [key, Number(value)];
              }
              return [key, Number(value)];
            }
            if (key === "nano_course_ids") {
              return [key, Array.isArray(value) ? value.map((id) => Number(id)) : []];
            }

            if (key === "intro_image" && value instanceof File) return [key, undefined];
            if (key === "syllabus" && value instanceof File) return [key, undefined];
            return [key, value];
          })
        ),
        skill_ids: data.course_data.skills?.filter((skill) => typeof skill === "number") || [],
        new_skills:
          data.course_data.skills
            ?.filter((skill) => typeof skill === "string")
            .map((skill) => skill.trim()) || [],
        tag_ids: data.course_data.tags?.filter((tag) => typeof tag === "number") || [],
        new_tags:
          data.course_data.tags
            ?.filter((tag) => typeof tag === "string")
            .map((tag) => tag.trim()) || [],
        nano_course_ids: Array.isArray(data.course_data.nano_course_ids)
          ? data.course_data.nano_course_ids.map((id) => Number(id))
          : [],

        new_category: data.course_data.new_category ? data.course_data.new_category.trim() : "",
      },
    }


    payload.course_data = Object.fromEntries(
      Object.entries(payload.course_data).filter(([, value]) => value !== undefined)
    );


    formData.append("data", JSON.stringify(payload))


    const files = ["intro_image", "syllabus"]
    let missingSyllabus = false

    files.forEach((file) => {

      if (data.course_data[file] instanceof File) {
        formData.append(file, data.course_data[file])
      }
    })


    if (!courseDetails?.syllabus_url && !(data.course_data.syllabus instanceof File)) {
      missingSyllabus = true;
    }

    if (missingSyllabus) {
      failureToast("Syllabus file is required", "Please select a syllabus file")
      return
    }

    console.log("Final payload structure (edit):", {
      payload: JSON.parse(formData.get("data")),
      hasIntroImage: formData.has("intro_image"),
      hasSyllabus: formData.has("syllabus"),
    })

    updateCourse(
      { formData, course_id: courseDetails?.id },
      {
        onSuccess: (res) => {
          successToast("Successfully Updated!", "Course was successfully Updated!")
          resetForm()
          navigate("/live-courses")
        },
        onError: (error) => {
          console.error("Update failed:", error)
          failureToast("Update Failed", error.response?.data?.message || "Failed to update course")
        },
      }
    )
  }

  const [categoryEditId, setCategoryEditId] = useState(null)
  const [editSkils, setEditSkils] = useState([])
  const [editTags, setEditTags] = useState([])

  useEffect(() => {
    if (courseDetails && skillsData?.data && tagsData?.data) {
      console.log("Setting edit values:", courseDetails)
      console.log("Available skills:", skillsData?.data)
      console.log("Available tags:", tagsData?.data)

      // Set basic form values
      setValue("for_batch_course", courseDetails.for_batch_course || false)
      setValue("parent_data", courseDetails.parent_data || null)

      // Set course data
      setValue("course_data.category_id", String(courseDetails?.category_id || ""))
      // Crucial for new_category: If an existing category is loaded, new_category should be empty
      setValue("course_data.new_category", "") // Ensure new_category is cleared on edit load
      setValue("course_data.title", courseDetails?.title || "")
      setValue("course_data.description", courseDetails?.description || "")
      setValue("course_data.intro", courseDetails?.intro || "")
      setValue("course_data.duration_days", String(courseDetails?.duration_days || ""))
      setValue("course_data.total_hours", String(courseDetails?.total_hours || ""))
      setValue("course_data.course_price", String(courseDetails?.course_price || ""))
      setValue("course_data.base_currency", String(courseDetails?.base_currency || ""))
      setValue("course_data.course_discount", String(courseDetails?.course_discount || ""))
      setValue("course_data.prerequisites", courseDetails?.prerequisites || "")

      setValue("course_data.course_language", courseDetails?.course_language || "")
      setValue("course_data.course_type", courseDetails?.course_type || "")
      setValue("course_data.course_level", courseDetails?.course_level || "")
      setValue("course_data.pricing_type", courseDetails?.pricing_type || "")

      const nanoCourseIds = courseDetails?.nano_course_ids
      if (Array.isArray(nanoCourseIds)) {
        setValue("course_data.nano_course_ids", nanoCourseIds)
      } else {
        setValue("course_data.nano_course_ids", [])
      }

      if (courseDetails?.course_skills && Array.isArray(courseDetails.course_skills)) {
        const skillValues = Array.from(
          new Set(
            courseDetails.course_skills.map((skill) => skill.skill_name || skill.name || skill)
          )
        )

        setValue("course_data.skills", skillValues)
        setEditSkils(courseDetails.course_skills)
        console.log("Setting skills:", skillValues, courseDetails.course_skills)
      } else {
        setValue("course_data.skills", [])
        setEditSkils([])
      }

      if (courseDetails?.course_tags && Array.isArray(courseDetails.course_tags)) {
        const tagValues = Array.from(
          new Set(
            courseDetails.course_tags.map((tag) => {
              if (tag.id || tag.tag_id) return tag.id || tag.tag_id
              return tag.tag_name || tag
            })
          )
        )


        setValue("course_data.tags", tagValues)
        setEditTags(courseDetails.course_tags)
        console.log("Setting tags:", tagValues, courseDetails.course_tags)
      } else {
        setValue("course_data.tags", [])
        setEditTags([])
      }

      // Handle existing files - set as URLs or keep existing references
      // If it's a URL, we don't need to re-upload it. If it's a File object, it means it's new.
      // For editing, these would typically be URLs, unless a new file is explicitly chosen by the user later.
      if (courseDetails?.intro_image_url) {
        setValue("course_data.intro_image", courseDetails.intro_image_url)
      } else if (courseDetails?.intro_image) {
        setValue("course_data.intro_image", courseDetails.intro_image) // Fallback for direct image data
      }


      if (courseDetails?.syllabus_url) {
        setValue("course_data.syllabus", courseDetails.syllabus_url)
      } else if (courseDetails?.syllabus) {
        setValue("course_data.syllabus", courseDetails.syllabus) // Fallback for direct syllabus data
      }


      // Set edit state
      setCategoryEditId(courseDetails?.category_id)
    }
  }, [courseDetails, setValue, skillsData, tagsData])

  const calculateProgress = () => {
    const requiredFields = [
      "category_id",
      "title",
      "intro",
      "intro_image",
      "skills",
      "tags",
      "duration_days",
      "course_type",
      "course_level",
      "course_language",
      "pricing_type",
      "prerequisites",
      "syllabus",
    ]

    const totalFields = requiredFields.length
    let filledFields = 0

    requiredFields.forEach((key) => {
      const value = formValues.course_data[key]

      if (
        (typeof value === "string" && value.trim() !== "") ||
        (typeof value === "number" && !Number.isNaN(value) && value > 0) ||
        (Array.isArray(value) && value.length > 0 && value.every((item) => item !== "" && item !== null)) || // Added null check
        value instanceof File ||
        (typeof value === "string" && value.startsWith("http"))
      ) {
        filledFields += 1
      }
    })

    return totalFields > 0 ? Math.round((filledFields / totalFields) * 100) : 0
  }

  useEffect(() => {
    const calculatedProgress = calculateProgress()
    setProgress(calculatedProgress)
  }, [
    formValues.course_data.category_id,
    formValues.course_data.new_category,
    formValues.course_data.title,
    formValues.course_data.intro,
    formValues.course_data.intro_image,
    formValues.course_data.skills,
    formValues.course_data.tags,
    formValues.course_data.duration_days,
    formValues.course_data.course_type,
    formValues.course_data.course_level,
    formValues.course_data.course_language,
    formValues.course_data.pricing_type,
    formValues.course_data.prerequisites,
    formValues.course_data.syllabus,
  ])

  const onSubmit = async (data) => {
    const formData = new FormData()
    const numericFields = [
      "duration_days",
      "total_hours",
      "course_price",
      "course_discount",
    ]

    // Prepare the payload structure
    const payload = {
      for_batch_course: data.for_batch_course,
      parent_data: data.parent_data || null,
      course_data: {
        ...Object.fromEntries(
          Object.entries(data.course_data).map(([key, value]) => {
            if (numericFields.includes(key)) {
              return [key, Number(value)]
            }
            if (key === "nano_course_ids") {
              return [key, Array.isArray(value) ? value.map((id) => Number(id)) : []]
            }

            // Exclude file objects (which will be appended separately)
            if (key === "intro_image" && value instanceof File) return [key, undefined];
            if (key === "syllabus" && value instanceof File) return [key, undefined];
            return [key, value]
          })
        ),
        skill_ids: data.course_data.skills?.filter((skill) => typeof skill === "number") || [],
        new_skills:
          data.course_data.skills
            ?.filter((skill) => typeof skill === "string")
            .map((skill) => skill.trim()) || [],
        tag_ids: data.course_data.tags?.filter((tag) => typeof tag === "number") || [],
        new_tags:
          data.course_data.tags
            ?.filter((tag) => typeof tag === "string")
            .map((tag) => tag.trim()) || [],

        category_id: typeof data.course_data.category_id === 'number' || String(data.course_data.category_id).match(/^\d+$/)
          ? Number(data.course_data.category_id)
          : null,
        new_category: (typeof data.course_data.category_id === 'string' && !String(data.course_data.category_id).match(/^\d+$/))
          ? data.course_data.category_id.trim()
          : (data.course_data.new_category ? data.course_data.new_category.trim() : ""), // Fallback if new_category was set directly
      },
    }


    if (payload.course_data.category_id) {
      payload.course_data.new_category = "";
    } else if (payload.course_data.new_category === "") {

    }


    // Filter out undefined keys from course_data before stringifying
    payload.course_data = Object.fromEntries(
      Object.entries(payload.course_data).filter(([, value]) => value !== undefined)
    )

    // Append the main payload as JSON
    formData.append("data", JSON.stringify(payload))

    // Handle file uploads separately
    const filesToAppend = ["intro_image", "syllabus"]
    let missingSyllabus = false

    filesToAppend.forEach((fileKey) => {
      // Only append if it's a File object (meaning a new file was selected)
      if (data.course_data[fileKey] instanceof File) {
        formData.append(fileKey, data.course_data[fileKey])
      }
    })

    // Check if syllabus is required and missing a File or existing URL
    if (!(data.course_data.syllabus instanceof File) &&
      !(typeof data.course_data.syllabus === 'string' && data.course_data.syllabus.startsWith('http'))) {
      missingSyllabus = true
    }

    if (missingSyllabus) {
      failureToast("Syllabus file is required", "Please select a syllabus file or ensure an existing one is linked.")
      return
    }

    console.log("Final payload structure (submit):", {
      payload: JSON.parse(formData.get("data")),
      hasIntroImage: formData.has("intro_image"),
      hasSyllabus: formData.has("syllabus"),
    })

    if (location.pathname === "/edit-course") {
      editcourse(data)
    } else {
      try {
        await mutation.mutate(formData, {
          onSuccess: (res) => {
            successToast("Successfully Created!", "Course was successfully created!")
            resetForm()

            if (courseType === "Nano courses") {
              navigate(`/${ct.route.NANO_COURSES}`)
            } else {
              navigate(`${ct.route.MODULE_CONTENT}/${res?.data?.id}`, {
                state: { courseData: res?.data },
              })
            }
          },
          onError: (error) => {
            console.error("Submission error:", error)
            failureToast(
              "Failed to create course",
              error.response?.data?.message || "Failed to create course"
            )
          },
        })
      } catch (error) {
        console.error("Form submission error:", error)
        failureToast("Error", error.message)
      }
    }
  }

  const resourceProps = {
    skills: skillsData?.data || [],
    tags: tagsData?.data || [],
    categories: categoriesData?.data || [],
    isLoading: loadingSkills || loadingTags || loadingCategories,
  }

  return (
    <div className="max-w-6xl mx-auto bg-gradient-to-br from-gray-50 via-gray-50 to-blue-50 p-4">
      <form
        onSubmit={handleSubmit(
          (data) => onSubmit(data),
          (validationErrors) => {
            console.log("Validation failed:", validationErrors)
            failureToast(
              "Validation Error",
              validationErrors[Object.keys(validationErrors)[0]]?.message || // Added nullish coalescing for safety
              "Failed to create course"
            )
          }
        )}
        encType="multipart/form-data"
      >
        <HeaderSection progress={progress} location={location.pathname} />
        <ScrollArea className="h-[50vh]">
          <MainFormContent
            reRender={reRender}
            courseList={courseList}
            setRerender={setRerender}
            categoryEditId={categoryEditId}
            editSkils={editSkils}
            editTags={editTags}
            disabled={!!courseDetails?.title}
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            register={register}
            errors={errors}
            handleChange={handleChange}
            watch={watch}
            setValue={setValue}
            resources={resourceProps}
          />
        </ScrollArea>
        <SubmitButtonSection
          location={location.pathname}
          form={form}
          setIsForBatch={setIsForBatch}
          setActiveTab={setActiveTab}
          setProgress={setProgress}
          toast={toast}
          mutation={mutation}
        />
      </form>
    </div>
  )
}

export default CourseCreationForm