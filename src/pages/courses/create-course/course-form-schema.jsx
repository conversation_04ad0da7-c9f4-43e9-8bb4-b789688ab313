// course-form-schema.js - Updated for edit mode
import * as z from "zod"

export const courseFormSchema = z
  .object({
    for_batch_course: z.boolean(),
    parent_data: z.any().nullable(),
    course_data: z.object({
      category_id: z.string().min(1, "Category ID is required"),
      new_category: z.string().optional(),
      title: z
        .string()
        .min(1, "Title is required")
        .max(255, "Title must be less than 255 characters"),
      description: z.string().optional(),
      intro: z.string().min(15, "Introduction must be at least 15 characters"),

      intro_image: z.union([
        z
          .any()
          .refine((value) => value instanceof File, {
            message: "Introduction image is required",
          })
          .refine((file) => file instanceof File && file.type.startsWith("image/"), {
            message: "File must be an image",
          }),
        z.string().url("Invalid image URL"),
        z.string().min(1, "Introduction image is required"), // For existing file paths
      ]).refine(value => value !== null && value !== undefined && value !== '', {
        message: "Introduction image is required"
      }),

      nano_course_ids: z.array(z.number()).optional(),

      // skills and tags should accept array of numbers (IDs) or strings (new names)
      skills: z.array(z.union([z.number(), z.string()])).min(1, "At least one skill is required"),
      skill_ids: z.array(z.number()).optional(),
      new_skills: z.array(z.string()).optional(),

      tags: z.array(z.union([z.number(), z.string()])).min(1, "At least one tag is required"),
      tag_ids: z.array(z.number()).optional(),
      new_tags: z.array(z.string()).optional(),

      duration_days: z.string().min(1, "Duration is required"),
      course_type: z.string().min(1, "Course type is required"),
      total_hours: z.string().min(1, "Total hours is required"),
      course_level: z.string().min(1, "Course level is required"),
      course_language: z.string().min(1, "Course language is required"),

      pricing_type: z.string().min(1, "Pricing type is required"),
      course_price: z.string().optional(),
      base_currency: z.string().optional(),
      course_discount: z
        .string()
        .transform((val) => (val ? Number(val) : 1))
        .refine((val) => val >= 1, {
          message: "Discount should be at least 1",
        }),

      prerequisites: z.string().min(5, "Prerequisites must be at least 5 characters"),
      syllabus: z.union([
        z
          .any()
          .refine((value) => value instanceof File, {
            message: "Syllabus is required",
          })
          .refine(
            (file) => {
              if (!(file instanceof File)) return false
              const allowedTypes = [
                "image/",
                "application/pdf",
                "application/msword",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
              ]
              return allowedTypes.some((type) => file.type.startsWith(type))
            },
            {
              message: "File must be a PDF, Word document, or an image",
            }
          ),
        z.string().url("Invalid syllabus URL"),
        z.string().min(1, "Syllabus is required"), // For existing file paths
      ]).refine(value => value !== null && value !== undefined && value !== '', {
        message: "Syllabus is required"
      }),
    }),
  })
  .refine(
    (data) => {
      if (data.course_data.course_type === "NANO") {
        return data.course_data.nano_course_ids && data.course_data.nano_course_ids.length > 0
      }
      return true
    },
    {
      message: "At least one course must be selected for NANO degree",
      path: ["course_data", "nano_course_ids"],
    }
  )