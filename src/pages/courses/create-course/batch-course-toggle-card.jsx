// batch-course-toggle-card.jsx
import { Card, CardContent } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"

const BatchCourseToggleCard = ({ isForBatch, setIsForBatch, setValue }) => {
  return (
    <Card className="hover:shadow-xl transition-all duration-300 border-l-4 border-l-blue-500">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h3 className="text-lg font-semibold text-gray-800">Batch Course Settings</h3>
            <p className="text-gray-500 text-sm">
              Enable this option to create a course for a specific batch
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Switch
              checked={isForBatch}
              onCheckedChange={(checked) => {
                setIsForBatch(checked)
                setValue("for_batch_course", checked)
              }}
              className="data-[state=checked]:bg-blue-500"
            />
            <Label className="text-sm font-medium">Batch Course</Label>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default BatchCourseToggleCard
