// submit-button-section.jsx

import { Button } from "@/components/ui/button"

const SubmitButtonSection = ({
  location,
  form,
  setIsForBatch,
  setActiveTab,
  setProgress,
  toast,
  mutation,
}) => {
  let buttonText
  if (mutation.isLoading) {
    buttonText = (
      <div className="flex items-center space-x-2">
        <span className="animate-spin">⌛</span>
        <span>Creating...</span>
      </div>
    )
  } else {
    buttonText = location === "/edit-course" ? "Update Course" : "Create Course"
  }
  return (
    <div className="flex justify-end mt-2 space-x-4">
      <Button
        type="button"
        variant="outline"
        className="w-32 h-12 text-gray-600 hover:text-gray-800 hover:bg-gray-50"
        onClick={() => {
          form.reset()
          setIsForBatch(false)
          setActiveTab("basic")
          setProgress(0)
          toast({
            title: "Form Reset",
            description: "All form fields have been cleared",
            variant: "info",
          })
        }}
      >
        Clear
      </Button>
      <Button
        type="submit"
        className="w-32 h-12 bg-blue-600 hover:bg-blue-700 text-white font-medium"
        disabled={mutation.isLoading}
      >
        {buttonText}
      </Button>
    </div>
  )
}

export default SubmitButtonSection
