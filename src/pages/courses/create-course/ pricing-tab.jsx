import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { DollarSign } from "lucide-react"
import PropTypes from "prop-types"

const formatPrice = (value) => value.replace(/[^0-9.]/g, "")

const BASE_CURRENCY = "course_data.base_currency"
const PricingTab = ({ register, errors, handleChange, watch, setValue }) => {
  const pricingType = watch("course_data.pricing_type")
  const base_currency = watch(BASE_CURRENCY)

  console.log("__pricing_type", base_currency)
  const baseCurrency = watch(BASE_CURRENCY)
  return (
    <div className="grid grid-cols-2 gap-6">
      {/* Pricing Type */}
      <div className="space-y-3">
        <Label className="text-sm font-semibold text-gray-700">
          Pricing Type<span className="text-red-500 ml-1">*</span>
        </Label>
        <Select
          defaultValue={pricingType}
          onValueChange={(value) => handleChange("pricing_type", value)}
        >
          <SelectTrigger className="h-12  focus:ring-2 focus:ring-blue-200">
            <SelectValue placeholder="Select pricing type" />
          </SelectTrigger>
          <SelectContent>
            {[
              {
                value: "FREE",
                label: "Free Course",
                bgColor: "bg-green-100",
                textColor: "text-green-800",
              },
              {
                value: "PAID",
                label: "Paid Course",
                bgColor: "bg-blue-100",
                textColor: "text-blue-800",
              },
            ].map((type) => (
              <SelectItem key={type.value} value={type.value}>
                <div className="flex items-center space-x-2">
                  <Badge className={`${type.bgColor} ${type.textColor}`}>{type.label}</Badge>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.course_data?.pricing_type && (
          <span className="text-sm text-red-500">{errors.course_data.pricing_type.message}</span>
        )}
      </div>

      {/* Show only if PAID is selected */}
      {pricingType === "PAID" && (
        <>
          {/* Base Currency */}
          <div className="space-y-3">
            <Label className="text-sm font-semibold text-gray-700">Base Currency</Label>
            <Select
              {...register(BASE_CURRENCY)}
              onValueChange={(value) => {
                console.log("__base_currency", value)
                setValue(BASE_CURRENCY, value)
              }}
              defaultValue={base_currency}
            >
              <SelectTrigger className="h-12 focus:border-blue-500 focus:ring-2 focus:ring-blue-200">
                <SelectValue placeholder="Select currency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="INR">🇮🇳 INR</SelectItem>
                <SelectItem value="USD">🇺🇸 USD</SelectItem>
              </SelectContent>
            </Select>

            {errors.course_data?.base_currency && (
              <span className="text-sm text-red-500">
                {errors.course_data.base_currency.message}
              </span>
            )}
          </div>

          {/* Course Price */}
          <div className="space-y-3">
            <Label className="text-sm font-semibold text-gray-700">Course Price</Label>
            <div className="relative">
              {baseCurrency === "INR" ? (
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm font-medium">
                  ₹
                </span>
              ) : (
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              )}

              <Input
                {...register("course_data.course_price", {
                  onChange: (e) => {
                    const formattedValue = formatPrice(e.target.value)
                    setValue("course_data.course_price", formattedValue)
                  },
                })}
                type="text"
                inputMode="decimal"
                className="pl-10 h-12 focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
                placeholder="Enter course price"
              />
            </div>
            {errors.course_data?.course_price && (
              <span className="text-sm text-red-500">
                {errors.course_data.course_price.message}
              </span>
            )}
          </div>

          {/* Discount */}
          <div className="space-y-3">
            <Label className="text-sm font-semibold text-gray-700">Discount (%)</Label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                %
              </span>
              <Input
                {...register("course_data.course_discount")}
                type="number"
                min=""
                max="100"
                className="pl-10 h-12 focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
                placeholder="Enter discount percentage"
              />
            </div>
            {errors.course_data?.course_discount && (
              <span className="text-sm text-red-500">
                {errors.course_data.course_discount.message}
              </span>
            )}
          </div>
        </>
      )}
    </div>
  )
}

PricingTab.propTypes = {
  register: PropTypes.func.isRequired,
  handleChange: PropTypes.func.isRequired,
  watch: PropTypes.func.isRequired,
  setValue: PropTypes.func.isRequired,
  errors: PropTypes.shape({
    course_data: PropTypes.shape({
      pricing_type: PropTypes.shape({ message: PropTypes.string }),
      base_currency: PropTypes.shape({ message: PropTypes.string }),
      course_price: PropTypes.shape({ message: PropTypes.string }),
      course_discount: PropTypes.shape({ message: PropTypes.string }),
    }),
  }).isRequired,
}

export default PricingTab
