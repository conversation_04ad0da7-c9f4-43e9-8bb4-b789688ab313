// header-section.jsx

import PropTypes from "prop-types"

import { Progress } from "@/components/ui/progress"

const HeaderSection = ({ progress, location }) => {
  return (
    <div className="text-center space-y-2 mb-4">
      <h1 className="text-4xl font-bold text-primary">
        {location === "/edit-course" ? "Edit Course" : "Create New Course"}
      </h1>
      <p className="text-gray-600 max-w-2xl mx-auto">
        Design your perfect course with our intuitive form builder. Fill in the details below to
        bring your educational vision to life.
      </p>
      <div className="max-w-xl mx-auto mt-6">
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>Completion Progress</span>
          <span>{progress}%</span>
        </div>
        <Progress value={progress} className="h-2 bg-gray-200" />
      </div>
    </div>
  )
}
HeaderSection.propTypes = {
  progress: PropTypes.number.isRequired,
  location: PropTypes.string.isRequired,
}

export default HeaderSection
