// main-form-content.jsx
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Book, Clock, DollarSign, FileText } from "lucide-react"
import PropTypes from "prop-types"
import { useDispatch } from "react-redux"
import BasicInformationTab from "./basic-information-tab"
import CourseDetailsTab from "./course-details-tab"

import PricingTab from "./ pricing-tab"
import AdditionalInfoTab from "./additional-info-tab"

const MainFormContent = ({
  reRender,
  setRerender,
  activeTab,
  disabled,
  courseList,
  editSkils,
  editTags,
  categoryEditId,
  setActiveTab,
  register,
  errors,
  handleChange,
  watch,
  setValue,
  resources,
}) => {
  const dispatch = useDispatch()


  const tabs = [
    {
      id: "basic",
      label: "Basic Info",
      icon: <Book className="w-4 h-4 mr-2" />,
      content: (
        <BasicInformationTab
          register={register}
          disabled={disabled}
          categoryEditId={categoryEditId}
          handleChange={handleChange}
          errors={errors}
          setValue={setValue}
          resources={resources}
          watch={watch}
        />
      ),
    },
    {
      id: "details",
      label: "Course Details",
      icon: <Clock className="w-4 h-4 mr-2" />,
      content: (
        <CourseDetailsTab
          register={register}
          errors={errors}
          setValue={setValue}
          handleChange={handleChange}
          watch={watch}
          courseList={courseList}
        />
      ),
    },
    {
      id: "pricing",
      label: "Pricing",
      icon: <DollarSign className="w-4 h-4 mr-2" />,
      content: (
        <PricingTab
          register={register}
          errors={errors}
          handleChange={handleChange}
          watch={watch}
          setValue={setValue}
        />
      ),
    },
    {
      id: "additional",
      label: "Additional Info",
      icon: <FileText className="w-4 h-4 mr-2" />,
      content: (
        <AdditionalInfoTab
          register={register}
          errors={errors}
          editSkils={editSkils}
          editTags={editTags}
          handleChange={handleChange}
          setValue={setValue}
          resources={resources}
          reRender={reRender}
          setRerender={setRerender}
          watch={watch}
        />
      ),
    },
  ]

  return (
    <Card className="shadow-lg hover:shadow-xl transition-all mt-5 duration-300">
      <CardContent className="p-6">
        <Tabs
          value={activeTab}
          defaultValue="basic"
          className="w-full"
          onValueChange={(val) => dispatch(setActiveTab(val))}
        >
          <TabsList
            className="grid w-full h-14 bg-white rounded-[.6rem] mb-6 bg:border-[hsl(var(--border))] text-xl content-center justify-start relative flex-wrap"
            style={{ gridTemplateColumns: `repeat(${tabs.length}, 1fr)` }}
          >
            {tabs.map((tab) => (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                className={`flex items-center ${activeTab === tab.id
                  ? "data-[state=active]:bg-[hsl(var(--background))] data-[state=active]:text-[hsl(var(--primary))] button"
                  : ""
                  }`}
              >
                {tab.icon}
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>

          {tabs.map((tab) => (
            <TabsContent key={tab.id} value={tab.id}>
              <div className="rounded-2xl">{tab.content}</div>
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
    </Card>
  )
}

MainFormContent.propTypes = {
  activeTab: PropTypes.string.isRequired,
  setActiveTab: PropTypes.func.isRequired,
  register: PropTypes.func.isRequired,
  errors: PropTypes.objectOf(PropTypes.any).isRequired, // More specific if possible, but objectOf(PropTypes.any) for general objects
  handleChange: PropTypes.func.isRequired,
  watch: PropTypes.func.isRequired,
  setValue: PropTypes.func.isRequired,
  resources: PropTypes.shape({}).isRequired,
  reRender: PropTypes.bool.isRequired,
  setRerender: PropTypes.func.isRequired,
  disabled: PropTypes.bool.isRequired,
  editSkils: PropTypes.arrayOf(PropTypes.any).isRequired, // Assuming array
  editTags: PropTypes.arrayOf(PropTypes.any).isRequired,   // Assuming array
  categoryEditId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]), // Adjusted prop-type
  courseList: PropTypes.arrayOf(PropTypes.any), // Assuming array
}

export default MainFormContent