// basic-information-tab.jsx
import CustomSingleSelect from "@/components/custom/custom-single-select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Book, Globe, ImageIcon } from "lucide-react"
import PropTypes from "prop-types"
import { useEffect } from "react"

const BasicInformationTab = ({
  register,
  disabled,
  categoryEditId,
  errors,
  handleChange,
  setValue,
  resources,
  watch,
}) => {
  const languages = ["English", "Spanish", "French", "German", "Chinese", "Hindi"]
  const handleFileChange = (e) => {
    if (e.target.files?.[0]) {
      setValue("course_data.intro_image", e.target.files[0])
    }
  }
  const { categories, isLoading } = resources?.categories || {}

  // Watch specific fields to see their values
  const courseTitle = watch("course_data.title")
  const courseDescription = watch("course_data.description")
  const courseIntro = watch("course_data.intro")
  const selectedLanguage = watch("course_data.course_language")
  const introImage = watch("course_data.intro_image")
  const selectedCategory = watch("course_data.category_id")
  const newCategoryName = watch("course_data.new_category") // <--- Watch this too for debugging

  // Use useEffect to log values whenever they change
  useEffect(() => {
    console.log("BasicInformationTab - Course Title:", courseTitle)
    console.log("BasicInformationTab - Course Description:", courseDescription)
    console.log("BasicInformationTab - Course Introduction:", courseIntro)
    console.log("BasicInformationTab - Selected Language:", selectedLanguage)
    console.log("BasicInformationTab - Intro Image:", introImage)
    console.log("BasicInformationTab - Selected Category ID:", selectedCategory)
    console.log("BasicInformationTab - New Category Name:", newCategoryName) // <--- Log this
  }, [
    courseTitle,
    courseDescription,
    courseIntro,
    selectedLanguage,
    introImage,
    selectedCategory,
    newCategoryName,
  ])

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-6">
        <div className="space-y-3">
          <Label className="text-sm font-semibold text-gray-700">
            Course Title <span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="relative">
            <Book className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              disabled={disabled}
              {...register("course_data.title")}
              className="pl-10 h-12    focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
              placeholder="Enter an engaging title"
            />
          </div>
          {errors.course_data?.title && (
            <span className="text-sm text-red-500">{errors.course_data.title.message}</span>
          )}
        </div>
        <div className="space-y-3">
          <Label className="text-sm font-semibold text-gray-700">
            Course Language <span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="relative">
            <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Select
              onValueChange={(value) => handleChange("course_language", value)}
              value={selectedLanguage}
            >
              <SelectTrigger className={`pl-10 h-12 `}>
                <SelectValue placeholder="Select a language" />
              </SelectTrigger>
              <SelectContent>
                {languages.map((lang) => (
                  <SelectItem key={lang} value={lang}>
                    {lang}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          {errors.course_data?.course_language > 0 && (
            <span className="text-sm text-red-500">
              {errors.course_data.course_language.message}
            </span>
          )}
        </div>
      </div>
      <div className="space-y-3">
        <Label className="text-sm font-semibold text-gray-700">Course Description</Label>
        <Textarea
          {...register("course_data.description")}
          className="min-h-[150px]  focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
          placeholder="Provide a detailed description of your course..."
        />
        {errors.course_data?.description && (
          <span className="text-sm text-red-500">{errors.course_data.description.message}</span>
        )}
      </div>
      <div className="space-y-3">
        <Label className="text-sm font-semibold text-gray-700">
          Course Introduction <span className="text-red-500 ml-1">*</span>
        </Label>
        <Textarea
          {...register("course_data.intro")}
          className="min-h-[100px]    focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
          placeholder="Write a brief introduction (minimum 15 characters)..."
        />
        {errors.course_data?.intro && (
          <span className="text-sm text-red-500">{errors.course_data.intro.message}</span>
        )}
      </div>
      <div className="grid grid-cols-2 gap-6">
        <div className="space-y-3">
          <Label className="text-sm font-semibold text-gray-700">
            Introduction Image
            <span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="relative">
            <ImageIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              className="pl-10 h-12  focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
            />
          </div>
          {errors.course_data?.intro_image && (
            <span className="text-sm text-red-500">{errors.course_data.intro_image.message}</span>
          )}
        </div>
        <div className="space-y-3">
          <Label className="text-sm font-semibold text-gray-700">
            Category <span className="text-red-500 ml-1">*</span>
          </Label>
          <CustomSingleSelect
            isLoading={isLoading}
            categoryEditId={categoryEditId}
            iteratedList={categories?.map((cat) => ({
              value: cat.id.toString(), // Ensure value is string for consistency
              label: cat.category_name,
            }))}
            onChangeValue={(value) => handleChange("category_id", value)}
            setValue={setValue}
            watch={watch}
            name="category_id"
            placeholder="Select a category"
          />
          {errors.course_data?.category_id && (
            <span className="text-sm text-red-500">{errors.course_data.category_id.message}</span>
          )}
        </div>
      </div>
    </div>
  )
}
BasicInformationTab.propTypes = {
  register: PropTypes.func.isRequired,
  errors: PropTypes.shape({
    course_data: PropTypes.shape({
      title: PropTypes.shape({
        message: PropTypes.string,
      }),
      course_language: PropTypes.shape({
        message: PropTypes.string,
      }),
      description: PropTypes.shape({
        message: PropTypes.string,
      }),
      intro: PropTypes.shape({
        message: PropTypes.string,
      }),
      intro_image: PropTypes.shape({
        message: PropTypes.string,
      }),
      category_id: PropTypes.shape({
        message: PropTypes.string,
      }),
    }),
  }).isRequired,
  handleChange: PropTypes.func.isRequired,
  setValue: PropTypes.func.isRequired,
  resources: PropTypes.shape({
    categories: PropTypes.shape({
      data: PropTypes.arrayOf(
        PropTypes.shape({
          id: PropTypes.number,
          category_name: PropTypes.string,
        })
      ),
      isLoading: PropTypes.bool,
    }),
  }).isRequired,
  watch: PropTypes.func.isRequired,
  disabled: PropTypes.bool, // Added prop-type for disabled
  categoryEditId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]), // Added prop-type for categoryEditId
}

export default BasicInformationTab
