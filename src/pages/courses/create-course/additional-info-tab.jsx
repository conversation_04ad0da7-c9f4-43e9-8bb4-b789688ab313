// additional-info-tab.jsx
import CustomMultipleSelect from "@/components/custom/custom-multiple-select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"

import { ImageIcon } from "lucide-react"
import PropTypes from "prop-types"
import { useEffect, useState } from "react"

const AdditionalInfoTab = ({
  register,
  errors,
  reRender,
  setRerender,
  editSkils,
  editTags,
  setValue,
  resources,
  watch,
}) => {
  const { skills, tags, isLoading } = resources

  const [syllabusFile, setSyllabusFile] = useState(null)
  console.log(syllabusFile, "syllabusFile")
  const handleSyllabusChange = (e) => {
    const file = e.target.files?.[0]
    if (file) {
      setSyllabusFile(file)
      setValue("course_data.syllabus", file)
    }
  }
  const syllabus = watch("course_data.syllabus")
  const skillss = watch("course_data.skills")
  const tagss = watch("course_data.tags")
  const prerequisites = watch("course_data.prerequisites")
  useEffect(() => {
    console.log("AdditionalInfoTab:", syllabus)
    console.log("AdditionalInfoTab: name", skills)
    console.log("AdditionalInfoTab:", tagss)
    console.log("AdditionalInfoTab:", prerequisites)
  }, [skillss, syllabus, tagss, prerequisites])

  const handleSkillsChange = (values) => {
    console.log("Selected values skills:", values)

    // Filter existing skills (only numbers)
    const existingSkillIds = values.filter((value) => typeof value === "number")

    // Filter new skills (only strings)
    const newSkills = values
      .filter((value) => typeof value === "string")
      .map((value) => value.trim())

    // Set values in the form
    setValue("course_data.skill_ids", existingSkillIds)
    setValue("course_data.new_skills", newSkills)

  }

  const handleTagsChange = (values) => {
    console.log("Selected values tags:", values)

    // Filter existing tags (only numbers)
    const existingTagIds = values.filter((value) => typeof value === "number")

    // Filter new tags (only strings)
    const newTags = values.filter((value) => typeof value === "string").map((value) => value.trim())
    console.log("Existing Skill IDs:", newTags)
    // Set values in the form
    setValue("course_data.tag_ids", existingTagIds)
    setValue("course_data.new_tags", newTags)
  }
  console.log(editSkils, "editSkils")
  return (
    <div className="space-y-6">
      <div className="space-y-3">
        <Label>
          Syllabus <span className="text-red-500 ml-1">*</span>
        </Label>
        <div className="relative">
          <ImageIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <div className="flex items-center space-x-4">
            <Input
              type="file"
              accept=".doc,.docx,.pdf,image/*"
              onChange={handleSyllabusChange}
              className="pl-10 h-12   focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
            />
          </div>
          {errors.course_data?.syllabus && (
            <span className="text-sm text-red-500 block mt-1">
              {errors.course_data.syllabus.message}
            </span>
          )}
        </div>
      </div>
      <div className="space-y-3">
        <Label>
          Skills<span className="text-red-500 ml-1">*</span>
        </Label>
        <CustomMultipleSelect
          name="skills"
          editValue={editSkils}
          resources={resources}
          reRender={reRender}
          setRerender={setRerender}
          onChangeValue={handleSkillsChange}
          setValue={setValue}
          iteratedList={skills?.skills?.map(({ id, skill_name }) => ({
            value: id,
            label: skill_name,
          }))}
          watch={watch}
          isLoading={isLoading}
          placeholder="Select or add new skills..."
        />
        {errors.course_data?.skills && (
          <span className="text-sm text-red-500">{errors.course_data.skills.message}</span>
        )}
      </div>

      <div className="space-y-3">
        <Label>
          Tags <span className="text-red-500 ml-1">*</span>
        </Label>
        <div className="relative z-10">
          <CustomMultipleSelect
            name="tags"
            editValue={editTags}
            resources={resources}
            reRender={reRender}
            setRerender={setRerender}
            onChangeValue={handleTagsChange}
            setValue={setValue}
            iteratedList={tags?.tags?.map(({ id, tag_name }) => ({
              value: id,
              label: tag_name,
            }))}
            watch={watch}
            isLoading={isLoading}
            placeholder="Select or add new tags..."
          />
        </div>
        {errors.course_data?.tags && (
          <span className="text-sm text-red-500">{errors.course_data.tags.message}</span>
        )}
      </div>

      <div className="space-y-3">
        <Label>
          Prerequisites <span className="text-red-500 ml-1">*</span>
        </Label>
        <Textarea
          {...register("course_data.prerequisites")}
          className={`min-h-[100px]  `}
          placeholder="List any prerequisites for this course..."
        />
        {errors.course_data?.prerequisites && (
          <span className="text-sm text-red-500">{errors.course_data.prerequisites.message}</span>
        )}
      </div>
    </div>
  )
}

AdditionalInfoTab.propTypes = {
  register: PropTypes.func.isRequired,
  errors: PropTypes.shape({
    course_data: PropTypes.shape({
      syllabus: PropTypes.shape({
        message: PropTypes.string,
      }),
      intro_image: PropTypes.shape({
        message: PropTypes.string,
      }),
      skills: PropTypes.shape({
        message: PropTypes.string,
      }),
      tags: PropTypes.shape({
        message: PropTypes.string,
      }),
      prerequisites: PropTypes.shape({
        message: PropTypes.string,
      }),
    }),
  }).isRequired,
  watch: PropTypes.func.isRequired,
  resources: PropTypes.shape({
    skills: PropTypes.arrayOf(PropTypes.string).isRequired,
    tags: PropTypes.arrayOf(PropTypes.string).isRequired,
    isLoading: PropTypes.bool.isRequired,
  }).isRequired,
  handleFileChange: PropTypes.func.isRequired,
  setValue: PropTypes.func.isRequired,
}

export default AdditionalInfoTab
