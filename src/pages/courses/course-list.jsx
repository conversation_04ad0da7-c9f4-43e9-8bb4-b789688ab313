/* eslint-disable no-param-reassign */
import CourseCard from "@/components/course-card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import ct from "@constants/"
import PropTypes from "prop-types"
import { useDispatch, useSelector } from "react-redux"
import { useLocation, useNavigate, useParams } from "react-router-dom"

import CustomSearchbar from "@/components/custom/custom-search"
import LoadingSpinner from "@/components/custom/LoadingSpinner"
import { Button } from "@/components/ui/button"
import useDebounce, {
  useFilterHooks,
  useOpenCloseHooks,
  usePaginationHooks,
} from "@/hooks/common.hooks"
import { DATA_TYPES, RESOURCE_TYPES, useResources } from "@/services/query/create-course-form.query"
import { useFetchCourseMinInfo, useGetCourse } from "@/services/query/live-course.query"
import { setActiveCourse, setMyCourseDetailsAC } from "@/services/store/slices/courses.slice"
import { USER_ROLES } from "@/utils/constants"
import { CURRENCY_CODES, CURRENCY_SYMBOLS } from "@/utils/location-based-helpers"
import { debounce } from "lodash"
import { AlignLeft, CircleX, Layers } from "lucide-react"
import { useCallback, useEffect, useMemo, useRef, useState } from "react"
import CourseFilterSidebar from "./components/course-list-filter-sidebar"
import CustomSidebar from "./components/custom-sidebar"
import LiveCourses from "./live-courses"
import LiveCourseDetailsPage from "./live-courses/course-details/live-course-details-page"

const CourseList = ({ name }) => {
  const user = useSelector((st) => st[ct.store.USER_STORE])

  const [courses, setCourses] = useState([])
  const [totalCourse, setTotalCourse] = useState(0)
  const [selectedCategories, setSelectedCategories] = useState([])
  const [category, setCategory] = useState("")
  const prevDataLengthRef = useRef(0)
  const [hasMore, setHasMore] = useState(true)
  const [searchingValue, setSearchingValue] = useState("")
  const [min, setMin] = useState(0)
  const [max, setMax] = useState(0)
  const [selectedCurrency, setSelectedCurrency] = useState("USD")

  console.log("selectedCurrency", courses)

  const scrollAreaRef = useRef(null)
  // const prevDataLengthRef = useRef(0)

  const debouncedSearchQuery = useDebounce(searchingValue, 500)
  const { limit, offset, setOffset } = usePaginationHooks()
  const { open, handleClose, handleOpen } = useOpenCloseHooks()
  const { sortBy } = useFilterHooks()

  // router dome
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { id } = useParams()
  const location = useLocation()

  // Extract the last part of the URL
  const pathSegments = location.pathname.split("/")
  const lastSegment = pathSegments[pathSegments.length - 1]

  const { data: categoriesData } = useResources(DATA_TYPES.categories, RESOURCE_TYPES.CATEGORIES)
  const displayTheCoursesBasedByRole = () => {
    let roleLoggedIn = false
    if (user?.userRole === USER_ROLES.TRAINER) roleLoggedIn = true
    if (user?.userRole === USER_ROLES.STUDENT) {
      roleLoggedIn = name === "My Courses" || name === "Nano courses"
    }
    if (name === "Nano courses") roleLoggedIn = false
    return roleLoggedIn
  }

  // Prepare filters
  const filters = {
    userID: user.id,
    courseType: name,
    nano_course_ids: courses?.nano_course_ids,
    limit,
    offset,
    order: sortBy,
    search_query: debouncedSearchQuery?.length > 0 ? debouncedSearchQuery : null,
    for_subscribed: displayTheCoursesBasedByRole(),
    // base_currency: selectedCurrency,
    // from_price: min,
    // to_price: max,
  }
  // Only show published courses for non-admin
  if (user?.userRole !== USER_ROLES.ADMIN) filters.publish_status = "PUBLISHED"

  const { data: coursesData, isLoading, error } = useGetCourse(filters, selectedCategories)
  console.log("courses_list", coursesData)

  useEffect(() => {
    const data = coursesData?.get_course

    if (data?.total_records) {
      setTotalCourse(data.total_records)
    }

    if (categoriesData?.data?.categories) {
      const transformedCategories = categoriesData.data.categories.map((cat, index) => ({
        id: cat.id || `temp-${index + 1}`,
        name: cat.category_name || "Untitled Category",
      }))
      setCategory(transformedCategories)
    }

    // Calculate the course pricing
    const getPricing = (currency, price = 0, discount = 0) => {
      if (!currency) currency = CURRENCY_CODES.IN
      let currentPrice = price
      if (discount > 0) currentPrice = (price - (discount * price) / 100).toFixed(2)

      return {
        currency,
        currencySymbol: CURRENCY_SYMBOLS[currency],
        originalPrice: price,
        currentPrice,
      }
    }

    if (data?.courses) {
      const newData = data.courses.map((course, index) => ({
        id: course.id || `temp-${index + 1}`,
        courseName: course.title || "Untitled Course",
        courseDesc: course.description || "No description available",
        publish_status: course.publish_status || "DRAFT",
        imageUrl: course.intro_image || "/api/placeholder/400/300",
        lessons: course.total_hours || 0,
        syllabus_url: course.syllabus_url,
        students: 0,
        trainer: {
          name: "Instructor",
          type: course.course_level || "Instructor",
          avatarUrl: "/api/placeholder/64/64",
        },
        pricing: getPricing(course.base_currency, course.course_price, course.course_discount),
        courseDetails: course,
        nano_course_ids: course.nano_course_ids || [],
        isModuleExists: course.is_module_exists || false,
      }))

      // Ensure pagination and prevent duplicate entries
      setCourses((prev) => (offset === 0 ? newData : [...prev, ...newData]))

      prevDataLengthRef.current = newData.length || 0
      setHasMore(newData.length >= limit)
    }
  }, [coursesData?.get_course, categoriesData, limit, offset])

  // If the current user is a student, fetch his enrolled courses
  const { data: myCoursesInfo, isLoading: myCoursesInfoLoading } = useFetchCourseMinInfo(
    {
      userID: user.id,
      courseType: "ALL",
      for_subscribed: true,
    },
    user?.userRole === USER_ROLES.STUDENT
  )

  useEffect(() => {
    if (myCoursesInfoLoading) return

    if (myCoursesInfo?.get_course?.courses) {
      dispatch(setMyCourseDetailsAC(myCoursesInfo.get_course.courses))
    }
  }, [myCoursesInfo])

  const handleCategoryChange = useMemo(
    () =>
      debounce((categoryId, isChecked) => {
        setSelectedCategories((prev) => {
          if (isChecked) {
            return [...prev, categoryId]
          }
          return prev.filter((f) => f !== categoryId)
        })
      }, 300),
    []
  )
  const handleViewCourse = (course) => {
    dispatch(setActiveCourse(course))

    const courseDetailsPath = ct.route.COURSE_DETAILS.replace(":id", course?.id)

    navigate(courseDetailsPath, {
      state: { courseData: course.courseDetails, id: course?.id },
    })
  }

  useEffect(() => {
    setCourses([])
    setOffset(0)
    setHasMore(true)
    prevDataLengthRef.current = 0
  }, [sortBy])

  // useEffect(() =>
  // {
  //   if (!coursesData?.get_course?.courses) return

  //   // setOffset(0)
  //   const newData = coursesData?.get_course?.courses ?? []  // Ensuring it's always an array
  //   setCourses((prev) =>
  //   {

  //     // Prevent duplicate entries
  //     return offset === 0 ? newData : [...prev, ...newData]
  //   });

  //   prevDataLengthRef.current = newData?.length || []
  //   setHasMore(newData?.length >= limit)
  // }, [coursesData?.get_course?.courses, limit, offset])

  // First, memoize the handleScroll function
  const handleScroll = useCallback(() => {
    const scrollAreaElement = scrollAreaRef.current?.querySelector(
      "[data-radix-scroll-area-viewport]"
    )
    if (!scrollAreaElement) return

    const { scrollHeight, scrollTop, clientHeight } = scrollAreaElement

    // Add a small threshold before the bottom (e.g. 50px)
    const threshold = 100
    const isNearBottom = scrollTop + clientHeight >= scrollHeight - threshold
    if (isNearBottom && hasMore && !isLoading) {
      setOffset((prev) => prev + limit)
    }
  }, [hasMore, isLoading, scrollAreaRef, limit])

  // Then, properly clean up the event listener
  useEffect(() => {
    const scrollAreaElement = scrollAreaRef.current?.querySelector(
      "[data-radix-scroll-area-viewport]"
    )

    // Guard against null element
    if (!scrollAreaElement) return

    scrollAreaElement.addEventListener("scroll", handleScroll)

    // Clean up function
    // eslint-disable-next-line consistent-return
    return () => {
      scrollAreaElement.removeEventListener("scroll", handleScroll)
    }
  }, [handleScroll]) // Include handleScroll in dependencies

  const renderCourses = () => {
    if (isLoading) {
      return <LoadingSpinner />
    }

    if (courses?.length > 0) {
      return courses.map((course) => (
        <CourseCard
          key={course.id}
          course={course}
          onViewCourseDetails={() => handleViewCourse(course)}
          courseType={name}
          userRole={user.userRole}
        />
      ))
    }

    return (
      <div className="text-center flex flex-col items-center justify-center py-8 text-gray-500">
        <Layers className="text-gray-300 mx-auto w-8/12 h-2/6" />
        <p className="text-xl mt-5 text-gray-300 font-semibold">Courses Not Found</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex justify-center flex-col text-red-500 items-center h-vh">
        <CircleX className="h-36 w-36 mb-3" />
        <p className="font-semibold  text-red-500 text-lg">
          Error loading courses: {error.message}
        </p>
      </div>
    )
  }

  if (id) {
    return lastSegment === "course-overview" ? (
      <LiveCourses />
    ) : (
      <LiveCourseDetailsPage
        selectedCategories={selectedCategories}
        courses={courses}
        name={name}
      />
    )
  }

  return (
    <div>
      <div className="flex justify-between items-center">
        <h6 className="text-sm mb-0 lg:text-lg text-primary font-semibold  items-center gap-2">
          {" "}
          {name}
        </h6>
        <div className="flex gap-x-4 items-center">
          <p className="text-sm lg:text-lg  text-primary font-semibold  block lg:hidden">
            Total Courses: {totalCourse}
          </p>

          <Button className="p-1  px-2 h-8 block lg:hidden" variant="outline" onClick={handleOpen}>
            <AlignLeft size={15} />
          </Button>
        </div>
      </div>
      <div className="flex flex-col md:flex-row md:items-center justify-between py-5 gap-4">
        {/* Search Bar: Full width on small screens, limited width on medium+ screens */}
        <CustomSearchbar
          inputSize="w-full sm:w-[15rem] md:w-[20rem]"
          placeholder="Search by course name..."
          searchedValue={searchingValue}
          setSearchedValue={(e) => setSearchingValue(e?.target.value)}
        />

        {/* Right-side content: Aligns vertically on small screens, horizontal on larger screens */}
        <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-4">
          <p className="text-lg text-primary font-semibold   lg:block hidden">
            Total Courses: {totalCourse}
          </p>
          {user.userRole === USER_ROLES.ADMIN && (
            <Button
              variant="primary"
              onClick={() => navigate(ct.route.COURSE_CREATION, { state: { courseType: name } })}
              className="w-full md:w-auto"
            >
              Create Course
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-8 gap-4">
        <div className="md:col-span-6">
          <Separator orientation="horizontal" />
          <ScrollArea
            ref={scrollAreaRef}
            className="h-[44.4rem]  md:h-[54rem] lg:h-[42rem] flex  sm:pr-3 pr-0"
          >
            <div className="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-1 gap-1">
              {renderCourses()}
            </div>
          </ScrollArea>
        </div>
        <div className="hidden lg:block col-span-2">
          <Separator orientation="horizontal" />
          <CourseFilterSidebar
            name={name}
            courses={courses}
            category={category}
            selectedCategories={selectedCategories}
            onCategoryChange={handleCategoryChange}
            setSelectedCurrency={setSelectedCurrency}
            setMax={setMax}
            setMin={setMin}
          />

          {/* <PriceRangeFilter setMin={setMin} setMax={setMax} /> */}
        </div>

        {/* Grading Sidebar */}
        <CustomSidebar
          title="Grade Assignment Submission"
          description="Provide marks and feedback for this submission"
          isOpen={open}
          onClose={handleClose}
          content={
            <CourseFilterSidebar
              category={category}
              selectedCategories={selectedCategories}
              onCategoryChange={handleCategoryChange}
              setSelectedCurrency={setSelectedCurrency}
              setMax={setMax}
              setMin={setMin}
            />
          }
        />
      </div>
    </div>
  )
}

CourseList.propTypes = {
  name: PropTypes.string.isRequired,
}

export default CourseList
