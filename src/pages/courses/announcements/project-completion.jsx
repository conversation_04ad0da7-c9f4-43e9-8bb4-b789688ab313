import { Card, CardContent } from "@/components/ui/card"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import PropTypes from "prop-types"
import { useEffect, useState } from "react"
import { Bar, BarChart, CartesianGrid, ResponsiveContainer, XAxis } from "recharts"

// Chart configuration with blue colors
const chartConfig = {
  assignment_count: {
    label: "Assignment",
    color: "#1E90FF", // DodgerBlue
  },
  quiz_count: {
    label: "Quiz",
    color: "#4682B4", // SteelBlue
  },
}

function ProjectCompletion({ data }) {
  const [formattedData, setFormattedData] = useState(data || [])
  const [windowWidth, setWindowWidth] = useState(
    typeof window !== "undefined" ? window.innerWidth : 0
  )


  // Handle window resize and format month labels for mobile
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth)
    }

    // Format month labels based on screen size
    const formatData = () => {
      if (windowWidth <= 480) {
        // Use short month format for very small screens
        return (
          data?.map((item) => ({
            ...item,
            displayMonth: item.month.substring(0, 3), // First 3 chars (Jan, Feb, etc.)
          })) || []
        )
      }
      // Use full month names for larger screens
      return (
        data?.map((item) => ({
          ...item,
          displayMonth: item.month,
        })) || []
      )
    }

    window.addEventListener("resize", handleResize)
    setFormattedData(formatData())

    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [data, windowWidth])

  return (
    <Card className="shadow-lg border-0 rounded-2xl">
      <div className="">
        <p className=" p-4 text-base font-semibold">Assignment & Quiz Submitted</p>
      </div>

      <CardContent className="px-2 sm:px-6">
        <ChartContainer config={chartConfig} className="h-[300px] md:h-[220px] w-full mx-auto">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={formattedData}>
              <CartesianGrid vertical={false} />
              <XAxis
                dataKey="displayMonth"
                tickLine={false}
                tickMargin={10}
                axisLine={false}
                fontSize={windowWidth < 640 ? 10 : 12}
                interval={windowWidth < 480 ? 0 : "preserveStartEnd"}
              />
              <ChartTooltip cursor={false} content={<ChartTooltipContent indicator="dashed" />} />
              <Bar
                dataKey="assignment_count"
                fill={chartConfig.assignment_count.color}
                radius={4}
                barSize={windowWidth < 640 ? 16 : 20}
              />
              <Bar
                dataKey="quiz_count"
                fill={chartConfig.quiz_count.color}
                radius={4}
                barSize={windowWidth < 640 ? 16 : 20}
              />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}

ProjectCompletion.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      month: PropTypes.string.isRequired,
      assignment_count: PropTypes.number.isRequired,
      quiz_count: PropTypes.number.isRequired,
    })
  ).isRequired,
}

export default ProjectCompletion
