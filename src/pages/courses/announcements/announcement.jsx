import PropTypes from "prop-types"
import { AnnousementCard } from "./annousement-card"

function AnnoucementPage({ setIsSideBarOpen }) {
  const assignments = [
    {
      id: 1,
      title: "OS Assignment Submission",
      date: "22 July",
      message: "Mail sent on 15th July, 20 pending to reply back. Please ensure timely response.",
      postedBy: "Manish Indoria",
    },
    {
      id: 2,
      title: "DBMS Project Proposal",
      date: "5 August",
      message:
        "Final project proposals must be submitted before 5th August. Late submissions will not be entertained.",
      postedBy: "<PERSON><PERSON>",
    },
    {
      id: 3,
      title: "AI Research Paper Review",
      date: "15 September",
      message:
        "Review feedback on AI research papers should be provided by 15th September. Kindly check the shared documents.",
      postedBy: "<PERSON><PERSON>",
    },
    {
      id: 4,
      title: "Web Development Final Exam",
      date: "30 October",
      message:
        "The final exam for Web Development is scheduled for 30th October. Prepare according to the syllabus shared.",
      postedBy: "<PERSON><PERSON><PERSON>",
    },
  ]

  return (
    <div>
      <AnnousementCard assignments={assignments} setIsSideBarOpen={setIsSideBarOpen} />
    </div>
  )
}

AnnoucementPage.propTypes = {
  setIsSideBarOpen: PropTypes.func.isRequired,
}

export default AnnoucementPage
