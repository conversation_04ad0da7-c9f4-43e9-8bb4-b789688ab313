import GradiantCard from "@/components/custom/custom-cards/graidiant.cards"
import PropTypes from "prop-types"

// eslint-disable-next-line import/prefer-default-export
export function AnnousementCard({ assignments, setIsSideBarOpen }) {
  const truncate = (str, maxLength = 25) => {
    if (str?.length > maxLength) {
      return `${str?.slice(0, maxLength)}...`
    }
    return str
  }
  return (
    <div className="grid grid-cols-3 gap-4">
      {assignments?.map(({ id, title, modified_at, posted_by, message }) => (
        <GradiantCard
          key={id}
          id={id}
          title={title}
          modified_at={modified_at}
          postedBy={posted_by}
          message={message}
        />
      ))}
    </div>
  )
}

// Define PropTypes
AnnousementCard.propTypes = {
  assignments: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      title: PropTypes.string.isRequired,
      date: PropTypes.string.isRequired,
      message: PropTypes.string.isRequired,
      postedBy: PropTypes.string.isRequired,
    })
  ).isRequired,
  setIsSideBarOpen: PropTypes.func.isRequired,
}
