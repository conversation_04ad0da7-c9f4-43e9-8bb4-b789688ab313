import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { USER_ROLES } from "@/utils/constants"
import ct from "@constants/"
import { ArrowLeft, Award, Calendar, Clock, Globe, Play, Star, Users } from "lucide-react"
import { useSelector } from "react-redux"
import { useLocation, useNavigate, useParams } from "react-router-dom"

export default function CourseDescription() {
  const location = useLocation()
  const navigate = useNavigate()
  const rawCourseData = location.state?.courseData
  const params = useParams()
  const { userRole } = useSelector((st) => st[ct.store.USER_STORE])

  // Transform the API response to match the expected structure in the component
  const courseData = rawCourseData
    ? {
      id: rawCourseData.id,
      courseName: rawCourseData.title,
      courseDesc: rawCourseData.description,
      lessons: rawCourseData.total_hours ? `${rawCourseData.total_hours} hours` : "N/A",
      imageUrl: rawCourseData.intro_image,
      pricing: {
        currentPrice: `$${((rawCourseData.course_price * (100 - rawCourseData.course_discount)) / 100).toFixed(2)}`,
        originalPrice: `$${rawCourseData.course_price.toFixed(2)}`,
      },
      trainer: {
        name: "Expert Instructor", // Default value as it's not in the API response
        type: rawCourseData.course_level || "Instructor",
      },
    }
    : null

  const handleBack = () => {
    navigate(-1)
  }

  if (!courseData) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md p-8 text-center">
          <div className="text-3xl mb-4">😕</div>
          <h2 className="text-2xl font-bold mb-2">Course Not Found</h2>
          <p className="text-gray-500 mb-6">
            We couldn&apos;t find the course you&apos;re looking for.
          </p>
          <Button onClick={handleBack} className="w-full">
            Go Back
          </Button>
        </Card>
      </div>
    )
  }

  const handleAttachModule = () => {
    navigate(`${ct.route.MODULE_CONTENT}/${params?.id}`, {
      state: { courseData: rawCourseData },
    })
  }

  const handleNavigate = () => {
    handleAttachModule()
  }

  return (
    <div className="container mx-auto p-6 space-y-8 bg-gray-50  ">
      <div className="flex items-center justify-between">
        <Button
          onClick={handleBack}
          variant="ghost"
          className="flex items-center gap-2 hover:bg-gray-100 transition-colors"
        >
          <ArrowLeft size={16} />
          Back to Courses
        </Button>

        {rawCourseData.publish_status === "DRAFT" && (
          <div className="bg-amber-100 text-amber-800 px-4 py-1 rounded-full text-sm font-medium flex items-center">
            <span className="w-2 h-2 bg-amber-500 rounded-full mr-2" />
            Draft Mode
          </div>
        )}
      </div>

      {/* Hero section with gradient background */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-indigo-600 to-violet-600 text-white">
        <div className="absolute inset-0 bg-black opacity-20" />
        <div className="absolute bottom-0 left-0 w-full h-1/2 bg-gradient-to-t from-black/40 to-transparent" />

        <div className="relative z-10 p-8 md:p-12">
          <div className="flex items-center justify-between mb-4">
            {rawCourseData.course_discount > 0 && (
              <div className="inline-block bg-red-500 text-white px-4 py-1 rounded-full text-sm font-bold mb-4 shadow-lg">
                {rawCourseData.course_discount}% OFF
              </div>
            )}
            {userRole === USER_ROLES.ADMIN && (
              <Button variant="outline" onClick={handleAttachModule}>
                Attach Module
              </Button>
            )}
          </div>
          <h1 className="text-3xl md:text-4xl font-bold mb-3">{courseData.courseName}</h1>

          <p className="text-lg opacity-90 max-w-3xl mb-6">
            {rawCourseData.intro || "Master this course and advance your skills!"}
          </p>

          <div className="flex flex-wrap items-center gap-4 md:gap-6 text-sm">
            <div className="flex items-center gap-x-1 bg-white/20 px-3 py-1.5 rounded-full">
              <Star size={16} className="text-yellow-300" />
              <span>4.8 (200+ ratings)</span>
            </div>

            <div className="flex items-center gap-x-1 bg-white/20 px-3 py-1.5 rounded-full">
              <Users size={16} />
              <span>500+ students</span>
            </div>

            <div className="flex items-center gap-x-1 bg-white/20 px-3 py-1.5 rounded-full">
              <Award size={16} />
              <span>{rawCourseData.course_level}</span>
            </div>

            <div className="flex items-center gap-x-1 bg-white/20 px-3 py-1.5 rounded-full">
              <Globe size={16} />
              <span>{rawCourseData.course_language}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-8">
          {/* Video preview */}
          <Card className="overflow-hidden shadow-md">
            <div className="relative group aspect-video">
              <img
                src={courseData.imageUrl}
                alt={`${courseData.courseName} Preview`}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 group-hover:bg-opacity-50 transition-all">
                <div className="bg-white rounded-full p-4 shadow-lg transform transition-transform group-hover:scale-110">
                  <Play size={32} className="text-indigo-600" />
                  {/* <VideoRendering /> */}
                </div>
              </div>
            </div>
          </Card>

          {/* Course information tabs */}
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="w-full flex space-x-1 bg-gray-100 p-1 rounded-xl">
              <TabsTrigger value="overview" className="flex-1 rounded-lg">
                Overview
              </TabsTrigger>
              <TabsTrigger value="content" className="flex-1 rounded-lg">
                Content
              </TabsTrigger>
              <TabsTrigger value="details" className="flex-1 rounded-lg">
                Details
              </TabsTrigger>
              <TabsTrigger value="instructor" className="flex-1 rounded-lg">
                Instructor
              </TabsTrigger>
              <TabsTrigger value="reviews" className="flex-1 rounded-lg">
                Reviews
              </TabsTrigger>
            </TabsList>

            <Card className="mt-4 border-0 shadow-md">
              <CardContent className="p-0">
                <TabsContent value="overview" className="p-6">
                  <h3 className="text-xl font-semibold mb-4">Course Overview</h3>
                  <p className="text-gray-700 leading-relaxed">{courseData.courseDesc}</p>
                </TabsContent>

                <TabsContent value="content" className="p-6">
                  <h3 className="text-xl font-semibold mb-4">Course Content</h3>
                  <div className="bg-gray-50 rounded-lg p-4 mb-6 flex items-center justify-between">
                    <div>
                      <div className="text-sm text-gray-500">Total Learning</div>
                      <div className="text-lg font-semibold">{rawCourseData.total_hours} hours</div>
                    </div>
                    <div className="h-12 border-r border-gray-300" />
                    <div>
                      <div className="text-sm text-gray-500">Duration</div>
                      <div className="text-lg font-semibold">
                        {rawCourseData.duration_days} days
                      </div>
                    </div>
                    <div className="h-12 border-r border-gray-300" />
                    <div>
                      <div className="text-sm text-gray-500">Category</div>
                      <div className="text-lg font-semibold">{rawCourseData.course_type}</div>
                    </div>
                  </div>

                  {rawCourseData.syllabus_url && (
                    <div className="bg-indigo-50 border border-indigo-100 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-indigo-700">Course Syllabus</h4>
                          <p className="text-sm text-indigo-600">
                            Download the complete course syllabus
                          </p>
                        </div>
                        <a
                          href={rawCourseData.syllabus_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                        >
                          Download
                        </a>
                      </div>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="details" className="p-6">
                  <h3 className="text-xl font-semibold mb-4">Course Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card className="border-0 shadow-sm">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="bg-indigo-100 p-2 rounded-lg">
                            <Award className="h-6 w-6 text-indigo-600" />
                          </div>
                          <div>
                            <div className="text-sm text-gray-500">Course Level</div>
                            <div className="font-medium">{rawCourseData.course_level}</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card className="border-0 shadow-sm">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="bg-purple-100 p-2 rounded-lg">
                            <Globe className="h-6 w-6 text-purple-600" />
                          </div>
                          <div>
                            <div className="text-sm text-gray-500">Language</div>
                            <div className="font-medium">{rawCourseData.course_language}</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card className="border-0 shadow-sm">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="bg-blue-100 p-2 rounded-lg">
                            <Calendar className="h-6 w-6 text-blue-600" />
                          </div>
                          <div>
                            <div className="text-sm text-gray-500">Duration</div>
                            <div className="font-medium">{rawCourseData.duration_days} days</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card className="border-0 shadow-sm">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="bg-green-100 p-2 rounded-lg">
                            <Clock className="h-6 w-6 text-green-600" />
                          </div>
                          <div>
                            <div className="text-sm text-gray-500">Total Hours</div>
                            <div className="font-medium">{rawCourseData.total_hours} hours</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {rawCourseData.prerequisites && (
                    <div className="mt-6">
                      <h4 className="text-lg font-medium mb-2">Prerequisites</h4>
                      <Card className="border-0 bg-gray-50">
                        <CardContent className="p-4">
                          <p className="text-gray-700">{rawCourseData.prerequisites}</p>
                        </CardContent>
                      </Card>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="instructor" className="p-6">
                  <h3 className="text-xl font-semibold mb-4">Instructor</h3>
                  <div className="flex flex-col md:flex-row items-start gap-6 bg-gray-50 rounded-lg p-6">
                    <img
                      src="https://instructor-academy.onlinecoursehost.com/content/images/2023/05/How-to-Create-an-Online-Course-For-Free--Complete-Guide--6.jpg"
                      alt="Instructor"
                      className="w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg"
                    />
                    <div>
                      <h4 className="text-xl font-semibold">{courseData.trainer.name}</h4>
                      <p className="text-indigo-600 mb-3">
                        {rawCourseData.course_level} Instructor
                      </p>
                      <p className="text-gray-600">
                        An experienced instructor specialized in teaching{" "}
                        {rawCourseData.course_language} {courseData.courseName} courses. With
                        extensive knowledge in the field, they provide practical insights and
                        hands-on experience to help students master the subject.
                      </p>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="reviews" className="p-6">
                  <h3 className="text-xl font-semibold mb-4">Student Reviews</h3>
                  <div className="flex items-center gap-4 bg-gray-50 p-6 rounded-lg mb-6">
                    <div className="text-center">
                      <div className="text-4xl font-bold text-indigo-600">4.8</div>
                      <div className="flex gap-1 text-yellow-400 my-1">
                        <Star size={16} fill="currentColor" />
                        <Star size={16} fill="currentColor" />
                        <Star size={16} fill="currentColor" />
                        <Star size={16} fill="currentColor" />
                        <Star
                          size={16}
                          className="text-yellow-400"
                          stroke="currentColor"
                          fill="currentColor"
                          opacity={0.5}
                        />
                      </div>
                      <div className="text-xs text-gray-500">200+ ratings</div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <div className="text-sm font-medium w-6">5</div>
                        <div className="flex-1 bg-gray-200 rounded-full h-2 overflow-hidden">
                          <div
                            className="bg-yellow-400 h-full rounded-full"
                            style={{ width: "75%" }}
                          />
                        </div>
                      </div>
                      <div className="flex items-center gap-2 mb-1">
                        <div className="text-sm font-medium w-6">4</div>
                        <div className="flex-1 bg-gray-200 rounded-full h-2 overflow-hidden">
                          <div
                            className="bg-yellow-400 h-full rounded-full"
                            style={{ width: "20%" }}
                          />
                        </div>
                      </div>
                      <div className="flex items-center gap-2 mb-1">
                        <div className="text-sm font-medium w-6">3</div>
                        <div className="flex-1 bg-gray-200 rounded-full h-2 overflow-hidden">
                          <div
                            className="bg-yellow-400 h-full rounded-full"
                            style={{ width: "3%" }}
                          />
                        </div>
                      </div>
                      <div className="flex items-center gap-2 mb-1">
                        <div className="text-sm font-medium w-6">2</div>
                        <div className="flex-1 bg-gray-200 rounded-full h-2 overflow-hidden">
                          <div
                            className="bg-yellow-400 h-full rounded-full"
                            style={{ width: "1%" }}
                          />
                        </div>
                      </div>
                      <div className="flex items-center gap-2 mb-1">
                        <div className="text-sm font-medium w-6">1</div>
                        <div className="flex-1 bg-gray-200 rounded-full h-2 overflow-hidden">
                          <div
                            className="bg-yellow-400 h-full rounded-full"
                            style={{ width: "1%" }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="text-center p-6 border border-dashed rounded-lg">
                    <p className="text-gray-500">
                      No reviews have been submitted for this course yet.
                    </p>
                  </div>
                </TabsContent>
              </CardContent>
            </Card>
          </Tabs>
        </div>

        {/* Course enrollment card */}
        <div className="lg:col-span-1">
          <Card className="border-0 shadow-lg sticky top-6">
            <CardContent className="p-6">
              <div className="aspect-video relative rounded-lg overflow-hidden mb-6">
                <img
                  src={courseData.imageUrl}
                  alt={`${courseData.courseName} Preview`}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 flex items-center justify-center bg-black/40 hover:bg-black/50 transition-colors">
                  <div className="bg-white rounded-full p-3 shadow-lg">
                    <Play size={24} className="text-indigo-600" />
                  </div>
                </div>
              </div>

              <div className="flex items-baseline gap-2 mb-1">
                <span className="text-2xl font-bold">{courseData.pricing.currentPrice}</span>
                {rawCourseData.course_discount > 0 && (
                  <span className="text-gray-500 line-through text-lg">
                    {courseData.pricing.originalPrice}
                  </span>
                )}
              </div>

              {rawCourseData.course_discount > 0 && (
                <div className="flex items-center gap-2 mb-6">
                  <span className="bg-red-100 text-red-600 px-2 py-0.5 rounded text-sm font-medium">
                    {rawCourseData.course_discount}% OFF
                  </span>
                  <span className="text-red-600 text-sm font-medium">Limited time offer!</span>
                </div>
              )}

              <Button
                onClick={(e) => handleNavigate(e)}
                variant="primary"
                className="w-full py-6 text-lg mb-6 "
                disabled={rawCourseData.publish_status !== "PUBLISHED"}
              >
                {rawCourseData.pricing_type === "FREE" ? "Enroll for Free" : "Enroll Now"}
              </Button>

              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5 text-indigo-600" />
                  <div className="text-sm">
                    <span className="font-medium">Duration:</span> {rawCourseData.duration_days}{" "}
                    days
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Clock className="h-5 w-5 text-indigo-600" />
                  <div className="text-sm">
                    <span className="font-medium">Total Hours:</span> {rawCourseData.total_hours}{" "}
                    hours
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Award className="h-5 w-5 text-indigo-600" />
                  <div className="text-sm">
                    <span className="font-medium">Level:</span> {rawCourseData.course_level}
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Globe className="h-5 w-5 text-indigo-600" />
                  <div className="text-sm">
                    <span className="font-medium">Language:</span> {rawCourseData.course_language}
                  </div>
                </div>
              </div>

              <div className="border-t border-gray-200 mt-6 pt-6">
                <h3 className="font-medium mb-2">This course includes:</h3>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2 text-sm">
                    <div className="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center">
                      <span className="text-green-600 text-xs">✓</span>
                    </div>
                    <span>Full lifetime access</span>
                  </li>
                  <li className="flex items-center gap-2 text-sm">
                    <div className="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center">
                      <span className="text-green-600 text-xs">✓</span>
                    </div>
                    <span>Access on mobile and desktop</span>
                  </li>
                  <li className="flex items-center gap-2 text-sm">
                    <div className="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center">
                      <span className="text-green-600 text-xs">✓</span>
                    </div>
                    <span>Certificate of completion</span>
                  </li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {rawCourseData.publish_status === "DRAFT" && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mt-4">
          <div className="flex items-start gap-3">
            <div className="bg-amber-100 p-2 rounded-full">
              <span className="text-amber-600 text-lg">⚠️</span>
            </div>
            <div>
              <p className="font-bold text-amber-800">Draft Mode</p>
              <p className="text-amber-700">
                This course is currently in draft mode and is not yet published to students.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
