import { Card } from "@/components/ui/card"
import { AlertCircle, Calendar, Check, Clock, XCircle } from "lucide-react"
import moment from "moment"
import { useMemo, useState } from "react"
import "./GitHubCalendar.css"

const GitHubCalendar = ({ data = [], title = "Attendance Calendar", showControls = true }) => {
  const [viewMode, setViewMode] = useState("3month")
  const [startDateOffset, setStartDateOffset] = useState(0)

  // Check if there's data available
  const hasData = data.length > 0

  // Process data and convert UTC dates to local time
  const processData = () => {
    const processedData = {}
    data.forEach((item) => {
      const localDate = moment.parseZone(item.day).local()
      const dateString = localDate.format("YYYY-MM-DD")
      processedData[dateString] = item.status
    })
    return processedData
  }

  // Generate calendar data with flexible time range
  const generateCalendarData = () => {
    if (data.length === 0) return { weeks: {}, startDate: moment(), endDate: moment() }

    const processedData = processData()
    const weeks = {}

    // Get date range from data
    const dates = data.map((item) => moment.parseZone(item.day).local())
    let earliestDate = moment.min(dates)

    // Apply offset based on navigation controls
    if (startDateOffset !== 0) {
      const offsetAmount = viewMode === "3month" ? 3 : 1
      earliestDate = earliestDate.clone().add(startDateOffset * offsetAmount, "months")
    }

    // Set range based on view mode
    const startDate = earliestDate.clone().startOf("month")
    const endDate =
      viewMode === "3month"
        ? startDate.clone().add(2, "months").endOf("month")
        : startDate.clone().endOf("month")

    // Find first Sunday to start the calendar
    let currentDate = startDate.clone()
    while (
      currentDate.day() !== 0 &&
      currentDate.isSameOrAfter(startDate.clone().startOf("week"))
    ) {
      currentDate.subtract(1, "day")
    }

    // Generate days
    while (currentDate.isSameOrBefore(endDate)) {
      const dayOfWeek = currentDate.day()
      const weekStart = currentDate.clone().startOf("week")
      const weekKey = weekStart.format("YYYY-MM-DD")

      if (
        currentDate.isBetween(startDate.clone().subtract(1, "day"), endDate.clone().add(1, "day"))
      ) {
        if (!weeks[weekKey]) {
          weeks[weekKey] = Array(7).fill(null)
        }

        const dateString = currentDate.format("YYYY-MM-DD")
        weeks[weekKey][dayOfWeek] = {
          date: dateString,
          status: processedData[dateString] || null,
          localDate: currentDate.clone(),
          isInRange: currentDate.isBetween(startDate, endDate, "day", "[]"),
          isToday: currentDate.isSame(moment(), "day"),
        }
      }

      currentDate.add(1, "day")
    }

    return { weeks, startDate, endDate }
  }

  // Get month labels with year if crossing year boundary
  const getMonthLabels = (startDate, endDate) => {
    const months = []
    const durationInMonths = endDate.diff(startDate, "month") + 1

    for (let i = 0; i < durationInMonths; i++) {
      const monthDate = startDate.clone().add(i, "months")
      months.push({
        name: monthDate.format("MMM"),
        year: monthDate.year(),
        month: monthDate.month(),
      })
    }
    return months
  }

  // Calculate month positions for rendering
  const calculateMonthSpans = (weeksData, months) => {
    const sortedWeeks = Object.keys(weeksData).sort()
    return months.map((month) => {
      const firstWeek = sortedWeeks.findIndex((week) => {
        const weekDate = moment(week)
        return weekDate.month() === month.month && weekDate.year() === month.year
      })
      const lastWeek = sortedWeeks.findIndex((week, i) => {
        const weekDate = moment(week)
        return i > firstWeek && (weekDate.month() !== month.month || weekDate.year() !== month.year)
      })
      return {
        span: (lastWeek === -1 ? sortedWeeks.length : lastWeek) - firstWeek,
        ...month,
      }
    })
  }

  // Calculate attendance statistics
  const calculateStats = (processedData) => {
    const stats = {
      present: 0,
      late: 0,
      absent: 0,
      total: 0,
    }

    Object.values(processedData).forEach((status) => {
      if (status) {
        stats[status.toLowerCase()]++
        stats.total++
      }
    })

    return stats
  }

  // Get attendance status for tooltip display
  const getStatusDisplay = (status) => {
    if (!status) return "No record"
    return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()
  }

  // Generate status class for styling
  const getStatusClass = (dayData) => {
    if (!dayData) return "empty"
    if (!dayData.isInRange) return "out-of-range"
    if (dayData.isToday) return `today ${dayData?.status?.toLowerCase() || "empty"}`
    return dayData?.status?.toLowerCase() || "empty"
  }

  // Memoized calendar data
  const { weeks, startDate, endDate } = useMemo(
    () => generateCalendarData(),
    [data, viewMode, startDateOffset]
  )
  const sortedWeeks = Object.keys(weeks).sort()
  const monthLabels = useMemo(() => getMonthLabels(startDate, endDate), [startDate, endDate])
  const monthSpans = useMemo(() => calculateMonthSpans(weeks, monthLabels), [weeks, monthLabels])
  const stats = useMemo(() => calculateStats(processData()), [data])

  return (
    <Card className="shadow-lg border-0 rounded-2xl">
      <div className="p-3 rounded-t-lg"> {/* Reduced padding from p-5 to p-3 */}
        <h3 className="calendar-title">
          <Calendar className="inline-block mr-2 h-4 w-4" /> {/* Reduced icon size from h-5 w-5 to h-4 w-4 */}
          {title}
        </h3>
      </div>
      <div className="calendar-container">
        {hasData ? (
          <>
            <div className="attendance-stats">
              <div className="stat-item">
                <Check size={14} className="text-green-600" /> {/* Reduced from size={16} */}
                <span className="stat-label">Present:</span>
                <span className="stat-value text-green-600 font-bold">{stats.present}</span>
              </div>
              <div className="stat-item">
                <Clock size={14} className="text-amber-500" /> {/* Reduced from size={16} */}
                <span className="stat-label">Late:</span>
                <span className="stat-value text-amber-500 font-bold">{stats.late}</span>
              </div>
              <div className="stat-item">
                <XCircle size={14} className="text-red-600" /> {/* Reduced from size={16} */}
                <span className="stat-label">Absent:</span>
                <span className="stat-value text-red-600 font-bold">{stats.absent}</span>
              </div>
            </div>

            <div className="github-calendar">
              <div className="month-header">
                <div className="day-name-spacer"></div>
                {monthSpans.map((month, i) => (
                  <div key={i} className="month-label" style={{ flex: month.span }}>
                    <span>{month.name}</span>
                    {month.year !== startDate.year() && (
                      <span className="year-label">{month.year}</span>
                    )}
                  </div>
                ))}
              </div>

              <div className="calendar-grid">
                {["S", "M", "T", "W", "T", "F", "S"].map((day, i) => (
                  <div key={day} className="day-row">
                    <div className="day-label">{day}</div>
                    <div className="day-cells-container">
                      {sortedWeeks.map((weekKey) => {
                        const dayData = weeks[weekKey][i]
                        return (
                          <div
                            key={`${weekKey}-${i}`}
                            className={`day-cell ${getStatusClass(dayData)}`}
                            data-tooltip={
                              dayData
                                ? `${dayData.localDate.format("MMM D, YYYY")}\n${getStatusDisplay(dayData.status)}`
                                : ""
                            }
                          ></div>
                        )
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        ) : (
          <div className="no-data-container flex flex-col items-center justify-center py-6 text-gray-500 h-[12rem]"> {/* Reduced height from h-[15.5rem] to h-[12rem] and py-10 to py-6 */}
            <AlertCircle className="h-8 w-8 mb-2" /> {/* Reduced from h-10 w-10 */}
            <p className="text-base">No attendance available</p> {/* Reduced from text-lg */}
          </div>
        )}
      </div>
    </Card>
  )
}

export default GitHubCalendar