import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import {
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  <PERSON>A<PERSON>s,
  <PERSON><PERSON>xis,
} from "recharts"

const chartData = [
  { month: "1", desktop: 186 },
  { month: "2", desktop: 305 },
  { month: "3", desktop: 237 },
  { month: "4", desktop: 73 },
  { month: "5", desktop: 209 },
  { month: "6", desktop: 214 },
  { month: "7", desktop: 0 },
  { month: "8", desktop: 0 },
  { month: "9", desktop: 0 },
  { month: "10", desktop: 0 },
  { month: "11", desktop: 0 },
  { month: "12", desktop: 0 },
]

// eslint-disable-next-line react/prop-types
function ChartContainer({ children }) {
  return <div className="w-full h-[200px]">{children}</div>
}

function GraphCard() {
  return (
    <Card className="w-full rounded-[.6rem]">
      <CardHeader>
        <CardTitle>Quiz Performance</CardTitle>
      </CardHeader>
      <CardContent>
        <ChartContainer>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={chartData}
              margin={{
                top: 5,
                right: 10,
                left: 10,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis
                dataKey="month"
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                tickFormatter={(value) => value.slice(0, 3)}
              />
              <YAxis tickLine={false} axisLine={false} tickMargin={8} width={30} />
              <Tooltip />
              <Line
                dataKey="desktop"
                type="monotone"
                stroke="#2563eb"
                strokeWidth={2}
                dot={false}
                activeDot={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}

export default GraphCard
