.calendar-container {
  max-width: 100%;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  min-height: 255px;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.calendar-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  display: flex;
  align-items: center;
}

.time-range {
  font-size: 0.875rem;
  color: #666;
  font-weight: 500;
}

/* Control styles */
.calendar-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
  flex-wrap: wrap;
  gap: 14px;
}

.control-buttons {
  display: flex;
  gap: 8px;
}

.control-button,
.view-toggle-button {
  padding: 6px 10px;
  background: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.control-button:hover,
.view-toggle-button:hover {
  background: #eaeaea;
  border-color: #ccc;
}

.view-toggle-button {
  background: #f0f7ff;
  border-color: #c7deff;
  color: #0969da;
}

.view-toggle-button:hover {
  background: #e1f1ff;
  border-color: #96c0ff;
}

/* Stats section */
.attendance-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.85rem;
}

.stat-label {
  font-weight: 600;
  color: #333;
}

.stat-value {
  font-weight: 500;
}

/* Main calendar styles */
.github-calendar {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  width: 100%;
  margin: 0;
}

.month-header {
  display: flex;
  margin-bottom: 8px;
  padding-left: 24px;
  width: 100%;
}

.month-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 13px;
  font-weight: 600;
  color: #333;
  padding: 0 4px;
}

.year-label {
  font-size: 10px;
  color: #888;
  margin-top: 1px;
}

.calendar-grid {
  display: flex;
  flex-direction: column;
  gap: 0px;
  width: 100%;
}

.day-row {
  display: flex;
  align-items: center;
  height: 18px;
  width: 100%;
}

.day-label {
  width: 24px;
  font-size: 10px;
  color: #666;
  text-align: center;
  font-weight: 500;
  flex-shrink: 0;
}

.day-cells-container {
  display: flex;
  flex: 1;
  width: 100%;
  justify-content: space-between;
  /* Distribute cells across full width */
  align-items: center;
}

.day-cell {
  width: 16px;
  height: 16px;
  min-width: 16px;
  border-radius: 3px;
  margin: 0;
  /* Removed horizontal margin */
  box-sizing: border-box;
  transition: all 0.2s ease;
  position: relative;
  border: 1px solid transparent;
}

.day-cell.out-of-range {
  visibility: hidden;
}

.day-cell:hover {
  transform: scale(1.15);
  z-index: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.day-cell.empty {
  background-color: #ebedf0;
}

.day-cell.present {
  background-color: #006d32;
}

.day-cell.late {
  background-color: #ff9f1c;
}

.day-cell.absent {
  background-color: #c41313;
  border: 1px solid #e0e0e0;
}

/* Today highlighting */
.day-cell.today {
  border: 2px solid #0969da;
}

.day-cell::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  color: white;
  padding: 5px 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: pre;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
  z-index: 10;
  min-width: 120px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.day-cell:hover::after {
  opacity: 1;
  margin-bottom: 8px;
}

.calendar-legend {
  display: flex;
  justify-content: center;
  gap: 1px;
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #555;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 3px;
  border: 1px solid transparent;
}

.today-marker {
  border: 2px solid #0969da !important;
}

/* Animation for button press */
@keyframes buttonPulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.98);
  }

  100% {
    transform: scale(1);
  }
}

.control-button:active,
.view-toggle-button:active {
  animation: buttonPulse 0.2s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .calendar-container {
    padding: 14px;
  }

  .calendar-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .calendar-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .control-buttons {
    justify-content: space-between;
  }

  .attendance-stats {
    flex-direction: column;
    gap: 6px;
  }

  .day-label {
    width: 18px;
    font-size: 9px;
  }

  .day-cell {
    width: 12px;
    height: 12px;
    min-width: 12px;
  }

  .day-cells-container {
    width: 100%;
    justify-content: space-between;
    /* Keep full width distribution on mobile */
  }

  .calendar-legend {
    flex-wrap: wrap;
    gap: 10px;
    justify-content: flex-start;
  }
}