/* eslint-disable no-nested-ternary */
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { format } from "date-fns"
import { motion } from "framer-motion"
import { AlertCircle, Calendar, ChevronLeft, ChevronRight, Clock } from "lucide-react"
import PropTypes from "prop-types"
import { useEffect, useRef, useState } from "react"
import DegreeCourseCard from "../live-courses/course-details/degree-course-card"

function UpCommingEvents({ data, name, nanoCourses, onAuditClick }) {
  const carouselRef = useRef(null)
  const [position, setPosition] = useState(0)
  const [cardsToShow, setCardsToShow] = useState(3)
  const [cardWidth, setCardWidth] = useState(300)
  const [autoplay, setAutoplay] = useState(true)
  const [filter, setFilter] = useState(name === "Nano courses" ? "courses" : "all")
  console.log("UpCommingEvents_data", name)
  const isNano = name === "Nano courses"
  const processEvents = () => {
    if (!data) return []

    const events = []

    // Process quizzes
    if (data?.quizes?.length) {
      data.quizes.forEach((quiz) => {
        events.push({
          id: `quiz-${quiz.id}`,
          title: quiz.title,
          date: new Date(quiz.due_date),
          description: quiz.description,
          type: "Quiz",
          deadline: new Date(quiz.due_date),
          priority: getPriority(new Date(quiz.due_date)),
          course: quiz?.courseName,
          isNano,
        })
      })
    }

    // Process assignments
    if (data?.assignments?.length) {
      data.assignments.forEach((assignment) => {
        events.push({
          id: `assignment-${assignment.id}`,
          title: assignment.title,
          date: new Date(assignment.due_date),
          description: assignment.description,
          type: "Assignment",
          deadline: new Date(assignment.due_date),
          priority: getPriority(new Date(assignment.due_date)),
          course: assignment?.courseName,
          isNano,
        })
      })
    }

    // Process projects
    if (data?.projects?.length) {
      data.projects.forEach((project) => {
        events.push({
          id: `project-${project.id}`,
          title: project.title,
          date: new Date(project.due_date),
          description: project.description,
          type: "Project",
          deadline: new Date(project.due_date),
          priority: getPriority(new Date(project.due_date)),
          course: project?.courseName,
          isNano,
        })
      })
    }

    // Sort events by date
    return events.sort((a, b) => a.date - b.date)
  }

  const getPriority = (date) => {
    const now = new Date()
    const diff = date - now
    const daysDiff = diff / (1000 * 60 * 60 * 24)

    if (daysDiff < 0) return "overdue"
    if (daysDiff < 1) return "urgent"
    if (daysDiff < 3) return "high"
    if (daysDiff < 7) return "medium"
    return "low"
  }

  const filterEvents = (events) => {
    let filteredEvents = [...events]

    // Filter by type
    if (filter !== "all" && filter !== "courses") {
      filteredEvents = filteredEvents.filter((event) => event.type.toLowerCase() === filter)
    }

    return filteredEvents
  }

  const allEvents = processEvents()
  const events = filterEvents(allEvents)
  const eventCount = events.length || 1

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) {
        setCardsToShow(1)
        setCardWidth(280)
      } else if (window.innerWidth < 1024) {
        setCardsToShow(2)
        setCardWidth(280)
      } else {
        setCardsToShow(3)
        setCardWidth(290)
      }
      // Reset position when screen size changes
      setPosition(0)
    }

    handleResize()
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [""])

  // Reset position when filters change
  useEffect(() => {
    setPosition(0)
  }, [filter])

  // Autoplay functionality
  useEffect(() => {
    if (!autoplay || (filter === "courses" ? nanoCourses?.length : events.length) <= cardsToShow)
      return

    const interval = setInterval(() => {
      const itemCount = filter === "courses" ? nanoCourses?.length || 0 : eventCount
      const maxPosition = -((itemCount - cardsToShow) * cardWidth)
      setPosition((prev) => (prev <= maxPosition ? 0 : prev - cardWidth))
    }, 5000)

    return () => clearInterval(interval)
  }, [eventCount, cardsToShow, cardWidth, autoplay, events.length, filter, nanoCourses])

  const slideLeft = () => {
    const newPosition = position + cardWidth
    setPosition(newPosition > 0 ? 0 : newPosition)
  }

  const slideRight = () => {
    const itemCount = filter === "courses" ? nanoCourses?.length || 0 : eventCount
    const maxPosition = -((itemCount - cardsToShow) * cardWidth)
    const newPosition = position - cardWidth
    setPosition(newPosition < maxPosition ? maxPosition : newPosition)
  }

  // Pause autoplay when hovering
  const handleMouseEnter = () => setAutoplay(false)
  const handleMouseLeave = () => setAutoplay(true)

  // Calculate if we can slide in either direction
  const itemCount = filter === "courses" ? nanoCourses?.length || 0 : eventCount
  const canSlideLeft = position < 0
  const canSlideRight = position > -((itemCount - cardsToShow) * cardWidth)

  // Get filter counts
  const filterCounts = {
    all: allEvents.length,
    quiz: allEvents.filter((e) => e.type.toLowerCase() === "quiz").length,
    assignment: allEvents.filter((e) => e.type.toLowerCase() === "assignment").length,
    project: allEvents.filter((e) => e.type.toLowerCase() === "project").length,
    courses: nanoCourses?.length || 0,
  }

  // Urgent events count
  const urgentCount = allEvents.filter((e) => ["urgent", "overdue"].includes(e.priority)).length

  // Determine what to display
  const displayingCourses = filter === "courses"
  const displayItems = displayingCourses ? nanoCourses || [] : events

  return (
    <Card
      className="overflow-hidden rounded-xl shadow-lg border-0"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <CardHeader className="p-4 pb-2">
        <div className="flex justify-between items-center flex-wrap gap-2">
          <div className="flex items-center gap-2">
            <div className="bg-blue-100 p-2 rounded-lg">
              <Calendar size={18} className="text-blue-600" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold">
                {displayingCourses ? "More Info" : "Upcoming Events"}
              </CardTitle>
              <CardDescription className="text-sm font-medium text-muted">
                {displayItems.length === 0
                  ? displayingCourses
                    ? "No courses available"
                    : "No events match your filters"
                  : `${displayItems.length} ${displayingCourses ? "courses" : `upcoming ${filter !== "all" ? filter : "events"}`}`}
              </CardDescription>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {!displayingCourses && urgentCount > 0 && (
              <Badge variant="destructive" className="flex items-center gap-1">
                <AlertCircle size={12} />
                {urgentCount} urgent
              </Badge>
            )}
          </div>
        </div>

        {/* Filter controls */}
        <div className="flex flex-wrap gap-0 pt-2 border-t">
          <div className="flex flex-col gap-1">
            <div className="flex flex-wrap gap-0 bg-gray-100 rounded-lg p-1">
              {/* Define constants for repeated class names */}
              {(() => {
                const activeClass = "bg-blue-600 text-white shadow-sm"
                const inactiveClass = "text-gray-600 hover:text-gray-900 hover:bg-white/50"
                return name === "Nano courses" ? (
                  <>
                    <button
                      type="button"
                      className={`px-4 py-2 text-sm font-medium rounded-md capitalize transition-colors ${filter === "courses" ? activeClass : inactiveClass
                        }`}
                      onClick={() => setFilter("courses")}
                    >
                      courses {filterCounts.courses > 0 && `(${filterCounts.courses})`}
                    </button>
                    {["all", "quiz", "assignment", "project"].map((type) => (
                      <button
                        key={type}
                        type="button"
                        className={`px-4 py-2 text-sm font-medium rounded-md capitalize transition-colors ${filter === type ? activeClass : inactiveClass
                          }`}
                        onClick={() => setFilter(type)}
                      >
                        {type} {filterCounts[type] > 0 && `(${filterCounts[type]})`}
                      </button>
                    ))}
                  </>
                ) : (
                  ["all", "quiz", "assignment", "project"].map((type) => (
                    <button
                      key={type}
                      type="button"
                      className={`px-4 py-2 text-sm font-medium rounded-md capitalize transition-colors ${filter === type ? activeClass : inactiveClass
                        }`}
                      onClick={() => setFilter(type)}
                    >
                      {type} {filterCounts[type] > 0 && `(${filterCounts[type]})`}
                    </button>
                  ))
                )
              })()}
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-4">
        <div className="relative min-h-[10rem]">
          {displayItems.length > cardsToShow && (
            <div className="hidden sm:block">
              <Button
                onClick={slideLeft}
                disabled={!canSlideLeft}
                className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white/80 backdrop-blur-sm p-0 w-8 h-8 rounded-full shadow-md disabled:opacity-0 transition-all"
                size="icon"
                variant="outline"
              >
                <ChevronLeft size={18} />
              </Button>

              <Button
                onClick={slideRight}
                disabled={!canSlideRight}
                className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white/80 backdrop-blur-sm p-0 w-8 h-8 rounded-full shadow-md disabled:opacity-0 transition-all"
                size="icon"
                variant="outline"
              >
                <ChevronRight size={18} />
              </Button>
            </div>
          )}

          <div className="w-full overflow-hidden">
            {displayItems.length > 0 ? (
              <motion.div
                ref={carouselRef}
                className="flex gap-3"
                animate={{ x: position }}
                transition={{ type: "spring", stiffness: 120, damping: 20 }}
                drag="x"
                dragConstraints={{ right: 0, left: -((itemCount - cardsToShow) * cardWidth) }}
                dragElastic={0.2}
                onDragEnd={(_, info) => {
                  if (info.offset.x > 100 && canSlideLeft) {
                    slideLeft()
                  } else if (info.offset.x < -100 && canSlideRight) {
                    slideRight()
                  }
                }}
              >
                {displayingCourses
                  ? // Render DegreeCourseCard components for courses
                  nanoCourses?.map((course, index) => (
                    <motion.div
                      key={course.id || index}
                      className="flex-shrink-0"
                      style={{ width: cardWidth }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <DegreeCourseCard course={course} onAuditClick={onAuditClick} />
                    </motion.div>
                  ))
                  : // Render EventCard components for events
                  events.map((evt) => (
                    <motion.div
                      key={evt.id}
                      className="flex-shrink-0"
                      style={{ width: cardWidth }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <EventCard {...evt} />
                    </motion.div>
                  ))}
              </motion.div>
            ) : (
              <div className="flex flex-col items-center justify-center h-48 bg-slate-50 rounded-lg border border-dashed border-slate-200">
                <Calendar className="h-8 w-8 text-slate-400" />
                <p className="text-slate-800 font-semibold mb-1">
                  {displayingCourses ? "No courses found" : "No events found"}
                </p>
                <p className="text-slate-500 text-sm max-w-xs">
                  {displayingCourses
                    ? "No courses available at this time"
                    : "No upcoming events at this time"}
                </p>
              </div>
            )}
          </div>

          {/* Mobile swipe indicator */}
          {displayItems.length > cardsToShow && (
            <div className="flex justify-center mt-4 text-xs text-slate-400 sm:hidden">
              <p>Swipe to view more</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

const EventCard = ({ title, date, description, type, deadline, priority, course, isNano }) => {
  console.log("isNano", isNano)

  const getTypeStyles = () => {
    switch (type) {
      case "Quiz":
        return "bg-amber-50 text-amber-700 border-amber-200"
      case "Assignment":
        return "bg-blue-50 text-blue-700 border-blue-200"
      case "Project":
        return "bg-emerald-50 text-emerald-700 border-emerald-200"
      default:
        return "bg-purple-50 text-purple-700 border-purple-200"
    }
  }

  // Priority styling
  const getPriorityStyles = () => {
    switch (priority) {
      case "overdue":
        return "bg-red-100 text-red-800 border-red-200"
      case "urgent":
        return "bg-orange-100 text-orange-800 border-orange-200"
      case "high":
        return "bg-amber-100 text-amber-800 border-amber-200"
      case "medium":
        return "bg-blue-100 text-blue-800 border-blue-200"
      default:
        return "bg-green-100 text-green-800 border-green-200"
    }
  }

  // Format dates
  const formattedDate = format(date, "MMM d, h:mm a")
  const daysUntil = Math.ceil((deadline - new Date()) / (1000 * 60 * 60 * 24))

  const getDueText = () => {
    if (daysUntil < 0) return "Overdue"
    if (daysUntil === 0) return "Due today"
    if (daysUntil === 1) return "Due tomorrow"
    return `Due in ${daysUntil} days`
  }

  return (
    <Card className="h-full overflow-hidden group">
      <div
        className={`h-1 w-full ${priority === "overdue" ? "bg-red-500" : priority === "urgent" ? "bg-orange-500" : "bg-blue-500"}`}
      />
      <div className="p-4">
        <div className="flex justify-between items-start mb-3">
          <div
            className={`text-xs inline-flex items-center px-2 py-1 rounded-full font-medium ${getTypeStyles()}`}
          >
            {type}
          </div>
          <div className={`text-[11px] px-2 rounded-full font-medium ${getPriorityStyles()}`}>
            {getDueText()}
          </div>
        </div>
        <p className="font-semibold text-slate-800 line-clamp-2 mb-2 group-hover:text-blue-700 transition-colors">
          {title}
        </p>
        <div className="flex items-center text-xs text-slate-500 mb-3">
          <Clock size={12} className="mr-1" />
          <span>{formattedDate}</span>
        </div>
        <div className="flex justify-between items-center whitespace-nowrap">
          <p className="text-xs text-slate-600 overflow-hidden text-ellipsis max-w-[180px]">
            {description?.length > 23 ? `${description?.slice(0, 23)}...` : description}
          </p>
          {isNano && course && (
            <p className="text-xs text-gray-500 ml-2 shrink-0">
              Course:
              <span className="text-primary text-xs shrink-0">
                {course?.length > 10 ? `${course?.slice(0, 10)}...` : course}
              </span>
            </p>
          )}
        </div>
      </div>
    </Card>
  )
}

UpCommingEvents.propTypes = {
  data: PropTypes.shape({
    quizes: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.number.isRequired,
        title: PropTypes.string.isRequired,
        due_date: PropTypes.string.isRequired,
        description: PropTypes.string.isRequired,
      })
    ),
    assignments: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.number.isRequired,
        title: PropTypes.string.isRequired,
        due_date: PropTypes.string.isRequired,
        description: PropTypes.string.isRequired,
      })
    ),
    projects: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.number.isRequired,
        title: PropTypes.string.isRequired,
        due_date: PropTypes.string.isRequired,
        description: PropTypes.string.isRequired,
      })
    ),
  }).isRequired,
  name: PropTypes.string,
  nanoCourses: PropTypes.array,
  onAuditClick: PropTypes.func,
}

export default UpCommingEvents
