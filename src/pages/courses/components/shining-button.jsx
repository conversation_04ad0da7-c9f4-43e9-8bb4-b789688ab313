export default function ShiningButton({ children, className, ...props }) {
  return (
    <button
      className={`relative overflow-hidden h-12 px-8 rounded-lg bg-[#3d3a4e] text-white border-none cursor-pointer transition-all duration-500 
      before:absolute before:top-0 before:left-0 before:w-full before:h-full before:rounded-lg before:bg-gradient-to-r 
      before:from-[#965DE9] before:to-[#6358EE] before:scale-x-0 before:origin-left before:transition-transform before:duration-\[0.475s\] 
      hover:before:scale-x-100 ${className}`}
      {...props}
    >
      <span className="relative z-10">{children}</span>
    </button>
  )
}
