import { Percent } from "lucide-react"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"

const OfferBadge = ({ course }) => {
  return (
    <div className="absolute bottom-4 right-14">
      <div className="relative bg-gradient-to-r from-violet-700 to-black text-white px-5 py-3 flex items-center space-x-3 rounded-lg shadow-2xl">
        {/* Icon */}
        <Percent className="h-5 w-5 text-white" />
        <p className="text-md font-bold" />

        {/* Text Content */}
        <div>
          <AlertTitle className="text-sm font-bold text-white">
            <div className="flex items-center gap-1.5">
              <p>{course.offer_title}</p>
            </div>
          </AlertTitle>
          <AlertDescription className="text-xs text-gray-200">
            Enroll now and get a {course.course_discount}% discount
          </AlertDescription>
        </div>

        {/* Right Ribbon Triangle for More Styling */}
        <div className="absolute -right-2 top-1/2 -translate-y-1/2 h-3 w-3 bg-violet-600 rotate-45" />
      </div>
    </div>
  )
}

export default OfferBadge
