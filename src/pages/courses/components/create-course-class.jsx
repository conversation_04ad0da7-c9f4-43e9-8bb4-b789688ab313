import FormComboBox from "@/components/custom/custom-forms/combo-box.form"
import FormDate from "@/components/custom/custom-forms/form-date"
import FormInput from "@/components/custom/custom-forms/form-input"
import FormTextArea from "@/components/custom/custom-forms/form-textarea"
import { Button } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { USER_ROLES } from "@/utils/constants"
import PropTypes from "prop-types"

const CreateCourse = ({
  form,
  control,
  onCreateClassForm,
  handleSubmit,
  setValue,
  isUpdate,
  onClose,
  modulesData,
  selectedValue,
  setSelectedValue,
  comboIsOpen,
  setComboIsOpen,
  userRole,
}) => {
  return (
    <ScrollArea className="h-[calc(100vh-200px)] md:h-[calc(100vh-150px)] sm:h-[calc(100vh-100px)] w-full overflow-auto pr-4 ">
      <Form {...form}>
        <form onSubmit={handleSubmit(onCreateClassForm)} className="space-y-3 grid gap-y-3">
          {["admin", "trainer"]?.includes(userRole) && (
            <>
              <FormInput
                placeholder="Enter Topic"
                label="Topic"
                fieldControlName="topic"
                control={control}
                isRequired
              />

              <FormInput
                placeholder="Enter Duration"
                label="Duration"
                fieldControlName="duration_minutes"
                control={control}
                isRequired
                formDes="eg (30-mins,60-mins,90-1-30mins,120-2hrs)"
              />

              <FormComboBox
                iterateData={modulesData}
                selectedValue={selectedValue}
                setSelectedValue={setSelectedValue}
                comboIsOpen={comboIsOpen}
                setComboIsOpen={setComboIsOpen}
                placeholder="Search Module"
                label="Module"
                fieldControlName="module"
                notFoundMessage="module"
                control={control}
                width="w-full"
                setValue={setValue}
              />

              <FormInput
                dataTestID="test-id"
                dataTestIDError="error"
                fieldControlName="resourse_link"
                control={control}
                label="Meet Link"
                placeholder="Add Link"
                // isRequired
              />

              {/* <FormComboBox
              iterateData={topicsData}
              selectedValue={selectedValue}
              setSelectedValue={setSelectedValue}
              comboIsOpen={comboIsOpen}
              setComboIsOpen={setComboIsOpen}
              placeholder="Search Topics"
              label="Topic"
              fieldControlName="topic"
              notFoundMessage="topic"
              control={control}
              width="w-full"
            />

            <FormComboBox
              iterateData={topicsData}
              selectedValue={selectedValue}
              setSelectedValue={setSelectedValue}
              comboIsOpen={comboIsOpen}
              setComboIsOpen={setComboIsOpen}
              placeholder="Search Trainers"
              label="Trainers"
              fieldControlName="trainers"
              notFoundMessage="trainers"
              control={control}
              width="w-full"
            /> */}

              <FormDate
                dataTestID="test-id"
                dataTestIDError="error"
                fieldControlName="schedule_date"
                control={control}
                label="Schedule date"
                placeholder="Schedule Date"
              />
              <FormTextArea
                dataTestID="test-id"
                dataTestIDError="error"
                fieldControlName="description"
                control={control}
                label="Description"
                placeholder="Enter Description"
                
              />

              {/* <div className="">
              <div>
                <Label className="mr-2">Add Notes Link </Label>{" "}
                <span className="text-muted text-xs font-medium">(optional) </span>
                <p className="text-muted text-xs font-medium"> (or Upload Docs)</p>{" "}
              </div>
              <FormInput
                placeholder="Add Notes Link"
                label=""
                fieldControlName="notes_link"
                control={control}
                isRequired={false}
              />
              <p className="text-slate-400 text-xs my-4 font-semibold mx-auto w-5">(or)</p>
              <FileUploadCard
                setValue={setValue}
                descriptions="Drag & drop any file
or browse files on your computer"
              />
            </div> */}
            </>
          )}

          {userRole === USER_ROLES.STUDENT && (
            <div>
              <Label className="mr-2">Feeback </Label>{" "}
              <FormTextArea
                placeholder="Enter Feedback"
                label=""
                fieldControlName="feedback"
                control={control}
                isRequired={false}
              />
            </div>
          )}

          <div className="pt-12 flex gap-x-3 justify-end">
            <Button onClick={onClose} variant="secondary" type="reset">
              Cancel
            </Button>
            <Button type="submit" variant="primary">
              {isUpdate ? "Update & Save" : "Create"}
            </Button>
          </div>
        </form>
      </Form>
    </ScrollArea>
  )
}

CreateCourse.propTypes = {
  form: PropTypes.objectOf.isRequired,
  control: PropTypes.objectOf.isRequired,
  onCreateClassForm: PropTypes.func.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  setValue: PropTypes.func.isRequired,
  isUpdate: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  modulesData: PropTypes.string,
  topicsData: PropTypes.string,
  selectedValue: PropTypes.string,
  setSelectedValue: PropTypes.func,
  comboIsOpen: PropTypes.bool,
  setComboIsOpen: PropTypes.func,
  userRole: PropTypes.string,
}

export default CreateCourse
