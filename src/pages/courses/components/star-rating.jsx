import { useState } from "react"
import PropTypes from "prop-types"
import { Star } from "lucide-react"

const StarRating = ({ ratingCount, reviews, onRatingChange, ratingFnStatus }) => {
  const [rating, setRating] = useState(ratingCount)

  const handleRating = (newRating) => {
    if (!ratingFnStatus) return
    setRating(newRating)
    if (onRatingChange) {
      onRatingChange(newRating)
    }
  }

  return (
    <div className="flex items-center space-x-1">
      {[...Array(5)].map((_, index) => {
        const starValue = index + 1
        return (
          <Star
            key={index}
            className={`w-6 h-6 cursor-pointer transition-all duration-200 ${
              starValue <= rating
                ? "text-yellow-600 fill-yellow-500"
                : "text-gray-400 stroke-gray-400"
            }`}
            onClick={() => handleRating(starValue)}
          />
        )
      })}

      <span className="text-yellow-500 text-lg font-semibold">({reviews})</span>
    </div>
  )
}

StarRating.propTypes = {
  ratingCount: PropTypes.number.isRequired,
  reviews: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  onRatingChange: PropTypes.func,
  ratingFnStatus: PropTypes.bool.isRequired,
}

export default StarRating
