import {
  Sheet,
  SheetContent,
  SheetDes<PERSON>,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet"

import { CircleAlert } from "lucide-react"
import PropTypes from "prop-types"

const CustomSidebar = ({
  title = "Add Title",
  description = `Write feedback here. Click add feeback when
              you're done.`,
  isOpen,
  onClose,
  content,
}) => {
  return (
    <Sheet className="w-[400px] mr-4 " open={isOpen} onOpenChange={onClose}>
      <SheetContent className="bg-white dark:bg-[#1E1E1E]  rounded-[.5rem] mr-3">
        <SheetHeader className="pb-2 border-b-2 border-b-gray-100">
          <SheetTitle className="text-lg font-bold first-letter:border-b-2 text-nowrap first-letter:border-black first-letter:pb-1">
            {title}
            {description && (
              <SheetDescription className="text-xs mt-2 text-gray-400 font-semibold flex gap-x-2">
                <CircleAlert className="" size={16} /> {description}
              </SheetDescription>
            )}
          </SheetTitle>
        </SheetHeader>

        <div className="mt-8">{content}</div>

        {/* <div className="grid gap-4 py-4">
          <div className="">
            <Label htmlFor="name" className="text-right">
              Name
            </Label>
            <Input id="name" value="" className="col-span-3 input" placeholder="type name" />
          </div>
          <div className="">
            <Label htmlFor="" className="text-right">
              Feedback
            </Label>
            <Textarea
              rows="7"
              placeholder="Type your feedback here."
              id="username"
              value=""
              className="col-span-3 input "
            />
          </div>
        </div> */}
        {/* <SheetFooter>
            <SheetClose asChild>
              <Button className="button-primary" type="submit">
                Add Feeback
              </Button>
            </SheetClose>
          </SheetFooter> */}
      </SheetContent>
    </Sheet>
  )
}

CustomSidebar.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string,
  description: PropTypes.string,
  content: PropTypes.node,
}

export default CustomSidebar
