import PriceRangeFilter from "@/components/range-slider/range-slider"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import PropTypes from "prop-types"

const CourseFilterSidebar = ({
  category,
  courses,
  selectedCategories,
  onCategoryChange,
  setMin,
  setMax,
  setSelectedCurrency,
  name,
}) => {
  const uniquePrices = courses?.map((course) => course.pricing.currentPrice) || []
  console.log("uniquePrices", courses)

  return (
    <Card className="mt-4">
      <CardHeader>
        <CardTitle className="text-md">Filter Courses</CardTitle>
      </CardHeader>
      <CardContent>
        <Accordion type="single" collapsible className="w-full p-0">
          <AccordionItem value="category">
            <AccordionTrigger className="text-sm">Category</AccordionTrigger>
            <AccordionContent>
              <ul>
                {category && category?.length > 0 ? (
                  category?.map((cat) => (
                    <li key={cat.id} className="flex items-center space-x-2 py-2">
                      <Checkbox
                        id={`category-${cat.id}`}
                        className="mr-2"
                        checked={selectedCategories?.includes(cat.id)}
                        onCheckedChange={(checked) => onCategoryChange(cat.id, checked)}
                      />
                      <label
                        htmlFor={`category-${cat.id}`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {cat.name}
                      </label>
                    </li>
                  ))
                ) : (
                  <li className="text-sm text-gray-500">No categories available</li>
                )}
              </ul>
            </AccordionContent>
          </AccordionItem>
          {/** {name === "Live courses" && (
            <AccordionItem value="price">
              <AccordionTrigger className="text-sm">Price</AccordionTrigger>
              <AccordionContent>
                <PriceRangeFilter
                  setSelectedCurrency={setSelectedCurrency}
                  setMax={setMax}
                  setMin={setMin}
                />
              </AccordionContent>
            </AccordionItem>
          )} */}
        </Accordion>
      </CardContent>
    </Card>
  )
}
CourseFilterSidebar.propTypes = {
  category: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      name: PropTypes.string.isRequired,
    })
  ),
  courses: PropTypes.arrayOf(
    PropTypes.shape({
      pricing: PropTypes.shape({
        currentPrice: PropTypes.number.isRequired,
      }).isRequired,
    })
  ),
  selectedCategories: PropTypes.arrayOf(PropTypes.number),
  onCategoryChange: PropTypes.func.isRequired,
}

export default CourseFilterSidebar
