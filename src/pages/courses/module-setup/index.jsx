import { But<PERSON> } from "@/components/ui/button"
import CourseDetails from "@/pages/courses/live-courses/course-content/index"
import { ArrowRight, ChevronLeftIcon } from "lucide-react"
import ct from "@constants/"
import { useLocation, useNavigate } from "react-router-dom"

const ModuleCreation = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const courseData = location.state?.courseData



  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      {/* Header Section */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-3xl flex font-bold text-primary">
          {" "}
          {courseData?.course_type === "SHORT" && (
            <ChevronLeftIcon
              className="cursor-pointer mt-[2px]"
              onClick={() => navigate(-1)}
              size={32}
            />
          )}
          Course Content
        </h2>

        <div className="flex   justify-center gap-12">
          <p className="text-xl font-semibold text-primary  mt-1">
            <span className="font-medium text-2xl text-gray-500">Course Name:</span>{" "}
            {courseData?.title || "Untitled Course"}
          </p>
          <Button
            variant="outline"
            onClick={() => {
              if (courseData?.course_type === "LIVE") {
                navigate("/live-courses")
              } else if (courseData?.course_type === "SHORT") {
                navigate(`/${ct.route.SHORT_COURSES_LIST}`)
              } else if (courseData?.course_type === "NANO") {
                navigate(`/${ct.route.NANO_COURSES}`)
              } else {
                navigate("/live-courses")
              }
            }}
            className="flex items-center me-5 gap-5 mb-4"
          >
            Next
            <ArrowRight size={18} />
          </Button>
        </div>
      </div>

      <hr className="border-[#D9DBDE] my-2" />
      {/* Course Details */}
      <div className="mt-6">
        <CourseDetails />
      </div>
    </div>
  )
}

export default ModuleCreation
