import { Card } from "@/components/ui/card"
import { ArrowR<PERSON>, Bell, Calendar } from "lucide-react"
import PropTypes from "prop-types"

const AnnouncementBanner = ({
  title = "Announcement",
  msg,
  date,
  onClick,
  priority = "default",
}) => {
  const priorityStyles = {
    default: "bg-gradient-to-r from-gray-50 to-gray-100 hover:from-gray-100 hover:to-gray-200",
    urgent: "bg-gradient-to-r from-orange-50 to-red-50 hover:from-orange-100 hover:to-red-100",
    info: "bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100",
  }

  const iconStyles = {
    default: "text-gray-600",
    urgent: "text-orange-600",
    info: "text-blue-600",
  }

  return (
    <Card
      className={`
        max-w-3xl mx-auto my-4 
        ${priorityStyles[priority]}
        transition-all duration-300 ease-in-out
        cursor-pointer
        group
      `}
      onClick={onClick}
    >
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div
              className={`
              p-2 rounded-full 
              ${priority === "default" ? "bg-gray-200/50" : "bg-white/50"}
              group-hover:scale-110 transition-transform duration-300
            `}
            >
              <Bell className={`h-5 w-5 ${iconStyles[priority]}`} />
            </div>
            <div>
              <h3 className={`text-sm font-semibold ${iconStyles[priority]}`}>{title}</h3>
              <p className="text-sm text-gray-600 mt-1 pr-8">{msg}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {date && (
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <Calendar className="h-3 w-3" />
                <span>{date}</span>
              </div>
            )}
            <ArrowRight className="h-4 w-4 text-gray-400 group-hover:translate-x-1 transition-transform duration-300" />
          </div>
        </div>
      </div>
    </Card>
  )
}

AnnouncementBanner.propTypes = {
  title: PropTypes.string,
  msg: PropTypes.string.isRequired,
  date: PropTypes.string,
  onClick: PropTypes.func,
  priority: PropTypes.oneOf(["default", "urgent", "info"]),
}

export default AnnouncementBanner
