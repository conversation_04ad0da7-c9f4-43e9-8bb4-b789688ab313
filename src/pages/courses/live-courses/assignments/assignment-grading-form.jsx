import { Form } from "@/components/ui/form"
import { But<PERSON> } from "@/components/ui/button"
import FormInput from "@/components/custom/custom-forms/form-input"
import FormTextArea from "@/components/custom/custom-forms/form-textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { zodResolver } from "@hookform/resolvers/zod"
import PropTypes from "prop-types"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { useEffect, useState } from "react"
import FileUploadCard from "@/components/custom/upload-file"
import FormSelect from "@/components/custom/custom-forms/form-select"
import { quizStatus } from "@/utils/constants"

// ✅ Schema as a factory function to use total_marks
const assignmentGradingSchema = (totalMarks = Infinity) =>
  z.object({
    marks_obtained: z
      .coerce
      .number()
      .min(0, "Marks must be at least 0")
      .max(totalMarks, `Marks cannot exceed total marks (${totalMarks})`)
      .optional(),
    trainer_feedback: z.string().optional(),
    acceptance_status: z.enum(["ACCEPTED", "PENDING", "REJECTED"]).default("PENDING"),
  })

const AssignmentGradingForm = ({ onSubmit, onClose, initialData = {} }) => {
  const [documentData, setDocumentData] = useState(null)

  const totalMarks = initialData.total_marks || 0

  const form = useForm({
    resolver: zodResolver(assignmentGradingSchema(totalMarks)),
    defaultValues: {
      marks_obtained: initialData.marks_obtained || 0,
      trainer_feedback: initialData.trainer_feedback || "",
      acceptance_status: initialData.acceptance_status || "PENDING",
    },
    mode: "onChange",
  })

  useEffect(() => {
    form.reset({
      marks_obtained: initialData.marks_obtained || 0,
      trainer_feedback: initialData.trainer_feedback || "",
      acceptance_status: initialData.acceptance_status || "PENDING",
    })
  }, [initialData, form])

  const { control, handleSubmit, getValues } = form

  const handleFormSubmit = (data) => {
    const formData = new FormData()

    const requestDataObj = {
      marks_obtained: data.marks_obtained,
      trainer_feedback: data.trainer_feedback,
      acceptance_status: data.acceptance_status || "PENDING",
    }

    formData.append("request_data", JSON.stringify(requestDataObj))

    if (documentData && documentData.length > 0) {
      formData.append("file", documentData[0])
    }

    onSubmit(formData)
  }

  return (
    <ScrollArea className="overflow-y-auto w-full rounded-md pr-2">
      <Form {...form}>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-5 p-2 lg:mb-8">
          <FormSelect
            dataTestID="acceptance-status"
            dataTestIDError="acceptance-status-error"
            fieldControlName="acceptance_status"
            control={control}
            label="Acceptance Status"
            getValues={getValues}
            iterateData={quizStatus}
          />

          <FormInput
            dataTestID="marks-input"
            dataTestIDError="marks-error"
            fieldControlName="marks_obtained"
            control={control}
            label={`Marks Obtained (Max: ${totalMarks})`}
            placeholder="Enter marks"
            isTypeNumer
          />

          <FormTextArea
            dataTestID="feedback-input"
            dataTestIDError="feedback-error"
            fieldControlName="trainer_feedback"
            control={control}
            label="Feedback"
            placeholder="Enter feedback for the student"
          />

          <div className="mt-4">
            <h3 className="text-sm font-medium mb-2">Attach Feedback File (Optional)</h3>
            <FileUploadCard
              onChange={(files) => {
                setDocumentData(files)
              }}
              value={documentData}
              fileType="document"
            />
          </div>

          <div className="pt-4 flex gap-x-3 justify-end">
            <Button onClick={onClose} variant="secondary" type="reset">
              Cancel
            </Button>
            <Button type="submit" variant="primary">
              Submit Grading
            </Button>
          </div>
        </form>
      </Form>
    </ScrollArea>
  )
}

AssignmentGradingForm.propTypes = {
  onSubmit: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired,
  initialData: PropTypes.shape({
    marks_obtained: PropTypes.number,
    trainer_feedback: PropTypes.string,
    acceptance_status: PropTypes.string,
    total_marks: PropTypes.number, // ✅ added for validation
  }),
}

export default AssignmentGradingForm
