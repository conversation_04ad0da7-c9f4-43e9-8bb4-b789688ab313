import AppointmentScheduler from "@/components/custom/custom-forms/date-time-picker"
import FormInput from "@/components/custom/custom-forms/form-input"
import FormSelect from "@/components/custom/custom-forms/form-select"
import FormTextArea from "@/components/custom/custom-forms/form-textarea"
import FileUploadCard from "@/components/custom/upload-file"
import { Button } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { USER_ROLES } from "@/utils/constants"
import PropTypes from "prop-types"

const CreateAssignment = ({
  getValues,
  form,
  onClose,
  control,
  handleSubmit,
  onCreateClassForm,
  setLinkData,
  linkData,
  setDocumentData,
  handleQuizSubmisson,
  documentData,
  isUpdate,
  userRole,
  setFileTypes,
}) => {
  // Toggle function to switch between LINK and FILE
  const handleFileTypeChange = (type) => {
    setFileTypes(type)
    // Clear the other submission type when switching
    if (type === "LINK") {
      setDocumentData(null)
    } else if (type === "FILE") {
      setLinkData("")
    }
  }

  const publishStatus = [
    { value: "PUBLISHED", label: "Published" },
    { value: "DRAFT", label: "Draft" },
    { value: "ARCHIVED", label: "Archived" },
    { value: "UNLISTED", label: "Unlisted" },
  ]

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(onCreateClassForm)}>
        <div className="flex gap-y-4 flex-col justify-end">
          {userRole === USER_ROLES.STUDENT && (
            <div className="space-y-2">
              <div>
                <Label className="text-sm font-medium">
                  Add Assignment Link <span className="text-muted text-xs">(or Upload Docs)</span>
                </Label>
                <Input
                  placeholder="Add link"
                  value={linkData || ""}
                  onChange={(e) => {
                    const { value } = e.target
                    setLinkData(value)
                    // Only change file type if user actually enters something
                    if (value && value.trim()) {
                      handleFileTypeChange("LINK")
                    }
                  }}
                  className="w-full"
                />

                <p className="text-slate-400 text-xs my-2 font-semibold text-center">(or)</p>

                <FileUploadCard
                  onChange={(files) => {
                    console.log("Files received from FileUploadCard:", files) // <-- Add this log
                    setDocumentData(files)
                    if (files && files.length > 0) {
                      handleFileTypeChange("FILE")
                    }
                  }}
                  value={documentData}
                  fileType="document"
                />

                {/* Debug info - remove in production */}
                <div className="text-xs text-gray-500 mt-2">
                  Debug: {documentData?.length || 0} file(s) selected, Link: {linkData?.length || 0}{" "}
                  chars
                </div>
              </div>

              <div className="pt-5 flex gap-x-3 justify-end">
                <Button type="button" variant="secondary" onClick={onClose}>
                  Cancel
                </Button>
                <Button type="button" variant="primary" onClick={handleQuizSubmisson}>
                  Submit
                </Button>
              </div>
            </div>
          )}

          {[USER_ROLES.TRAINER, USER_ROLES.ADMIN]?.includes(userRole) && (
            <>
              <FormInput
                placeholder="Enter title"
                label="Title"
                fieldControlName="title"
                isRequired
                control={control}
              />
              {/* <FormDate
                dataTestID="test-id"
                dataTestIDError="error"
                fieldControlName="due_date"
                control={control}
                label="Deadline"
                placeholder="Pick a date"
                futureDateDisabled={null}
              /> */}

              <AppointmentScheduler
                dataTestID="test-id"
                dataTestIDError="error"
                fieldControlName="due_date"
                control={control}
                label="Deadline"
                placeholder="Pick a date"
                isRequired
              />
              <FormInput
                placeholder="Total Marks"
                label="Total Marks"
                fieldControlName="total_marks"
                isRequired
                control={control}
                isTypeNumer
              />
              <FormTextArea
                placeholder="Description"
                label="Description"
                fieldControlName="description"
                isRequired
                control={control}
              />

              {isUpdate && (
                <div>
                  <FormSelect
                    dataTestID="test-id"
                    dataTestIDError="error"
                    fieldControlName="publish_status"
                    control={control}
                    label="Publish Status"
                    getValues={getValues}
                    iterateData={publishStatus}
                    placeholder="Select status"
                  />
                </div>
              )}
              <div className="pt-5 flex gap-x-3 justify-end">
                <Button onClick={onClose} variant="secondary" type="reset">
                  Cancel
                </Button>
                <Button type="submit" variant="primary">
                  {isUpdate ? "Update Assignment" : "Create Assignment"}
                </Button>
              </div>
            </>
          )}
        </div>
      </form>
    </Form>
  )
}

CreateAssignment.propTypes = {
  form: PropTypes.objectOf.isRequired,
  onClose: PropTypes.func.isRequired,
  setValue: PropTypes.func,
  control: PropTypes.objectOf.isRequired,
  handleCreateClassForm: PropTypes.func,
  onCreateClassForm: PropTypes.func,
  handleSubmit: PropTypes.func.isRequired,
  userRole: PropTypes.string.isRequired,
  userType: PropTypes.string,
  setLinkData: PropTypes.func.isRequired,
  setDocumentData: PropTypes.func.isRequired,
  handleClearProjectSubmit: PropTypes.func,
  linkData: PropTypes.string,
  documentData: PropTypes.arrayOf.isRequired,
  editData: PropTypes.objectOf,
  isUpdate: PropTypes.bool,
  fileTypes: PropTypes.string,
  setFileTypes: PropTypes.func.isRequired,
  studentFeedBack: PropTypes.string,
  setStudentFeedBack: PropTypes.func.isRequired,
  handleQuizSubmisson: PropTypes.func.isRequired,
}

export default CreateAssignment
