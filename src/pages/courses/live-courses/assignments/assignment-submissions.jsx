import DataTable from "@/components/custom/cutsom-table"
import {
  <PERSON><PERSON>ell,
  Date<PERSON><PERSON>,
  LinkCell,
  StatusUpdationCell,
} from "@/components/custom/cutsom-table/table-cells"
import { Button } from "@/components/ui/button"
import useTableConfig from "@/hooks/use-table.hooks"
import { useGetAssignmentSubmissionView } from "@/services/query/assignment-query"
import ct from "@constants/"
import { flexRender } from "@tanstack/react-table"
import { ChevronLeft } from "lucide-react"
import PropTypes from "prop-types"
import { useEffect, useState } from "react"
import { useSelector } from "react-redux"
import { useLocation, useNavigate } from "react-router-dom"
import viewSubmission from "./utils/assignment-coloumns"

const AssignmentSubmission = ({ handleStatusUpdate, handleEdit, setPagination, pagination }) => {
  const location = useLocation()
  const navigate = useNavigate()
  const [submissionData, setSubmissionData] = useState([])

  // Get course details from Redux store
  const course = useSelector((st) => st[ct.store.COURSES]?.activeCourseDetails)

  // Extract parameters from location state
  const assignmentId = location.state?.assignmentData
  const moduleId = location.state?.module_id
  const courseId = location.state?.course_id || course?.id

  // Fetch assignment submission data
  const { data: assignmentData, isLoading } = useGetAssignmentSubmissionView({
    course_id: courseId,
    module_id: moduleId,
    assignment_id: assignmentId,
  })

  // Update state when data is received
  useEffect(() => {
    if (assignmentData?.data) {
      setSubmissionData(assignmentData.data)
    }
  }, [assignmentData])

  const renderCellContent = (cell, row) => {
    const {
      id,
      submission_time,
      submission,
      submission_status,
      submission_type,
      trainer_feedback,
      marks_obtained,
      student_name,
      acceptance_status,
    } = row?.original || {}

    switch (cell.column.id) {
      case "id":
        return <p className="text-sm font-medium">{id || "-"}</p>
      case "name":
        return <p className="text-sm font-medium">{student_name || "-"}</p>
      case "submissionTime":
        return <DateCell value={submission_time || "-"} />

      case "submission":
        return submission_type === "LINK" ? (
          <LinkCell value={submission} target="_blank" />
        ) : (
          <Button variant="outline" size="sm" onClick={() => window.open(submission, "_blank")}>
            Download Submission
          </Button>
        )

      case "obtainedMarks":
        return <p>{marks_obtained || "Not graded"}</p>

      case "status":
        return <StatusUpdationCell value={submission_status || "PENDING"} key={`status-${id}`} />

      case "remarks":
        return <p>{trainer_feedback || "No feedback yet"}</p>

      case "acceptanceStatus":
        return (
          <StatusUpdationCell value={acceptance_status || "PENDING"} key={`acceptance-${id}`} />
        )

      case "actions":
        return (
          <ActionCell
            label2="Grade Submission"
            row={row}
            isEdit
            onView={handleStatusUpdate}
            onEdit={handleEdit}
          />
        )

      default:
        return flexRender(cell.column.columnDef.cell, cell.getContext())
    }
  }

  const { table, found, pageCount } = useTableConfig(
    submissionData?.data || [],
    viewSubmission,
    submissionData?.metadata?.total_records || 0,
    setPagination,
    pagination
  )

  return (
    <div className="w-full">
      <div className="flex items-center text-primary mt-3 mb-4">
        <ChevronLeft
          size={30}
          className="cursor-pointer"
          // onClick={() => navigate(ct.route.COURSE_OVERVIEW, { replace: true })}
          onClick={() => navigate(`/live-courses/${course?.id}${ct.route.COURSE_OVERVIEW}`)}
        />
        <span className="ml-2 text-2xl font-medium">Assignment Submissions</span>
      </div>

      {assignmentData?.assignment_details && (
        <div className="mb-4 p-4 bg-gray-50 rounded-md">
          <h3 className="font-medium text-lg">{assignmentData.assignment_details.title}</h3>
          <div className="grid grid-cols-3 gap-4 mt-2">
            <div>
              <p className="text-sm text-gray-500">Due Date</p>
              <p>
                <DateCell value={assignmentData.assignment_details.due_date} />
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Total Marks</p>
              <p>{assignmentData.assignment_details.total_marks}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Submissions</p>
              <p>{submissionData?.length || 0}</p>
            </div>
          </div>
        </div>
      )}

      <DataTable
        renderCellContent={renderCellContent}
        columns={viewSubmission}
        table={table}
        found={found}
        pageCount={pageCount}
        pageName="Assignment Submissions"
        pagination={pagination}
        notFoundPlaceholder="No submissions found for this assignment"
        isLoading={isLoading}
      />
    </div>
  )
}
AssignmentSubmission.propTypes = {
  handleStatusUpdate: PropTypes.func.isRequired,
  handleEdit: PropTypes.func.isRequired,
  setPagination: PropTypes.func.isRequired,
  pagination: PropTypes.shape({
    pageIndex: PropTypes.number.isRequired,
    pageSize: PropTypes.number.isRequired,
  }).isRequired,
}
export default AssignmentSubmission
