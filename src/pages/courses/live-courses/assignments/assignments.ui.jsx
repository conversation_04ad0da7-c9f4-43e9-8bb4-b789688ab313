import CustomComboBox from "@/components/custom/custom-combo-box/custom-combo-box"
import CustomSearchbar from "@/components/custom/custom-search"
import DataTable from "@/components/custom/cutsom-table"
import {
  ActionCell,
  DateCell,
  LinkCell,
  StatusUpdationCell,
} from "@/components/custom/cutsom-table/table-cells"
import { But<PERSON> } from "@/components/ui/button"
import useTableConfig from "@/hooks/use-table.hooks"
import { USER_ROLES } from "@/utils/constants"
import { flexRender } from "@tanstack/react-table"
import { FilePlus2 } from "lucide-react"
import PropTypes from "prop-types"
import { useEffect } from "react"
import { useLocation } from "react-router-dom"
import { getColumnsByUserType } from "./utils/assignment-coloumns"

const AssignmentUI = ({
  handleCreateClass,
  userType,
  handleDelete,
  reset,
  handleOpen,
  setAssignmentId,
  handleUpdate,
  assignment,
  pagination,
  setPagination,
  isLoading,
  moduleName,
  setSelectedModuleId,
  onViewSubmission,
  selectedModule,
  setSelectedModule,
  isPaidUser,
  setSearchingValue,
  searchingValue,
  setSelectedAssignmentEndDate,
  submissionClear,
}) => {
  useEffect(() => {
    if (selectedModule) {
      setSelectedModuleId(selectedModule?.id)
    }
  }, [selectedModule])
  const moduleOptions = []
    .concat(moduleName || [])
    .map((m) => ({
      id: m?.id,
      name: m?.module_name,
    }))
    .filter((m) => m.id && m.name)

  const handleEdit = (row) => {
    const { title, description, due_date, total_marks, publish_status, id } = row?.original ?? {}

    let formattedDueDate = due_date
    if (due_date) {
      // Parse the original date string
      const date = new Date(due_date)
      // Format to ISO string (will give UTC time)
      formattedDueDate = date.toISOString()
    }

    setAssignmentId({ ...assignment, ass_id: id })
    handleUpdate()
    reset({
      due_date: formattedDueDate,
      title: title?.toString(),
      total_marks: total_marks?.toString(),
      description: description?.toString(),
      publish_status: publish_status?.toString(),
    })
    handleOpen()
  }

  const handleSubmitAssignment = (row) => {
    submissionClear()
    const { id, module_id, due_date } = row?.original ?? {}
    setSelectedAssignmentEndDate(due_date)
    // Set the assignment ID and module ID for submission
    setAssignmentId({
      ass_id: id,
      module_id: module_id || selectedModule?.id,
      course_id: assignment?.course_id || 2, // Using default from your code
    })

    // Reset the form and pre-select the module
    reset({
      module_id: module_id || selectedModule?.id,
    })

    // Open the sidebar for assignment submission
    handleOpen()
  }
  const renderCellContent = (cell, row) => {
    const {
      id,
      title,
      due_date,
      remarks,
      total_marks,

      marks_obtained,
      modified_at,
      trainer,
      feedback,
      publish_status,
      submission_count,
      submission_status,
    } = row?.original || {}
    switch (cell.column.id) {
      case "date":
        return <DateCell value={modified_at || "-"} />
      case "assignment_No":
        return <p className="text-sm font-medium">{id || "-"}</p>
      case "topic":
        return <p>{title || "-"}</p>
      case "remarks":
        return <p>{remarks || "-"}</p>
      case "link":
        return <LinkCell value="Value" isPaidUser={isPaidUser} />
      case "trainer":
        return <p>{trainer || "-"}</p>
      case "deadline":
        return <DateCell value={due_date || "-"} />
      case "status":
        return <StatusUpdationCell value={publish_status} key={id} />
      case "submissionCount":
        return (
          <Button
            variant="secondary"
            size="xs"
            onClick={() => onViewSubmission(row)}
            className={isPaidUser?.class}
            disabled={isPaidUser?.paidUser}
          >
            View{" "}
            <span className="h-4 w-4 text-xs font-semibold bg-[hsl(var(--primary)/.1)] rounded-full ">
              {submission_count ?? 0}
            </span>
          </Button>
        )
      case "submission":
        return (
          <Button
            className={isPaidUser?.class}
            disabled={isPaidUser?.paidUser}
            variant="primary"
            onClick={() => handleSubmitAssignment(row)}
          >
            {submission_status === "SUBMITTED" ? "Resubmit" : "Submit"}
          </Button>
        )
      case "feedback":
        return <p>{feedback || "-"}</p>
      case "obtained_numbers":
        return <p>{marks_obtained ? `${marks_obtained}/${total_marks}` : "-"}</p>
      case "actions":
        return (
          <div>
            {isPaidUser?.paidUser ? (
              "-"
            ) : (
              <ActionCell
                label2="Edit"
                label3="Delete Quiz"
                row={row}
                isEdit
                isDelete
                onView={() => handleEdit(row)}
                onDelete={handleDelete}
                onEdit={handleEdit}
                isPaidUser={isPaidUser}
              />
            )}
          </div>
        )
      default:
        return flexRender(cell.column.columnDef.cell, cell.getContext())
    }
  }
  const { table, found, pageCount } = useTableConfig(
    assignment?.data,
    getColumnsByUserType(userType),
    assignment?.metadata?.total_records,
    setPagination,
    pagination
  )
  const location = useLocation()
  const dynamicHeight = assignment?.data?.length > 6 ? "h-[54vh]" : undefined
  return (
    <div className="w-full">
      <div className="flex items-center justify-between">
        <div>
          {!location.pathname?.includes("/assignment-view") ? (
            <div>
              <CustomSearchbar
                inputSize="w-[20rem]"
                placeholder="Search by title..."
                searchedValue={searchingValue}
                setSearchedValue={(e) => setSearchingValue(e?.target.value)}
              />
            </div>
          ) : null}
        </div>
        <div className="flex gap-5 justify-end">
          <p className="text-lg font-medium text-gray-700 bg-gray-100 px-3 py-2 rounded-md shadow-sm">
            Module Name:{" "}
            <span className="text-primary font-semibold">{selectedModule?.name || "-"}</span>
          </p>
          <CustomComboBox
            iterateData={moduleOptions}
            selectedValue={selectedModule?.name}
            setSelectedValue={(module) => setSelectedModule(module)}
            notFoundMessage="modules"
            placeholder="Select module..."
            width="min-w-[285px]"
          />
          {(userType === USER_ROLES.TRAINER || userType === USER_ROLES.ADMIN) && (
            <div>
              <Button
                className={isPaidUser?.class}
                disabled={isPaidUser?.paidUser}
                variant="primary"
                onClick={handleCreateClass}
              >
                <FilePlus2 size={16} /> Create
              </Button>
            </div>
          )}
          {/* {userType === USER_ROLES.STUDENT && (
            <Button
              className={isPaidUser?.class}
              disabled={isPaidUser?.paidUser}
              variant="primary"
              onClick={handleCreateClass}
            >
              <FilePlus2 size={17} /> Submit
            </Button>
          )} */}
        </div>
      </div>

      <DataTable
        renderCellContent={renderCellContent}
        columns={getColumnsByUserType(userType)}
        table={table}
        found={found}
        pageCount={pageCount}
        height={dynamicHeight}
        pageName="Assignment"
        pagination={pagination}
        notFoundPlaceholder="No Assignment Found"
        isLoading={isLoading}
      />
    </div>
  )
}

AssignmentUI.propTypes = {
  handleCreateClass: PropTypes.func.isRequired,
  userType: PropTypes.oneOf(["trainer", "admin", "student"]).isRequired,
  handleDelete: PropTypes.func.isRequired,
  reset: PropTypes.func.isRequired,
  handleOpen: PropTypes.func.isRequired,
  setAssignmentId: PropTypes.func.isRequired,
  handleUpdate: PropTypes.func.isRequired,
  assignment: PropTypes.shape({
    course_id: PropTypes.number.isRequired,
    data: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.number.isRequired,
        title: PropTypes.string.isRequired,
        description: PropTypes.string.isRequired,
        due_date: PropTypes.string.isRequired,
        total_marks: PropTypes.number.isRequired,
        publish_status: PropTypes.string.isRequired,
      })
    ),
    metadata: {
      total_records: PropTypes.number,
    },
  }).isRequired,
  isLoading: PropTypes.bool,
  moduleName: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      module_name: PropTypes.string.isRequired,
    })
  ),
  pagination: PropTypes.string,
  setPagination: PropTypes.func.isRequired,
  setSelectedModuleId: PropTypes.func.isRequired,
  onViewSubmission: PropTypes.func.isRequired,
  selectedModule: PropTypes.shape({
    id: PropTypes.number.isRequired,
    name: PropTypes.string.isRequired,
  }),
  setSelectedModule: PropTypes.func.isRequired,
  isPaidUser: PropTypes.objectOf({
    class: PropTypes.string,
  }),
}

export default AssignmentUI
