import { DeleteDialog } from "@/components/custom/dialogs/delete.dialog"
import { failureToast, successToast, warningToast } from "@/components/custom/toasts/tosters"
import {
  useDeleteDialogHooks,
  useFilterHooks,
  useIsUpdateHook,
  useOpenCloseHooks,
  usePaginationHooks,
  usePublishStatus,
  useSelectedIds,
} from "@/hooks/common.hooks"
import { useDebounce } from "@/hooks/useDebounce"
import { useGetModuleResource } from "@/services/query/live-course.query"
import { USER_ROLES } from "@/utils/constants"
import { getOffset, isStudent } from "@/utils/helper"
import ct from "@constants/"
import { zodResolver } from "@hookform/resolvers/zod"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { useSelector } from "react-redux"
import { useLocation, useNavigate } from "react-router-dom"

import moment from "moment"
import {
  useCreateAssignment,
  useDeletAssignment,
  useFetchAssignments,
  useSubmitAssignment,
  useUpdateAssignment,
  useUpdateAssignmentSubmissionStatus,
} from "../../../../services/query/assignment-query"
import CustomSidebar from "../../components/custom-sidebar"
import AssignmentGradingForm from "./assignment-grading-form"
import AssignmentSubmission from "./assignment-submissions"
import AssignmentUI from "./assignments.ui"
import CreateAssignment from "./create-assignment"
import { AssignmentSchema } from "./utils/aggignment-validation"

function Assignments() {
  const loginData = useSelector((st) => st[ct.store.USER_STORE])
  const userRole = useSelector((st) => st[ct.store.USER_STORE]?.userRole)
  const userType = loginData?.userRole
  const [documentData, setDocumentData] = useState(null)
  const [linkData, setLinkData] = useState("")
  const [editData, setEditData] = useState(null)
  const [moduleName, setModuleName] = useState("")

  const location = useLocation()
  const navigate = useNavigate()
  const [assViewId, setAssViewId] = useState(null)
  const [searchingValue, setSearchingValue] = useState("")
  const { sortBy, sortByField } = useFilterHooks()
  const { limit, offSet } = usePaginationHooks()
  const debouncedSearchQuery = useDebounce(searchingValue, 500)
  const [assignmentId, setAssignmentId] = useState({
    module_id: location?.state?.module_id,
    course_id: location?.state?.course_id,
    ass_id: 2,
  })

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })

  const course = useSelector((st) => st[ct.store.COURSES]?.activeCourseDetails)
  const courseDet = useSelector((st) => st[ct.store.COURSES])
  const { mutate: submitAssignment } = useSubmitAssignment()
  const { data: moduleResourceData, isLoading: moduleLoading } = useGetModuleResource(course?.id)
  const { isDelete, handleDelete, handleCancel } = useDeleteDialogHooks(false)
  const { isUpdate, handleResetUpdate, handleUpdate } = useIsUpdateHook()
  const { mutate: createAssignment } = useCreateAssignment()
  const { mutate: EditAssignment } = useUpdateAssignment()
  const { mutate: deleteAssignment } = useDeletAssignment()
  const { selectedIDs, setSelectedIDs } = useSelectedIds()
  const { mutate: updateAssignmentSubmissionStatus, refetch } =
    useUpdateAssignmentSubmissionStatus()
  const [selectedModule, setSelectedModule] = useState("")
  const [isGradingSidebarOpen, setIsGradingSidebarOpen] = useState(false)
  const [selectedSubmission, setSelectedSubmission] = useState(null)
  const [setSubmissionStatusDialogOpen] = useState(false)
  const { handleSetPublishStatus } = usePublishStatus()
  const [fileTypes, setFileTypes] = useState("FILE")
  const [selectedAssignmentEndDate, setSelectedAssignmentEndDate] = useState(null)

  const currentDate = moment().startOf("day") // Today at 00:00:00 (local time)
  const deadLine = moment(selectedAssignmentEndDate).startOf("day") // Auto-detects ISO

  const { data: assignment, isLoading } = useFetchAssignments({
    data: { limit, offSet, sort_by: sortBy, sort_by_field: sortByField },
    module_id: selectedModule?.id,
    course_id: course?.id,
    filters: {
      limit,
      offset: getOffset(pagination.pageIndex, pagination.pageSize),
      sort_by: sortBy,
      sort_by_field: sortByField,
      search_query: debouncedSearchQuery?.length > 0 ? debouncedSearchQuery : null,
    },
  })

  useEffect(() => {
    if (moduleResourceData?.get_module_resource?.modules) {
      setModuleName(moduleResourceData?.get_module_resource?.modules)
    }
  }, [moduleResourceData, moduleLoading])

  const form = useForm({
    resolver: zodResolver(AssignmentSchema),
    defaultValues: null,
    mode: "onChange",
  })

  const { handleSubmit, control, setValue, reset, getValues } = form
  const handleSelectedModuleId = (id) => {
    setAssignmentId({ ...assignmentId, module_id: id })
  }

  const { open, setOpen, handleClose, handleOpen } = useOpenCloseHooks()

  const handleCancelForm = () => {
    reset({})
    setEditData(null)
    handleClose()
  }

  const handleCreateClass = () => {
    handleResetUpdate()
    reset({
      title: "",
      due_date: "",
      total_marks: "",
      description: "",
      publish_status: "",
    })
    setOpen(true)
  }

  const handleCreateClassForm = async (data) => {
    const { ass_id } = assignmentId
    if (isUpdate) {
      EditAssignment(
        {
          course_id: course?.id,
          modules_id: selectedModule?.id,
          assignment_id: ass_id,
          publish_status: data?.publish_status,
          data,
        },
        {
          onSuccess: () => {
            handleCancelForm()

            successToast("Successfully Edited!", "Assignment was successfully Edited!")
          },
          onError: () => {
            failureToast("Failed to update Assignment")
          },
        }
      )
      return
    }

    createAssignment(
      { course_id: course?.id, modules_id: assignmentId?.module_id, data },
      {
        onSuccess: () => {
          setTimeout(() => {
            successToast("Success!", "Assignment has been created successfully.")
            reset(null)
            setOpen(false)
          }, 500)
        },
        onError: () => {
          failureToast("Error!", "Something went wrong. Please try again.")
        },
      }
    )
  }

  const handleConfirmDelete = () => {
    deleteAssignment(
      {
        course_id: course?.id,
        modules_id: selectedModule?.id,
        assignment_id: assignmentId?.ass_id,
      },
      {
        onSuccess: () => {
          handleCancel()
          successToast("Assignment deleted successfully", "The Assignment has been removed.")
        },
        onError: () => {
          failureToast("Deletion Error", "Unable to delete the Assignment. Please try again later.")
        },
      }
    )
  }

  const handleClearProjectSubmit = () => {
    reset(null)
    setDocumentData(null)
    setLinkData("")
    handleClose()
  }

  const handleQuizSubmisson = () => {
    if (!deadLine.isValid()) {
      failureToast("Error", "Invalid deadline date!")
      return
    }

    if (currentDate.isAfter(deadLine)) {
      warningToast("Submission deadline has passed!")
      return
    }
    console.log(selectedAssignmentEndDate, "currentDate, deadLine")

    // Determine submission type based on what data is provided
    const submissionType = fileTypes === "FILE" ? "FILE" : "LINK"

    // Validate submission data
    if (submissionType === "LINK" && !linkData.trim()) {
      failureToast("Error", "Please provide a link to your submission")
      return
    }

    // Create FormData for API request
    const formData = new FormData()

    // Create submission object
    const submissionObj = {
      submission: submissionType === "LINK" ? linkData : null,
      submission_type: submissionType,
    }

    // Add submission data to FormData
    formData.append("submission", JSON.stringify(submissionObj))

    // If file submission, add the file to FormData
    if (submissionType === "FILE" && documentData && documentData.length > 0) {
      formData.append("file", documentData[0])
    }

    // Call submit assignment mutation
    submitAssignment(
      {
        course_id: course?.id,
        module_id: assignmentId.module_id,
        assignment_id: assignmentId.ass_id,
        data: formData,
      },
      {
        onSuccess: () => {
          successToast("Success", "Assignment submitted successfully")
          handleClearProjectSubmit()
        },
        onError: (error) => {
          const errorMsg = error?.response?.data?.message || "Failed to submit assignment"
          failureToast("Error", errorMsg)
        },
      }
    )
  }

  const handleDeleteQuiz = (data) => {
    setAssignmentId({ ...assignmentId, ass_id: data?.original?.id })
    handleDelete()
  }

  const AssignmentProps = {
    form,
    control,
    onClose: handleCancelForm,
    handleSubmit,
    onCreateClassForm: handleCreateClassForm,
    setValue,
    userType,
    setDocumentData,
    setLinkData,
    handleQuizSubmisson,
    handleClearProjectSubmit,
    linkData,
    documentData,
    editData,
    isUpdate,
    moduleName,
    userRole,
    fileTypes,
    setFileTypes,
    getValues,
  }

  const handleOpenGradingForm = (submission) => {
    setAssViewId(submission?.id)
    setSelectedSubmission(submission.original)
    setIsGradingSidebarOpen(true)
  }

  const handleGradingSubmit = (formData) => {
    updateAssignmentSubmissionStatus(
      {
        course_id: course?.id,
        module_id: assignmentId?.module_id,
        assignment_id: assViewId,
        data: formData,
      },
      {
        onSuccess: () => {
          setIsGradingSidebarOpen(false)
          successToast("Assignment Graded", "The assignment has been successfully graded!")
          refetch()
        },
      }
    )
  }

  const handleStatusUpdate = (data) => {
    setAssViewId(data?.id)
    const { id, acceptance_status } = data?.original || {}
    handleSetPublishStatus(acceptance_status || "PENDING")
    setSelectedIDs({
      ...selectedIDs,
      submissionId: id,
    })
    setSubmissionStatusDialogOpen(true)
  }

  const handleSubmission = (row) => {
    setAssignmentId({ ...assignmentId, ass_id: row?.original?.id })
    if (userRole === USER_ROLES.ADMIN || userRole === USER_ROLES.TRAINER) {
      navigate(`${ct.route.COURSE_OVERVIEW}/${ct.route.ASSIGNMENT_VIEW}/${row?.original?.id}`, {
        state: {
          assignmentData: row?.original?.id,
          module_id: assignmentId.module_id,
          course_id: assignmentId.course_id,
        },
        replace: true,
      })
    } else {
      handleOpen()
    }
  }

  const submissionClear = () => {
    setLinkData("")
    setDocumentData(null)
  }

  return (
    <div>
      <CustomSidebar
        isOpen={open}
        onClose={handleClose}
        description=""
        title={userType === USER_ROLES.STUDENT ? "Submit Assignment" : "Create Assignment"}
        content={<CreateAssignment {...AssignmentProps} />}
      />

      <DeleteDialog
        onOpen={isDelete}
        onDelete={handleConfirmDelete}
        onCancel={handleCancel}
        title="Delete Assignment"
        content="Are you sure want to delete this Assignment?"
      />
      {/* Conditional Rendering Based on `state` */}
      {location.pathname?.includes("assignment-view") ? (
        <AssignmentSubmission
          handleStatusUpdate={handleStatusUpdate}
          handleEdit={handleOpenGradingForm}
          pagination={pagination}
          setPagination={setPagination}
        />
      ) : (
        <AssignmentUI
          setSelectedAssignmentEndDate={setSelectedAssignmentEndDate}
          handleCreateClass={handleCreateClass}
          userType={userType}
          onViewSubmission={handleSubmission}
          handleDelete={handleDeleteQuiz}
          reset={reset}
          handleOpen={handleOpen}
          setAssignmentId={setAssignmentId}
          handleUpdate={handleUpdate}
          assignment={assignment?.data}
          pagination={pagination}
          setPagination={setPagination}
          isLoading={isLoading}
          moduleName={moduleName}
          setSelectedModuleId={handleSelectedModuleId}
          selectedModule={selectedModule}
          setSelectedModule={setSelectedModule}
          isPaidUser={isStudent(courseDet?.activeCourse, userRole)}
          setSearchingValue={setSearchingValue}
          searchingValue={searchingValue}
          submissionClear={submissionClear}
        />
      )}

      {/* Grading Sidebar */}
      <CustomSidebar
        title="Grade Assignment Submission"
        description="Provide marks and feedback for this submission"
        isOpen={isGradingSidebarOpen}
        onClose={() => setIsGradingSidebarOpen(false)}
        content={
          <AssignmentGradingForm
            onSubmit={handleGradingSubmit}
            onClose={() => setIsGradingSidebarOpen(false)}
            initialData={selectedSubmission || {}}
          />
        }
      />
    </div>
  )
}

export default Assignments
