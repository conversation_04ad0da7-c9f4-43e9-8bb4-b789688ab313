import { USER_ROLES } from "@/utils/constants"

export const trainer = [
  {
    id: "title",
    accessorKey: "title",
    header: "Title",
  },
  {
    id: "link",
    accessorKey: "link",
    header: "Link",
  },

  {
    id: "submissionCount",
    accessorKey: "submissionCount",
    header: "Submission",
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Published Status",
  },

  {
    accessorKey: "date",
    // eslint-disable-next-line sonarjs/no-duplicate-string
    header: "Updated At",
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Status",
  },

  {
    id: "deadline",
    accessorKey: "deadline",
    header: "Deadline",
  },
  {
    id: "actions",
    accessorKey: "actions",
    header: "Actions",
    type: "assignment",
  },
]

export const vendor = [
  {
    id: "title",
    accessorKey: "title",
    header: "Title",
  },

  {
    id: "obtained_numbers",
    accessorKey: "obtained_numbers",
    // eslint-disable-next-line sonarjs/no-duplicate-string
    header: "Obtained Marks",
  },
  {
    id: "remarks",
    accessorKey: "remarks",
    header: "Remarks",
  },
  {
    accessorKey: "date",
    header: "Updated At",
  },
  {
    id: "deadline",
    accessorKey: "deadline",
    header: "Deadline",
  },
]

export const student = [
  {
    id: "title",
    accessorKey: "title",
    header: "Title",
  },

  {
    id: "obtained_numbers",
    accessorKey: "obtained_numbers",
    header: "Obtained Marks",
  },
  {
    id: "remarks",
    accessorKey: "remarks",
    header: "Remarks",
  },
  {
    accessorKey: "date",
    header: "Updated At",
  },
  {
    id: "deadline",
    accessorKey: "deadline",
    header: "Deadline",
  },
  {
    id: "submission",
    accessorKey: "submission",
    header: "Submission",
  },
]

export const admin = [
  {
    id: "title",
    accessorKey: "title",
    header: "Title",
  },

  // {
  //   id: "remarks",
  //   accessorKey: "remarks",
  //   header: "Remarks",
  // },
  {
    id: "submissionCount",
    accessorKey: "submissionCount",
    header: "Submission",
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Published Status",
  },
  {
    accessorKey: "date",
    // eslint-disable-next-line sonarjs/no-duplicate-string
    header: "Updated At",
  },
  {
    id: "deadline",
    accessorKey: "deadline",
    header: "Deadline",
  },
  {
    accessorKey: "actions",
    header: "Action",
  },
]

const viewSubmission = [
  {
    id: "name",
    accessorKey: "name",
    header: "Name",
  },
  {
    id: "remarks",
    accessorKey: "trainer_feedback",
    header: "Remarks",
  },
  {
    id: "obtainedMarks",
    accessorKey: "marks_obtained",
    header: "Obtained Marks",
  },
  {
    id: "status",
    accessorKey: "submission_status",
    header: "Status",
  },
  {
    id: "submission",
    accessorKey: "submission",
    header: "Submission",
  },

  {
    id: "acceptanceStatus",
    accessorKey: "acceptance_status",
    header: "Acceptance Status",
  },

  {
    id: "submissionTime",
    accessorKey: "submission_time",
    header: "Submission Time",
  },
  {
    id: "actions",
    accessorKey: "actions",
    header: "Actions",
    type: "assignment",
  },
]

export default viewSubmission

export const getColumnsByUserType = (userType) => {
  switch (userType) {
    case USER_ROLES.TRAINER:
      return trainer
    case USER_ROLES.STUDENT:
      return student
    case USER_ROLES.VENDOR:
      return vendor
    case USER_ROLES.ADMIN:
      return admin
    default:
      return []
  }
}
