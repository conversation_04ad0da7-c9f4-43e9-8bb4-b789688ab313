import moment from "moment"
import { z } from "zod"

export const AssignmentSchema = z.object({
  // module_id: z.number().int({ message: "Module ID is required" }).positive(),
  title: z.string().min(3, { message: "A valid title is required" }),
  due_date: z
    .preprocess(
      (val) => (typeof val === "string" ? new Date(val) : val),
      z.date({ required_error: "Due date is required" })
    )
    .refine((val) => moment(val).isSameOrAfter(moment(), "minute"), {
      message: "Due date cannot be in the past",
    })
    .transform((val) => moment(val).format("YYYY-MM-DDTHH:mm:ss")), // Local time format

  total_marks: z.string().min(1, { message: "A valid marks is required" }).optional(),
  description: z.string().min(15, { message: "A valid description is required" }).optional(),
  publish_status: z.string().optional(),
})
