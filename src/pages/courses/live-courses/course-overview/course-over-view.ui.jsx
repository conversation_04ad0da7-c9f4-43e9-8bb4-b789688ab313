import DataTable from "@/components/custom/cutsom-table"
import { DateCell, LinkCell } from "@/components/custom/cutsom-table/table-cells"
import useTableConfig from "@/hooks/use-table.hooks"
import { flexRender } from "@tanstack/react-table"
import courseContent from "../utils/course-content.json"

export const columns = [
  {
    accessorKey: "date",
    header: "Date",
    cell: ({ row }) => <div className="capitalize">{row.getValue("status")}</div>,
  },
  {
    id: "quiz",
    accessorKey: "quiz",
    header: "Quiz",
  },
  {
    id: "title",
    accessorKey: "title",
    header: "Title",
  },
  {
    id: "link",
    accessorKey: "link",
    header: "Link",
  },
  {
    id: "deadline",
    accessorKey: "deadline",
    header: "Deadline",
  },
  {
    id: "mark_obtained",
    accessorKey: "mark_obtained",
    header: "Notes",
  },
  {
    id: "feedback",
    accessorKey: "feedback",
    header: "Feedback",
  },

  {
    id: "actions",
    enableHiding: false,
  },
]

const CourseOverViewPage = () => {
  const renderCellContent = (cell, row) => {
    console.log("_table_cell", cell, row?.original)

    const { date, class: classes, topic, meet_link, trainer, notes, feedback } = row?.original || {}

    switch (cell.column.id) {
      case "date":
        return <DateCell value={date} />
      case "class":
        return <p>{classes}</p>
      case "topic":
        return <p>{topic}</p>
      case "meetlink":
        return <LinkCell value={meet_link} />
      case "trainer":
        return <p>{trainer}</p>
      case "notes":
        return <p>{notes}</p>
      case "feedback":
        return <p>{feedback}</p>

      default:
        return flexRender(cell.column.columnDef.cell, cell.getContext())
    }
  }
  const { table, found, pagination, pageCount } = useTableConfig(courseContent, columns)

  return (
    <div className="w-full">
      <DataTable
        renderCellContent={renderCellContent}
        columns={columns}
        table={table}
        found={found}
        pageCount={pageCount}
        pagination={pagination}
      />
    </div>
  )
}

export default CourseOverViewPage
