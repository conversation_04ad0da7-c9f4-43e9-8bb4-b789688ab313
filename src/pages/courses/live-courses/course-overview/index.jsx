//* eslint-disable eqeqeq */
import { BadgeCheck, CheckCircle2Icon, ShoppingCart, Zap } from "lucide-react"
import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useParams } from "react-router-dom"
import LineGraph from "@/components/custom/line-graph"
import { Card } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useFetchCourseOverview } from "@/services/query/course-overview.query"
import { USER_ROLES } from "@/utils/constants"
import { isStudent } from "@/utils/helper"
import ct from "@constants/"
import ProjectCompletition from "../../announcements/project-completion"
import GitHubCalendar from "../../course-over-view/course-activity"
import UpCommingEvents from "../../course-over-view/upComming-events"
import Announcement from "./components/announcement"
import InterviewDetails from "./components/interview-details"
import PaymentButtons from "./components/payment-buttons/payment-buttons"

import { aggregateCourseDataForDashboard } from "./utils/nano-helper"
import { UpcomingSession } from "./components/upcoming-session"
import LoadingSpinner from "@/components/custom/LoadingSpinner"


function SingleCourseOverview({
  courseData,
  nanoCourses,
  onAuditClick,
  user,
  course,
  dispatch,
  id,
  name,
}) {
  const isStudentUser = isStudent(course?.activeCourse, user?.userRole)
  const isStudentRole = user?.userRole === USER_ROLES.STUDENT

  const getPaymentButtons = () => {
    if (course?.activeCourseDetails?.courseDetails.parent_course_id) return null

    const isEnrolled = course?.myCourseDetails?.some((item) => item?.parent_course_id == id)

    return isEnrolled ? (
      <Card className="shadow-lg border-0 rounded-2xl overflow-hidden flex flex-col p-16">
        <div className="flex items-center justify-between p-12">
          <div className="flex items-center gap-3">
            <div className="p-3 rounded-lg">
              <CheckCircle2Icon className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-xl font-semibold mt-3">Enrollment Status</h3>
              <p className="text-sm mt-1">You&#39;re enrolled in this course</p>
            </div>
          </div>
          <span className="text-sm px-3 py-1.5 rounded-md bg-green-500 text-white">Active</span>
        </div>
      </Card>
    ) : (
      <Card className="shadow-lg border-0 rounded-2xl overflow-hidden flex flex-col">
        <div className="p-6 flex items-center justify-between border-b border-gray-100/80">
          <div className="flex items-center gap-4">
            <div className="p-3 rounded-lg bg-blue-100">
              <ShoppingCart className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-900">Enroll Now</h3>
              <p className="text-sm text-gray-500 mt-1">Get full access to this course</p>
            </div>
          </div>
        </div>

        <div className="flex-1 p-3 flex flex-col justify-center">
          <div className="space-y-4">
            <div className="flex items-center gap-3 bg-blue-50/50 p-4 rounded-xl">
              <Zap className="h-5 w-5 text-blue-500" />
              <p className="text-sm text-blue-700">Instant access after payment</p>
            </div>

            <div className="flex items-center gap-3 bg-blue-50/50 p-4 rounded-xl">
              <BadgeCheck className="h-5 w-5 text-blue-500" />
              <p className="text-sm text-blue-700">Certificate of completion</p>
            </div>
          </div>
        </div>

        <div className="px-6 pb-6">
          <PaymentButtons />
        </div>
      </Card>
    )
  }

  return (
    <div className="mb-8">
      {/* Main grid container with improved responsive layout */}
      <div
        className={`grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 ${!isStudentRole ? "xl:grid-cols-4" : "xl:grid-cols-3"
          } gap-6`}
      >
        {/* Top Section */}
        <div
          className={`md:col-span-3 lg:col-span-2 ${!isStudentRole ? "xl:col-span-2" : "xl:col-span-1"
            }`}
        >
          <UpcomingSession name={name} isPaidUser={isStudentUser} data={courseData?.upcoming_session} />
        </div>

        <div
          className={`md:col-span-3 lg:col-span-2 ${!isStudentRole ? "xl:col-span-2" : "xl:col-span-1"
            }`}
        >
          <Announcement
            isPaidUser={isStudentUser}
            data={courseData?.announcements}
            dispatch={dispatch}
            name={name}
          />
        </div>

        {isStudentRole &&
          (course?.activeCourseDetails?.courseDetails.parent_course_id ? (
            <div className="md:col-span-3 lg:col-span-2 xl:col-span-1">
              <InterviewDetails
                isPaidUser={isStudentUser}
                data={courseData?.course_materials}
                title="Course Materials"
                height="h-[230px]"
                name={name}
              />
            </div>
          ) : (
            <div className="md:col-span-3 lg:col-span-2 xl:col-span-1">{getPaymentButtons()}</div>
          ))}

        {/* Student Specific Components */}
        {isStudentRole && (
          <>
            <div className="md:col-span-3 lg:col-span-2 xl:col-span-1">
              <ProjectCompletition
                isPaidUser={isStudentUser}
                data={courseData?.assignment_quiz_submission}
              />
            </div>

            <div className="md:col-span-3 lg:col-span-2 xl:col-span-1">
              <InterviewDetails
                isPaidUser={isStudentUser}
                data={courseData?.interview_materials}
                title="Interview Materials"
                name={name}
              />
            </div>

            <div className="md:col-span-3 lg:col-span-2 xl:col-span-1">
              <LineGraph data={courseData?.quiz_perfomence} />
            </div>
          </>
        )}

        {isStudentRole ? (
          <>
            <div className="md:col-span-3 lg:col-span-2 xl:col-span-2">
              <UpCommingEvents
                name={name}
                nanoCourses={nanoCourses}
                onAuditClick={onAuditClick}
                data={courseData?.upcomeing_events}
              />
            </div>
            <div className="md:col-span-3 lg:col-span-2 xl:col-span-1">
              <GitHubCalendar data={courseData?.course_activity} title="Course Activity" />
            </div>
          </>
        ) : (
          <div
            className={`md:col-span-3 lg:col-span-2 ${!isStudentRole ? "xl:col-span-4" : "xl:col-span-2"
              }`}
          >
            <UpCommingEvents
              name={name}
              nanoCourses={nanoCourses}
              onAuditClick={onAuditClick}
              data={courseData?.upcomeing_events}
            />
          </div>
        )}
      </div>
    </div>
  )
}

function NanoCoursesOverview({
  aggregatedData,
  nanoCourses,
  onAuditClick,
  user,
  course,
  dispatch,
  name,
}) {
  const isStudentUser = isStudent(course?.activeCourse, user?.userRole)
  const isStudentRole = user?.userRole === USER_ROLES.STUDENT


  return (
    <div className="mb-8">
      <div
        className={`grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 ${!isStudentRole ? "xl:grid-cols-4" : "xl:grid-cols-3"
          } gap-6`}
      >
        <div
          className={`md:col-span-3 lg:col-span-2 ${!isStudentRole ? "xl:col-span-2" : "xl:col-span-1"
            }`}
        >
          <UpcomingSession name={name} isPaidUser={isStudentUser} data={aggregatedData?.upcomingSessions} />
        </div>

        <div
          className={`md:col-span-3 lg:col-span-2 ${!isStudentRole ? "xl:col-span-2" : "xl:col-span-1"
            }`}
        >
          <Announcement
            isPaidUser={isStudentUser}
            data={aggregatedData?.announcements}
            dispatch={dispatch}
            name={name}
          />
        </div>

        {isStudentRole && (
          <>
            <div className="md:col-span-3 lg:col-span-2 xl:col-span-1">
              <InterviewDetails
                isPaidUser={isStudentUser}
                data={aggregatedData?.courseMaterials}
                name={name}
                title="Course Materials"
                height="h-[230px]"
              />
            </div>

            <div className="md:col-span-3 lg:col-span-2 xl:col-span-1">
              <GitHubCalendar data={aggregatedData?.courseActivity} title="Course Activity" />
            </div>

            <div className="md:col-span-3 lg:col-span-2 xl:col-span-1">
              <InterviewDetails
                isPaidUser={isStudentUser}
                data={aggregatedData?.interviewMaterials}
                title="Interview Materials"
                name={name}
              />
            </div>
            <div className="md:col-span-3 lg:col-span-2 xl:col-span-1">
              <ProjectCompletition
                isPaidUser={isStudentUser}
                data={aggregatedData?.monthlySubmissions}
              />
            </div>

          </>
        )}

        {/* UpCommingEvents - Full width single column */}
        <div className="col-span-full">
          <UpCommingEvents
            name={name}
            nanoCourses={nanoCourses}
            onAuditClick={onAuditClick}
            data={{
              quizes: aggregatedData?.upcomingQuizes,
              projects: aggregatedData?.upcomingProjects,
              assignments: aggregatedData?.upcomingAssignments,
            }}
          />
        </div>
      </div>
    </div>
  )
}

function CourseOverview({ name, nanoIds, nanoCourses, onAuditClick }) {
  const { id } = useParams()
  const dispatch = useDispatch()
  const user = useSelector((st) => st[ct.store.USER_STORE])
  const course = useSelector((st) => st[ct.store.COURSES])
  const [coursesOverview, setCoursesOverview] = useState([])
  const [aggregatedNanoData, setAggregatedNanoData] = useState(null)
  console.log("nanoIds", nanoIds);

  const { data: courseOverviewData } = useFetchCourseOverview({
    course_id: id,
    course_ids: name === "Nano courses" ? nanoIds : undefined,
  })

  console.log("CourseOverview_data", coursesOverview)

  useEffect(() => {
    if (courseOverviewData?.data?.data) {
      const { data } = courseOverviewData.data

      if (name === "Nano courses") {
        const aggregated = aggregateCourseDataForDashboard(courseOverviewData.data)
        setAggregatedNanoData(aggregated)
        console.log("Aggregated nano data:", aggregated)
      } else if (Array.isArray(data)) {
        setCoursesOverview(data)
      } else {
        setCoursesOverview([data])
      }
    }
  }, [courseOverviewData, name])

  return (
    <ScrollArea className="h-[75vh]">
      <div className="p-4">
        {name === "Nano courses" ? (
          aggregatedNanoData ? (
            <NanoCoursesOverview
              aggregatedData={aggregatedNanoData}
              nanoCourses={nanoCourses}
              onAuditClick={onAuditClick}
              user={user}
              course={course}
              dispatch={dispatch}
              name={name}
            />
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <LoadingSpinner />
              </div>
            </div>
          )
        ) : coursesOverview.length > 0 ? (
          coursesOverview.map((courseData) => (
            <SingleCourseOverview
              key={courseData.id}
              courseData={courseData}
              user={user}
              course={course}
              dispatch={dispatch}
              id={id}
              name={name}
              courseName={courseData?.course_name}
              nanoCourses={nanoCourses}
              onAuditClick={onAuditClick}
            />
          ))
        ) : (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <LoadingSpinner />
            </div>
          </div>
        )}
      </div>
    </ScrollArea>
  )
}

export default CourseOverview
