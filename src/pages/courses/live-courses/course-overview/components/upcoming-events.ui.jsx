import { Card } from "@/components/ui/card"
import { motion } from "framer-motion"
import moment from "moment"
import PropTypes from "prop-types"

const UpcomingEventsCard = ({ title, descriptions, deadline }) => {
  return (
    <motion.div
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      className="w-[350px] flex-shrink-0"
    >
      <Card className="p-4 mx-2 rounded-lg transition-all duration-300 w-full h-48">
        <div className="flex justify-between items-center">
          <p className="text-lg mb-3 font-semibold">
            {title?.length > 20 ? title?.slice(0, 20)?.concat("...") : title}
          </p>
          <span className="text-xs font-semibold text-muted">
            {moment(new Date()).format("ll")}
          </span>
        </div>
        <p className="text-sm font-medium text-muted mb-2 line-clamp-3">{descriptions}</p>
        <div className="flex justify-end items-center text-muted">
          <span className="text-xs font-semibold">Deadline - </span>
          <span className="text-xs font-semibold">{moment(new Date()).format("ll")}</span>
        </div>
      </Card>
    </motion.div>
  )
}

UpcomingEventsCard.propTypes = {
  title: PropTypes.string,
  descriptions: PropTypes.string,
  deadline: PropTypes.string,
}

export default UpcomingEventsCard
