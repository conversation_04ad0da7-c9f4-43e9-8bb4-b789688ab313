/* eslint-disable no-nested-ternary */
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip" // Import Tooltip components
import { Calendar, CalendarX2, ExternalLink, Video } from "lucide-react"
import moment from "moment"
import PropTypes from "prop-types"

export const UpcomingSession = ({ data, name }) => {
  console.log("UpcomingSession_data", data)

  const getTimeStatus = (sessionData) => {
    if (!sessionData) return null

    const now = moment()
    const sessionTime = moment(sessionData.resource_date)
    const diffHours = sessionTime.diff(now, "hours")
    const isSessionToday = moment().isSame(sessionTime, "day")

    if (diffHours < 1 && diffHours >= 0) return { label: "Starting soon", color: "bg-red-500" }
    if (isSessionToday) return { label: "Today", color: "bg-green-500" }
    if (diffHours < 24 && diffHours > 0) return { label: "Tomorrow", color: "bg-blue-500" }
    return { label: "Upcoming", color: "bg-indigo-500" }
  }

  // Helper function to truncate text and provide tooltip
  const renderTruncatedText = (text, maxLength = 20) => {
    if (!text) return null;
    const needsTruncation = text.length > maxLength;
    const truncatedText = needsTruncation ? `${text.substring(0, maxLength)}...` : text;

    return needsTruncation ? (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span>{truncatedText}</span>
          </TooltipTrigger>
          <TooltipContent>
            <p>{text}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    ) : (
      <span>{text}</span>
    );
  };


  const headerTimeStatus = !Array.isArray(data) ? getTimeStatus(data) : null
  const isNano = name === "Nano courses"

  return (
    <Card className="relative overflow-hidden border-0 rounded-2xl shadow-lg h-[20rem]">
      {/* Colored top accent */}
      {data && !Array.isArray(data) && (
        <div className={`h-1.5 w-full ${headerTimeStatus?.color}`} />
      )}
      <div className="p-5 h-full">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-2">
            <div className="bg-slate-100 p-2 rounded-xl">
              <Calendar className="h-5 w-5 text-slate-700" />
            </div>
            <h3 className="text-lg font-bold text-slate-800">
              {" "}
              {isNano ? "Time Table" : "Upcoming Session"}
            </h3>
          </div>
          {data && !Array.isArray(data) && headerTimeStatus && (
            <Badge
              variant="outline"
              className={`text-xs font-medium border-0 ${headerTimeStatus.color === "bg-red-500"
                ? "bg-red-50 text-red-700"
                : headerTimeStatus.color === "bg-amber-500"
                  ? "bg-amber-50 text-amber-700"
                  : headerTimeStatus.color === "bg-green-500"
                    ? "bg-green-50 text-green-700"
                    : headerTimeStatus.color === "bg-blue-500"
                      ? "bg-blue-50 text-blue-700"
                      : "bg-indigo-50 text-indigo-700"
                }`}
            >
              <span
                className={`inline-block w-2 h-2 rounded-full mr-1.5 ${headerTimeStatus.color}`}
              />
              {headerTimeStatus.label}
            </Badge>
          )}
        </div>
        <ScrollArea className="h-[12rem] w-full pr-2">
          {Array.isArray(data) && data.length > 0 ? (
            data.map((session) => {
              const sessionFormattedDate = moment
                .parseZone(session.resource_date)
                .local()
                .format("dddd, MMMM D")
              const sessionFormattedTime = moment
                .parseZone(session.resource_date)
                .local()
                .format("h:mm A")
              const sessionTimeStatus = getTimeStatus(session)

              return (
                <div key={session.resource_date + session.module_name} className="py-3">
                  {/* Card Wrapper */}
                  <div className="rounded-xl border-[0.5px] border-slate-100 hover:shadow-md hover:duration-500 bg-white overflow-hidden">
                    {/* Header */}
                    <div className="flex items-center justify-between px-4 py-2 bg-slate-50 ">
                      <div className="flex items-center gap-2 text-sm font-medium text-slate-700">
                        <Video className="w-4 h-4 text-slate-600" />
                        {/* Apply truncation and tooltip to module_name */}
                        {renderTruncatedText(session.module_name, 20)}
                      </div>
                      {isNano && (
                        <div className="text-sm text-slate-600">
                          <span>Course : </span>
                          {/* Apply truncation and tooltip to courseName */}
                          <span className="text-primary text-sm">
                            {renderTruncatedText(session.courseName, 20)}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Body */}
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-3">
                      {/* Date Info */}
                      <div className="flex items-center gap-3 flex-1">
                        <div className="p-2 bg-blue-50 rounded-lg">
                          <Calendar className="h-5 w-5 text-blue-600" />
                        </div>
                        <div className="flex flex-col">
                          <span className="text-xs text-slate-500">Date & Time</span>
                          <div className="flex gap-2 text-sm font-medium text-slate-800">
                            <span>{sessionFormattedDate}</span>{" "}
                            <span>{sessionFormattedTime}</span>{" "}
                          </div>
                        </div>
                      </div>

                      {/* Join Button */}
                      <div className="text-right">
                        <Button
                          onClick={() => window.open(session.join_link, "_blank")}
                          className="bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg px-4 py-2 text-sm flex items-center gap-1.5 shadow-sm"
                        >
                          <ExternalLink className="w-4 h-4" />
                          Join
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })
          ) : data && !Array.isArray(data) ? (
            // Single session case
            <div className="space-y-4">
              <div className="rounded-xl border-[0.5px] border-slate-100 hover:shadow-md hover:duration-500 bg-white overflow-hidden">
                {/* Header */}
                <div className="flex items-center justify-between px-4 py-2 bg-slate-50 ">
                  <div className="flex items-center gap-2 text-sm font-medium text-slate-700">
                    <Video className="w-4 h-4 text-slate-600" />
                    {renderTruncatedText(data.module_name, 20)}
                  </div>
                  {isNano && (
                    <div className="text-sm text-slate-600">
                      <span>Course : </span>
                      <span className="text-primary text-sm">
                        {renderTruncatedText(data.courseName, 20)}
                      </span>
                    </div>
                  )}
                </div>

                {/* Body */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-3">
                  {/* Date Info */}
                  <div className="flex items-center gap-3 flex-1">
                    <div className="p-2 bg-blue-50 rounded-lg">
                      <Calendar className="h-5 w-5 text-blue-600" />
                    </div>
                    <div className="flex flex-col">
                      <span className="text-xs text-slate-500">Date & Time</span>
                      <div className="flex gap-2 text-sm font-medium text-slate-800">
                        <span>
                          {moment.parseZone(data.resource_date).local().format("dddd, MMMM D")}
                        </span>
                        <span>{moment.parseZone(data.resource_date).local().format("h:mm A")}</span>
                      </div>
                    </div>
                  </div>

                  {/* Join Button */}
                  <div className="text-right">
                    <Button
                      onClick={() => window.open(data.join_link, "_blank")}
                      className="bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg px-4 py-2 text-sm flex items-center gap-1.5 shadow-sm"
                    >
                      <ExternalLink className="w-4 h-4" />
                      Join
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            // No sessions
            <div className="flex flex-col items-center justify-center py-10 text-center rounded-xl">
              <div className="p-3 rounded-full shadow-sm mb-4 bg-slate-100">
                <CalendarX2 className="h-8 w-8 text-slate-400" />
              </div>
              <p className="text-slate-800 font-semibold mb-1">No Upcoming Sessions</p>
              <p className="text-slate-500 text-sm max-w-xs">
                Sessions will appear here once they are scheduled by your instructor
              </p>
            </div>
          )}
        </ScrollArea>
      </div>
    </Card>
  )
}

UpcomingSession.propTypes = {
  data: PropTypes.oneOfType([
    PropTypes.shape({
      module_name: PropTypes.string.isRequired,
      resource_date: PropTypes.string.isRequired,
      join_link: PropTypes.string.isRequired,
      instructor_name: PropTypes.string,
      courseName: PropTypes.string,
    }),
    PropTypes.arrayOf(
      PropTypes.shape({
        module_name: PropTypes.string.isRequired,
        resource_date: PropTypes.string.isRequired,
        join_link: PropTypes.string.isRequired,
        instructor_name: PropTypes.string,
        courseName: PropTypes.string,
      })
    ),
  ]),
  name: PropTypes.string,
}