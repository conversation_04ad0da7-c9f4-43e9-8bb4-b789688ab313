/* eslint-disable prettier/prettier */
import { Button } from "@/components/ui/button"
import { useCreatePaymentSession } from "@/services/query/payment.query"
import { PAYMENT_OPTIONS, PAYMENT_PERCENTAGES } from "@/utils/constants"
import PropTypes from "prop-types"
// import { detectCurrency } from "@/utils/location-based-helpers"
import { loadStripe } from "@stripe/stripe-js"
import { useState } from "react"
import LoadingSpinner from "@/components/custom/LoadingSpinner"
import { failureToast } from "@/components/custom/toasts/tosters"
import ConfirmationDialog from "./confirmation-dialog"
import { getSuccessAndCancelUrls } from "./payment-buttons"

function PayNow({ provider = PAYMENT_OPTIONS.STRIPE, activeCourseDetails, userRole, requestID = null, isImmediatePayment = false }) {

  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [amount, setAmount] = useState(activeCourseDetails.pricing.currentPrice)
  const [amountPercentage, setAmountPercentage] = useState(PAYMENT_PERCENTAGES["100%"])
  const { mutate: mutateCreateSession, isPending} = useCreatePaymentSession()

  const handlePersentageChange = (percentage) => {
    setAmountPercentage(percentage)
    setAmount((activeCourseDetails.pricing.currentPrice * percentage) / 100)
  }

  const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY)
  const handleStripePayment = async (sessionId) => {
    try {
      if (!sessionId) return

      // Redirect to Stripe Checkout
      const stripe = await stripePromise
      await stripe.redirectToCheckout({ sessionId })
    } catch (error) {
      console.error("Error creating Stripe Checkout session", error)
    }
  }

  const handlePayment = async () => {
    const {successUrl, cancelUrl} = getSuccessAndCancelUrls(userRole)

    try {
      // const currency = await detectCurrency()
      const payload = {
        course_id: activeCourseDetails?.id,
        course_name: activeCourseDetails?.courseName,
        image: activeCourseDetails?.imageUrl,
        amount_percentage: amountPercentage,
        success_url: successUrl,
        cancel_url: cancelUrl,
        amount,
        currency: activeCourseDetails?.pricing.currency,
      }

      if (requestID) // Include request_id, Only if payment is made by Vendor
        payload.request_id = requestID

      mutateCreateSession(
        { provider, payload },
        {
          onSuccess: (response) => {
            const sessionId = response?.data?.payment_id
            if (provider === PAYMENT_OPTIONS.STRIPE) handleStripePayment(sessionId)
          },
          onError: (error) => {
            console.error("Error while creating payment session", error, error?.response?.data?.message)
            failureToast("Payment Failed", "Try again later")
            
          }
        }
      )
    } catch (error) {
      console.error("Error creating Stripe Checkout session", error)
    }
  }

  // IF IMMEDIATE PAYMENT IS ENABLED, THEN NO NEED TO SHOW CONFIRMATION DIALOG
  if (isImmediatePayment)
    return (
      <Button variant="primary" onClick={handlePayment}>
        {isPending ? <LoadingSpinner className="flex items-center justify-center mx-4" variant="pulse"/> : "Pay Now"}
      </Button>
    )

  return (
    <>
      <Button variant="primary" onClick={() => setIsDialogOpen(true)} className="flex gap-x-2">
        <span className="animate-bounce">$</span>Pay Now
      </Button>
      <ConfirmationDialog
        isOpen={isDialogOpen}
        setIsOpen={setIsDialogOpen}
        courseName={activeCourseDetails?.courseName}
        onConfirm={handlePayment}
        onPercentageChange={handlePersentageChange}
        title="Confirm Payment"
        confirmButtonName={`Pay ${amount}`}
        isLoading={isPending}
      />
    </>
  )
}

PayNow.propTypes = {
  provider: PropTypes.string.isRequired,
  requestID: PropTypes.number,
  activeCourseDetails: PropTypes.shape({
    id: PropTypes.number.isRequired,
    courseName: PropTypes.string.isRequired,
    imageUrl: PropTypes.string.isRequired,
    pricing: PropTypes.shape({
      currency: PropTypes.string.isRequired,
      currencySymbol: PropTypes.string.isRequired,
      currentPrice: PropTypes.string.isRequired,
      originalPrice: PropTypes.string.isRequired,
    }).isRequired,
  }),
  userRole: PropTypes.string.isRequired,
  isImmediatePayment: PropTypes.bool,
}

export default PayNow
