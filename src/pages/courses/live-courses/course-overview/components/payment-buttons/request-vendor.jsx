import { Button } from "@/components/ui/button"
import PropTypes from "prop-types"
import { useSelector } from "react-redux"
import ct from "@constants/"
import { useState } from "react"
import { PAYMENT_PERCENTAGES, USER_ROLES } from "@/utils/constants"
import { useSentPaymentRequest } from "@/services/query/payment.query"
import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import ConfirmationDialog from "./confirmation-dialog"

function RequestVendor({ vendorData, activeCourseDetails }) {
  const {
    id: student_id,
    user_name: student_name,
    email: student_email,
    userRole,
  } = useSelector((st) => st[ct.store.USER_STORE])

  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [amount, setAmount] = useState(activeCourseDetails.pricing.currentPrice)
  const [amountPercentage, setAmountPercentage] = useState(PAYMENT_PERCENTAGES["100%"])
  const mutateRequest = useSentPaymentRequest()

  // Page Specific For Only Student
  if (userRole !== USER_ROLES.STUDENT) return null

  const handlePersentageChange = (percentage) => {
    setAmountPercentage(percentage)
    setAmount((activeCourseDetails.pricing.currentPrice * percentage) / 100)
  }

  const handleSentRequest = async () => {
    const payload = {
      amount,
      student_id,
      student_name,
      student_email,
      vendor_id: vendorData?.id,
      vendor_name: vendorData?.vendor_name,
      course_id: activeCourseDetails?.id,
      course_name: activeCourseDetails?.courseName,
      amount_percentage: amountPercentage,
      currency_code: activeCourseDetails?.pricing.currency,
    }

    mutateRequest.mutate(
      { payload, courseID: activeCourseDetails?.id },
      {
        onSuccess: () => {
          successToast("Payment Request Sent", "The payment request has been sent to your vendor!")
        },
        onError: (error) => {
          console.error(
            "Error while sending payment request to vendor",
            error,
            error?.response?.data?.message
          )
          failureToast("Sending Payment Request Failed", "Try again later")
        },
      }
    )
    setIsDialogOpen(false)
  }

  return (
    <>
      <Button variant="primary" onClick={() => setIsDialogOpen(true)}>
        Request Vendor
      </Button>
      <ConfirmationDialog
        isOpen={isDialogOpen}
        setIsOpen={setIsDialogOpen}
        courseName={activeCourseDetails?.courseName}
        onConfirm={handleSentRequest}
        onPercentageChange={handlePersentageChange}
        amount={`${activeCourseDetails.pricing.currencySymbol}${amount}`}
        title="Confirm Request"
        confirmButtonName={`Request ${amount}`}
      />
    </>
  )
}

RequestVendor.propTypes = {
  vendorData: PropTypes.shape({
    id: PropTypes.string.isRequired,
    vendor_name: PropTypes.string.isRequired,
    logo: PropTypes.string,
  }),
  activeCourseDetails: PropTypes.shape({
    id: PropTypes.number.isRequired,
    courseName: PropTypes.string.isRequired,
    imageUrl: PropTypes.string.isRequired,
    pricing: PropTypes.shape({
      currency: PropTypes.string.isRequired,
      currencySymbol: PropTypes.string.isRequired,
      currentPrice: PropTypes.string.isRequired,
      originalPrice: PropTypes.string.isRequired,
    }).isRequired,
  }),
}

export default RequestVendor
