import LoadingSpinner from "@/components/custom/LoadingSpinner"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { PAYMENT_PERCENTAGES } from "@/utils/constants"
import PropTypes from "prop-types"

function ConfirmationDialog({
  isOpen,
  setIsOpen,
  courseName,
  onConfirm,
  onPercentageChange,
  title = "Confirm",
  confirmButtonName = "Done",
  isLoading = false,
}) {
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-[450px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div className="flex flex-wrap items-center gap-4">
            <Label htmlFor="course-name">Course Title</Label>
            <Input id="course-name" className="read-only" value={courseName} />
          </div>
          <div className="flex flex-wrap items-center gap-4">
            <Label htmlFor="course-name">Payment Percentage</Label>
            <Select onValueChange={onPercentageChange}>
              <SelectTrigger className="text-xs font-semibold">
                <SelectValue placeholder="Select the percentage" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(PAYMENT_PERCENTAGES).map(([label, value]) => (
                  <SelectItem key={label} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter className="mt-4">
          <Button variant="primary" onClick={onConfirm}>
            {isLoading ? (
              <LoadingSpinner className="flex items-center justify-center mx-4" variant="pulse" />
            ) : (
              confirmButtonName
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

ConfirmationDialog.propTypes = {
  isOpen: PropTypes.bool,
  setIsOpen: PropTypes.func,
  courseName: PropTypes.string,
  onConfirm: PropTypes.func,
  onPercentageChange: PropTypes.func,
  title: PropTypes.string,
  confirmButtonName: PropTypes.string,
  isLoading: PropTypes.bool,
}

export default ConfirmationDialog
