import { PAYMENT_OPTIONS, USER_ROLES } from "@/utils/constants"
import { useSelector } from "react-redux"
import ct from "@constants/"
import PayNow from "./pay-now"
import RequestVendor from "./request-vendor"

export const getSuccessAndCancelUrls = (userRole) => {
  const currentPageUrl = window.location.href
  const { origin } = window.location
  if (userRole === USER_ROLES.STUDENT || userRole === USER_ROLES.VENDOR) {
    return {
      successUrl: `${origin}${ct.route.PAYMENTS}`,
      cancelUrl: currentPageUrl,
    }
  }

  return { successUrl: currentPageUrl, cancelUrl: currentPageUrl }
}

function PaymentButtons() {
  // Get Details from store
  const { userRole, vendorData } = useSelector((st) => st[ct.store.USER_STORE])
  const { activeCourseDetails } = useSelector((st) => st[ct.store.COURSES])

  // TODO: Get my course details, then check

  return (
    <div className="flex gap-x-4 flex-wrap">
      <PayNow
        provider={PAYMENT_OPTIONS.STRIPE}
        activeCourseDetails={activeCourseDetails}
        userRole={userRole}
      />
      {vendorData?.id && (
        <RequestVendor vendorData={vendorData} activeCourseDetails={activeCourseDetails} />
      )}
    </div>
  )
}

export default PaymentButtons
