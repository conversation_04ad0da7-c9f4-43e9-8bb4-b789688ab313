import { Card } from "@/components/ui/card"
import { setActiveTab } from "@/services/store/slices/courses.slice"
import { Bell, MessageSquare } from "lucide-react"
import PropTypes from "prop-types"

const Announcement = ({ dispatch, data = [], title = "Announcements", name }) => {
  const hasAnnouncements = Array.isArray(data) && data.length > 0

  const truncateText = (text, maxLength = 37) => {
    if (!text) return ""
    return text.length > maxLength ? `${text.slice(0, maxLength)}...` : text
  }
  const isNano = name === "Nano courses"
  return (
    <Card className="relative shadow-lg border-0 rounded-2xl overflow-hidden h-[20rem] flex flex-col">
      {/* Header */}
      <div className="p-4 pb-3 flex items-center  justify-between ">
        <div className="flex items-center gap-3">
          <div className={`p-2 rounded-lg ${hasAnnouncements ? "bg-blue-100" : "bg-gray-100"}`}>
            <Bell className={`h-5 w-5 ${hasAnnouncements ? "text-blue-600" : "text-gray-500"}`} />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              {title}
              {hasAnnouncements && (
                <span className="relative flex h-2 w-2">
                  <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75" />
                  <span className="relative inline-flex rounded-full h-2 w-2 bg-blue-500" />
                </span>
              )}
            </h3>
            <p className="text-xs text-gray-500">
              {hasAnnouncements ? "view all announcements" : "No recent updates"}
            </p>
          </div>
        </div>
      </div>

      {/* Announcements List */}
      <div
        className={`flex-1  overflow-y-auto custom-scrollbar px-4 py-3 mb-10 space-y-3 ${hasAnnouncements ? "cursor-pointer" : ""
          }`}
        onClick={() => hasAnnouncements && dispatch(setActiveTab("announcement"))}
      >
        {hasAnnouncements ? (
          data.map((item) => (
            <div
              key={item.id}
              className="bg-white  dark:bg-gray-900 border-[0.5px] border-slate-100  hover:shadow-md hover:uration-500 dark:border-gray-800 shadow-sm rounded-lg p-3   dark:hover:bg-gray-800 transition-colors"
            >
              <div className="flex justify-between items-center">
                <h4 className="text-sm font-medium text-slate-800">
                  {item.title}
                </h4>
                {isNano && (
                  <div className="text-xs text-muted-foreground sm:text-right">
                    Course:{" "}
                    <span className="  text-primary text-xs">
                      {item?.courseName}
                    </span>
                  </div>
                )}
              </div>
              <p className="text-xs text-gray-600 dark:text-gray-300">
                {truncateText(item.content)}
              </p>
            </div>
          ))
        ) : (
          <div className="h-full flex flex-col items-center justify-center bg-gray-50/40 dark:bg-black/20 rounded-lg p-6 text-center">
            <MessageSquare className="h-8 w-8 text-slate-400 mb-2" />
            <p className="text-slate-800 dark:text-white font-semibold mb-1">
              No announcements yet
            </p>
            <p className="text-slate-500 text-sm max-w-xs">
              When new announcements are posted, they'll appear here
            </p>
          </div>
        )}
      </div>
    </Card>
  )
}

Announcement.propTypes = {
  dispatch: PropTypes.func.isRequired,
  data: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number,
      title: PropTypes.string,
      content: PropTypes.string,
    })
  ),
  title: PropTypes.string,
}

export default Announcement
