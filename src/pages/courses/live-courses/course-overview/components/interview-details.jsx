import { <PERSON><PERSON><PERSON> } from "lucide-react"
import PropTypes from "prop-types"
import { useDispatch } from "react-redux"

import CustomTodo from "@/components/custom/custom-todo"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { setActiveTab, setActiveTabDetails } from "@/services/store/slices/courses.slice"

const InterviewDetails = ({
  data = [],
  title = "Interview Materials",
  height = "h-[210px]",
  name,
}) => {
  const dispatch = useDispatch()

  const handleNavigate = (task) => {
    dispatch(setActiveTab("interview-materials"))
    dispatch(setActiveTabDetails((prev) => ({ ...prev, interview_material: task })))
  }

  return (
    <Card className="overflow-hidden rounded-2xl shadow-md border-0">
      <div className="px-4 py-1">
        <div className="flex justify-between items-center gap-2">
          <div className="flex items-center gap-2">
            <BookOpen className="h-4 w-4 text-blue-600" />
            <h2 className="text-base font-semibold text-gray-800 mt-3">{title}</h2>
          </div>

          {data.length > 0 && (
            <div className="flex gap-1.5">
              <Badge variant="outline" className="bg-white py-1 px-2 text-xs">
                {data.length} Items
              </Badge>
            </div>
          )}
        </div>
      </div>

      <div className={`p-4 ${data.length === 0 ? "flex items-center justify-center" : ""}`}>
        {data.length > 0 ? (
          <CustomTodo classes={height} onNavigate={handleNavigate} items={data} name={name} />
        ) : (
          <div className="text-center py-8 px-4">
            <div className="bg-gray-100 rounded-full h-16 w-16 flex items-center justify-center mx-auto mb-4">
              <BookOpen className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-700">No materials available</h3>
            <p className="text-gray-500 mt-2 max-w-sm mx-auto">
              There are currently no interview materials to display
            </p>
          </div>
        )}
      </div>
    </Card>
  )
}

InterviewDetails.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      title: PropTypes.string.isRequired,
      status: PropTypes.string,
      description: PropTypes.string,
    })
  ),
  title: PropTypes.string,
  maxHeight: PropTypes.string,
}

export default InterviewDetails
