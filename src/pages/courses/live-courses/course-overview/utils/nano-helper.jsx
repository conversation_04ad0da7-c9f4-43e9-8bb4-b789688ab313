/**
 * Aggregates course data from multiple courses for dashboard display
 * @param {Object} apiResponse - The API response containing course data
 * @param {Array} apiResponse.data - Array of course objects
 * @returns {Object|null} - Aggregated course data or null if invalid input
 */
export function aggregateCourseDataForDashboard(apiResponse) {
    // Basic validation of the input structure
    if (!apiResponse || !apiResponse.data || !Array.isArray(apiResponse.data)) {
        console.error("Invalid API response structure provided to aggregateCourseDataForDashboard.")
        return null // Or return an empty default structure {}
    }

    const aggregatedData = {
        upcomingQuizes: [],
        upcomingProjects: [],
        upcomingAssignments: [],
        announcements: [],
        interviewMaterials: [],
        courseMaterials: [],
        upcomingSessions: [],
        quizPerformance: [], // Note: empty in your sample, but included for completeness
        courseActivity: [], // Note: empty in your sample, but included for completeness
        // Using an object/map for monthly submissions initially for easier summing
        monthlySubmissions: {},
    }

    // Define the expected order of months based on your input structure
    const monthOrder = ["Jan", "Feb", "Mar", "Apr", "May", "Jun"]

    // Initialize monthly submissions map with zeroes for known months
    monthOrder.forEach((month) => {
        aggregatedData.monthlySubmissions[month] = {
            month,
            quiz_count: 0,
            assignment_count: 0,
        }
    })

    // Iterate through each course
    apiResponse.data.forEach((course) => {
        const courseName = course.course_name || "Unnamed Course" // Use course name, default if missing

        // Aggregate Upcoming Events
        if (course.upcomeing_events) {
            if (Array.isArray(course.upcomeing_events.quizes)) {
                // Add course context to each item before pushing
                const quizesWithContext = course.upcomeing_events.quizes.map((item) => ({
                    ...item,
                    courseName,
                }))
                aggregatedData.upcomingQuizes.push(...quizesWithContext)
            }
            if (Array.isArray(course.upcomeing_events.projects)) {
                const projectsWithContext = course.upcomeing_events.projects.map((item) => ({
                    ...item,
                    courseName,
                }))
                aggregatedData.upcomingProjects.push(...projectsWithContext)
            }
            if (Array.isArray(course.upcomeing_events.assignments)) {
                const assignmentsWithContext = course.upcomeing_events.assignments.map((item) => ({
                    ...item,
                    courseName,
                }))
                aggregatedData.upcomingAssignments.push(...assignmentsWithContext)
            }
        }

        // Aggregate Announcements
        // Announcements can be null or an array
        if (Array.isArray(course.announcements)) {
            const announcementsWithContext = course.announcements.map((item) => ({ ...item, courseName }))
            aggregatedData.announcements.push(...announcementsWithContext)
        }

        // Aggregate Interview Materials
        if (Array.isArray(course.interview_materials)) {
            const interviewMaterialsWithContext = course.interview_materials.map((item) => ({
                ...item,
                courseName,
            }))
            aggregatedData.interviewMaterials.push(...interviewMaterialsWithContext)
        }

        // Aggregate Course Materials
        if (Array.isArray(course.course_materials)) {
            const courseMaterialsWithContext = course.course_materials.map((item) => ({
                ...item,
                courseName,
            }))
            aggregatedData.courseMaterials.push(...courseMaterialsWithContext)
        }

        // Aggregate Upcoming Sessions
        // upcoming_session can be null or an array (even if it contains only one item)
        if (Array.isArray(course.upcoming_session)) {
            const sessionsWithContext = course.upcoming_session.map((item) => ({ ...item, courseName }))
            aggregatedData.upcomingSessions.push(...sessionsWithContext)
        }

        // Aggregate Quiz Performance (assuming it's an array of performance records)
        if (Array.isArray(course.quiz_perfomence)) {
            const performanceWithContext = course.quiz_perfomence.map((item) => ({ ...item, courseName }))
            aggregatedData.quizPerformance.push(...performanceWithContext)
        }

        // Aggregate Course Activity (assuming it's an array of activity records)
        if (Array.isArray(course.course_activity)) {
            const activityWithContext = course.course_activity.map((item) => ({ ...item, courseName }))
            aggregatedData.courseActivity.push(...activityWithContext)
        }

        // Aggregate Assignment/Quiz Submissions by summing counts per month
        if (Array.isArray(course.assignment_quiz_submission)) {
            course.assignment_quiz_submission.forEach((monthlyEntry) => {
                const { month } = monthlyEntry
                if (aggregatedData.monthlySubmissions[month]) {
                    // Check if the month exists in our initialized map
                    aggregatedData.monthlySubmissions[month].quiz_count += monthlyEntry.quiz_count
                    aggregatedData.monthlySubmissions[month].assignment_count += monthlyEntry.assignment_count
                } else {
                    // Handle unexpected months if necessary, e.g., initialize it or log a warning
                    console.warn(
                        `Unexpected month "${month}" found in submission data from course "${courseName}".`
                    )
                    // If you want to include unexpected months, you'd need to initialize them here
                    // aggregatedData.monthlySubmissions[month] = { month: month, quiz_count: monthlyEntry.quiz_count, assignment_count: monthlyEntry.assignment_count };
                }
            })
        }
    })

    // Convert the monthlySubmissions map back to an array, preserving the month order
    // This step is important if you need the monthly data in a specific order for charts/tables
    aggregatedData.monthlySubmissions = monthOrder
        .map((month) => aggregatedData.monthlySubmissions[month])
        // Optional: Filter out months that were expected but not found in the data (unlikely with your structure)
        .filter((entry) => entry !== undefined)

    return aggregatedData
}
