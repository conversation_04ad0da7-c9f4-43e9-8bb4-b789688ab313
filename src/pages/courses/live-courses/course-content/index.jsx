import CustomAccordionTimeline from "@/components/custom/custom-accordion-timeline"
import MarqueeCard from "@/components/custom/custom-marquee"
import CustomSearchbar from "@/components/custom/custom-search"
import { CustomSelect } from "@/components/custom/custom-select"
import { DeleteDialog } from "@/components/custom/dialogs/delete.dialog"
import { GeneralDialog } from "@/components/custom/dialogs/general.dialog"
import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import useDebounce, {
  useDeleteDialogHooks,
  useIsEditDialogHook,
  usePublishStatus,
} from "@/hooks/common.hooks"
import {
  useUpdateStatusMutation,
  useUpdateTopicStatusMutation,
} from "@/services/query/course-content.query"
import { useGetCourseContent } from "@/services/query/live-course.query"
import { publishStatus, USER_ROLES } from "@/utils/constants"
import { isStudent } from "@/utils/helper"
import ct from "@constants/"
import { zodResolver } from "@hookform/resolvers/zod"
import { Layers } from "lucide-react"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { useSelector } from "react-redux"
import { useLocation, useNavigate, useParams } from "react-router-dom"
import Feedback from "../feedback"
import CourseModule from "./course-module"
import CourseTopic from "./course-topic"
import {
  createModuleSchema,
  createModuleSchemaDefaultValues,
  createTopicDefaultValues,
  createTopicSchema,
} from "./utils/course-content.schema"

function CourseContent() {
  const course = useSelector((st) => st[ct.store.COURSES]?.activeCourseDetails)
  const user = useSelector((st) => st[ct.store.USER_STORE])

  // general hooks
  const [isTopicSidebarOpen, setIsTopicSidebarOpen] = useState(false)
  const [isModuleSidebarOpen, setisModuleSidebarOpen] = useState(false)
  const [isModuleDeleteOpen, setIsModuleDeleteOpen] = useState(false)
  const [isTopiceDeleteOpen, setisTopiceDeleteOpen] = useState(false)
  const [isTopicEdit, setIsTopicEdit] = useState(false)
  const [isModuleEdit, setIsModuleEdit] = useState(false)
  const [isCompleteDialog, setIsCompleteDialog] = useState(false)
  const [completeStatus, setCompleteStatus] = useState("Yes")

  // cutom hooks
  const { isDelete, handleCancel } = useDeleteDialogHooks(false)
  const { publishedStatus, handleSetPublishStatus } = usePublishStatus()
  const { isEditDialog, handleResetEditDialog } = useIsEditDialogHook()
  const [isTopicDialog, setIsTopicDialog] = useState(false)
  const [isModuleDialog, setIsModuleDialog] = useState(false)
  const [searchingValue, setSearchingValue] = useState("")

  const [selectedIds, setSelectedIds] = useState({
    module_id: "",
    course_id: "",
    topic_id: "",
  })

  const params = useParams()

  const pageSize = 50
  const currentPage = 0
  const [totalRecords, setTotalRecords] = useState(0)

  const debouncedSearchQuery = useDebounce(searchingValue, 500)
  const navigate = useNavigate()
  const location = useLocation()

  // apis
  const { mutate: updateStatusMutation } = useUpdateStatusMutation()
  const { mutate: updateTopicStatusMutation } = useUpdateTopicStatusMutation()
  const { data: coursesContentData, isLoading } = useGetCourseContent(
    {
      userID: user.id,
      userRole: user?.userRole,
      course_id: params?.id || course?.id, // Move course_id inside the object
      signature: "SIGNATURE",
    },
    debouncedSearchQuery?.length > 0 ? debouncedSearchQuery : "", // Search query
    currentPage,
    pageSize,
    {} // Filters (optional)
  )
  console.log(coursesContentData, "coursesContentData")
  useEffect(() => {
    if (coursesContentData?.get_module_resource.modules) {
      const formatedData = coursesContentData.get_module_resource
      setTotalRecords(formatedData?.total_records)
    }
  }, [coursesContentData?.get_module_resource.modules])

  const { userRole } = useSelector((st) => st[ct.store.USER_STORE])
  const courseDet = useSelector((st) => st[ct.store.COURSES])

  // Check if we're in the module content route
  const isModuleContentRoute = location.pathname.includes(ct.route.MODULE_CONTENT)

  const moduleForm = useForm({
    resolver: zodResolver(createModuleSchema),
    defaultValues: createModuleSchemaDefaultValues,
    mode: "onChange",
  })

  const topicForm = useForm({
    resolver: zodResolver(createTopicSchema),
    defaultValues: createTopicDefaultValues,
    mode: "onChange",
  })

  const handleCreateTopic = (tID) => {
    setSelectedIds({
      ...selectedIds,
      module_id: tID,
    })

    topicForm.reset({
      resource_name: "",
      resource_description: "",
      resource_type: "",
      notes: "",
      resource_link: "",
    })
    setIsTopicEdit(false)
    setIsTopicSidebarOpen(true)
  }

  const handleDeleteTopicOpenDialog = (tID, mID) => {
    console.log("_Data", tID, mID)
    setSelectedIds({
      ...selectedIds,
      module_id: mID,
      topic_id: tID,
    })
    setisTopiceDeleteOpen(true)
  }

  const handleCreateModule = () => {
    moduleForm.reset({
      module_name: "",
      module_description: "",
      duration_minutes: "",
    })
    setIsModuleEdit(false)
    setisModuleSidebarOpen(true)
  }

  const handleDeleteModuleOpenDialog = (mID) => {
    setSelectedIds({
      ...selectedIds,
      module_id: mID,
    })
    setIsModuleDeleteOpen(true)
  }

  const handleEditModule = (data) => {
    setIsModuleEdit(true)
    setSelectedIds({
      ...selectedIds,
      module_id: data?.id,
    })
    const { module_description, duration_minutes, module_name } = data || {}
    moduleForm.reset({
      duration_minutes,
      module_description,
      module_name,
    })
    setisModuleSidebarOpen(true)
  }

  const handleEditTopic = (id, data) => {
    setIsTopicEdit(true)
    setSelectedIds({
      ...selectedIds,
      module_id: id,
      topic_id: data?.id,
    })
    const {
      resource_name,
      resource_description,
      resource_date,
      resource_type,
      notes,
      resource_link,
    } = data || {}
    topicForm.reset({
      resource_name,
      resource_description,
      resource_date,
      resource_type,
      notes,
      resource_link,
    })
    setIsTopicSidebarOpen(true)
  }

  const handleOpenStatusDialog = (row) => {
    const { id, publish_status } = row || {}
    handleSetPublishStatus(publish_status)

    setIsModuleDialog(true)
    setSelectedIds({
      ...selectedIds,
      module_id: id,
      topic_id: id,
    })
  }

  const handleUpdateStatus = () => {
    updateStatusMutation(
      {
        course_id: course?.id || 0,
        module_id: selectedIds?.module_id,
        status: publishedStatus,
      },
      {
        onSuccess: () => {
          successToast("Module Status Updated", "Status has been successfully updated!")
          setIsModuleDialog(false)
        },
        onError: () => {
          failureToast(
            "Module Status Update Failed",
            "An error occurred while updating the status. Please try again."
          )
          setIsModuleDialog(false)
        },
      }
    )
  }
  const handleUpdateTopicStatus = () => {
    updateTopicStatusMutation(
      {
        course_id: course?.id || 0,
        module_id: selectedIds?.module_id,
        resource_id: selectedIds?.topic_id,
        status: publishedStatus,
      },
      {
        onSuccess: () => {
          successToast("Status Updated", "Status has been successfully updated!")
          setIsTopicDialog(false)
        },
        onError: () => {
          failureToast(
            "Status Update Failed",
            "An error occurred while updating the status. Please try again."
          )
          setIsTopicDialog(false)
        },
      }
    )
  }

  const handleTopicStatus = (data, m_id) => {
    const { id, publish_status } = data || {}
    console.log("__data", data, m_id)
    handleSetPublishStatus(publish_status)
    setSelectedIds({
      ...selectedIds,
      module_id: m_id,
      topic_id: id,
    })
    setIsTopicDialog(true)
  }

  const handleOpenCompleteDialog = (data, m_id) => {
    setSelectedIds({
      ...selectedIds,
      module_id: m_id,
      topic_id: data,
    })
    setIsCompleteDialog(true)
  }

  const handleCompleteStatus = () => {
    updateTopicStatusMutation(
      {
        course_id: course?.id || 0,
        module_id: selectedIds?.module_id,
        resource_id: selectedIds.topic_id,
        status: completeStatus === "YES",
        type: "complete",
      },
      {
        onSuccess: () => {
          successToast(
            "Status Updated",
            "Resource marked as complete has been successfully Updated!"
          )
          setIsCompleteDialog(false)
        },
        onError: () => {
          failureToast(
            "Status Updation Failed",
            "An error occurred while update the resource mark as complete. Please try again."
          )
          setIsCompleteDialog(false)
        },
      }
    )
  }

  if (location.pathname?.includes("feedback")) {
    return <Feedback />
  }

  const hasModules = coursesContentData?.get_module_resource?.modules?.length > 0
  return (
    <div className="w-full overflow-hidden">
      {/* Conditionally render MarqueeCard based on route */}
      {!isModuleContentRoute && (
        <div className="md:max-w-[40rem] lg:max-w-[55rem] xl:max-w-[80rem] mx-auto mb-5">
          <MarqueeCard data={coursesContentData?.get_module_resource?.trainers} />
        </div>
      )}
      <Card className="border-0 outline-none shadow-none pt-2 px-4 pb-2  max-w-6xl mx-auto rounded-2xl ">
        <div className="w-full flex justify-between mb-5">
          <CustomSearchbar
            inputSize="w-[20rem]"
            placeholder="Search by title..."
            searchedValue={searchingValue}
            setSearchedValue={(e) => setSearchingValue(e?.target.value)}
          />

          <div className="flex gap-x-3">
            {(userRole === USER_ROLES.TRAINER || userRole === USER_ROLES.ADMIN) && (
              <div className="flex gap-2">
                {!isModuleContentRoute && (
                  <Button
                    variant="secondary"
                    onClick={() => {
                      navigate(ct.route.COURSE_ATTENDANCE, {
                        state: { coursesContentData },
                      })
                    }}
                  >
                    Attendance
                  </Button>
                )}
                <Button variant="primary" onClick={handleCreateModule}>
                  Create Module
                </Button>
              </div>
            )}
          </div>
        </div>

        {hasModules ? (
          <CustomAccordionTimeline
            modulesList={coursesContentData?.get_module_resource.modules}
            onDeleteTopic={handleDeleteTopicOpenDialog}
            onEditTopic={handleEditTopic}
            onDeleteModule={handleDeleteModuleOpenDialog}
            onEditModule={handleEditModule}
            onCreateTopic={handleCreateTopic}
            onUpdateStatus={handleOpenStatusDialog}
            onTopicStatus={handleTopicStatus}
            onCompleteStatus={handleOpenCompleteDialog}
            userRole={userRole}
            totalRecords={totalRecords}
            isLoading={isLoading}
            isPaidUser={isStudent(courseDet?.activeCourse, userRole)}
          />
        ) : (
          <div className="flex flex-col items-center justify-center">
            <div className="flex flex-col opacity-30 items-center justify-center">
              <Layers className="block h-16 w-16 mr-2" />
              <p className="text-lg mt-2 font-semibold">Modules Not Found</p>
            </div>
          </div>
        )}
      </Card>

      <DeleteDialog onOpen={isDelete} onCancel={handleCancel} />

      <CourseModule
        userRole={userRole}
        setisModuleSidebarOpen={setisModuleSidebarOpen}
        isModuleSidebarOpen={isModuleSidebarOpen}
        isModuleEdit={isModuleEdit}
        setIsModuleEdit={setIsModuleEdit}
        isModuleDeleteOpen={isModuleDeleteOpen}
        setIsModuleDeleteOpen={setIsModuleDeleteOpen}
        form={moduleForm}
        selectedIds={selectedIds}
        isEditDialog={isEditDialog}
        handleResetEditDialog={handleResetEditDialog}
        publishedStatus={publishedStatus}
        handleSetPublishStatus={handleSetPublishStatus}
        courseID={course?.id}
      />

      <CourseTopic
        userRole={userRole}
        setIsTopicSidebarOpen={setIsTopicSidebarOpen}
        isTopicSidebarOpen={isTopicSidebarOpen}
        isTopicEdit={isTopicEdit}
        isTopiceDeleteOpen={isTopiceDeleteOpen}
        setisTopiceDeleteOpen={setisTopiceDeleteOpen}
        form={topicForm}
        selectedIds={selectedIds}
        courseID={course?.id}
      />

      <GeneralDialog
        onOpen={isModuleDialog}
        title="Update Module Status"
        onClickCTA={handleUpdateStatus}
        ctaLabel="Update Status"
        onCancel={() => setIsModuleDialog(false)}
        ctaPosition="justify-end"
        content={
          <div className="mb-3">
            <Label>Publish Status</Label>
            <CustomSelect
              publishedStatus={publishedStatus}
              setPublishedStatus={handleSetPublishStatus}
              iteratedData={publishStatus}
            />
          </div>
        }
      />

      <GeneralDialog
        onOpen={isCompleteDialog}
        title="Complete Status"
        onClickCTA={handleCompleteStatus}
        ctaLabel="Update Status"
        onCancel={() => setIsCompleteDialog(!isCompleteDialog)}
        ctaPosition="justify-end"
        content={
          <CustomSelect
            publishedStatus={completeStatus}
            setPublishedStatus={setCompleteStatus}
            iteratedData={["Yes", "No"]}
          />
        }
      />

      <GeneralDialog
        onOpen={isTopicDialog}
        title="Update Topic Status"
        onClickCTA={handleUpdateTopicStatus}
        ctaLabel="Update Status"
        onCancel={() => setIsTopicDialog(!isTopicDialog)}
        ctaPosition="justify-end"
        content={
          <CustomSelect
            publishedStatus={publishedStatus}
            setPublishedStatus={handleSetPublishStatus}
            iteratedData={publishStatus}
          />
        }
      />
    </div>
  )
}
export default CourseContent
