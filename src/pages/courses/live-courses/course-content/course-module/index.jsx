import { DeleteDialog } from "@/components/custom/dialogs/delete.dialog"
import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import CustomSidebar from "@/pages/courses/components/custom-sidebar"
import {
  useCreateCourseModuleMutation,
  useDeleteCourseModuleMutation,
  useUpdateCourseModuleMutation,
} from "@/services/query/course-content.query"

import PropTypes from "prop-types"
import { useParams } from "react-router-dom"
import CreateModuleUI from "./create-module.ui"

const CourseModule = ({
  userRole,
  setisModuleSidebarOpen,
  form,
  isModuleEdit,
  selectedIds,
  publishedStatus,
  isModuleDeleteOpen,
  setIsModuleDeleteOpen,
  isModuleSidebarOpen,
  courseID,
}) => {
  const { mutate: createModuleMutation } = useCreateCourseModuleMutation()
  const { mutate: updateModuleMutation } = useUpdateCourseModuleMutation()
  const { mutate: deleteModuleMutation } = useDeleteCourseModuleMutation()

  const params = useParams()

  const { id } = params

  const { handleSubmit, control, reset } = form
  console.log("course_id", { courseID, id })

  const handleCreateModule = (data) => {
    setisModuleSidebarOpen(false)

    const formatedPayload = {
      ...data,
      sequence_number: 4,
    }

    const mutationHandler = (mutation, successMessage, errorMessage) => {
      mutation(
        { course_id: id || 0, module_id: selectedIds?.module_id, data: formatedPayload },
        {
          onSuccess: () =>
            successToast(
              successMessage,
              `The module has been successfully ${successMessage.toLowerCase()}!`
            ),
          onError: () =>
            failureToast(
              errorMessage,
              `An error occurred while ${errorMessage.toLowerCase()} the module. Please try again.`
            ),
        }
      )
    }

    if (isModuleEdit) {
      mutationHandler(updateModuleMutation, "Updated", "Updation Error")
    } else {
      mutationHandler(createModuleMutation, "Created", "Creation Failed")
    }
  }

  const handleDeleteModule = () => {
    deleteModuleMutation(
      { course_id: 2, module_id: selectedIds?.module_id },
      {
        onSuccess: () => {
          setIsModuleDeleteOpen(false)
          successToast("Deleted", "The module has been successfully deleted!")
        },
        onError: () => {
          setIsModuleDeleteOpen(false)
          failureToast("Deletion Failed", "Unable to delete the module. Please try again later.")
        },
      }
    )
  }

  const handleCancelForm = () => {
    reset(null)
    setisModuleSidebarOpen(!isModuleSidebarOpen)
  }

  return (
    <div>
      <CustomSidebar
        title={`${isModuleEdit ? "Update" : "Create"} Module`}
        description=""
        isOpen={isModuleSidebarOpen}
        onClose={() => setisModuleSidebarOpen(!isModuleSidebarOpen)}
        content={
          <CreateModuleUI
            form={form}
            control={control}
            onCreateModule={handleCreateModule}
            handleSubmit={handleSubmit}
            onClose={handleCancelForm}
            isModuleEdit={isModuleEdit}
            userRole={userRole}
          />
        }
      />
      <DeleteDialog
        title="Delete Module"
        onOpen={isModuleDeleteOpen}
        onCancel={() => setIsModuleDeleteOpen(!isModuleDeleteOpen)}
        onDelete={handleDeleteModule}
      />
    </div>
  )
}

CourseModule.propTypes = {
  userRole: PropTypes.oneOf(["admin", "teacher", "student"]).isRequired,
  setisModuleSidebarOpen: PropTypes.bool.isRequired,
  isModuleEdit: PropTypes.bool.isRequired,
  form: PropTypes.shape({
    formState: PropTypes.func.isRequired,
    handleSubmit: PropTypes.func.isRequired,
    reset: PropTypes.func.isRequired,
    control: PropTypes.func.isRequired,
  }).isRequired,

  // Selected IDs for editing/updating
  selectedIds: PropTypes.shape({
    module_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    topic_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  }),
  publishedStatus: PropTypes.string.isRequired,
  isModuleDeleteOpen: PropTypes.bool.isRequired,
  setIsModuleDeleteOpen: PropTypes.func.isRequired,
  isModuleSidebarOpen: PropTypes.bool.isRequired,
  courseID: PropTypes.number,
}

export default CourseModule
