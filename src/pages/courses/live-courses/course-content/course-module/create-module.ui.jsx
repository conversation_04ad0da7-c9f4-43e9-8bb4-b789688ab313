import FormInput from "@/components/custom/custom-forms/form-input"
import FormTextArea from "@/components/custom/custom-forms/form-textarea"
import { Button } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { USER_ROLES } from "@/utils/constants"
import PropTypes from "prop-types"

const CreateModuleUI = ({
  form,
  control,
  onCreateModule,
  handleSubmit,
  isModuleEdit,
  onClose,
  userRole,
}) => {
  return (
    <ScrollArea className="h-[calc(100vh-200px)] md:h-[calc(100vh-150px)] sm:h-[calc(100vh-100px)] w-full overflow-auto pr-4 ">
      <Form {...form}>
        <form onSubmit={handleSubmit(onCreateModule)} className="space-y-3 grid gap-y-3">
          {["ADMIN", "TRAINER"]?.includes(userRole) && (
            <>
              <FormInput
                dataTestID="module_name"
                placeholder="Enter Module Name"
                label="Module Name"
                fieldControlName="module_name"
                control={control}
                isRequired
              />

              <FormInput
                placeholder="Enter Duration"
                label="Duration"
                fieldControlName="duration_minutes"
                control={control}
                isRequired
                formDes="eg (30-mins,60-mins,90-1-30mins,120-2hrs)"
                isTypeNumer
              />

              <FormTextArea
                dataTestID="module_description"
                dataTestIDError="error"
                fieldControlName="module_description"
                control={control}
                label="Description"
                placeholder="Enter Description"
                isRequired
              />
            </>
          )}

          {userRole === USER_ROLES.STUDENT && (
            <div>
              <Label className="mr-2">Feeback </Label>{" "}
              <FormTextArea
                placeholder="Enter Feedback"
                label=""
                fieldControlName="feedback"
                control={control}
                isRequired={false}
              />
            </div>
          )}

          <div className="pt-12 flex gap-x-3 justify-end">
            <Button onClick={onClose} variant="secondary" type="reset">
              Cancel
            </Button>
            <Button type="submit" variant="primary">
              {isModuleEdit ? "Update & Save" : "Create"}
            </Button>
          </div>
        </form>
      </Form>
    </ScrollArea>
  )
}

CreateModuleUI.propTypes = {
  form: PropTypes.objectOf.isRequired,
  control: PropTypes.objectOf.isRequired,
  onCreateModule: PropTypes.func.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  isModuleEdit: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  userRole: PropTypes.string,
}

export default CreateModuleUI
