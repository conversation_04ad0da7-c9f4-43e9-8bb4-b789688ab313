import CustomComboBox from "@/components/custom/custom-combo-box/custom-combo-box"
import CustomSearchbar from "@/components/custom/custom-search"
import DataTable from "@/components/custom/cutsom-table"
import { ActionCell, LinkCell, UserAvatarCell } from "@/components/custom/cutsom-table/table-cells"
import NavigationLinks from "@/components/custom/navigation-links"
import { Button } from "@/components/ui/button"
import useTableConfig from "@/hooks/use-table.hooks"
import { USER_ROLES } from "@/utils/constants"
import ct from "@constants/"
import { flexRender } from "@tanstack/react-table"
import { Menu } from "lucide-react"
import PropTypes from "prop-types"
import { useNavigate } from "react-router-dom"
import { getColumns } from "./utils/course-content-columns"

function CourseContentUI({
  handleView,
  handleFeedback,
  handleDelete,
  handleEdit,
  handleCreateTopic,
  userRole,
  handleEditFeedback,
  searchValue,
  handleSearch,
  onOpenMenus,
  courseContentList,
}) {
  const navigate = useNavigate()

  const renderCellContent = (cell, row) => {
    const { id, topic, meet_link, trainer, notes, type, date } = row?.original || {}

    switch (cell.column.id) {
      case "Sno":
        return <p className="text-sm font-medium flex">{id}</p>
      case "topic":
        return <p className="text-sm font-medium flex">{topic}</p>
      case "meetlink":
        return <LinkCell value={meet_link} type={type} date={date} />
      case "trainer":
        return <UserAvatarCell value={trainer} />
      case "notes":
        return <NavigationLinks url={meet_link} item={notes} />
      case "actions":
        return (
          <div>
            {(() => {
              if (userRole === USER_ROLES.ADMIN) {
                return (
                  <ActionCell
                    label1="View"
                    label2="Edit"
                    label3="Delete Feedback"
                    row={row}
                    isEdit
                    isDelete
                    onDelete={handleDelete}
                    onEdit={handleEdit}
                  />
                )
              }
              if (userRole === USER_ROLES.TRAINER) {
                return (
                  <ActionCell
                    label1="View Feedback"
                    label2="Edit"
                    label3="Delete Feedback"
                    row={row}
                    isView
                    isEdit
                    isDelete
                    onDelete={handleDelete}
                    onEdit={handleEdit}
                    onFeedback={handleFeedback}
                    onView={handleView}
                  />
                )
              }
              return (
                <ActionCell
                  label1="Add Feedback"
                  label2="Edit Feedback"
                  row={row}
                  isView
                  isEdit
                  onView={handleFeedback}
                  onEdit={handleEditFeedback}
                />
              )
            })()}
          </div>
        )

      default:
        return flexRender(cell.column.columnDef.cell, cell.getContext())
    }
  }
  const { table, found, pagination, pageCount } = useTableConfig(
    courseContentList?.modules,
    getColumns(userRole)
  )

  return (
    <div>
      <div className="w-full flex justify-between">
        <CustomSearchbar
          inputSize="w-[20rem]"
          placeholder="Search by title..."
          searchedValue={searchValue}
          setSearchedValue={handleSearch}
        />

        <div className="flex gap-x-3">
          <CustomComboBox
            // iterateData={iterateData}
            // selectedValue={selectedValue}
            // setSelectedValue={setSelectedValue}
            // comboIsOpen={comboIsOpen}
            // setComboIsOpen={setComboIsOpen}
            placeholder="Search Modules"
            // field={field}
            // notFoundMessage={notFoundMessage}
            width="w-[18rem]"
            // dataTestID={dataTestID}
          />

          <Button variant="secondary" className="px-3" onClick={onOpenMenus}>
            <Menu size={18} className="text-primary" />
          </Button>

          {(userRole === USER_ROLES.TRAINER || userRole === USER_ROLES.ADMIN) && (
            <div className="flex gap-2">
              <Button
                variant="secondary"
                onClick={() => {
                  navigate(ct.route.COURSE_ATTENDANCE)
                }}
              >
                Attendance
              </Button>

              {userRole === USER_ROLES.ADMIN && (
                <Button variant="primary" onClick={handleCreateTopic}>
                  Create Topic
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
      <DataTable
        renderCellContent={renderCellContent}
        columns={getColumns(userRole)}
        table={table}
        found={found}
        pageCount={pageCount}
        pagination={pagination}
        pageName="Course Content"
        notFoundPlaceholder="No course content available"
      />
    </div>
  )
}

CourseContentUI.propTypes = {
  handleView: PropTypes.func.isRequired,
  handleFeedback: PropTypes.func.isRequired,
  handleDelete: PropTypes.func.isRequired,
  handleEdit: PropTypes.func.isRequired,
  handleCreateTopic: PropTypes.func.isRequired,
  userRole: PropTypes.string.isRequired,
  handleEditFeedback: PropTypes.func.isRequired,
  searchValue: PropTypes.string.isRequired,
  handleSearch: PropTypes.func.isRequired,
  onOpenMenus: PropTypes.func.isRequired,
  courseContentList: PropTypes.shape({
    modules: PropTypes.shape({
      id: PropTypes.number.isRequired,
      module_name: PropTypes.string.isRequired,
      module_description: PropTypes.string,
    }),
  }),
}

export default CourseContentUI
