import AppointmentScheduler from "@/components/custom/custom-forms/date-time-picker"
import FormInput from "@/components/custom/custom-forms/form-input"
import FormSelect from "@/components/custom/custom-forms/form-select"
import FormTextArea from "@/components/custom/custom-forms/form-textarea"
import FileUploadCard from "@/components/custom/upload-file"
import { Button } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { fileType, USER_ROLES } from "@/utils/constants"
import PropTypes from "prop-types"
import { useEffect } from "react"

const CreateCourseTopicUI = ({
  form,
  reset,
  control,
  onCreateTopic,
  handleSubmit,
  isTopicEdit,
  onClose,
  userRole,
  setValue,
  watch,
  documentData,
  onChangeFile,
}) => {
  const resourceType = watch("resource_type")

  console.log("resourceType", resourceType)
  useEffect(() => {
    if (resourceType === "DOCUMENT" || resourceType === "VIDEO") {
      setValue("resource_link", "")
    }
  }, [resourceType, setValue])
  return (
    <ScrollArea className="h-[calc(100vh-200px)] w-full md:h-[calc(100vh-150px)] overflow-auto pr-4 sm:h-[calc(100vh-100px)]">
      <Form {...form}>
        <form onSubmit={handleSubmit(onCreateTopic)} className="grid gap-y-3 space-y-3">
          {["ADMIN", "TRAINER"]?.includes(userRole) && (
            <>
              <FormInput
                dataTestID="resource_name"
                placeholder="Enter Topic"
                label="Topic"
                fieldControlName="resource_name"
                control={control}
                isRequired
              />

              <FormSelect
                dataTestID="resource_type"
                dataTestIDError="error"
                fieldControlName="resource_type"
                control={control}
                label="Type"
                isRequired
                placeholder="Select Type"
                iterateData={fileType}
              />

              <AppointmentScheduler
                dataTestID="test-id"
                dataTestIDError="error"
                fieldControlName="resource_date"
                control={control}
                label="Schedule Date & Time"
                placeholder="Schedule date & time"
                isRequired
                blockPastDates
              />

              <FormTextArea
                dataTestID="notes"
                dataTestIDError="error"
                fieldControlName="notes"
                control={control}
                label="Notes"
                placeholder="Enter Notes"
              />

              <FormTextArea
                dataTestID="resource_description"
                dataTestIDError="error"
                fieldControlName="resource_description"
                control={control}
                label="Description"
                isRequired
                placeholder="Enter Description"
              />
              <div className="mb-2">
                {resourceType !== "LINK" ? (
                  <FileUploadCard
                    descriptions="Upload files"
                    fileType={resourceType === "VIDEO" ? "video" : "all-docs"}
                    setValue={setValue}
                    onChange={onChangeFile}
                    value={documentData}
                  />
                ) : (
                  <div>
                    <Label>
                      Meet Link <span className="text-muted text-xs"> (or Upload Docs)</span>
                    </Label>
                    <FormInput
                      dataTestID="test-id"
                      dataTestIDError="error"
                      placeholder="add link"
                      label=""
                      fieldControlName="resource_link"
                      control={control}
                      isRequired={false}
                      disabled={resourceType !== "LINK"}
                    />
                  </div>
                )}

                {/* <p className="text-slate-400 text-xs w-5 font-semibold mx-auto my-4">(or)</p> */}
              </div>
            </>
          )}

          {userRole === USER_ROLES.STUDENT && (
            <div>
              <Label className="mr-2">Feeback </Label>{" "}
              <FormTextArea
                placeholder="Enter Feedback"
                label=""
                fieldControlName="feedback"
                control={control}
                isRequired={false}
              />
            </div>
          )}

          <div className="flex justify-end gap-x-3 pt-12">
            <Button type="submit" variant="primary">
              {isTopicEdit ? "Update & Save" : "Create"}
            </Button>
            <Button onClick={onClose} variant="secondary" type="reset">
              Cancel
            </Button>
          </div>
        </form>
      </Form>
    </ScrollArea>
  )
}

CreateCourseTopicUI.propTypes = {
  form: PropTypes.objectOf.isRequired,
  control: PropTypes.objectOf.isRequired,
  onCreateTopic: PropTypes.func.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  setValue: PropTypes.func.isRequired,
  isTopicEdit: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  modulesData: PropTypes.string,
  topicsData: PropTypes.string,
  selectedValue: PropTypes.string,
  setSelectedValue: PropTypes.func,
  comboIsOpen: PropTypes.bool,
  setComboIsOpen: PropTypes.func,
  userRole: PropTypes.string,
  watch: PropTypes.func,
  onChangeFile: PropTypes.func,
  documentData: PropTypes.oneOfType([
    PropTypes.instanceOf(File),
    PropTypes.arrayOf(PropTypes.instanceOf(File)),
  ]),
}

export default CreateCourseTopicUI
