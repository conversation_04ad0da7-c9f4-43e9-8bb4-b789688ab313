import { DeleteDialog } from "@/components/custom/dialogs/delete.dialog"
import {
  failureToast,
  loadingToast,
  successToast,
  warningToast,
  dismissLoadingToast,
} from "@/components/custom/toasts/tosters"
import { useUploadDocs } from "@/hooks/common.hooks"
import CustomSidebar from "@/pages/courses/components/custom-sidebar"
import {
  useCreateCourseToicMutation,
  useDeleteCourseTopicMutation,
  useUpdateCourseTopicMutation,
} from "@/services/query/course-content.query"
import PropTypes from "prop-types"
import { useEffect, useRef } from "react"
import { useParams } from "react-router-dom"
import CreateCourseTopicUI from "./create-topic"

const CourseTopic = ({
  userRole,
  isTopicSidebarOpen,
  setIsTopicSidebarOpen,
  isTopicEdit,
  isTopiceDeleteOpen,
  setisTopiceDeleteOpen,
  form,
  selectedIds,
}) => {
  const {
    handleSubmit,
    control,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = form

  console.log("__errors", errors)
  const courseId = useParams()
  const { id } = courseId
  console.log("course_id", id)

  const isLoadingToastShown = useRef(false)

  const { mutate: createTopicMutation, isPending: createTopicPending } =
    useCreateCourseToicMutation()
  const { mutate: deleteTopicMutation } = useDeleteCourseTopicMutation()
  const {
    mutate: updateTopicMutation,
    isPending: editTopicPending,
    isError: editTopicError,
  } = useUpdateCourseTopicMutation()

  const { documentData, handleChangeFile, handleResetFile } = useUploadDocs()

  const handleCancelForm = () => {
    reset(null)
    setIsTopicSidebarOpen(false)
  }
  console.log("editTopicError", editTopicError)

  // Handle loading toast display
  useEffect(() => {
    if ((createTopicPending || editTopicPending) && !isLoadingToastShown.current) {
      // Show loading toast only if not already shown
      loadingToast("Processing", `${isTopicEdit ? "Updating" : "Creating"} topic...`)
      isLoadingToastShown.current = true
    } else if (!createTopicPending && !editTopicPending && isLoadingToastShown.current) {
      // Reset the flag when mutations complete
      isLoadingToastShown.current = false
    }
  }, [createTopicPending, editTopicPending, isTopicEdit])

  const handleCreateTopic = (data) => {
    console.log(data, "formatedPayload")
    const { resource_link, resource_date, resource_type, docs, ...rest } = data

    if (!resource_link && !docs) {
      warningToast("Upload Resource link or document", "")
      return
    }

    const formatedData = {
      resource_link: resource_link?.length > 0 ? resource_link : null,
      resource_type: resource_type?.toUpperCase(),
      resource_date,
      file: docs?.[0],
      ...rest,
    }

    const formData = new FormData()
    formData.append("data", JSON.stringify(formatedData))
    if (documentData?.[0]) {
      formData.append("file", !documentData?.[0] ? null : documentData?.[0])
    }

    let params = {}
    if (selectedIds?.topic_id) {
      params = {
        course_id: id,
        module_id: selectedIds?.module_id,
        resource_id: selectedIds?.topic_id,
        formData,
      }
    } else {
      params = {
        course_id: id,
        module_id: selectedIds?.module_id,
        formData,
      }
    }

    setIsTopicSidebarOpen(false)

    const mutationHandler = (mutation, successMessage, errorMessage) => {
      console.log("mutationHandler", errorMessage)

      mutation(
        { ...params },
        {
          onSuccess: () => {
            // Dismiss loading toast and reset flag
            dismissLoadingToast()
            isLoadingToastShown.current = false

            successToast(
              successMessage,
              `The topic has been successfully ${successMessage.toLowerCase()}!`
            )
            handleResetFile()
          },

          onError: () => {
            // Dismiss loading toast and reset flag
            dismissLoadingToast()
            isLoadingToastShown.current = false

            handleResetFile()
            failureToast(
              errorMessage,
              `An error occurred while ${errorMessage.toLowerCase()} the topic. Please try again.`
            )
          },
        }
      )
    }

    if (isTopicEdit) {
      mutationHandler(updateTopicMutation, "Updated", "Update Failed")
    } else {
      mutationHandler(createTopicMutation, "Created", "Creation Failed")
    }
  }

  const handleDeleteTopic = () => {
    deleteTopicMutation(
      { course_id: id, module_id: selectedIds?.module_id, resource_id: selectedIds?.topic_id },
      {
        onSuccess: () => {
          setisTopiceDeleteOpen(false)
          successToast("Topic Deleted", "The topic has been removed.")
        },
        onError: () => {
          setisTopiceDeleteOpen(false)
          failureToast("Deletion Failed", "Unable to delete the topic. Please try again later.")
        },
      }
    )
  }

  return (
    <div>
      <CustomSidebar
        title={`${isTopicEdit ? "Update" : "Create"} Topic`}
        description=""
        isOpen={isTopicSidebarOpen}
        onClose={() => setIsTopicSidebarOpen(!isTopicSidebarOpen)}
        content={
          <CreateCourseTopicUI
            form={form}
            reset={reset}
            control={control}
            onCreateTopic={handleCreateTopic}
            handleSubmit={handleSubmit}
            onClose={handleCancelForm}
            userRole={userRole}
            isTopicEdit={isTopicEdit}
            watch={watch}
            setValue={setValue}
            documentData={documentData}
            onChangeFile={handleChangeFile}
          />
        }
      />

      <DeleteDialog
        title="Delete Topic"
        onOpen={isTopiceDeleteOpen}
        onCancel={() => setisTopiceDeleteOpen(!isTopiceDeleteOpen)}
        onDelete={handleDeleteTopic}
      />
    </div>
  )
}

CourseTopic.propTypes = {
  userRole: PropTypes.oneOf(["admin", "teacher", "student"]).isRequired,
  isTopicSidebarOpen: PropTypes.bool.isRequired,
  setIsTopicSidebarOpen: PropTypes.func.isRequired,
  isTopicEdit: PropTypes.bool.isRequired,
  isTopiceDeleteOpen: PropTypes.bool.isRequired,
  setisTopiceDeleteOpen: PropTypes.func.isRequired,
  form: PropTypes.shape({
    formState: PropTypes.func.isRequired,
    handleSubmit: PropTypes.func.isRequired,
    reset: PropTypes.func.isRequired,
    control: PropTypes.func.isRequired,
    setValue: PropTypes.func.isRequired,
    watch: PropTypes.func.isRequired,
    getValues: PropTypes.func.isRequired,
  }).isRequired,

  // Selected IDs for editing/updating
  selectedIds: PropTypes.shape({
    module_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    topic_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  }),
}

// Default props (optional)
CourseTopic.defaultProps = {
  selectedIds: null,
}

export default CourseTopic
