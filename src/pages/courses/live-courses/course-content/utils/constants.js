export const topicsData = [
  {
    value: "next.js",
    label: "Next.js",
  },
  {
    value: "sveltekit",
    label: "SvelteKit",
  },
  {
    value: "nuxt.js",
    label: "Nuxt.js",
  },
  {
    value: "remix",
    label: "Remix",
  },
  {
    value: "astro",
    label: "Astro",
  },
]

export const modulesData = [
  {
    value: "next.js",
    label: "Next.js",
  },
  {
    value: "sveltekit",
    label: "SvelteKit",
  },
  {
    value: "nuxt.js",
    label: "Nuxt.js",
  },
  {
    value: "remix",
    label: "Remix",
  },
  {
    value: "astro",
    label: "Astro",
  },
]

export const modulesList = [
  {
    title: "Week 1: Introduction to CNNs",
    sections: [
      {
        title: "A conversation with <PERSON>",
        type: "video",
        duration: "1 min",
        isCompleted: true,
      },
      {
        title: "What are convolutions and pooling?",
        type: "video",
        duration: "2 min",
        isCompleted: true,
      },
    ],
  },
  {
    title: "Week 2: Implementation",
    sections: [
      {
        title: "Coding convolutions and pooling layers",
        type: "reading",
        duration: "10 min",
        isCompleted: false,
      },
      {
        title: "Implementing convolutional layers",
        type: "video",
        duration: "1 min",
        isCompleted: false,
      },
    ],
  },
  {
    title: "Week 3: Advanced Topics",
    sections: [
      {
        title: "Learn more about convolutions",
        type: "reading",
        duration: "1 min",
        isCompleted: false,
      },
      {
        title: "Implementing pooling layers",
        type: "video",
        duration: "4 min",
        isCompleted: false,
      },
      {
        title: "Getting hands-on, your first ConvNet",
        type: "reading",
        duration: "",
        isCompleted: false,
      },
    ],
  },
]
