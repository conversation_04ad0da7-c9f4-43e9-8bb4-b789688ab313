const videCell = {
  id: "meetlink",
  accessorKey: "meetlink",
  header: "Meet/Video Link",
}

export const courseContentAdminColumns = [
  {
    id: "Sno",
    accessorKey: "Sno",
    header: "Topic No",
  },
  {
    id: "trainer",
    accessor<PERSON>ey: "trainer",
    header: "Trainer",
  },
  {
    id: "module",
    accessorKey: "module_name",
    header: "Module",
  },
  {
    id: "topic",
    accessorKey: "topic",
    header: "Topic",
  },

  {
    id: "notes",
    accessorKey: "notes",
    header: "Notes",
  },
  {
    id: "actions",
    accessorKey: "actions",
    header: "Actions",
  },
  videCell,
]

export const courseContentTrainerColumns = [
  {
    id: "Sno",
    accessorKey: "Sno",
    header: "Topic No",
  },
  {
    id: "trainer",
    accessorKey: "trainer",
    header: "Trainer",
  },
  {
    id: "module",
    accessorKey: "module_name",
    header: "Module",
  },
  {
    id: "topic",
    accessorKey: "topic",
    header: "Topic",
  },
  {
    id: "notes",
    accessorKey: "notes",
    header: "Notes",
  },
  {
    id: "actions",
    accessorKey: "actions",
    header: "Actions",
  },
  videCell,
]

export const courseContentStudentColumns = [
  {
    id: "Sno",
    accessorKey: "Sno",
    header: "Topic No",
  },
  {
    id: "trainer",
    accessorKey: "trainer",
    header: "Trainer",
  },
  {
    id: "module",
    accessorKey: "module_name",
    header: "Module",
  },
  {
    id: "topic",
    accessorKey: "topic",
    header: "Topic",
  },
  {
    id: "notes",
    accessorKey: "notes",
    header: "Notes",
  },
  {
    id: "feedback",
    accessorKey: "feedback",
    header: "Feedback",
  },
  {
    id: "actions",
    accessorKey: "actions",
    header: "Actions",
  },
  videCell,
]

export const getColumns = (userType) => {
  switch (userType) {
    case "trainer":
      return courseContentTrainerColumns
    case "admin":
      return courseContentAdminColumns
    default:
      return courseContentStudentColumns
  }
}
