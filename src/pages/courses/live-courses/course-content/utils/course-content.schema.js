import { z } from "zod"
import moment from "moment"

export const createTopicSchema = z
  .object({
    resource_name: z
      .string({ required_error: "Topic is required" })
      .min(1, "Topic is required")
      .trim(),

    notes: z.string().optional(),

    resource_link: z
      .string()
      .optional()
      .refine(
        (val) => {
          // If empty or undefined, it's valid (optional)
          if (!val || val.trim() === "") return true
          // If has value, must be valid URL
          try {
            new URL(val)
            return true
          } catch {
            return false
          }
        },
        {
          message: "Please enter a valid URL (e.g., https://example.com)",
        }
      ),

    resource_date: z
      .preprocess(
        (val) => {
          // Handle null, undefined, or empty string
          if (!val || val === "") return null
          return typeof val === "string" ? new Date(val) : val
        },
        z.date({ required_error: "Start date is required" }).nullable()
      )
      .refine((val) => val !== null, {
        message: "Start date is required",
      }),
    // Uncomment if you want to validate future dates
    // .refine((val) => val && moment(val).isSameOrAfter(moment(), "minute"), {
    //   message: "Start date cannot be in the past",
    // }),

    resource_type: z
      .string({ required_error: "Resource type is required" })
      .min(1, "Resource type is required"),

    resource_description: z
      .string({ required_error: "Description is required" })
      .min(15, "Description must be at least 15 characters")
      .trim(),

    docs: z.any().optional(),
  })
  .superRefine((data, ctx) => {
    console.log("__schema", data, ctx)

    if (
      data.resource_type === "link" &&
      (!data.resource_link || data.resource_link.trim() === "")
    ) {
      ctx.addIssue({
        path: ["resource_link"],
        message: "Resource link is required when resource type is 'link'",
      })
    }

    if (data.resource_type === "docus" && !data.docs) {
      ctx.addIssue({
        path: ["docs"], // Changed from "file" to "docs" to match the field name
        message: "File is required when resource type is 'docus'",
      })
    }
  })

export const createTopicDefaultValues = {
  resource_name: "",
  notes: "",
  resource_type: "LINK",
  resource_link: "",
  resource_date: new Date().toISOString(),
  resource_description: "",
  docs: null,
}

export const createModuleSchema = z.object({
  module_name: z
    .string({ required_error: "Module is required" })
    .min(1, "Module is required")
    .trim(),

  module_description: z
    .string({ required_error: "Description is required" })
    .min(15, "The description should be at least 15 characters")
    .trim(),

  duration_minutes: z
    .string({ required_error: "Duration is required" })
    .min(1, "Duration cannot be empty")
    .transform((val) => {
      const num = Number(val)
      if (isNaN(num) || num <= 0) {
        throw new Error("Duration must be a valid positive number")
      }
      return num
    }),

  sequence_number: z.string().optional(),
})

export const createModuleSchemaDefaultValues = {
  module_name: "",
  sequence_number: "10",
  module_description: "",
  duration_minutes: "",
}
