import { USER_ROLES } from "@/utils/constants"

export const displayTheCourseTopics = (userRole, isUpdate) => {
  const isTrainerOrAdmin = userRole === USER_ROLES.ADMIN || userRole === USER_ROLES.TRAINER

  if (isTrainerOrAdmin) {
    return isUpdate ? "Edit Course Topics" : "Add Course Topics"
  }

  return isUpdate ? "Edit Feedback" : "Add Feedback"
}

export const resetCourseFormValues = (reset, isModule) => {
  if (isModule) {
    return reset({
      resource_name: "",
      resource_description: "",
      duration_minutes: "",
      resource_type: "",
      notes: "",
      resource_link: "",
    })
  }

  return reset({
    resource_name: "",
    resource_description: "",
    resource_type: "",
    notes: "",
    resource_link: "",
  })
}
