import { CustomSelect } from "@/components/custom/custom-select"
import { DeleteDialog } from "@/components/custom/dialogs/delete.dialog"
import { GeneralDialog } from "@/components/custom/dialogs/general.dialog"
import { deleteToast, failureToast, successToast } from "@/components/custom/toasts/tosters"
import { Button } from "@/components/ui/button"
import {
  useDeleteDialogHooks,
  useFilterHooks,
  useIsEditDialogHook,
  useIsUpdateHook,
  useOpenCloseHooks,
  usePaginationHooks,
  usePublishStatus,
  useSelectedIds,
} from "@/hooks/common.hooks"
import { useDebounce } from "@/hooks/useDebounce"
import { useGetModuleResource } from "@/services/query/live-course.query"
import {
  useCreateQuizMutation,
  useDeleteQuizMutaion,
  useFetchQuizDetails,
  useUpdateQuizMutation,
  useUpdateQuizSubmissionStatus,
  useUpdateStatusMutation,
} from "@/services/query/quiz.query"
import { publishStatus, USER_ROLES } from "@/utils/constants"
import { getOffset, isStudent } from "@/utils/helper"
import ct from "@constants/"
import { zodResolver } from "@hookform/resolvers/zod"
import { ArrowLeft } from "lucide-react"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { useSelector } from "react-redux"
import { useLocation, useNavigate, useParams } from "react-router-dom"
import CustomSidebar from "../../components/custom-sidebar"
import { displayTheHeadings } from "../utils/helper"
import QuizCreationUpdaionUI from "./quiz-creation-updation.ui"
import QuizFormSubmit from "./quiz-form-submit"
import QuizGradingForm from "./quiz-sidebar"
import QuizViewUI from "./quiz-view.ui"
import QuizUI from "./quiz.ui"
import { getColumns } from "./utils/columns"
import { defaultQuizValues, quizSchema } from "./utils/quiz.schema"

function Quiz() {
  // store\

  const userRole = useSelector((st) => st[ct.store.USER_STORE]?.userRole)
  const course = useSelector((st) => st[ct.store.COURSES]?.activeCourseDetails)
  const courseDet = useSelector((st) => st[ct.store.COURSES])
  const courseId = useParams()

  // hooks
  const [searchingValue, setSearchingValue] = useState("")
  const [activeColumns, setActiveColumns] = useState(getColumns(userRole))

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })
  const [selectedQuiz, setSelectedQuiz] = useState(null)
  const [selectedModule, setSelectedModule] = useState("")

  // custom hooks
  const debouncedSearchQuery = useDebounce(searchingValue, 500)
  const { handleOpen, handleClose, open } = useOpenCloseHooks()
  const { isUpdate, handleResetUpdate, handleUpdate } = useIsUpdateHook()
  const { isDelete, handleDelete, handleCancel } = useDeleteDialogHooks(false)
  const { isEditDialog, handleOpenEditDialog, handleResetEditDialog } = useIsEditDialogHook()
  const { publishedStatus, handleSetPublishStatus } = usePublishStatus()
  const { sortBy, sortByField } = useFilterHooks()
  const { limit } = usePaginationHooks()
  const { selectedIDs, setSelectedIDs } = useSelectedIds()
  const [moduleName, setModuleName] = useState("")
  const [isSubmitSidebarOpen, setIsSubmitSidebarOpen] = useState(false)
  const [isGradingSidebarOpen, setIsGradingSidebarOpen] = useState(false)

  const navigate = useNavigate()
  const location = useLocation()
  const [quizIDs, setQuizIDs] = useState({
    module_id: location?.state?.module_id,
    course_id: location?.state?.course_id,
    quiz_id: "",
  })

  const [selectedSubmission, setSelectedSubmission] = useState(null)

  const form = useForm({
    resolver: zodResolver(quizSchema),
    defaultValues: defaultQuizValues,
    mode: "onChange",
  })

  const { data: moduleResourceData, isLoading: moduleLoading } = useGetModuleResource(
    typeof courseId?.id === "number" ? courseId?.id : parseInt(courseId?.id, 10)
  )
  const { mutate: deleteQuizMutate } = useDeleteQuizMutaion()
  const { mutate: createQuizMutate } = useCreateQuizMutation()
  const { mutate: updateQuizMutate } = useUpdateQuizMutation()
  const { mutate: updateQuizStusMutate } = useUpdateStatusMutation()
  const { mutate: updateQuizSubmission } = useUpdateQuizSubmissionStatus()

  const { data: listOfQuiz, isLoading } = useFetchQuizDetails({
    course_id: course?.id || 0,
    module_id: selectedModule?.id || 0,

    filters: {
      quiz_id: null,
      limit,
      offset: getOffset(pagination.pageIndex, pagination.pageSize),
      sort_by: sortBy,
      sort_by_field: sortByField,
      search_query: debouncedSearchQuery?.length > 0 ? debouncedSearchQuery : null,
    },
  })

  useEffect(() => {
    if (moduleResourceData?.get_module_resource?.modules) {
      setModuleName(moduleResourceData?.get_module_resource?.modules)
    }
  }, [moduleResourceData, moduleLoading])

  const { handleSubmit, control, setValue, reset } = form

  const handleQuizCreation = (data) => {
    const isUpdating = Boolean(isUpdate)
    const mutationFn = isUpdating ? updateQuizMutate : createQuizMutate
    const quizPayload = {
      data,
      course_id: course?.id,
      module_id: selectedModule?.id,
      ...(isUpdating && { quiz_id: quizIDs?.quiz_id }),
    }

    mutationFn(quizPayload, {
      onSuccess: () => {
        handleClose()
        successToast(
          `Quiz ${isUpdating ? "Updated" : "Created"}`,
          `The quiz has been successfully ${isUpdating ? "updated" : "created"}!`
        )
        if (isUpdating) handleResetUpdate()
      },
      onError: () => {
        handleClose()
        failureToast(
          `Quiz ${isUpdating ? "Update" : "Creation"} Failed`,
          `An error occurred while ${isUpdating ? "updating" : "creating"} the quiz. Please try again.`
        )
        if (isUpdating) handleResetUpdate()
      },
    })
  }

  const handleQuizCreationSidebar = () => {
    handleResetUpdate()
    reset({
      title: "",
      dead_line: null,
      resource_link: "",
      passing_marks: "",
      total_marks: "",
      description: "",
      publish_status: "DRAFT",
    })
    handleOpen()
  }

  const handleQuizSubmisson = (data) => {
    setQuizIDs({
      ...quizIDs,
      quiz_id: data,
    })
    setIsSubmitSidebarOpen(true)
  }

  const handleCancelForm = () => {
    handleClose()
    reset()
  }

  const handleOpenQuizStatusDialog = (data) => {
    const { id, publish_status } = data?.original || {}
    handleSetPublishStatus(publish_status)
    setSelectedIDs({
      ...selectedIDs,
      quizID: id,
    })
    handleOpenEditDialog()
  }

  // Handle opening submission status dialog
  const handleStatusUpdate = (data) => {
    const { id, acceptance_status } = data?.original || {}
    handleSetPublishStatus(acceptance_status || "PENDING")
    setSelectedIDs({
      ...selectedIDs,
      submissionId: id,
    })
  }

  // Update quiz status
  const handleUpdateQuizStatus = () => {
    updateQuizStusMutate(
      {
        course_id: course?.id,
        module_id: selectedModule?.id,
        quiz_id: selectedIDs.quizID,
        status: publishedStatus,
      },
      {
        onSuccess: () => {
          handleResetEditDialog()
          successToast("Quiz Status Updated", "The quiz status has been successfully updated!")
        },
        onError: () => {
          handleResetEditDialog()
          failureToast(
            "Quiz Status Update Failed",
            "An error occurred while updating the quiz status. Please try again."
          )
        },
      }
    )
  }

  // Open grading sidebar and set the selected submission
  const handleOpenGradingForm = (submission) => {
    setSelectedSubmission(submission.original)
    setIsGradingSidebarOpen(true)
  }

  // Handle grading form submission
  const handleGradingSubmit = (data) => {
    updateQuizSubmission(
      {
        course_id: course?.id,
        module_id: selectedModule?.id,
        quiz_tracking_id: selectedSubmission.id,
        data: {
          marks_obtained: data.marks_obtained,
          trainer_feedback: data.trainer_feedback,
          acceptance_status: data.acceptance_status,
        },
      },
      {
        onSuccess: () => {
          setIsGradingSidebarOpen(false)
          successToast("Quiz Graded", "The quiz has been successfully graded!")
        },
        onError: () => {
          setIsGradingSidebarOpen(false)
          failureToast(
            "Grading Failed",
            "An error occurred while grading the quiz. Please try again."
          )
        },
      }
    )
  }

  const handleDeleteQuiz = (data) => {
    setQuizIDs({ ...quizIDs, quiz_id: data?.original?.id })
    handleDelete()
  }

  const handleConfirmDelete = () => {
    deleteQuizMutate(
      { course_id: course?.id, module_id: selectedModule?.id, quiz_id: quizIDs?.quiz_id },
      {
        onSuccess: () => {
          successToast("Quiz Deleted", "The quiz has been successfully deleted!")
          handleCancel()
        },
        onError: () => {
          deleteToast(
            "Quiz Deletion Failed",
            "An error occurred while deleting the quiz. Please try again."
          )
        },
      }
    )
  }

  const handleEdit = (row) => {
    const { dead_line, passing_marks, total_marks, id, ...rest } = row?.original ?? {}

    let formattedDueDate = dead_line
    if (dead_line) {
      // Parse the original date string
      const date = new Date(dead_line)
      // Format to ISO string (will give UTC time)
      formattedDueDate = date.toISOString()
    }

    setQuizIDs({ ...quizIDs, quiz_id: id })
    handleUpdate()
    reset({
      dead_line: formattedDueDate,
      passing_marks: passing_marks?.toString(),
      total_marks: total_marks?.toString(),
      ...rest,
    })
    handleOpen()
  }

  const handleSubmission = (row) => {
    setQuizIDs({ ...quizIDs, quiz_id: row })
    if (userRole === USER_ROLES.ADMIN || userRole === USER_ROLES.TRAINER) {
      navigate(`${ct.route.COURSE_OVERVIEW}/${ct.route.QUIZ_VIEW}/${row}`, {
        state: {
          quizData: row,
          module_id: selectedModule?.id,
          course_id: course?.id,
        },
      })

      return
    }
    handleOpen()
  }

  const quizProps = {
    form,
    control,
    handleSubmit,
    onCreations: handleQuizCreation,
    setValue,
    onClose: handleCancelForm,
    isUpdate,
    userRole,
    moduleName,
  }

  const handleNavigate = () => {
    navigate(-1)
  }

  const handleSelectedModuleId = (id) => {
    setQuizIDs({
      ...quizIDs,
      module_id: id,
    })
  }

  const isQuizView = location.pathname?.includes("quiz-view")

  return (
    <div>
      {isQuizView && (
        <div className="flex justify-between items-center">
          <h6 className="text-primary text-xl">Submissions</h6>
          <Button
            className="flex font-semibold gap-x-1 group items-center"
            onClick={handleNavigate}
          >
            <ArrowLeft
              size={15}
              className="font-semibold group-hover:-translate-x-1 transform transition-transform"
            />
            Back
          </Button>
        </div>
      )}
      <DeleteDialog
        onOpen={isDelete}
        onDelete={handleConfirmDelete}
        onCancel={handleCancel}
        title="Delete Quiz"
        content="Are you sure want to delete this Quiz?"
      />

      {isQuizView ? (
        <QuizViewUI
          handleNavigate={handleNavigate}
          setPagination={setPagination}
          pagination={pagination}
          handleStatusUpdate={handleStatusUpdate}
          handleEdit={handleOpenGradingForm}
          isPaidUser={isStudent(courseDet?.activeCourse, userRole)}
        />
      ) : (
        <QuizUI
          onQuizCreation={handleQuizCreationSidebar}
          onUpdateStatus={handleOpenQuizStatusDialog}
          handleDelete={handleDeleteQuiz}
          handleEdit={handleEdit}
          userType={userRole}
          onViewSubmission={handleSubmission}
          activeColumns={activeColumns}
          setActiveColumns={setActiveColumns}
          listOfQuiz={listOfQuiz?.data}
          isLoading={isLoading}
          pagination={pagination}
          setPagination={setPagination}
          searchingValue={searchingValue}
          setSearchingValue={setSearchingValue}
          handleQuizSubmisson={handleQuizSubmisson}
          moduleName={moduleName}
          setSelectedModuleId={handleSelectedModuleId}
          selectedModule={selectedModule}
          selectedQuiz={selectedQuiz}
          setSelectedQuiz={setSelectedQuiz}
          setSelectedModule={setSelectedModule}
          isPaidUser={isStudent(courseDet?.activeCourse, userRole)}
        />
      )}

      {/* Quiz Creation/Editing Sidebar */}
      <CustomSidebar
        title={displayTheHeadings(
          userRole,
          isUpdate,
          "Update Quiz",
          "Create Quiz",
          "Resubmit Quiz",
          "Submit Quiz"
        )}
        description=""
        isOpen={open}
        onClose={handleClose}
        content={<QuizCreationUpdaionUI {...quizProps} />}
      />

      {/* Quiz Status Update Dialog */}
      <GeneralDialog
        onOpen={isEditDialog}
        title="Update Quiz Status"
        onClickCTA={handleUpdateQuizStatus}
        ctaLabel="Update Status"
        onCancel={handleResetEditDialog}
        ctaPosition="justify-end"
        content={
          <CustomSelect
            publishedStatus={publishedStatus}
            setPublishedStatus={handleSetPublishStatus}
            iteratedData={publishStatus}
          />
        }
      />

      {/* Quiz Form Submit Sidebar */}
      <QuizFormSubmit
        isSubmitSidebarOpen={isSubmitSidebarOpen}
        setIsSubmitSidebarOpen={setIsSubmitSidebarOpen}
        quizIDs={quizIDs}
      />

      {/* Grading Sidebar - Using the component's internal form as provided */}
      <CustomSidebar
        title="Grade Quiz Submission"
        description="Provide marks and feedback for this submission"
        isOpen={isGradingSidebarOpen}
        onClose={() => setIsGradingSidebarOpen(false)}
        content={
          <QuizGradingForm
            onSubmit={handleGradingSubmit}
            onClose={() => setIsGradingSidebarOpen(false)}
            initialData={selectedSubmission || {}}
          />
        }
      />
    </div>
  )
}
export default Quiz
