import DataTable from "@/components/custom/cutsom-table"
import {
  <PERSON><PERSON>ell,
  Date<PERSON>ell,
  LinkCell,
  RenderTableData,
  StatusUpdationCell,
} from "@/components/custom/cutsom-table/table-cells"
import useTableConfig from "@/hooks/use-table.hooks"
import { useGetQuizSubmissionsView } from "@/services/query/quiz.query"
import ct from "@constants/"
import { flexRender } from "@tanstack/react-table"
import PropTypes from "prop-types"
import { useEffect, useState } from "react"
import { useSelector } from "react-redux"
import { useLocation } from "react-router-dom"
import { quizViewColumns } from "./utils/columns"

const QuizViewUI = ({ handleStatusUpdate, handleEdit, setPagination, pagination, isPaidUser }) => {
  const location = useLocation()
  const [quizSubmisson, setQuizSubmisson] = useState([])
  const course = useSelector((st) => st[ct.store.COURSES]?.activeCourseDetails)
  const quizId = location.state?.quizData
  const moduleId = location.state?.module_id
  const courseId = location.state?.course_id || course?.id

  const { data: quizSubmissionsData } = useGetQuizSubmissionsView({
    course_id: courseId || course?.id,
    module_id: moduleId,
    quiz_id: quizId,
  })

  useEffect(() => {
    if (quizSubmissionsData?.data) {
      setQuizSubmisson(quizSubmissionsData.data)
    }
  }, [quizSubmissionsData])

  const renderCellContent = (cell, row) => {
    const {
      id,
      student_name,
      submission_time,
      submission,
      submission_status,
      submission_type,
      trainer_feedback,
      student_feedback,
      acceptance_status,
      marks_obtained,
    } = row?.original || {}

    switch (cell.column.id) {
      case "student_id":
        return <RenderTableData content={student_name} />
      case "submission_time":
        return <DateCell value={submission_time} />
      case "submission":
        return <LinkCell value={submission} label="View Submission" />
      case "submission_status":
        return <StatusUpdationCell value={submission_status} key={id} />
      case "submission_type":
        return <RenderTableData content={submission_type} />
      case "trainer_feedback":
        return <RenderTableData content={trainer_feedback || "No feedback yet"} />
      case "student_feedback":
        return <RenderTableData content={student_feedback || "No feedback yet"} />
      case "acceptance_status":
        return <StatusUpdationCell value={acceptance_status} key={id} />
      case "marks_obtained":
        return <RenderTableData content={marks_obtained || "Not graded"} />
      case "actions":
        return (
          <ActionCell
            label2="Grade Quiz"
            row={row}
            isEdit
            onView={handleStatusUpdate}
            onEdit={handleEdit}
          />
        )
      default:
        return flexRender(cell.column.columnDef.cell, cell.getContext())
    }
  }

  const { table, found, pageCount } = useTableConfig(
    quizSubmisson?.data,
    quizViewColumns,
    quizSubmisson?.metadata?.total_records,
    setPagination,
    pagination
  )

  return (
    <DataTable
      renderCellContent={renderCellContent}
      columns={quizViewColumns}
      table={table}
      found={found}
      pageName="Quiz Submission"
      pageCount={pageCount}
      pagination={pagination}
      notFoundPlaceholder="No Submissions Found"
    />
  )
}

QuizViewUI.propTypes = {
  handleStatusUpdate: PropTypes.func.isRequired,
  handleEdit: PropTypes.func.isRequired,

  setPagination: PropTypes.func.isRequired,
  pagination: PropTypes.shape({
    pageIndex: PropTypes.number,
    pageSize: PropTypes.number,
  }),
}

export default QuizViewUI
