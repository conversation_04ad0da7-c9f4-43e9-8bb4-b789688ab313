import { USER_ROLES } from "@/utils/constants"

// Trainer columns
export const commonColumns = [
  {
    id: "title",
    accessorKey: "title",
    header: "Title",
    type: "trainer",
  },
  {
    id: "link",
    accessorKey: "resource_link",
    header: "Link",
    type: "trainer",
  },

  {
    id: "submissions",
    accessorKey: "submissions",
    header: "Submissions",
    type: "trainer",
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Status",
    type: "trainer",
  },

  {
    id: "dead_line",
    accessorKey: "dead_line",
    header: "Deadline",
    type: "trainer",
  },
  {
    id: "actions",
    accessorKey: "actions",
    header: "Actions",
    type: "trainer",
  },
]

// Student columns
export const studentColumns = [
  {
    id: "title",
    accessorKey: "title",
    header: "Title",
    type: "student",
  },
  {
    id: "link",
    accessorKey: "resource_link",
    header: "Link",
    type: "trainer",
  },

  {
    id: "mark_obtained",
    accessorKey: "mark_obtained",
    header: "Marks Obtained",
    type: "student",
  },

  {
    id: "remarks",
    accessorKey: "remarks",
    header: "Remarks",
    type: "student",
  },

  {
    id: "dead_line",
    accessorKey: "dead_line",
    header: "Deadline",
    type: "student",
  },
  {
    id: "submission",
    accessorKey: "actions",
    header: "Submissions",
    type: "student",
  },
]

export const quizViewColumns = [

  {
    id: "student_name",
    accessorKey: "student_name",
    header: "Student Name",
    type: "quiz",
  },

  {
    id: "submission_time",
    accessorKey: "submission_time",
    header: "Submission Time",
    type: "quiz",
  },
  {
    id: "submission",
    accessorKey: "submission",
    header: "Submission Link",
    type: "quiz",
  },
  {
    id: "submission_status",
    accessorKey: "submission_status",
    header: "Status",
    type: "quiz",
  },
  {
    id: "submission_type",
    accessorKey: "submission_type",
    header: "Type",
    type: "quiz",
  },
  {
    id: "acceptance_status",
    accessorKey: "acceptance_status",
    header: "Acceptance",
    type: "quiz",
  },
  {
    id: "marks_obtained",
    accessorKey: "marks_obtained",
    header: "Marks Obtained",
    type: "quiz",
  },
  {
    id: "trainer_feedback",
    accessorKey: "trainer_feedback",
    header: "Trainer Feedback",
    type: "quiz",
  },
  {
    id: "actions",
    accessorKey: "actions",
    header: "Actions",
    type: "quiz",
  },
]

// You can use these columns based on the user type
export const getColumns = (userType, isView) => {
  if ([USER_ROLES.TRAINER, USER_ROLES.ADMIN]?.includes(userType)) {
    return isView ? quizViewColumns : commonColumns
  }
  return studentColumns
}
