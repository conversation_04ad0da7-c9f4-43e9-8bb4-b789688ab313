import moment from "moment"
import { z } from "zod"

export const quizSchema = z
  .object({
    title: z.string({ required_error: "Title is required" }).min(5, "Title is required").trim(),

    dead_line: z
      .preprocess(
        (val) => {
          // Handle null, undefined, or empty string
          if (!val || val === "") return null
          return typeof val === "string" ? new Date(val) : val
        },
        z.date({ required_error: "Deadline is required" }).nullable()
      )
      .refine((val) => val !== null, {
        message: "Deadline is required",
      })
      .refine((val) => val && moment(val).isSameOrAfter(moment(), "minute"), {
        message: "Deadline cannot be in the past",
      }),

    resource_link: z
      .string({ required_error: "Quiz link is required" })
      .min(5, "Quiz link is required")
      .trim()
      .url("Please enter a valid URL (e.g., https://example.com)"),

    passing_marks: z
      .string({ required_error: "Passing marks are required" })
      .min(1, "Passing marks are required")
      .transform((val) => {
        const num = Number(val)
        if (isNaN(num) || num < 0) {
          throw new Error("Passing marks must be a valid positive number")
        }
        return num
      }),

    total_marks: z
      .string({ required_error: "Total marks are required" })
      .min(1, "Total marks are required")
      .transform((val) => {
        const num = Number(val)
        if (isNaN(num) || num <= 0) {
          throw new Error("Total marks must be a valid positive number")
        }
        return num
      }),

    description: z.string().optional(),

    publish_status: z.string().optional(),
  })
  .superRefine((data, ctx) => {
    if (data.passing_marks > data.total_marks) {
      ctx.addIssue({
        code: "custom",
        message: "Passing marks cannot be greater than total marks",
        path: ["passing_marks"],
      })
    }
  })

export const defaultQuizValues = {
  title: "",
  dead_line: null,
  resource_link: "",
  passing_marks: "",
  total_marks: "",
  description: "",
  publish_status: "DRAFT",
}
