import AppointmentScheduler from "@/components/custom/custom-forms/date-time-picker"
import FormInput from "@/components/custom/custom-forms/form-input"
import FormTextArea from "@/components/custom/custom-forms/form-textarea"
import { formsProps } from "@/components/custom/custom-forms/utils/props-type"
import { Button } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { ScrollArea } from "@/components/ui/scroll-area"
import { USER_ROLES } from "@/utils/constants"
import { displayTheHeadings } from "../utils/helper"

const QuizCreationUpdaionUI = ({
  form,
  control,
  onCreations,
  handleSubmit,
  isUpdate,
  onClose,
  userRole,
}) => {
  return (
    <ScrollArea className="overflow-y-auto w-full rounded-md pr-2">
      <Form {...form}>
        <form onSubmit={handleSubmit(onCreations)} className="space-y-5 p-2 lg:mb-8">
          {[USER_ROLES.TRAINER, USER_ROLES.ADMIN]?.includes(userRole) && (
            <>
              <FormInput
                dataTestID="test-id"
                dataTestIDError="error"
                fieldControlName="title"
                control={control}
                label="Title"
                placeholder="Enter Title"
                isRequired
              />
              <FormInput
                dataTestID="test-id"
                dataTestIDError="error"
                fieldControlName="resource_link"
                control={control}
                label="Quiz Link"
                placeholder="Enter Quiz Link"
                isRequired
              />
              <FormInput
                dataTestID="test-id"
                dataTestIDError="error"
                fieldControlName="total_marks"
                control={control}
                label="Total Marks"
                placeholder="0"
                isTypeNumer
                isRequired
              />
              <FormInput
                dataTestID="test-id"
                dataTestIDError="error"
                fieldControlName="passing_marks"
                control={control}
                label="Passing Marks"
                placeholder="0"
                isTypeNumer
                isRequired
              />

              <AppointmentScheduler
                dataTestID="test-id"
                dataTestIDError="error"
                fieldControlName="dead_line"
                control={control}
                label="Submission date"
                placeholder="Pick a date"
                futureDateDisabled={null}
                isRequired
              />
              {/* 
              <FormDate
                dataTestID="test-id"
                dataTestIDError="error"
                fieldControlName="dead_line"
                control={control}
                label="Submission date"
                placeholder="Pick a date"
                futureDateDisabled={null}
                // isRequired
              /> */}
              <FormTextArea
                dataTestID="test-id"
                dataTestIDError="error"
                fieldControlName="description"
                control={control}
                label="Description"
                placeholder="Enter Description"
              />
            </>
          )}
          <div className="p t-12 flex gap-x-3 justify-end">
            <Button onClick={onClose} variant="secondary" type="reset">
              Cancel
            </Button>
            <Button type="submit" variant="primary">
              {displayTheHeadings(userRole, isUpdate, "Update", "Create", "Submit", "Submit")}
            </Button>
          </div>
        </form>
      </Form>
    </ScrollArea>
  )
}

QuizCreationUpdaionUI.propTypes = formsProps

export default QuizCreationUpdaionUI
