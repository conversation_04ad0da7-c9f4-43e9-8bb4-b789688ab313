import CustomComboBox from "@/components/custom/custom-combo-box/custom-combo-box"
import CustomSearchbar from "@/components/custom/custom-search"
import DataTable from "@/components/custom/cutsom-table"
import {
  ActionCell,
  DateCell,
  LinkCell,
  RenderTableData,
  StatusUpdationCell,
  SubmissionButton,
} from "@/components/custom/cutsom-table/table-cells"
import { Button } from "@/components/ui/button"
import useTableConfig from "@/hooks/use-table.hooks"
import { USER_ROLES } from "@/utils/constants"
import { flexRender } from "@tanstack/react-table"
import PropTypes, { objectOf } from "prop-types"
import { useEffect } from "react"
import { getColumns } from "./utils/columns"

const QuizUI = ({
  userType = "trainer",

  onQuizCreation,
  onUpdateStatus,
  handleDelete,
  handleEdit,
  onViewSubmission,
  setActiveColumns,
  activeColumns,
  listOfQuiz,
  isLoading,
  setPagination,
  pagination,
  setSearchingValue,
  searchingValue,
  handleQuizSubmisson,
  moduleName,
  setSelectedModuleId,
  selectedModule,
  selectedQuiz,
  setSelectedQuiz,
  setSelectedModule,
  moduleLoading,
  isPaidUser,
}) => {
  useEffect(() => {
    if (selectedModule) {
      setSelectedModuleId(selectedModule?.id)
    }
  }, [selectedModule])

  const moduleOptions = []
    .concat(moduleName || [])
    .map((m) => ({
      id: m?.id,
      name: m?.module_name,
    }))
    .filter((m) => m.id && m.name)

  const handleBack = () => {
    setSelectedQuiz(null)
    setActiveColumns(getColumns(userType))
  }

  const renderCellContent = (cell, row) => {
    const {
      id,
      resource_link,
      quiz,
      title,
      feedback,
      mark_obtained,
      quizNo,
      student,
      oppno,
      totno,
      isSubmitted,
      dead_line,
      publish_status,
      marks_obtained,
      total_marks,
      submission_status,

      submission_count,
    } = row?.original || {}

    switch (cell.column.id) {
      case "dead_line":
        return <DateCell value={dead_line} />
      case "quiz":
        return <RenderTableData content={quiz} />
      case "topic":
        return <RenderTableData content={Array.isArray(title) ? title?.join(", ") : title} />
      case "link":
        return <LinkCell value={resource_link} isPaidUser={isPaidUser} />

      case "mark_obtained":
        return <p>{marks_obtained ? `${marks_obtained}/${total_marks}` : "-"}</p>
      case "submissions":
        return (
          <SubmissionButton
            onSubmit={() => onViewSubmission(id)}
            isSubmitted={isSubmitted}
            userRole={userType}
            isPaidUser={isPaidUser}
            data={submission_count}
          />
        )

      case "remarks":
        return <RenderTableData content={feedback} />
      case "quizNo":
        return <RenderTableData content={quizNo} />
      case "student":
        return <RenderTableData content={student} />
      case "oppno":
        return <RenderTableData content={oppno} />
      case "totno":
        return <RenderTableData content={totno} />

      case "status":
        return <StatusUpdationCell value={publish_status} key={id} />
      case "submission":
        return (
          <SubmissionButton
            isDisabled={!!["VENDOR", "ADMIN", "MARKETING"]?.includes(userType)}
            onSubmit={handleQuizSubmisson}
            isSubmitted={isSubmitted}
            data={id}
            isPaidUser={isPaidUser}
            submitedStatus={submission_status}
          />
        )

      case "actions":
        return (
          <ActionCell
            label1="Update Status"
            label2="Edit"
            label3="Delete Quiz"
            row={row}
            isEdit
            isDelete
            isView
            onView={onUpdateStatus}
            onDelete={handleDelete}
            onEdit={handleEdit}
          />
        )

      default:
        return flexRender(cell.column.columnDef.cell, cell.getContext())
    }
  }

  const { table, found, pageCount } = useTableConfig(
    listOfQuiz?.data,
    getColumns(userType),
    listOfQuiz?.metadata?.total_records,
    setPagination,
    pagination
  )
  const dynamicHeight = listOfQuiz?.data?.length >= 7 ? "h-[55vh]" : undefined

  return (
    <div className="w-full">
      <div className="flex justify-between gap-9">
        {selectedQuiz ? (
          <div className="flex items-center justify-end gap-4">
            <div className="flex ">
              <Button onClick={handleBack} className="rounded-[.4rem] py-4" variant="outline">
                Back
              </Button>
              <h1 className="text-2xl ms-8 font-medium">Quiz {selectedQuiz.quizNo} Submissions</h1>
            </div>
            <Button variant="primary">Create Submission</Button>
          </div>
        ) : (
          ""
        )}
      </div>

      <div className="flex items-center justify-between py-2">
        <CustomSearchbar
          inputSize="w-[20rem]"
          placeholder="Search by title..."
          searchedValue={searchingValue}
          setSearchedValue={(e) => setSearchingValue(e?.target.value)}
        />
        <div className="flex items-center gap-4">
          <p className="text-lg font-medium text-gray-700 bg-gray-100 px-3 py-2 rounded-md shadow-sm">
            Module Name:{" "}
            <span className="text-primary font-semibold">{selectedModule?.name || "-"}</span>
          </p>
          <CustomComboBox
            iterateData={moduleOptions}
            selectedValue={selectedModule?.name}
            setSelectedValue={(module) => setSelectedModule(module)}
            notFoundMessage="modules"
            placeholder="Select module..."
            width="min-w-[285px]"
            loading={moduleLoading}
          />
          {[USER_ROLES.TRAINER, USER_ROLES.ADMIN]?.includes(userType) && (
            <div className="text-end flex justify-between gap-4">
              <Button variant="primary" onClick={onQuizCreation}>
                Create Quiz
              </Button>
            </div>
          )}
        </div>
      </div>

      <DataTable
        renderCellContent={renderCellContent}
        columns={activeColumns}
        table={table}
        found={found}
        height={dynamicHeight}
        pageName="Quiz"
        pageCount={pageCount}
        pagination={pagination}
        className={userType === USER_ROLES.TRAINER && !selectedQuiz ? "cursor-pointer" : ""}
        isLoading={isLoading}
        notFoundPlaceholder="Quiz Not Found"
      />
    </div>
  )
}
QuizUI.propTypes = {
  userType: PropTypes.string,
  onUpdateStatus: PropTypes.func.isRequired,
  handleDelete: PropTypes.func.isRequired,
  handleEdit: PropTypes.func.isRequired,
  onViewSubmission: PropTypes.func.isRequired,
  onQuizCreation: PropTypes.func,
  setActiveColumns: PropTypes.func,
  activeColumns: PropTypes.string,
  listOfQuiz: PropTypes.arrayOf(objectOf),
  isLoading: PropTypes.bool,
  setPagination: PropTypes.func,
  pagination: PropTypes.string,
  setSearchingValue: PropTypes.func,
  searchingValue: PropTypes.string,
  handleQuizSubmisson: PropTypes.func,
  moduleName: PropTypes.arrayOf(objectOf),
  setSelectedModuleId: PropTypes.func.isRequired,
  selectedModule: PropTypes.string,
  selectedQuiz: PropTypes.string,
  setSelectedQuiz: PropTypes.func,
  setSelectedModule: PropTypes.func,
  moduleLoading: PropTypes.bool,
  isPaidUser: PropTypes.bool,
}

export default QuizUI
