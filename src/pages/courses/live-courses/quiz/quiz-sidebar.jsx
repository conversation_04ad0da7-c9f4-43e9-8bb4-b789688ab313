import { Form } from "@/components/ui/form"
import { Button } from "@/components/ui/button"
import FormInput from "@/components/custom/custom-forms/form-input"
import FormTextArea from "@/components/custom/custom-forms/form-textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { zodResolver } from "@hookform/resolvers/zod"
import PropTypes from "prop-types"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { useEffect } from "react"
import FormSelect from "@/components/custom/custom-forms/form-select"
import { quizStatus } from "@/utils/constants"

const getGradingSchema = (maxMarks) =>
  z
    .object({
      marks_obtained: z.coerce.number().min(0, "Marks must be at least 0").optional(),
      trainer_feedback: z.string().optional(),
      acceptance_status: z.enum(["ACCEPTED", "PENDING", "REJECTED"]).default("PENDING"),
    })
    .superRefine((data, ctx) => {
      if (
        typeof data.marks_obtained === "number" &&
        typeof maxMarks === "number" &&
        data.marks_obtained > maxMarks
      ) {
        ctx.addIssue({
          path: ["marks_obtained"],
          code: "custom",
          message: `Marks obtained cannot exceed total marks (${maxMarks})`,
        });
      }
    });

const QuizGradingForm = ({ onSubmit, onClose, initialData = {} }) => {
  const totalMarks = initialData.total_marks ?? Infinity;

  const form = useForm({
    resolver: zodResolver(getGradingSchema(totalMarks)),
    defaultValues: {
      marks_obtained: initialData.marks_obtained || 0,
      trainer_feedback: initialData.trainer_feedback || "",
      acceptance_status: initialData.acceptance_status || "PENDING",
    },
    mode: "onChange",
  });

  // Reset form when initialData changes
  useEffect(() => {
    form.reset({
      marks_obtained: initialData.marks_obtained || 0,
      trainer_feedback: initialData.trainer_feedback || "",
      acceptance_status: initialData.acceptance_status || "PENDING",
    });
  }, [initialData, form]);

  const { control, handleSubmit, getValues } = form;

  return (
    <ScrollArea className="overflow-y-auto w-full rounded-md pr-2">
      <Form {...form}>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-5 p-2 lg:mb-8">
          <FormSelect
            dataTestID="acceptance-status"
            dataTestIDError="acceptance-status-error"
            fieldControlName="acceptance_status"
            control={control}
            label="Acceptance Status"
            getValues={getValues}
            iterateData={quizStatus}
          />
          <FormInput
            dataTestID="marks-input"
            dataTestIDError="marks-error"
            fieldControlName="marks_obtained"
            control={control}
            label={`Marks Obtained (Max ${totalMarks})`}
            placeholder="Enter marks"
            isTypeNumer
          />
          <FormTextArea
            dataTestID="feedback-input"
            dataTestIDError="feedback-error"
            fieldControlName="trainer_feedback"
            control={control}
            label="Feedback"
            placeholder="Enter feedback for the student"
          />
          <div className="pt-4 flex gap-x-3 justify-end">
            <Button onClick={onClose} variant="secondary" type="reset">
              Cancel
            </Button>
            <Button type="submit" variant="primary">
              Submit Grading
            </Button>
          </div>
        </form>
      </Form>
    </ScrollArea>
  );
};

QuizGradingForm.propTypes = {
  onSubmit: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired,
  initialData: PropTypes.shape({
    marks_obtained: PropTypes.number,
    trainer_feedback: PropTypes.string,
    acceptance_status: PropTypes.string,
    total_marks: PropTypes.number,
  }),
};

export default QuizGradingForm;
