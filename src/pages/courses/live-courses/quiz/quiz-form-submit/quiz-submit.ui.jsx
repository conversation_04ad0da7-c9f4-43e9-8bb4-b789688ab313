import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import PropTypes from "prop-types"

const QuizSubmitUI = ({
  handleSubmit,
  formData,
  handleChange,
  isSubmitting,
  errors,
  submitStatus,
}) => {
  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="quizLink" className="text-sm font-medium">
          Quiz Link
        </Label>
        <Input
          id="quizLink"
          type="text"
          name="quizLink"
          value={formData.quizLink}
          onChange={handleChange}
          placeholder="https://your-quiz-link.com"
          className={`w-full ${errors.quizLink ? "border-red-500" : ""}`}
          disabled={isSubmitting}
        />
        {errors.quizLink && <p className="text-sm text-red-500 mt-1">{errors.quizLink}</p>}
      </div>

      <Button type="submit" variant="primary" className="w-full" disabled={isSubmitting}>
        {isSubmitting ? "Submitting..." : "Submit Quiz"}
      </Button>

      {submitStatus && (
        <p
          className={`text-sm font-medium mt-2 ${
            submitStatus.type === "success" ? "text-green-600" : "text-red-600"
          }`}
        >
          {submitStatus.message}
        </p>
      )}
    </form>
  )
}

QuizSubmitUI.propTypes = {
  handleSubmit: PropTypes.string,
  formData: PropTypes.func.isRequired,
  handleChange: PropTypes.func.isRequired,
  isSubmitting: PropTypes.bool.isRequired,
  errors: {
    quizLink: PropTypes.string,
  },
  submitStatus: {
    type: PropTypes.string,
    message: PropTypes.string,
  },
}

export default QuizSubmitUI
