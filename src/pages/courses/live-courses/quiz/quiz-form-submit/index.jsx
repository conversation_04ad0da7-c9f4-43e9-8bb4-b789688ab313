import CustomSidebar from "@/pages/courses/components/custom-sidebar"
import { useState } from "react"
import { useSubmitQuizMutation } from "@/services/query/quiz.query"
import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import PropTypes from "prop-types"
import QuizSubmitUI from "./quiz-submit.ui"

const QuizFormSubmit = ({ isSubmitSidebarOpen, setIsSubmitSidebarOpen, quizIDs }) => {
  const [formData, setFormData] = useState({ quizLink: "" })
  const [errors, setErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState(null)

  const { mutate: submitMutate } = useSubmitQuizMutation()

  const validateForm = (data) => {
    const errorsObj = {}

    if (!data.quizLink) {
      errorsObj.quizLink = "Quiz Link is required."
    } else if (!data.quizLink.startsWith("http")) {
      errorsObj.quizLink = "Please enter a valid URL starting with http:// or https://"
    } else {
      try {
        // eslint-disable-next-line no-new
        new URL(data.quizLink)
      } catch {
        errorsObj.quizLink = "Please enter a valid URL"
      }
    }

    return errorsObj
  }

  const handleQuizSubmit = async (e) => {
    e.preventDefault()

    // Validate form
    const validationErrors = validateForm(formData)
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors)
      return
    }

    setIsSubmitting(true)
    setSubmitStatus(null)

    const requestData = {
      submission: formData.quizLink,
      submission_type: "LINK",
    }

    try {
      submitMutate(
        {
          course_id: 2,
          module_id: 2,
          quiz_id: quizIDs?.quiz_id,
          data: requestData,
        },
        {
          onSuccess: () => {
            setIsSubmitSidebarOpen(false)
            successToast("Quiz Submitted", "The quiz has been successfully submitted!")
          },
          onError: () => {
            setIsSubmitSidebarOpen(false)
            failureToast(
              "Quiz Submission Failed",
              "An error occurred while submitting the quiz. Please try again."
            )
          },
        }
      )
      setFormData({ quizLink: "" }) // Clear form after successful submission
    } catch (error) {
      setSubmitStatus({ type: "error", message: "Submission failed. Please try again." })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))

    // Clear errors when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }))
    }
  }

  return (
    <CustomSidebar
      //   title={isSubmitted ? "Update Quiz" : "Submit Quiz"}
      description=""
      isOpen={isSubmitSidebarOpen}
      onClose={() => setIsSubmitSidebarOpen(!isSubmitSidebarOpen)}
      content={
        <QuizSubmitUI
          handleSubmit={handleQuizSubmit}
          formData={formData}
          handleChange={handleChange}
          isSubmitting={isSubmitting}
          errors={errors}
          submitStatus={submitStatus}
        />
      }
    />
  )
}

QuizFormSubmit.propTypes = {
  isSubmitSidebarOpen: PropTypes.bool,
  setIsSubmitSidebarOpen: PropTypes.func,
  quizIDs: PropTypes.objectOf({
    quiz_id: PropTypes.number,
  }),
}

export default QuizFormSubmit
