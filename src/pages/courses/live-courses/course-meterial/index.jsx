import { CustomSelect } from "@/components/custom/custom-select"
import { GeneralDialog } from "@/components/custom/dialogs/general.dialog"
import {
  failureToast,
  loadingToast,
  successToast,
  warningToast,
} from "@/components/custom/toasts/tosters"
import {
  useDeleteDialogHooks,
  useFilterHooks,
  useIsEditDialogHook,
  useIsUpdateHook,
  useOpenCloseHooks,
  usePaginationHooks,
  usePublishStatus,
  useSelectedIds,
} from "@/hooks/common.hooks"
import { useDebounce } from "@/hooks/useDebounce"
import {
  useCreateCourseMaterial,
  useDeleteCourseMaterial,
  useFetchCourseMaterial,
  useUpdateCourseMaterial,
  useUpdateMaterialStatus,
} from "@/services/query/course-material.query"
import { publishStatus } from "@/utils/constants"
import { isStudent } from "@/utils/helper"
import ct from "@constants/"
import { zodResolver } from "@hookform/resolvers/zod"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { useSelector } from "react-redux"
import CustomSidebar from "../../components/custom-sidebar"
import { displayTheHeadings } from "../utils/helper"
import CourseMeterialUi from "./course-material"
import { CourseMaterialSchema, defaultMaterialValues } from "./course-material-schema"
import CreateCourseMeterialUi from "./create-coursemeterial-ui"

const CourseMeterial = () => {
  const userRole = useSelector((st) => st[ct.store.USER_STORE]?.userRole)
  const course = useSelector((st) => st[ct.store.COURSES]?.activeCourseDetails)
  const courseDet = useSelector((st) => st[ct.store.COURSES])

  const [searchingValue, setSearchingValue] = useState("")
  const debouncedSearchQuery = useDebounce(searchingValue, 500)

  const [status, setStatus] = useState("")
  const { handleOpen, handleClose, open } = useOpenCloseHooks()
  const { isUpdate, handleResetUpdate, handleUpdate } = useIsUpdateHook()
  const { isDelete, handleDelete, handleCancel } = useDeleteDialogHooks(false)
  const { isEditDialog, handleOpenEditDialog, handleResetEditDialog } = useIsEditDialogHook()
  const { sortBy, sortByField } = useFilterHooks()
  const { limit } = usePaginationHooks()
  const { selectedIDs, setSelectedIDs } = useSelectedIds()
  const { publishedStatus, handleSetPublishStatus } = usePublishStatus()
  // const location = useLocation()

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })

  const [materialIDs, setMaterialIDs] = useState({
    // course_id: location?.state?.course_id ?? 2,
    course_id: course?.id,
    material_id: 2,
  })

  const form = useForm({
    resolver: zodResolver(CourseMaterialSchema),
    defaultValues: defaultMaterialValues,
    mode: "onChange",
  })

  const { mutate: createCourseMaterial, isPending: createMaterialPending } =
    useCreateCourseMaterial()
  const { mutate: updateCourseMaterial, isPending: updateMaterialPending } =
    useUpdateCourseMaterial()
  const { mutate: deleteCourseMaterial } = useDeleteCourseMaterial()
  const { mutate: updateQuizStusMutate } = useUpdateMaterialStatus()

  const calculateOffset = (pageIndex, pageSize) => pageIndex * pageSize

  const { data: fetchCourseMaterials, isLoading } = useFetchCourseMaterial({
    course_id: course?.id,
    filters: {
      limit,
      offSet: calculateOffset(pagination.pageIndex, pagination.pageSize),
      sort_by: sortBy,
      sort_by_field: sortByField,
      search_query: debouncedSearchQuery?.length > 0 ? debouncedSearchQuery : null,
    },
  })

  useEffect(() => {
    if (createMaterialPending || updateMaterialPending) {
      loadingToast()
    }
  }, [createMaterialPending, updateMaterialPending])

  const {
    handleSubmit,
    formState: { errors },
    control,
    setValue,
    reset,
    getValues,
  } = form

  console.log("_errors", errors, getValues())

  const handleCancelForm = () => {
    handleClose()
    reset({
      title: "",
      description: "",
      resource_link: "",
      resource_type: "",
      publish_status: "DRAFT",
    })
  }
  const handleCreateClass = () => {
    handleResetUpdate()
    reset({
      title: "",
      description: "",
      resource_link: "",
      resource_type: "",
      publish_status: "DRAFT",
    })
    handleOpen()
  }

  const handleCreateClassForm = (data) => {
    // Validate: Must have either a file OR a link (but not necessarily both)
    if (!data.docs?.[0] && !data.resource_link) {
      warningToast("Required Field Missing", "Please upload a file OR enter a resource link")
      return
    }

    if (isUpdate) {
      const formData = new FormData()
      const updateData = {
        title: data?.title,
        description: data?.description,
        resource_type: data?.resource_type,
        resource_link: data?.resource_link,
        publish_status: data?.publish_status,
      }

      formData.append("data", JSON.stringify(updateData))

      if (data?.docs) {
        formData.append("file", data.docs[0])
      }
      updateCourseMaterial(
        {
          updatedData: formData,
          course_id: course?.id,
          material_id: materialIDs?.material_id,
        },
        {
          onSuccess: () => {
            handleResetUpdate()
            handleClose()
            successToast("Successfully Edited!", "Course Material was successfully Edited!")
          },
          onError: (error) => {
            handleResetUpdate()
            handleClose()
            failureToast("Failed to update Course Material", error)
          },
        }
      )
    } else {
      createCourseMaterial(
        { data, course_id: course?.id },
        {
          onSuccess: () => {
            handleClose()
            successToast("Successfully Created!", "Course Material was successfully created!")
            handleResetUpdate()
          },
          onError: () => {
            handleClose()
            failureToast("Something went wrong", "please try again")
            handleResetUpdate()
          },
        }
      )
    }
  }

  const handleEdit = (row) => {
    const { id, description, publish_status, resource_link, resource_type, title, ...rest } =
      row?.original ?? {}
    console.log(rest, "rowtest")
    setMaterialIDs({ ...materialIDs, material_id: id })
    handleUpdate()
    reset({
      title: title?.toString(),
      description: description?.toString(),
      resource_link: resource_link?.toString(),
      publish_status: publish_status?.toString(),
      resource_type: resource_type?.toString(),
    })
    handleOpen()
  }

  const handleOpenMaterialStatusDialog = (data) => {
    console.log("_useFetchQuizDetails", data?.original)
    const { id, publish_status } = data?.original || {}
    handleSetPublishStatus(publish_status)
    setSelectedIDs({
      ...selectedIDs,
      materialID: id,
    })

    handleOpenEditDialog()
  }

  const handleUpdateStatus = () => {
    updateQuizStusMutate(
      { course_id: course?.id, material_id: selectedIDs.materialID, status: publishedStatus },
      {
        onSuccess: () => {
          handleResetEditDialog()
          successToast(
            "Course Material Status Updated",
            "The Course Material has been successfully Updated!"
          )
        },
        onError: () => {
          handleResetEditDialog()
          failureToast(
            "Course Material Status Updation Failed",
            "An error occurred while update the Course Material. Please try again."
          )
        },
      }
    )
  }

  const handleDeleteMaterial = (data) => {
    setMaterialIDs({ ...materialIDs, material_id: data?.original?.id })
    handleDelete()
  }

  const handleConfirmDelete = () => {
    deleteCourseMaterial(
      { course_id: course?.id, material_id: materialIDs?.material_id },
      {
        onSuccess: () => {
          successToast(
            "Course Material deleted successfully",
            "The Course Material has been removed."
          )
          handleCancel()
        },
        onError: () => {
          failureToast(
            "Deletion Error",
            "Unable to delete the Course Material. Please try again later."
          )
        },
      }
    )
  }

  const courseMaterialProps = {
    form,
    control,
    onClose: handleCancelForm,
    handleSubmit,
    onCreateClassForm: handleCreateClassForm,
    setValue,
    isUpdate,
    userRole,
    reset,
  }
  // const filterDraft=fetchCourseMaterials?.data.filter((data)=>)

  return (
    <div>
      <CourseMeterialUi
        setIsSideBarOpen={handleCreateClass}
        handleConfirmDelete={handleConfirmDelete}
        handleEdit={handleEdit}
        onUpdateStatus={handleOpenMaterialStatusDialog}
        handleDelete={handleDeleteMaterial}
        userType={userRole}
        listOfMaterials={fetchCourseMaterials?.data}
        isLoading={isLoading}
        setStatus={setStatus}
        status={status}
        searchingValue={searchingValue}
        setSearchingValue={setSearchingValue}
        handleCancel={handleCancel}
        isDelete={isDelete}
        pagination={pagination}
        setPagination={setPagination}
        isPaidUser={isStudent(courseDet?.activeCourse, userRole)}
      />
      <CustomSidebar
        isOpen={open}
        title={displayTheHeadings(
          userRole,
          isUpdate,
          "Update Material",
          "Create Material",
          "Resubmit Material",
          "Submit Material"
        )}
        description=""
        onClose={handleClose}
        content={<CreateCourseMeterialUi watch={form.watch} {...courseMaterialProps} />}
      />

      <GeneralDialog
        onOpen={isEditDialog}
        title="Update Status"
        onClickCTA={handleUpdateStatus}
        ctaLabel="Update Status"
        onCancel={handleResetEditDialog}
        ctaPosition="justify-end"
        content={
          <CustomSelect
            publishedStatus={publishedStatus}
            setPublishedStatus={handleSetPublishStatus}
            iteratedData={publishStatus}
          />
        }
      />
    </div>
  )
}

export default CourseMeterial
