import { z } from "zod"

export const CourseMaterialSchema = z.object({
  title: z.string().min(3, { message: "Please enter a valid title" }),
  description: z.string().min(10, { message: "Please enter a valid description" }),
  resource_link: z
    .string()
    .optional()
    .refine(
      (val) => {
        // If empty or undefined, it's valid (optional)
        if (!val || val.trim() === "") return true
        // If has value, must be valid URL
        try {
          new URL(val)
          return true
        } catch {
          return false
        }
      },
      {
        message: "Please enter a valid URL (e.g., https://example.com)",
      }
    ),
  resource_type: z.string().nonempty({
    message: "Please select a valid resource type",
  }),
  publish_status: z
    .string({
      message: "Please select a valid publish status",
    })
    .optional(),
  docs: z.any().optional(),
})

export const defaultMaterialValues = {
  title: "",
  description: "",
  resource_link: "",
  resource_type: "VIDEO",
  publish_status: "DRAFT",
  docs: null,
}
