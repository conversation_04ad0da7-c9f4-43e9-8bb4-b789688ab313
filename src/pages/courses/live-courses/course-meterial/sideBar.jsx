import FormInput from "@/components/custom/custom-forms/form-input"
import FileUploadCard from "@/components/custom/upload-file"
import CustomeInput from "@/components/input/input"
import CustomeTextArea from "@/components/textArea/textArea"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Sheet, SheetContent, SheetHeader } from "@/components/ui/sheet"
import { X } from "lucide-react"
import PropTypes from "prop-types"
import { useState } from "react"

function CourseMeterialSideBar({ isOpen, onClose, control, setValue }) {
  const [files, setFiles] = useState([])

  // const handleFileChange = (e) => {
  //   const selectedFiles = Array.from(e.target.files)
  //   setFiles((prev) => [...prev, ...selectedFiles])
  // }

  const removeFile = (fileToRemove) => {
    setFiles(files.filter((file) => file !== fileToRemove))
  }

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="min-w-[500px] bg-white py-12 px-12">
        <SheetHeader>
          <h2 className="text-xl font-semibold">Add New Course Material</h2>
        </SheetHeader>
        <div className="flex flex-col gap-6 mt-2">
          <CustomeInput label="Title" className="rounded" />
          <CustomeInput label="Link" placeholder="https://example.com" className="rounded" />

          {/* File Upload Section */}
          <div className="space-y-4">
            <div className="relative">
              <Label>
                Add Notes Link <span className="text-muted text-xs">(or Upload Docs)</span>
              </Label>
              <FormInput
                placeholder="Add link"
                fieldControlName="notes_link"
                control={control}
                isRequired={false}
              />
              <p className="text-slate-400 text-xs my-4 font-semibold mx-auto w-5">(or)</p>
              <FileUploadCard setValue={setValue} />
            </div>

            {/* File List */}
            {files.length > 0 && (
              <div className="space-y-2">
                {files.map((file) => (
                  <div
                    key={file.name}
                    className="flex items-center justify-between p-2 bg-gray-50 rounded"
                  >
                    <span className="text-sm truncate max-w-[300px]">{file.name}</span>
                    <button
                      type="button"
                      onClick={() => removeFile(file)}
                      className="p-1 hover:bg-gray-200 rounded-full transition-colors"
                    >
                      <X className="w-4 h-4 text-gray-600" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
          <CustomeTextArea labelText="Description" className="h-[150px] rounded" />
          <div className="flex justify-end gap-4">
            <Button variant="secondary" onClick={() => onClose(false)}>
              Cancel
            </Button>
            <Button variant="primary" onClick={() => onClose(false)}>
              Save
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
}

CourseMeterialSideBar.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  control: PropTypes.objectOf.isRequired,
  setValue: PropTypes.func.isRequired,
}

export default CourseMeterialSideBar
