/* eslint-disable no-nested-ternary */
import CustomSearchbar from "@/components/custom/custom-search"
import DataTable from "@/components/custom/cutsom-table"
import {
  ActionCell,
  DateCell,
  LinkCell,
  RenderTableData,
  StatusUpdationCell,
} from "@/components/custom/cutsom-table/table-cells"
import { DeleteDialog } from "@/components/custom/dialogs/delete.dialog"
import { Button } from "@/components/ui/button"
import useTableConfig from "@/hooks/use-table.hooks"
import { openPublicUrl } from "@/services/api/project-api."
import { USER_ROLES } from "@/utils/constants"
import { isPaidUserPropTypes } from "@/utils/props-types"
import { flexRender } from "@tanstack/react-table"
import { FilePlus2, Video } from "lucide-react"
import PropTypes from "prop-types"

import { getCourseColumns } from "./columns"

const CourseMeterialUi = ({
  setIsSideBarOpen,
  handleConfirmDelete,
  isDelete,
  handleCancel,
  onUpdateStatus,
  handleEdit,
  handleDelete,
  listOfMaterials,
  userType,
  setSearchingValue,
  searchingValue,
  isLoading,
  setPagination,
  pagination,
  isPaidUser,
}) => {
  const downloadFile = (resource_link) => {
    openPublicUrl(resource_link)
  }
  const renderCellContent = (cell, row) => {
    const { id, title, resource_type, resource_link, modified_at, publish_status } =
      row?.original || {}
    switch (cell.column.id) {
      case "course_no":
        return <RenderTableData content={id} />
      case "title":
        return <RenderTableData content={title} />
      case "link":
        return resource_type === "LINK" ? (
          <LinkCell value={resource_link} isPaidUser={isPaidUser} />
        ) : (
          <Button onClick={() => downloadFile(resource_link)}>
            {resource_type === "VIDEO" ? (
              <Video className="w-5 h-5 text-yellow-500" />
            ) : resource_type === "DOCUMENT" ? (
              <FilePlus2 className="w-5 h-5 text-yellow-500" />
            ) : null}
          </Button>
        )
      case "status":
        return <StatusUpdationCell value={publish_status} key={id} />
      case "date":
        return <DateCell value={modified_at} />
      case "actions":
        return (
          <ActionCell
            label1="Update Status"
            label2="Edit"
            label3="Delete"
            row={row}
            isEdit
            isDelete
            isView
            onView={onUpdateStatus}
            onDelete={handleDelete}
            onEdit={handleEdit}
          />
        )
      default:
        return flexRender(cell.column.columnDef.cell, cell.getContext())
    }
  }

  // eslint-disable-next-line react/prop-types
  const draftFilter = listOfMaterials?.data.filter((data) => {
    return data.publish_status === "PUBLISHED"
  })

  const { table, found, pageCount } = useTableConfig(
    userType === "ADMIN" || userType === "TRAINER" ? listOfMaterials?.data : draftFilter,
    getCourseColumns(userType),
    listOfMaterials?.metadata?.total_records,
    setPagination,
    pagination
  )
  const dynamicHeight = listOfMaterials?.data?.length >= 7 ? "h-[55vh]" : undefined
  return (
    <div className="w-full">
      <DeleteDialog
        onOpen={isDelete}
        onDelete={handleConfirmDelete}
        onCancel={handleCancel}
        title="Delete Course Material"
        content="Are you sure want to delete this Course Material?"
      />
      <div className="flex items-center justify-between py-2">
        <CustomSearchbar
          inputSize="w-[20rem]"
          placeholder="Search by title..."
          searchedValue={searchingValue}
          setSearchedValue={(e) => setSearchingValue(e?.target.value)}
        />
        {[USER_ROLES.ADMIN, USER_ROLES.TRAINER]?.includes(userType) && (
          <div className="flex justify-end">
            <Button onClick={setIsSideBarOpen} variant="primary">
              <FilePlus2 size={18} className="" />
              Create
            </Button>
          </div>
        )}
      </div>

      <DataTable
        renderCellContent={renderCellContent}
        columns={getCourseColumns(userType)}
        table={table}
        found={found || 0}
        height={dynamicHeight}
        pageCount={pageCount || 1}
        pageName="Course Material"
        pagination={pagination}
        isLoading={isLoading}
        notFoundPlaceholder="Course Material Not Found..."
      />
    </div>
  )
}

CourseMeterialUi.propTypes = {
  setIsSideBarOpen: PropTypes.func.isRequired,
  handleDelete: PropTypes.func.isRequired,
  handleEdit: PropTypes.func.isRequired,
  handleConfirmDelete: PropTypes.func.isRequired,
  handleCancel: PropTypes.func.isRequired,
  isDelete: PropTypes.bool.isRequired,
  searchingValue: PropTypes.string.isRequired,
  setSearchingValue: PropTypes.string.isRequired,
  isLoading: PropTypes.bool.isRequired,
  onUpdateStatus: PropTypes.func.isRequired,
  setPagination: PropTypes.func.isRequired,
  pagination: PropTypes.shape({
    pageIndex: PropTypes.number.isRequired,
    pageSize: PropTypes.number.isRequired,
  }).isRequired,
  listOfMaterials: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      title: PropTypes.string.isRequired,
      resource_link: PropTypes.string,
      modified_at: PropTypes.string,
      publish_status: PropTypes.string,
      total_records: PropTypes.number.isRequired,
    })
  ).isRequired,
  userType: PropTypes.string.isRequired,
  isPaidUser: isPaidUserPropTypes,
}

export default CourseMeterialUi
