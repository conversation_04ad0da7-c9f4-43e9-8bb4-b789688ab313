import { LinkCell } from "@/components/custom/cutsom-table/table-cells"
import { USER_ROLES } from "@/utils/constants"

export const trainer = [
  {
    id: "title",
    accessorKey: "title",
    header: "Title",
  },
  {
    id: "link",
    accessorKey: "link",
    header: "Link",
    cell: LinkCell,
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Publish Status",
  },
  {
    accessorKey: "date",
    header: "Date",
  },
  {
    id: "actions",
    accessorKey: "actions",
    header: "Actions",
  },
]

export const student = [
  {
    id: "title",
    accessorKey: "title",
    header: "Title",
  },
  {
    id: "link",
    accessorKey: "link",
    header: "Link",
    cell: LinkCell,
  },
  {
    accessorKey: "date",
    header: "Date",
  },
]

export const admin = [
  {
    id: "title",
    accessorKey: "title",
    header: "Title",
  },
  {
    id: "link",
    accessorKey: "link",
    header: "Link",
    cell: LinkCell,
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Publish Status",
  },
  {
    accessorKey: "date",
    header: "Date",
  },
  {
    id: "actions",
    accessorKey: "actions",
    header: "Actions",
  },
]

export const vendor = [
  {
    id: "title",
    accessorKey: "title",
    header: "Title",
  },
  {
    id: "link",
    accessorKey: "link",
    header: "Link",
    cell: LinkCell,
  },
  {
    accessorKey: "date",
    header: "Date",
  },
]

export const getCourseColumns = (role) => {
  switch (role) {
    case USER_ROLES.TRAINER:
      return trainer
    case USER_ROLES.STUDENT:
      return student
    case USER_ROLES.VENDOR:
      return vendor
    case USER_ROLES.ADMIN:
      return admin

    default:
      return []
  }
}
