import FormInput from "@/components/custom/custom-forms/form-input"
import FormSelect from "@/components/custom/custom-forms/form-select"
import FormTextArea from "@/components/custom/custom-forms/form-textarea"
import FileUploadCard from "@/components/custom/upload-file"
import { But<PERSON> } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { fileType } from "@/utils/constants"
import PropTypes from "prop-types"
import { useEffect } from "react"
import { displayTheHeadings } from "../utils/helper"

const CreateCourseMeterialUi = ({
  form,
  onClose,
  setValue,
  control,
  handleSubmit,
  onCreateClassForm,
  isUpdate,
  userRole,
  watch,
  reset,
}) => {
  const publishStatus = [
    { value: "PUBLISHED", label: "Published" },
    { value: "DRAFT", label: "Draft" },
    { value: "ARCHIVED", label: "Archived" },
    { value: "UNLISTED", label: "Unlisted" },
  ]
  const resourceType = watch("resource_type")
  const resource_link = watch("resource_link")
  useEffect(() => {
    if (resourceType !== "LINK" && resource_link) {
      reset({ resource_link: "" })
    }
  }, [resourceType, resource_link, reset])

  return (
    <ScrollArea className="overflow-y-auto pr-2 h-[90vh]">
      <Form {...form}>
        <form className="p-2" onSubmit={handleSubmit(onCreateClassForm)}>
          <div className="max-h-full flex gap-y-2 flex-col justify-end lg:mb-8">
            <FormInput
              dataTestID="test-id"
              dataTestIDError="error"
              placeholder="Enter title"
              label="Title"
              fieldControlName="title"
              isRequired
              control={control}
            />
            <div className="mt-2">
              <FormSelect
                dataTestID="test-id"
                dataTestIDError="error"
                fieldControlName="resource_type"
                control={control}
                label="Resource Type"
                isRequired
                placeholder="Select Type"
                iterateData={fileType}
              />
            </div>
            {isUpdate && (
              <div className="mt-2">
                <Label>Publish Status</Label>
                <FormSelect
                  dataTestID="test-id"
                  dataTestIDError="error"
                  fieldControlName="publish_status"
                  control={control}
                  label=""
                  placeholder="Select status"
                  iterateData={publishStatus}
                />
              </div>
            )}
            <div className="my-4">
              <FormTextArea
                dataTestID="test-id"
                dataTestIDError="error"
                placeholder="add description"
                label="Description"
                fieldControlName="description"
                control={control}
                isRequired
              />
            </div>
            <div className="mb-2">
              {resourceType !== "LINK" ? (
                <FileUploadCard
                  disables={resourceType === "LINK"}
                  fileType={resourceType === "VIDEO" ? "video" : "all-docs"}
                  setValue={setValue}
                />
              ) : (
                <FormInput
                  dataTestID="test-id"
                  dataTestIDError="error"
                  placeholder="add link"
                  label="Material link"
                  fieldControlName="resource_link"
                  control={control}
                  isRequired={false}
                  disabled={resourceType !== "LINK"}
                />
              )}
              {/* <Label>
                Add Notes Link <span className="text-muted text-xs"> (or Upload Docs)</span>
              </Label>
          
              <p className="text-slate-400 text-xs my-4 font-semibold mx-auto w-5">(or)</p> */}
            </div>
            <div className="pt-5 flex gap-x-3 justify-end">
              <Button onClick={onClose} variant="secondary" type="reset">
                Cancel
              </Button>
              <Button type="submit" variant="primary">
                {displayTheHeadings(userRole, isUpdate, "Update", "Create", "Submit", "Submit")}
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </ScrollArea>
  )
}

CreateCourseMeterialUi.propTypes = {
  form: PropTypes.objectOf.isRequired,
  onClose: PropTypes.func.isRequired,
  setValue: PropTypes.func.isRequired,
  control: PropTypes.objectOf.isRequired,
  handleCreateClassForm: PropTypes.func,
  onCreateClassForm: PropTypes.func,
  handleSubmit: PropTypes.func.isRequired,
  isUpdate: PropTypes.bool,
  userRole: PropTypes.string,
  isEdit: PropTypes.bool,
  watch: PropTypes.func.isRequired,
  reset: PropTypes.func.isRequired,
  editData: PropTypes.shape({
    title: PropTypes.string,
    description: PropTypes.string,
    resource_type: PropTypes.string,
    resource_link: PropTypes.string,
    publish_status: PropTypes.string,
  }),
}

export default CreateCourseMeterialUi
