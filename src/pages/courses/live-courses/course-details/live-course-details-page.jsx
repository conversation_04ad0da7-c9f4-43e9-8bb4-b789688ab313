import { StatusUpdationCell } from "@/components/custom/cutsom-table/table-cells"
import {
  <PERSON><PERSON><PERSON>rumb,
  Bread<PERSON>rumbItem,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Card, CardContent } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import ct from "@constants/"
import {
  Activity,
  ChartNoAxesCombined,
  Clock10Icon,
  Languages,
  NotebookPen,
  Grid3X3,
  BookOpen,
} from "lucide-react"
import moment from "moment"
import { useEffect, useState } from "react"
import { MdOutlineDescription } from "react-icons/md"
import Markdown from "react-markdown"
import { useLocation, useNavigate, useParams } from "react-router-dom"
import { useGetCourse } from "@/services/query/live-course.query"
import PropTypes from "prop-types"
import { useSelector } from "react-redux"
import { useFilterHooks } from "@/hooks/common.hooks"
import LoadingSpinner from "@/components/custom/LoadingSpinner"
import ShiningButton from "../../components/shining-button"
import CourseOverview from "../course-overview"
import DegreeCourseCard from "./degree-course-card"

const LiveCourseDetailsPage = ({ courses, name, selectedCategories }) => {
  const navigate = useNavigate()
  const { id } = useParams()
  const [nanoCourses, setNanoCourses] = useState([])
  const [viewMode, setViewMode] = useState("degree")
  const user = useSelector((st) => st[ct.store.USER_STORE])
  const location = useLocation()
  const { sortBy } = useFilterHooks()
  const [courseDetails, setCourseDetails] = useState(null)
  const [loading, setLoading] = useState(true)
  const { courseData } = location.state || {}

  const getCurrentCourseNanoIds = () => {
    if (!courseData) return []

    return courseData.nano_course_ids || courseData.courseDetails?.nano_course_ids || []
  }

  const nanoIds = getCurrentCourseNanoIds()

  console.log("LiveCourseDetailsPage - Current Course Nano IDs:", nanoIds)
  console.log("LiveCourseDetailsPage - Course Data:", courseDetails)

  const isNanoCourse = name === "Nano courses"

  const filters = {
    userID: user.id,
    courseType: "LIVE",
    limit: 10,
    offset: 0,
    order: sortBy,
    search_query: "",
    for_subscribed: false,
    ...(isNanoCourse && nanoIds.length > 0 && { nano_course_ids: nanoIds }),
  }

  const { data: nanoData, isLoading, error } = useGetCourse(filters, selectedCategories)

  useEffect(() => {
    if (nanoData?.get_course?.courses) {
      setNanoCourses(nanoData.get_course.courses)
    }
  }, [nanoData])

  const formatPrerequisites = (prerequisites) => {
    if (!prerequisites) return ["No prerequisites specified"]
    if (typeof prerequisites === "string") {
      return prerequisites.split(",").map((item) => item.trim())
    }
    return prerequisites
  }

  useEffect(() => {
    const {
      title,
      description,
      rating,
      syllabus_url,
      reviews,
      base_currency,
      offer_title = "Special Offer!",
    } = courseData || {}

    if (courseData) {
      setCourseDetails({
        id: courseData?.id,
        title,
        description,
        rating,
        reviews,
        syllabus_url,
        skills: courseData?.course_skills || courseData?.courseDetails?.course_skills,
        base_currency,
        image: courseData.intro_image || courseData.imageUrl,
        offer_title,
        intro: courseData.intro,
        offer_desc:
          courseData.course_discount > 0
            ? `Enroll now and get a ${courseData.course_discount}% discount`
            : null,
        course_discount: courseData.course_discount || 0,
        course_price: courseData.course_price || 0,
        duration_days: `${courseData.duration_days} days`,
        course_level: courseData.course_level,
        last_updated_date: courseData.last_updated_date || "Last Updated December 23, 2024",
        course_type: courseData.course_type,
        course_language: courseData.course_language,
        prerequisites: formatPrerequisites(courseData.prerequisites),
      })
      setLoading(false)
    } else {
      const fetchCourseData = async () => {
        try {
          setLoading(true)
          navigate("/live-courses", {
            state: { error: "Course data not available. Please select a course from the list." },
          })
        } catch (error) {
          console.error("Error fetching course data:", error)
          navigate("/live-courses", {
            state: { error: "Failed to load course data. Please try again." },
          })
        }
      }
      fetchCourseData()
    }
  }, [courseData, id, navigate])

  const handleSyllabusDownload = () => {
    if (courseDetails?.syllabus_url) {
      window.open(courseDetails.syllabus_url, "_blank")
    }
  }

  const handleCourseAudit = (course) => {
    console.log("handleCourseAudit", course)

    navigate(`/live-courses/${course.id}${ct.route.COURSE_OVERVIEW}`, {
      state: { courseData: course, courseType: name },
    })
  }

  const renderCourseDetailsSection = () => {
    return (
      <CardContent>
        <div>
          <div className="flex flex-col gap-4">
            {/* Title */}
            <div className="flex items-center gap-2">
              <NotebookPen className="w-5 h-5 text-violet-600" />
              <h5 className="text-black dark:text-white font-semibold text-md">Prerequisites</h5>
            </div>



            {/* Prerequisites List */}
            <div className="w-full">
              {courseDetails?.prerequisites?.length > 0 ? (
                <ul className="list-disc list-inside space-y-2 text-sm text-gray-800 dark:text-gray-200">
                  {courseDetails.prerequisites.map((item, index) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-500 text-sm italic">No prerequisites required</p>
              )}
            </div>


          </div>

          {/* Description Section */}
          <div className="mt-3 mb-3">
            <h5 className="text-black dark:text-white font-semibold text-md mb-0 flex items-center">
              <MdOutlineDescription className="w-5 h-5 me-1 text-violet-600" /> Description
            </h5>
            <p className="mt-2 whitespace-pre-line leading-relaxed">
              <Markdown>
                {courseDetails?.description
                  ? courseDetails.description.replace(/^### /gm, "##### ").replace(/^- /gm, " ✅ ")
                  : ""}
              </Markdown>
            </p>
          </div>
        </div>
      </CardContent>
    )
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-violet-600" />
      </div>
    )
  }

  if (!courseDetails && !isNanoCourse) {
    return null
  }

  return (
    <ScrollArea className="h-[80vh] rounded-md border">
      <Card className="w-full max-w-full mx-auto space-y-8">
        {/* Course Header - Only show for non-nano courses or when in degree view */}
        {(!isNanoCourse || viewMode === "degree") && courseDetails && (
          <div className="bg-gradient-to-r from-[#0a0a2a] to-[#973DFF] text-white p-6 md:p-10 lg:p-12 rounded-lg shadow-lg">
            {/* Breadcrumbs */}
            <Breadcrumb className="text-white mb-4">
              <BreadcrumbList>
                <BreadcrumbItem />
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <span className="text-white font-medium">{courseDetails.title}</span>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>

            {/* Course Info */}
            <div className="flex flex-wrap lg:flex-nowrap items-center justify-between gap-6">
              <div className="space-y-4 w-full lg:max-w-2xl">
                <div className="flex items-center gap-4">
                  <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white">
                    {courseDetails.title}
                  </h1>
                  <StatusUpdationCell value={courseDetails.course_type} />
                </div>
                <p className="text-gray-300 text-sm md:text-base">{courseDetails.intro}</p>

                <div className="flex gap-x-3">
                  {viewMode === "degree" && !isNanoCourse && (
                    <ShiningButton
                      onClick={() =>
                        navigate(`/live-courses/${id}${ct.route.COURSE_OVERVIEW}`, {
                          state: { courseData },
                        })
                      }
                      className="mt-6"
                    >
                      Audit
                    </ShiningButton>
                  )}

                  <ShiningButton onClick={handleSyllabusDownload} className="mt-6">
                    Download Syllabus
                  </ShiningButton>
                </div>
              </div>

              {/* Course Image */}
              <div className="relative w-full lg:w-full flex justify-end">
                <img
                  src={courseDetails.image}
                  alt={courseDetails.title}
                  className="rounded-lg shadow-lg w-full max-w-md md:max-w-xl lg:max-w-xl lg:max-h-full object-cover"
                />
              </div>
            </div>
          </div>
        )}

        {/* Course Meta Information - Only show for non-nano courses or when in degree view */}
        {(!isNanoCourse || viewMode === "degree") && courseDetails && (
          <CardContent className="w-full">
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-center">
              <div className="flex items-center justify-center space-x-1.5">
                <ChartNoAxesCombined className="w-5 h-5 text-violet-600" />
                <p className="text-sm font-medium">{courseDetails.course_level}</p>
              </div>
              <div className="flex items-center justify-center space-x-1.5">
                <Clock10Icon className="w-5 h-5 text-violet-600" />
                <p className="text-sm font-medium">{courseDetails.duration_days}</p>
              </div>
              <div className="flex items-center justify-center space-x-1.5">
                <Activity className="w-5 h-5 text-violet-600" />
                <p>
                  {moment
                    .parseZone(courseData?.modified_at)
                    .local()
                    .format("MMM D, YYYY • hh:mm A")}
                </p>
              </div>
              <div className="flex items-center justify-center space-x-1.5">
                <Languages className="w-5 h-5 text-violet-600" />
                <p className="text-sm font-medium">{courseDetails.course_language}</p>
              </div>
            </div>
          </CardContent>
        )}

        {/* Improved Main Content Area */}
        {isNanoCourse ? (
          <div className="relative">
            {/* Content Container */}
            <CardContent className="p-0">
              <div
                className={`
           transition-all duration-300 ease-in-out
           ${viewMode === "degree"
                    ? "min-h-[600px] bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800"
                    : "min-h-[400px] bg-white dark:bg-gray-900"
                  }
        
           `}
              >
                {/* Loading State */}
                {isLoading && <LoadingSpinner />}

                {/* Error State */}
                {error && !isLoading && (
                  <div className="flex flex-col items-center justify-center h-64 space-y-4">
                    <div className="w-16 h-16 rounded-full bg-red-100 dark:bg-red-900/20 flex items-center justify-center">
                      <svg
                        className="w-8 h-8 text-red-600 dark:text-red-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <div className="text-center">
                      <p className="text-sm font-medium text-red-600 dark:text-red-400 mb-1">
                        Failed to load courses
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Please try refreshing the page or contact support
                      </p>
                    </div>
                  </div>
                )}

                {/* Content based on view mode */}
                {!isLoading && !error && (
                  <div>
                    {viewMode === "degree" ? (
                      <div className="h-full p-6">
                        {/* Course Overview Component */}
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                          <CourseOverview
                            name={name}
                            nanoIds={nanoIds}
                            nanoCourses={nanoCourses}
                            onAuditClick={handleCourseAudit}
                          />
                        </div>
                      </div>
                    ) : (
                      <div className="p-6">
                        {/* All Courses View Header */}
                        <div className="mb-6">
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 rounded-lg bg-violet-100 dark:bg-violet-900/20 flex items-center justify-center">
                                <Grid3X3 className="w-5 h-5 text-violet-600 dark:text-violet-400" />
                              </div>
                              <div>
                                <h2 className="text-xl mt-4 font-bold text-gray-900 dark:text-white">
                                  All Available Courses
                                </h2>
                              </div>
                            </div>

                            {/* Course Count Badge */}
                            <div className="flex items-center gap-2 px-3 py-1.5 bg-violet-100 dark:bg-violet-900/20 rounded-full">
                              <div className="w-2 h-2 rounded-full bg-violet-600 dark:bg-violet-400" />
                              <span className="text-sm font-medium text-violet-700 dark:text-violet-300">
                                {nanoCourses.length}{" "}
                                {nanoCourses.length === 1 ? "Course" : "Courses"}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Courses Grid */}
                        {nanoCourses && nanoCourses.length > 0 ? (
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                            {nanoCourses.map((course, index) => (
                              <div key={course.id || index}>
                                <DegreeCourseCard
                                  course={course}
                                  onAuditClick={handleCourseAudit}
                                  courseData={courseData}
                                />
                              </div>
                            ))}
                          </div>
                        ) : (
                          /* Empty State */
                          <div className="flex flex-col items-center justify-center h-64 space-y-4">
                            <div className="w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                              <Grid3X3 className="w-8 h-8 text-gray-400 dark:text-gray-600" />
                            </div>
                            <div className="text-center">
                              <p className="text-lg font-medium text-gray-600 dark:text-gray-400 mb-1">
                                No courses available
                              </p>
                              <p className="text-sm text-gray-500 dark:text-gray-500">
                                Check back later for new course offerings
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </div>
        ) : (
          /* Non-nano course content */
          <div className="bg-white dark:bg-gray-900 rounded-lg">{renderCourseDetailsSection()}</div>
        )}
      </Card>
    </ScrollArea>
  )
}

LiveCourseDetailsPage.propTypes = {
  courses: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      nano_course_ids: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])),
      courseDetails: PropTypes.shape({
        nano_course_ids: PropTypes.arrayOf(
          PropTypes.oneOfType([PropTypes.string, PropTypes.number])
        ),
      }),
    })
  ).isRequired,
  name: PropTypes.string,
  selectedCategories: PropTypes.array,
}

export default LiveCourseDetailsPage
