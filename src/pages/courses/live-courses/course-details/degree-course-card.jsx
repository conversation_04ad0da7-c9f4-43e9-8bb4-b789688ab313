import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Clock10Icon, Languages, Play } from "lucide-react"
import { StatusUpdationCell } from "@/components/custom/cutsom-table/table-cells"

import PropTypes from "prop-types"
import { Button } from "@/components/ui/button"

const DegreeCourseCard = ({ course, onAuditClick }) => {
    console.log("DegreeCourseCard", course)

    const formatDuration = (days) => {
        if (!days) return "Duration not specified"
        return `${days} days`
    }

    return (
        <Card className="group relative w-full max-w-xs mx-auto bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 overflow-hidden rounded-lg">
            {/* Course Image Container - Reduced height */}
            <div className="relative h-32 w-full overflow-hidden">
                <img
                    src={course.intro_image || "/placeholder-course.jpg"}
                    alt={course.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />

                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent" />

                {/* Course Type Badge */}
                <div className="absolute top-2 right-2 z-10">
                    <StatusUpdationCell value={course.course_type} />
                </div>

                {/* Course Level Badge */}
                <div className="absolute top-2 left-2 z-10">
                    <Badge
                        variant="secondary"
                        className="bg-white/90 text-gray-800 font-medium text-xs px-1.5 py-0.5"
                    >
                        {course.course_level || "Beginner"}
                    </Badge>
                </div>

                {/* Play Icon Overlay */}
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-white/20 backdrop-blur-sm rounded-full p-3">
                        <Play className="w-6 h-6 text-white fill-white" />
                    </div>
                </div>
            </div>

            <CardContent className="p-4 space-y-3">
                {/* Course Title - Reduced font size */}
                <div className="space-y-1">
                    <h3 className="text-base font-bold text-gray-900 dark:text-white line-clamp-2 leading-snug group-hover:text-blue-600 transition-colors duration-300">
                        {course.title}
                    </h3>

                    {/* Course Description - More compact */}
                    <p className="text-gray-600 dark:text-gray-400 text-xs line-clamp-2 leading-relaxed">
                        {course.intro ||
                            course.description ||
                            "Enhance your skills with this comprehensive course"}
                    </p>
                </div>

                {/* Course Meta Information - Compact grid */}
                <div className="grid grid-cols-2 gap-2">
                    <div className="flex items-center space-x-1.5 text-gray-600 dark:text-gray-400">
                        <div className="p-1 bg-blue-100 dark:bg-blue-900/30 rounded">
                            <Clock10Icon className="w-3 h-3 text-blue-600" />
                        </div>
                        <span className="text-xs font-medium truncate">
                            {formatDuration(course.duration_days)}
                        </span>
                    </div>

                    <div className="flex items-center space-x-1.5 text-gray-600 dark:text-gray-400">
                        <div className="p-1 bg-green-100 dark:bg-green-900/30 rounded">
                            <Languages className="w-3 h-3 text-green-600" />
                        </div>
                        <span className="text-xs font-medium truncate">
                            {course.course_language || "English"}
                        </span>
                    </div>
                </div>

                {/* Action Button - Compact */}
                <div className="pt-1">
                    <Button variant="secondary" onClick={() => onAuditClick(course)}>
                        View more
                    </Button>
                </div>
            </CardContent>
        </Card>
    )
}

DegreeCourseCard.propTypes = {
    course: PropTypes.shape({
        id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
        title: PropTypes.string.isRequired,
        intro: PropTypes.string,
        description: PropTypes.string,
        intro_image: PropTypes.string,
        course_type: PropTypes.string,
        course_level: PropTypes.string,
        duration_days: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        course_language: PropTypes.string,
        modified_at: PropTypes.string,
        course_price: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        base_currency: PropTypes.string,
        course_discount: PropTypes.number,
        course_skills: PropTypes.arrayOf(PropTypes.string),
    }).isRequired,
    onAuditClick: PropTypes.func.isRequired,
}

export default DegreeCourseCard
