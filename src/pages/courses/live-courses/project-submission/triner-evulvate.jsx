import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { zodResolver } from "@hookform/resolvers/zod"
import { ChevronDown, ChevronUp } from "lucide-react"
import PropTypes from "prop-types"
import { useEffect, useState } from "react"
import { Controller, useForm } from "react-hook-form"
import { z } from "zod"

// Define validation schema with dynamic marks validation
const createEvaluationSchema = (tasks) => z.object({
  projectStatus: z.string().refine((val) => ["ACCEPTED", "REJECTED", "PENDING"].includes(val), {
    message: "Project Acceptance is required",
  }),
  projectFeedback: z.string().min(5, "Min 5 characters required"),
  evaluations: z.array(
    z.object({
      trainer_feedback: z.string().min(5, "Min 5 characters required"),
      marks_obtained: z.number().positive("Please enter valid marks"),
      acceptance_status: z
        .string()
        .refine((val) => ["ACCEPTED", "REJECTED", "PENDING"].includes(val), {
          message: "Task status is required",
        }),
    })
  ).superRefine((evaluations, ctx) => {
    // Validate marks_obtained against total_marks for each task
    evaluations.forEach((evaluation, index) => {
      const task = tasks[index];
      const totalMarks = task?.total_marks || task?.max_marks || 100; // fallback to 100 if not provided

      if (evaluation.marks_obtained > totalMarks) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `Marks cannot exceed ${totalMarks}`,
          path: [index, "marks_obtained"],
        });
      }
    });
  }),
})

const TrainerEvaluate = ({
  onClose,
  titleText,
  tasks,
  onHandleEvaluate,
  studentId,
  studentName,
}) => {
  const [expandedTask, setExpandedTask] = useState(null)

  const {
    handleSubmit,
    control,
    formState: { errors, isSubmitted },
  } = useForm({
    resolver: zodResolver(createEvaluationSchema(tasks)),
    defaultValues: {
      projectStatus: "",
      projectFeedback: "",
      evaluations: tasks?.map((task) => ({
        task_id: task.id,
        trainer_feedback: "",
        marks_obtained: 0,
        acceptance_status: "",
      })),
    },
    mode: "onChange",
  })

  const toggleTask = (taskId) => {
    setExpandedTask(expandedTask === taskId ? null : taskId)
  }

  const onSubmit = (data) => {
    // Map through the evaluations and include task_id and student_id
    const evaluationsWithIds = data.evaluations.map((evaluation, index) => ({
      ...evaluation,
      task_id: tasks[index].id, // Include task_id
      student_id: studentId, // Include student_id
    }))

    // Call the onHandleEvaluate function with the updated data
    onHandleEvaluate(data.projectStatus, data.projectFeedback, evaluationsWithIds)

    // Log for debugging
    console.log("Submitted Evaluations:", {
      projectStatus: data.projectStatus,
      projectFeedback: data.projectFeedback,
      evaluations: evaluationsWithIds,
    })
  }

  // Automatically expand tasks with validation errors
  useEffect(() => {
    if (errors.evaluations && isSubmitted) {
      const taskWithError = tasks?.find((task, index) => {
        return (
          errors.evaluations[index]?.acceptance_status ||
          errors.evaluations[index]?.trainer_feedback ||
          errors.evaluations[index]?.marks_obtained
        )
      })

      if (taskWithError) {
        setExpandedTask(taskWithError.id)
      }
    }
  }, [errors, tasks, isSubmitted])

  // Submit handler with error handling
  const handleFormSubmit = () => {
    handleSubmit((data) => {
      onSubmit(data)
    })()
  }

  return (
    <div>
      <ScrollArea className="h-[75vh]">
        <div className="mb-4">
          <p className="text-lg font-medium">
            Project Title: <span className="font-semibold text-gray-700">{titleText}</span>
          </p>
          <p className="text-lg font-medium">
            Student Name: <span className="font-semibold text-gray-700">{studentName}</span>
          </p>
          <p className="text-lg font-medium">
            Student ID: <span className="font-semibold text-gray-700">{studentId}</span>
          </p>
        </div>

        {/* Project Acceptance */}
        <div className="m-1">
          <Label className="mb-1 text-gray-700">
            Project Acceptance <span className="text-red-500">*</span>
          </Label>
          <Controller
            name="projectStatus"
            control={control}
            rules={{ required: true }}
            render={({ field }) => (
              <Select value={field.value} onValueChange={field.onChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ACCEPTED">Accepted</SelectItem>
                  <SelectItem value="REJECTED">Rejected</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                </SelectContent>
              </Select>
            )}
          />
          {errors.projectStatus && (
            <p className="text-red-500 text-sm mt-1">
              {errors.projectStatus.message || "Project status is required"}
            </p>
          )}
        </div>

        {/* Project Feedback */}
        <div className="m-1">
          <Label className="mb-1 text-gray-700">
            Project Feedback <span className="text-red-500">*</span>
          </Label>
          <Controller
            name="projectFeedback"
            control={control}
            render={({ field }) => <Textarea {...field} placeholder="Enter project feedback" />}
          />
          {errors.projectFeedback && (
            <p className="text-red-500 text-sm mt-1">{errors.projectFeedback.message}</p>
          )}
        </div>

        {/* Task Evaluation Section */}
        <div className="space-y-4 mt-3">
          {tasks?.map((task, index) => {
            const totalMarks = task?.total_marks || task?.max_marks || 100; // fallback to 100

            return (
              <Card key={task.id} className="p-2">
                <Button
                  className="flex justify-between items-center cursor-pointer w-full text-left"
                  onClick={() => toggleTask(task.id)}
                  role="button"
                >
                  <h3 className="text-lg font-semibold">{task.title}</h3>
                  {expandedTask === task.id ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
                </Button>

                {expandedTask === task.id && (
                  <div>
                    <div className="mt-4">
                      <Label className="mb-1 text-gray-700">
                        Feedback <span className="text-red-500">*</span>
                      </Label>
                      <Controller
                        name={`evaluations.${index}.trainer_feedback`}
                        control={control}
                        render={({ field }) => <Textarea {...field} placeholder="Enter feedback" />}
                      />
                      {errors.evaluations?.[index]?.trainer_feedback && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.evaluations[index].trainer_feedback.message}
                        </p>
                      )}
                    </div>

                    {/* Marks */}
                    <div className="mt-4">
                      <Label className="mb-1 text-gray-700">
                        Marks (Max: {totalMarks}) <span className="text-red-500">*</span>
                      </Label>
                      <Controller
                        name={`evaluations.${index}.marks_obtained`}
                        control={control}
                        render={({ field }) => (
                          <Input
                            {...field}
                            placeholder={`Enter marks (0-${totalMarks})`}
                            onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                            onKeyDown={(e) => {
                              if (
                                !/[\d]/.test(e.key) && // Allow only digits
                                e.key !== "Backspace" &&
                                e.key !== "ArrowLeft" &&
                                e.key !== "ArrowRight" &&
                                e.key !== "Delete" &&
                                e.key !== "Tab"
                              ) {
                                e.preventDefault()
                              }
                            }}
                          />
                        )}
                      />
                      {errors.evaluations?.[index]?.marks_obtained && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.evaluations[index].marks_obtained.message}
                        </p>
                      )}
                    </div>

                    {/* Status */}
                    <div className="py-4">
                      <Label className="mb-1 text-gray-700">
                        Status <span className="text-red-500">*</span>
                      </Label>
                      <Controller
                        name={`evaluations.${index}.acceptance_status`}
                        control={control}
                        rules={{ required: true }}
                        render={({ field }) => (
                          <Select value={field.value} onValueChange={field.onChange}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="ACCEPTED">Accept</SelectItem>
                              <SelectItem value="REJECTED">Reject</SelectItem>
                              <SelectItem value="PENDING">Pending</SelectItem>
                            </SelectContent>
                          </Select>
                        )}
                      />
                      {errors.evaluations?.[index]?.acceptance_status && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.evaluations[index].acceptance_status.message ||
                            "Task status is required"}
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </Card>
            )
          })}
        </div>
      </ScrollArea>

      {/* Action Buttons */}
      <div className="mt-6 flex justify-end gap-x-3">
        <Button onClick={onClose} variant="secondary" type="button">
          Cancel
        </Button>
        <Button onClick={handleFormSubmit} type="button" variant="primary">
          Submit
        </Button>
      </div>
    </div>
  )
}

TrainerEvaluate.propTypes = {
  onClose: PropTypes.func.isRequired,
  titleText: PropTypes.string.isRequired,
  tasks: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      title: PropTypes.string.isRequired,
      description: PropTypes.string.isRequired,
      total_marks: PropTypes.number, // Added for total marks
      max_marks: PropTypes.number,   // Alternative field name
    })
  ).isRequired,
  onHandleEvaluate: PropTypes.func.isRequired,
  studentId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  studentName: PropTypes.string.isRequired,
}

export default TrainerEvaluate