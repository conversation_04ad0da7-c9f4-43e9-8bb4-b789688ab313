export const trainer = [
  {
    id: "students",
    accessorKey: "students",
    header: "Students Name",
  },

  {
    id: "submission",
    accessorKey: "submission",
    header: "Submission",
  },
  {
    id: "obtained_marks",
    accessorKey: "obtained_marks",
    header: "Obtained Marks",
  },

  {
    id: "feedback",
    accessorKey: "feedback",
    header: "Feedback",
  },

  {
    id: "reference",
    accessorKey: "reference",
    header: "Reference",
  },
  {
    id: "acceptance_status",
    accessorKey: "acceptance_status",
    header: "Acceptance Status",
  },

  {
    id: "action",
    accessorKey: "action",
    header: "Action",
  },
]

export const admin = [
  {
    id: "students",
    accessorKey: "students",
    header: "Students Name",
  },

  {
    id: "submission",
    accessorKey: "submission",
    header: "Submission",
  },
  {
    id: "obtained_marks",
    accessorKey: "obtained_marks",
    header: "Obtained Marks",
  },

  {
    id: "feedback",
    accessorKey: "feedback",
    header: "Feedback",
  },

  {
    id: "reference",
    accessorKey: "reference",
    header: "Reference",
  },
  {
    id: "acceptance_status",
    accessorKey: "acceptance_status",
    header: "Acceptance Status",
  },
  {
    id: "action",
    accessorKey: "action",
    header: "Action",
  },
]

export const student = [
  {
    id: "description",
    accessorKey: "description",
    header: "Description",
  },
  {
    id: "submission",
    accessorKey: "submission",
    header: "submission",
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Status",
  },
  {
    id: "remarks",
    accessorKey: "remarks",
    header: "Remarks",
  },
  {
    id: "obtained_marks",
    accessorKey: "obtained_marks",
    header: "Obtained Marks",
  },
]

export const vendor = [
  {
    id: "description",
    accessorKey: "description",
    header: "Description",
  },
  {
    id: "submit",
    accessorKey: "submit",
    header: "Submit",
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Status",
  },
  {
    id: "remarks",
    accessorKey: "remarks",
    header: "Remarks",
  },
  {
    id: "obtained_marks",
    accessorKey: "obtained_marks",
    header: "Obtained Marks",
  },
]

export const getColumnsByUserType = (userType) => {
  switch (userType.toUpperCase()) {
    case "TRAINER":
      return trainer
    case "STUDENT":
      return student
    case "ADMIN":
      return admin
    case "VENDOR":
      return vendor
    default:
      return [] // Return an empty array if the userType is invalid
  }
}
