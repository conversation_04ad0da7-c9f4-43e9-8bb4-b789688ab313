import CustomSearchbar from "@/components/custom/custom-search"
import DataTable from "@/components/custom/cutsom-table"
import {
  Date<PERSON><PERSON>,
  Link<PERSON><PERSON>,
  StatusUpdationCell,
} from "@/components/custom/cutsom-table/table-cells"
import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import { But<PERSON> } from "@/components/ui/button"
import { useFilterHooks, useOpenCloseHooks, usePaginationHooks } from "@/hooks/common.hooks"
import useTableConfig from "@/hooks/use-table.hooks"
import { useDebounce } from "@/hooks/useDebounce"
import { fetchTasks, openPublicUrl } from "@/services/api/project-api."
import { useCreateEvaluation, useProjectSubmission } from "@/services/query/project-query"; // Removed useFetchProjectTasks import
import { isPaidUserPropTypes } from "@/utils/props-types"
import ct from "@constants/"
import { flexRender } from "@tanstack/react-table"
import { ArrowLef<PERSON>, FilePlus2 } from "lucide-react"
import PropTypes from "prop-types"
import { useCallback, useState } from "react"; // Import useCallback
import { useSelector } from "react-redux"
import CustomSidebar from "../../components/custom-sidebar"
import { getColumnsByUserType } from "./project-submission-coloumns"
import TrainerEvaluate from "./triner-evulvate"

const ProjectSubmission = ({ listOfProject, setView, userType, projectId, isPaidUser }) => {
  const { id: userID, userRole } = useSelector((state) => state.user)

  const [searchingValue, setSearchingValue] = useState("")
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 })
  const [studentId, setStudentId] = useState(null)
  const [projectTrackingId, setProjectTrackingId] = useState(null)
  const [studentName, setStudentName] = useState("")
  const { sortBy, sortByField } = useFilterHooks()
  const { limit } = usePaginationHooks(0, 10)
  const { mutate: createEvaluation } = useCreateEvaluation()
  const debouncedSearchQuery = useDebounce(searchingValue, 500)
  const course = useSelector((st) => st[ct.store.COURSES]?.activeCourseDetails)
  const calculateOffset = (pageIndex, pageSize) => pageIndex * pageSize
  const { data: projectSubmission } = useProjectSubmission({
    course_id: course?.id,
    project_id: projectId?.project_id,
    filters: {
      limit,
      offset: calculateOffset(pagination.pageIndex, pagination.pageSize),
      sort_by: sortBy,
      sort_by_field: sortByField,
      search_query: debouncedSearchQuery?.length > 0 ? debouncedSearchQuery : null,
    },
  })

  const { table, found, pageCount } = useTableConfig(
    projectSubmission?.data,
    getColumnsByUserType(userType),
    projectSubmission?.metadata?.total_records,
    setPagination,
    pagination
  )
  const { handleOpen, open, handleClose } = useOpenCloseHooks()

  // State to hold tasks and query function for fetching tasks
  const [tasks, setTasks] = useState(null)

  const fetchTasksData = useCallback(
    async (student_id) => {
      const filters = {
        student_id,
        for_students: true,
      }
      try {
        const { data } = await fetchTasks(
          projectId?.project_id,
          course?.id,
          filters,
          userID,
          userRole,
          "SIGNATURE"
        )
        setTasks(data)
      } catch (error) {
        setTasks(null) // set null to tasks on error
      }
    },
    [projectId?.project_id, course?.id]
  )

  const onHandleEvaluate = (projectStatus, projectFeedback, evaluations) => {
    createEvaluation(
      {
        course_id: course?.id,
        project_id: projectId?.project_id,
        tasks: evaluations, // Moved inside the object correctly
        project_tracking_id: projectTrackingId,
        acceptance_status: projectStatus,
        trainer_feedback: projectFeedback,
      },
      {
        onSuccess: () => {
          handleClose()
          successToast("Project Evaluated", "The Project has been successfully evaluated!")
        },
        onError: () => {
          handleClose()
          failureToast("Evaluation Failed", "An error occurred while evaluating the project.")
        },
      }
    )
  }
  const downloadFile = (submission) => {
    openPublicUrl(submission, userID, userRole, "SIGNATURE")
  }
  // Memoized renderCellContent function with useCallback
  const renderCellContent = useCallback(
    (cell, row) => {
      const {
        project_id,
        submission,
        status,
        id,
        submission_time,
        student_id,
        student_name,
        marks_obtained,
        feedback_trainer,
        feedback_student,
        submission_type,
        acceptance_status,
      } = row?.original || {}

      switch (cell.column.id) {
        case "S_No":
          return <p>{cell.row.index + 1 + pagination.pageIndex * pagination.pageSize}</p>
        case "feedback":
          return <p>{feedback_student || "-"}</p>
        case "submission":
          return (
            <DateCell
              key="submission"
              value={submission_time ? submission_time.split(".")[0] : "-"}
            />
          )
        case "acceptance_status":
          return <StatusUpdationCell value={acceptance_status || "-"} />

        case "students":
          return <p key="students">{student_name || "-"}</p>
        case "obtained_marks":
          return <p key="students">{marks_obtained || "-"}</p>
        case "project_id":
          return <p key="task_id">{project_id || "-"}</p>
        case "status":
          return <p key="status">{status || "-"}</p>
        case "reference":
          return submission_type === "LINK" ? (
            <LinkCell value={submission} isPaidUser={isPaidUser} />
          ) : (
            <Button onClick={() => downloadFile(submission)}>
              <FilePlus2 className="w-5 h-5 text-yellow-500" />
            </Button>
          )
        case "remarks":
          return (
            <p key="remarks">
              {["student", "vendor"].includes(userType)
                ? feedback_trainer || "-"
                : feedback_student || "-"}
            </p>
          )
        case "action":
          return (
            <div>
              <Button
                className="h-5"
                // disabled={acceptance_status !== "PENDING"}
                variant="primary"
                onClick={() => {
                  handleOpen(true)
                  setStudentId(student_id)
                  fetchTasksData(student_id)
                  setProjectTrackingId(id)
                  setStudentName(student_name)
                }}
              >
                Evaluate
              </Button>
            </div>
          )

        default:
          return flexRender(cell.column.columnDef.cell, cell.getContext())
      }
    },
    [pagination.pageIndex, pagination.pageSize, userType, handleOpen, fetchTasksData]
  )

  const getProjectTitle = (Project, Id) => {
    return Project?.data?.find((project) => project.id === Id)?.title || "Untitled"
  }
  const titleText = getProjectTitle(listOfProject, projectId.project_id)

  const evaluateProps = {
    onClose: handleClose,
    titleText,
    tasks: tasks?.data, // Pass fetched tasks data
    onHandleEvaluate,
    projectTrackingId,
    studentId,
    studentName,
  }

  return (
    <>
      <CustomSidebar
        isOpen={open}
        onClose={handleClose}
        title="Evaluate Project"
        description=""
        content={<TrainerEvaluate {...evaluateProps} />}
      />
      <Button
        onClick={() => {
          setView("projects")
        }}
      >
        <ArrowLeft size={16} />
        <p className="text-lg font-semibold text-primary ml-1">{titleText}</p>
      </Button>

      <div className="mt-4">
        <CustomSearchbar
          inputSize="w-[20rem]"
          placeholder="Search by title..."
          searchedValue={searchingValue}
          setSearchedValue={(e) => setSearchingValue(e?.target.value)}
        />
      </div>
      <DataTable
        renderCellContent={renderCellContent}
        columns={getColumnsByUserType(userType)}
        table={table}
        found={found}
        pageCount={pageCount}
        pagination={pagination}
        notFoundPlaceholder="No Submissons Found"
      />
    </>
  )
}

ProjectSubmission.propTypes = {
  Submissionpagination: PropTypes.shape({
    pageIndex: PropTypes.number.isRequired,
    pageSize: PropTypes.number.isRequired,
  }).isRequired,
  setSubmissionPagination: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  setView: PropTypes.func.isRequired,
  userType: PropTypes.oneOf(["student", "trainer"]).isRequired,
  projectSubmission: PropTypes.shape({
    data: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.number.isRequired,
        project_id: PropTypes.number.isRequired,
        submission: PropTypes.string,
        status: PropTypes.string.isRequired,
        submission_status: PropTypes.string.isRequired,
        deadline: PropTypes.string.isRequired,
        student_id: PropTypes.number.isRequired,
        submission_type: PropTypes.string.isRequired,
        marks_obtained: PropTypes.number,
        feedback_trainer: PropTypes.string,
        feedback_student: PropTypes.string,
        remark: PropTypes.string,
      })
    ).isRequired,
  }).isRequired,
  listOfProject: PropTypes.shape({
    data: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.number.isRequired,
        title: PropTypes.string.isRequired,
      })
    ).isRequired,
  }).isRequired,
  projectId: PropTypes.shape({
    project_id: PropTypes.number.isRequired,
    course_id: PropTypes.number.isRequired,
  }).isRequired,

  isPaidUser: isPaidUserPropTypes,
}

export default ProjectSubmission

// Export only fetchTasks function
