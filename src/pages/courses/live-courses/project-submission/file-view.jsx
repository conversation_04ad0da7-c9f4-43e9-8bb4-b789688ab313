import { useLocation } from "react-router-dom"

const FileView = () => {
  const location = useLocation()
  const queryParams = new URLSearchParams(location.search)
  const fileUrl = queryParams.get("file") // Extract 'file' parameter from URL

  if (!fileUrl) {
    return <div className="text-red-500 text-center mt-10">No file provided</div>
  }

  return (
    <div className="w-full h-screen flex justify-center items-center">
      <iframe src={fileUrl} className="w-full h-full border-none" title="File Preview" />
    </div>
  )
}

export default FileView
