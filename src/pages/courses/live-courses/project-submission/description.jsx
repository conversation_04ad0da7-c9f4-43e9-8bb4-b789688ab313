import { Toolt<PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import PropTypes from "prop-types"

const Description = ({ des }) => {
  const truncate = (str, maxLength = 25) => {
    if (str?.length > maxLength) {
      return `${str?.slice(0, maxLength)}...`
    }
    return str
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger className="text-left">
          <div className="max-w-xs">{truncate(des, 50)}</div>
        </TooltipTrigger>
        {des?.length > 50 && (
          <TooltipContent>
            <p className="max-w-xs">{des}</p>
          </TooltipContent>
        )}
      </Tooltip>
    </TooltipProvider>
  )
}

Description.propTypes = {
  des: PropTypes.string.isRequired,
}

export default Description
