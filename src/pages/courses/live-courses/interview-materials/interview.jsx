import CustomSearchbar from "@/components/custom/custom-search"
import DataTable from "@/components/custom/cutsom-table"
import {
  <PERSON><PERSON>ell,
  Link<PERSON>ell,
  StatusUpdationCell,
} from "@/components/custom/cutsom-table/table-cells"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import useTableConfig from "@/hooks/use-table.hooks"
import { openPublicUrl } from "@/services/api/project-api."
import { USER_ROLES } from "@/utils/constants"
import { isPaidUserPropTypes } from "@/utils/props-types"
import ct from "@constants/"
import { FilePlus2 } from "lucide-react"
import PropTypes from "prop-types"
import { useState } from "react"
import { useSelector } from "react-redux"
import AichatpdfView from "./aichat-pdfView"
import CourseContantFeedback from "./course-contant-feedback"
import { getInterViewMeterialColumns } from "./interview-material-colums"

export const InterviewMeterialUi = ({
  setIsSideBarOpen,
  handleEdit,
  interViewMaterials,
  handleDelete,
  handleAddFeedBack,
  pagination,
  setPagination,
  handleOpenInterViewStatusDialoge,
  setSearchingValue,
  searchingValue,
  isPaidUser,
}) => {
  const loginData = useSelector((st) => st[ct.store.USER_STORE])
  const { id: userID, userRole } = useSelector((state) => state.user)
  const userType = loginData?.userRole
  const [view, setView] = useState("table")
  const [coursematerialId, setCourseMaterialId] = useState(null)
  const dynamicHeight = interViewMaterials?.data?.data?.length > 6 ? "h-[55vh]" : undefined
  const [feedbackTitle, setFeedbackTitle] = useState("")

  const { table, found, pageCount } = useTableConfig(
    interViewMaterials?.data?.data,
    getInterViewMeterialColumns(userType),
    interViewMaterials?.data.metadata?.total_records,
    setPagination,
    pagination
  )

  const handleFeedbackClick = (e, id, title) => {
    e.stopPropagation()
    if (userType === "TRAINER" || userType === "ADMIN") {
      setCourseMaterialId(id)
      setView("feedback")
      setFeedbackTitle(title)
    }
  }

  const downloadFile = (resource_link) => {
    openPublicUrl(resource_link)
  }

  const truncateText = (text = "", wordLimit = 3) => {
    if (!text) return ""

    const words = text.split(" ")
    const truncatedText =
      words.length > wordLimit ? `${words.slice(0, wordLimit).join(" ")}...` : text

    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <p className="truncate cursor-pointer">{truncatedText}</p>
          </TooltipTrigger>
          <TooltipContent>{text}</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  const renderCellContent = (cell, row) => {
    const {
      id = null, // Ensure id is always defined
      title,
      questions_count,
      resource_link,
      resource_type,
      description,
      publish_status,
      material_status,
      feedback_count,
    } = row?.original || {} // Ensure row.original exists

    switch (cell.column.id) {
      case "no":
        return <p>{cell.row.index + 1 + pagination.pageIndex * pagination.pageSize}</p>
      case "title":
        return <p>{title}</p>

      case "total_question":
        return <p>{questions_count}</p>
      case "link":
        return resource_type === "LINK" ? (
          <LinkCell value={resource_link} isPaidUser={isPaidUser} />
        ) : (
          <Button onClick={() => downloadFile(resource_link)}>
            <FilePlus2 className="w-5 h-5 text-yellow-500" />
          </Button>
        )
      case "status":
        return <StatusUpdationCell value={material_status || "-"} />
      case "published":
        return <StatusUpdationCell value={publish_status || "-"} />
      case "description":
        // eslint-disable-next-line no-nested-ternary
        return <p>{truncateText(description, 15) || "-"}</p>

      case "number_of_feedback":
        return (
          <Button
            variant="secondary"
            size="xs"
            type="button"
            onClick={(e) => handleFeedbackClick(e, id, title)}
            className={isPaidUser?.class}
            disabled={isPaidUser?.paidUser}
          >
            View{" "}
            <span className="h-4 w-4 text-xs font-semibold bg-[hsl(var(--primary)/.1)] rounded-full">
              {feedback_count || 0}
            </span>
          </Button>
        )

      case "action":
        return isPaidUser?.paidUser ? (
          "-"
        ) : (
          <div>
            {userType === USER_ROLES.STUDENT ? (
              <ActionCell
                row={row}
                label5="Add Feedback"
                onFeedback={handleAddFeedBack}
                isAddFeedback
              />
            ) : (
              <ActionCell
                label2="Edit"
                row={row}
                label3="Delete"
                onEdit={handleEdit}
                isDelete
                isEdit
                onDelete={handleDelete}
                label1="Update Status"
                isView
                onView={handleOpenInterViewStatusDialoge}
              />
            )}
          </div>
        )

      default:
        return cell.column.columnDef.cell ? cell.column.columnDef.cell(cell) : null
    }
  }

  const renderView = () => {
    switch (view) {
      case "feedback":
        return (
          <CourseContantFeedback
            setView={setView}
            coursematerialId={coursematerialId}
            feedbackTitle={feedbackTitle}
          />
        )
      case "aiChat":
        return <AichatpdfView userType={userType} setView={setView} />
      default:
        return (
          <>
            <div className="mt-3 flex justify-between">
              <CustomSearchbar
                inputSize="w-[20rem]"
                placeholder="Search by title..."
                searchedValue={searchingValue}
                setSearchedValue={(e) => setSearchingValue(e?.target.value)}
              />
              {(userType === "TRAINER" || userType === "ADMIN") && (
                <div className="flex justify-end">
                  <Button onClick={() => setIsSideBarOpen(true)} variant="primary">
                    <FilePlus2 /> Create
                  </Button>
                </div>
              )}
            </div>

            <DataTable
              renderCellContent={renderCellContent}
              columns={getInterViewMeterialColumns(userType)}
              table={table}
              found={found}
              height={dynamicHeight}
              pageCount={pageCount}
              pageName="Interview"
              pagination={pagination}
              // onRowClick={handleRowClick}
              notFoundPlaceholder="No Interview Materials Found"
            />
          </>
        )
    }
  }

  return <div className="w-full">{renderView()}</div>
}

InterviewMeterialUi.propTypes = {
  setIsSideBarOpen: PropTypes.func.isRequired,
  handleEdit: PropTypes.func.isRequired,
  interViewMaterials: PropTypes.objectOf.isRequired,
  handleDelete: PropTypes.func.isRequired,
  setPagination: PropTypes.func.isRequired,
  handleOpenInterViewStatusDialoge: PropTypes.func.isRequired,
  handleAddFeedBack: PropTypes.func.isRequired,
  pagination: PropTypes.objectOf({
    pageIndex: PropTypes.number.isRequired,
    pageCount: PropTypes.number.isRequired,
  }),
  isPaidUser: isPaidUserPropTypes,
  setSearchingValue: PropTypes.func,
  searchingValue: PropTypes.string,
}

export default InterviewMeterialUi
