import FormInput from "@/components/custom/custom-forms/form-input"
import FormSelect from "@/components/custom/custom-forms/form-select"
import FormTextArea from "@/components/custom/custom-forms/form-textarea"
import FileUploadCard from "@/components/custom/upload-file"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { USER_ROLES } from "@/utils/constants"
import { zodResolver } from "@hookform/resolvers/zod"
import PropTypes from "prop-types"
import { FormProvider, useForm } from "react-hook-form"
import * as z from "zod"

const fileType = [
  { value: "DOCUMENT", label: "Document" },
  { value: "LINK", label: "Link" },
]
const options = [
  { value: "PUBLISHED", label: "Published" },
  { value: "DRAFT", label: "Draft" },
  { value: "ARCHIVED", label: "Archived" },
  { value: "UNLISTED", label: "Unlisted" },
]
const materialStatusOptions = [
  { value: "NOT_STARTED", label: "Not Started" },
  { value: "IN_PROGRESS", label: "In Progress" },
  { value: "COMPLETED", label: "Completed" },
]

// ✅ Feedback Form Validation Schema
const feedbackSchema = z.object({
  student_feedback: z.string().min(5, "Feedback must be at least 5 characters"),
  material_status: z.enum(["NOT_STARTED", "IN_PROGRESS", "COMPLETED"], {
    errorMap: () => ({ message: "Material status is required" }),
  }),
})

const CreateInterviewMaterials = ({
  form,
  onClose,
  setValue,
  userType,
  handleSubmit,
  onCreateClassForm,
  setStudentFeedBack,
  interviewMaterialId,
  isLoading,
}) => {
  const feedbackForm = useForm({
    resolver: zodResolver(feedbackSchema),
    mode: "onSubmit",
    defaultValues: {
      student_feedback: "", // Ensures the error doesn't show immediately
      material_status: undefined, // Ensures no pre-selected value
    },
  })

  const { handleSubmit: handleFeedbackSubmit } = feedbackForm

  // const resourceType = form.watch("resource_type")?.toLowerCase()

  const onSubmitFeedback = (data) => {
    setStudentFeedBack(data)
  }
  console.log("isLoading", isLoading);

  const resourseType = form.watch("resource_type")

  return (
    <ScrollArea className="h-[85vh]">
      <div className="px-2">
        {/* ✅ Wrap FormProvider to Avoid "useFormContext is null" Error */}
        <FormProvider {...feedbackForm}>
          {userType !== "TRAINER" && userType !== "ADMIN" && (
            <form className="space-y-5" onSubmit={handleFeedbackSubmit(onSubmitFeedback)}>
              <FormTextArea
                control={feedbackForm.control}
                label="Add Feedback"
                placeholder="Add Feedback"
                fieldControlName="student_feedback"
              />

              <FormSelect
                control={feedbackForm.control}
                label="Material Status"
                placeholder="Select Material Status"
                iterateData={materialStatusOptions}
                fieldControlName="material_status"
              />

              <div className="pt-12 flex gap-x-3 justify-end">
                <Button onClick={onClose} variant="secondary" type="button">
                  Cancel
                </Button>
                <Button type="submit" variant="primary" disabled={isLoading}>
                  {isLoading ? "Submitting..." : "Submit"}
                </Button>
              </div>
            </form>
          )}
        </FormProvider>

        {/* ✅ Wrap Interview Material Form in FormProvider */}
        <FormProvider {...form}>
          {(userType === "TRAINER" || userType === "ADMIN") && (
            <Form {...form}>
              <form onSubmit={handleSubmit(onCreateClassForm)}>
                <div className="flex gap-y-5 flex-col justify-end">
                  <FormInput
                    placeholder="Enter title"
                    label="Title"
                    fieldControlName="title"
                    isRequired
                    control={form.control}
                  />
                  <FormInput
                    placeholder="Enter No Of Questions"
                    label="No Of Questions"
                    fieldControlName="questions_count"
                    isRequired
                    control={form.control}
                    isTypeNumer
                  />

                  <FormSelect
                    control={form.control}
                    label="Resource Type"
                    placeholder="Select File Type"
                    iterateData={fileType}
                    fieldControlName="resource_type"
                    defaultValue={fileType[0]}
                  />

                  {interviewMaterialId > 0 && (
                    <FormSelect
                      control={form.control}
                      label="Publish status"
                      placeholder="Select publish status"
                      iterateData={options}
                      fieldControlName="publish_status"
                    />
                  )}

                  <div>
                    {resourseType === "LINK" ? (
                      <>
                        <Label>
                          Add Interview Link{" "}
                          <span className="text-muted text-xs">(or Upload Docs)</span>
                        </Label>
                        <FormInput
                          placeholder="Add link"
                          fieldControlName="interview_material_link"
                          control={form.control}
                          isRequired={false}
                          disabled={resourseType === "DOCUMENT" || resourseType === ""}
                        />
                      </>
                    ) : (
                      <>
                        {/* <p className="text-slate-400 text-xs my-4 font-semibold mx-auto w-5">
                          (or)
                        </p> */}
                        <FileUploadCard
                          fileType="all-docs"
                          setValue={setValue}
                          disables={resourseType === "LINK" || resourseType === ""}
                        />
                      </>
                    )}
                  </div>

                  <FormTextArea
                    control={form.control}
                    label="Description"
                    placeholder="Enter Description"
                    fieldControlName="description"
                  />

                  <div className="pt-5 flex gap-x-3 justify-end">
                    <Button onClick={onClose} variant="secondary" type="reset">
                      Cancel
                    </Button>
                    <Button
                      // disabled={
                      //   (resourseType === "LINK" && !interview_material_link?.trim()) ||
                      //   (resourseType === "DOCUMENT" && (!Array.isArray(docs) || docs.length === 0))
                      // }
                      type="submit"
                      variant="primary"
                    >
                      {[USER_ROLES.ADMIN, USER_ROLES.TRAINER]?.includes(userType)
                        ? " Create"
                        : "Submit File"}
                    </Button>
                  </div>
                </div>
              </form>
            </Form>
          )}
        </FormProvider>
      </div>
    </ScrollArea>
  )
}

CreateInterviewMaterials.propTypes = {
  form: PropTypes.shape({
    register: PropTypes.func.isRequired,
    watch: PropTypes.func.isRequired,
    control: PropTypes.objectOf.isRequired,
    handleSubmit: PropTypes.func.isRequired,
    reset: PropTypes.func.isRequired,
  }).isRequired,
  onClose: PropTypes.func.isRequired,
  setValue: PropTypes.func.isRequired,
  userType: PropTypes.oneOf(["student", "trainer", "admin"]).isRequired,
  handleSubmit: PropTypes.func.isRequired,
  onCreateClassForm: PropTypes.func.isRequired,
  setStudentFeedBack: PropTypes.func.isRequired,
  interviewMaterialId: PropTypes.number.isRequired,
}

export default CreateInterviewMaterials
