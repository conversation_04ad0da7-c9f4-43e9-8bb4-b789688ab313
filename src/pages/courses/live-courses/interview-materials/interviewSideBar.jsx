import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Sheet, <PERSON>et<PERSON>onte<PERSON>, She<PERSON><PERSON>eader } from "@/components/ui/sheet"
import { Upload, X } from "lucide-react"
import PropTypes from "prop-types"
import { useState } from "react"

function InterviewSideBar({ isOpen, onClose }) {
  const [files, setFiles] = useState([])

  const handleFileChange = (e) => {
    const selectedFiles = Array.from(e.target.files)
    const pdfFiles = selectedFiles.filter((file) => file.type === "application/pdf")

    if (pdfFiles.length !== selectedFiles.length) {
      // eslint-disable-next-line no-alert
      alert("Only PDF files are allowed..")
    }

    setFiles((prev) => [...prev, ...pdfFiles])
  }

  const removeFile = (fileToRemove) => {
    setFiles(files.filter((file) => file !== fileToRemove))
  }

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="min-w-[500px] bg-white py-12 px-12">
        <SheetHeader>
          <h2 className="text-xl font-semibold">Title</h2>
        </SheetHeader>
        <div className="flex flex-col gap-6 mt-2">
          <p>Total Questions: 50</p>

          {/* File Upload Section */}
          <div className="space-y-4">
            <div className="relative">
              <input
                type="file"
                multiple
                accept="application/pdf"
                onChange={handleFileChange}
                className="hidden"
                id="file-upload"
              />
              <Label
                htmlFor="file-upload"
                className="flex items-center justify-center w-full p-4 border-2 border-dashed rounded-lg cursor-pointer hover:border-gray-400 transition-colors"
              >
                <div className="flex flex-col items-center space-y-2">
                  <Upload className="w-6 h-6 text-gray-600" />
                  <span className="text-sm text-gray-600">Click to upload PDF files</span>
                </div>
              </Label>
            </div>

            {/* File List */}
            {files.length > 0 && (
              <div className="space-y-2">
                {files.map((file) => (
                  <div
                    key={file.id}
                    className="flex items-center justify-between p-2 bg-gray-50 rounded"
                  >
                    <span className="text-sm truncate max-w-[300px]">{file.name}</span>
                    <Button
                      variant="link"
                      onClick={() => removeFile(file)}
                      className="p-1 hover:bg-gray-200 rounded-full transition-colors"
                    >
                      <X className="w-4 h-4 text-gray-600" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="flex justify-end gap-4">
            <Button variant="secondary" onClick={() => onClose(false)}>
              Cancel
            </Button>
            <Button variant="primary" onClick={() => onClose(false)}>
              Save
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
}

InterviewSideBar.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
}

export default InterviewSideBar
