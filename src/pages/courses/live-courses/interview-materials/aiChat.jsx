import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Send } from "lucide-react"
import { useState } from "react"

const ChatInterface = () => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Hello! How can I assist you today?",
      sender: "ai",
    },
    {
      id: 2,
      text: "Can you tell me about the weather?",
      sender: "user",
    },
    {
      id: 3,
      text: "Sure! The weather today is sunny with a high of 25°C.",
      sender: "ai",
    },
  ])
  const [input, setInput] = useState("")

  const handleSend = () => {
    if (input.trim() === "") return

    const newMessage = {
      id: messages.length + 1,
      text: input,
      sender: "user",
    }

    setMessages((prevMessages) => [...prevMessages, newMessage])
    setInput("")

    // Simulate AI response
    setTimeout(() => {
      const aiResponse = {
        id: messages.length + 2,
        text: "This is a mock response from the AI.",
        sender: "ai",
      }
      setMessages((prevMessages) => [...prevMessages, aiResponse])
    }, 1000) // 1-second delay for AI response
  }

  const handleInputChange = (e) => {
    setInput(e.target.value)
  }

  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      handleSend()
    }
  }

  return (
    <Card className="w-full mx-auto h-[80vh] flex flex-col rounded">
      <div className="border-b p-3 font-semibold">
        <p className="text-lg font-semibold">AI Chat</p>
      </div>

      <CardContent className="flex-1 overflow-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === "user" ? "justify-end" : "justify-start"}`}
          >
            <div
              className={`${
                message.sender === "user" ? "bg-[#005BE6] text-white" : "bg-gray-200 text-black"
              } rounded p-3 max-w-[80%]`}
            >
              <p>{message.text}</p>
            </div>
          </div>
        ))}
      </CardContent>

      <div className="p-4 mt-auto mb-10">
        <div className="flex gap-2">
          <Input
            placeholder="Type your message here..."
            className="flex-1 rounded"
            value={input}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
          />
          <Button size="icon" className="bg-[#005BE6] text-white rounded" onClick={handleSend}>
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </Card>
  )
}

export default ChatInterface
