import DataTable from "@/components/custom/cutsom-table"
import { StatusUpdationCell } from "@/components/custom/cutsom-table/table-cells"
import { Button } from "@/components/ui/button"
import useTableConfig from "@/hooks/use-table.hooks"
import ct from "@constants/"
import { flexRender } from "@tanstack/react-table"
import { ArrowLeft } from "lucide-react"
import PropTypes from "prop-types"
import { useState } from "react"
import { useSelector } from "react-redux"
import { feedBackColoums } from "./interview-material-colums"
import { useGetInterviewMaterialSubmissions } from "./utils/interview-materials.query"

const CourseContantFeedback = ({ setView, coursematerialId, feedbackTitle }) => {
  const { id: userID, userRole } = useSelector((state) => state.user)
  const course = useSelector((st) => st[ct.store.COURSES]?.activeCourseDetails)
  console.log(coursematerialId, "coursematerialId")
  const { data: SubmissionData } = useGetInterviewMaterialSubmissions({
    course_id: course?.id,
    interview_material_id: coursematerialId,
    userID,
    userRole,
    signature: "SIGNATURE",
  })
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })

  const renderCellContent = (cell, row) => {
    const { id, student_name, material_status, student_feedback } = row?.original || {}

    switch (cell.column.id) {
      case "S_No":
        return <p>{id}</p>
      case "student_name":
        return <p>{student_name}</p>
      case "student_feedback":
        return <StatusUpdationCell value={student_feedback || "-"} />
      case "material_status":
        return (
          <p>
            {material_status
              ? material_status.replace(/_/g, " ").replace(/\w\S*/g, function (txt) {
                  return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
                })
              : "-"}
          </p>
        )
      default:
        return flexRender(cell.column.columnDef.cell, cell.getContext())
    }
  }

  console.log(SubmissionData?.metadata, "SubmissionData")

  const { table, found, pageCount } = useTableConfig(
    SubmissionData?.data,
    feedBackColoums,
    SubmissionData?.metadata?.total_records,
    setPagination,
    pagination
  )

  return (
    <div className="w-full">
      <Button onClick={() => setView("")}>
        <ArrowLeft className="text-primary" size={18} />
        <span className="text-lg text-primary font-semibold">{feedbackTitle || ""}</span>
      </Button>

      {/* <Button variant="primary" onClick={() => setView("")}>
        Back
      </Button>

      <p className="text-2xl font-semibold text-gray-800 my-4">{feedbackTitle}</p> */}
      <DataTable
        renderCellContent={renderCellContent}
        columns={feedBackColoums}
        table={table}
        found={found}
        pageCount={pageCount}
        pageName="Interview"
        pagination={pagination}
      />
    </div>
  )
}

CourseContantFeedback.propTypes = {
  setView: PropTypes.func.isRequired,
}

export default CourseContantFeedback
