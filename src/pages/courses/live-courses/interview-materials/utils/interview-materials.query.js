import { tanstackConfig } from "@/services/query/tanstack-config"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import {
  CreateInterViewMaterial,
  deleteInterViewMaterial,
  EditInterViewMaterial,
  fetchInterViewMaterials,
  getInterviewMaterialFeedback,
  getInterviewMaterialSubmissions,
  InterViewMaterialPublishStatus,
} from "./interview-materials-api"

export const useFetchInterviewMaterials = ({ course_id, filters }) => {
  return useQuery({
    queryKey: ["FETCH_INTERVIEW_MATERIALS", course_id, filters],
    queryFn: () => fetchInterViewMaterials(course_id, filters),
    ...tanstackConfig,
    enabled: !!course_id,
  })
}

export const useCreateInterViewMaterial = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "CREATE_INTERVIEW_MATERIAL",
    mutationFn: ({ course_id, data, userID, userRole, signature }) =>
      CreateInterViewMaterial(course_id, data, userID, userRole, signature),
    ...tanstackConfig,
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "FETCH_INTERVIEW_MATERIALS", exact: false }])
    },
  })
}

export const useEditInterViewMaterial = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "EDIT_INTERVIEW_MATERIAL",
    mutationFn: ({ course_id, interview_material_id, data, userID, userRole, signature }) =>
      EditInterViewMaterial(course_id, interview_material_id, data, userID, userRole, signature),
    ...tanstackConfig,
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "FETCH_INTERVIEW_MATERIALS", exact: false }])
    },
  })
}

export const useDeleteInterViewMaterial = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: ({ course_id, interview_material_id, userID, userRole, signature }) =>
      deleteInterViewMaterial(course_id, interview_material_id, userID, userRole, signature),
    ...tanstackConfig,
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "FETCH_INTERVIEW_MATERIALS", exact: false }])
    },
  })
}

export const usePublishInterviewMaterial = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: ({
      course_id,
      interview_material_id,
      userID,
      userRole,
      signature,
      publish_status,
    }) =>
      InterViewMaterialPublishStatus(
        course_id,
        interview_material_id,
        userID,
        userRole,
        signature,
        publish_status
      ),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "FETCH_INTERVIEW_MATERIALS", exact: false }])
    },
    ...tanstackConfig,
  })
}

export const useGetInterviewMaterialSubmissions = ({
  course_id,
  interview_material_id,
  userID,
  userRole,
  signature,
}) => {
  return useQuery({
    queryKey: [
      "FETCH_INTERVIEW_MATERIAL_SUBMISSIONS",
      course_id,
      interview_material_id,
      userID,
      userRole,
      signature,
    ],
    queryFn: () =>
      getInterviewMaterialSubmissions(
        course_id,
        interview_material_id,
        userID,
        userRole,
        signature
      ),

    ...tanstackConfig,
    enabled: !!course_id && !!interview_material_id && !!userID && !!userRole && !!signature,
  })
}

export const useInterviewMaterialFeedBack = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: ({ course_id, interview_material_id, student_id, userRole, signature, data }) =>
      getInterviewMaterialFeedback(
        course_id,
        interview_material_id,
        student_id,
        userRole,
        signature,
        data
      ),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "FETCH_INTERVIEW_MATERIALS", exact: false }])
    },
    ...tanstackConfig,
  })
}
