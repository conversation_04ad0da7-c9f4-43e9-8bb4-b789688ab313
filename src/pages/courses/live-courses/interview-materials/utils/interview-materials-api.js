import instance from "@/services/api"

export const fetchInterViewMaterials = (course_id, filters) => {
  const response = instance.get(`/course-service/v1/courses/${course_id}/interview-materials`, {
    params: filters,
  })

  console.log(response.data)
  return response
}

export const CreateInterViewMaterial = (course_id, data, userID, userRole, signature) => {
  const formData = new FormData()

  // Create the data object
  const dataObject = {
    title: data.title,
    description: data.description,
    questions_count: data.questions_count,
    resource_type: data.resource_type,
    resource_link: data.interview_material_link,
  }

  if (data.docs) {
    formData.append("file", data.docs[0]) // Append file if it exists
  }

  formData.append("data", JSON.stringify(dataObject))

  return instance.post(`/course-service/v1/courses/${course_id}/interview-materials`, formData, {
    headers: {
      // eslint-disable-next-line sonarjs/no-duplicate-string
      "Content-Type": "multipart/form-data",
      "x-user-id": userID,
      "x-user-role": userRole,
      "x-signature": signature,
    },
  })
}

// eslint-disable-next-line sonarjs/no-identical-functions
export const EditInterViewMaterial = async (
  course_id,
  interview_material_id,
  data,
  userID,
  userRole,
  signature
) => {
  const formData = new FormData()

  const dataObject = {
    title: data.title,
    description: data.description,
    questions_count: data.questions_count,
    resource_type: data.resource_type,
    resource_link: data.interview_material_link,
    publish_status: data.publish_status,
  }

  if (data.docs) {
    formData.append("file", data.docs[0]) // Append file if it exists
  }

  formData.append("data", JSON.stringify(dataObject))

  const response = await instance.put(
    `/course-service/v1/courses/${course_id}/interview-materials/${interview_material_id}`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
        "x-user-id": userID,
        "x-user-role": userRole,
        "x-signature": signature,
      },
    }
  )
  return response.data
}

export const deleteInterViewMaterial = async (
  course_id,
  interview_material_id,
  userID,
  userRole,
  signature
) => {
  try {
    const response = await instance.delete(
      `/course-service/v1/courses/${course_id}/interview-materials/${interview_material_id}`,
      {
        headers: {
          "Content-Type": "multipart/form-data",
          "x-user-id": userID,
          "x-user-role": userRole,
          "x-signature": signature,
        },
      }
    )
    return response.data
  } catch (error) {
    return error // Re-throwing error for handling in calling function
  }
}

export const InterViewMaterialPublishStatus = async (
  course_id,
  interview_material_id,
  userID,
  userRole,
  signature,
  publish_status
) => {
  try {
    const response = await instance.patch(
      `/course-service/v1/courses/${course_id}/interview-materials/${interview_material_id}?publish_status=${publish_status}`, // Pass as query param
      {},
      {
        headers: {
          "Content-Type": "application/json",
          "x-user-id": userID,
          "x-user-role": userRole,
          "x-signature": signature,
        },
      }
    )
    return response.data
  } catch (error) {
    console.error("Error updating publish status:", error)
    throw error
  }
}

export const getInterviewMaterialSubmissions = async (
  course_id,
  interview_material_id,
  userID,
  userRole,
  signature
) => {
  try {
    const response = await instance.get(
      `/course-tracking-service/v1/courses/${course_id}/interview-materials/${interview_material_id}`,
      {
        headers: {
          "x-user-id": userID,
          "x-user-role": userRole,
          "x-signature": signature,
        },
      }
    )
    return response.data
  } catch (error) {
    return error
  }
}

export const getInterviewMaterialFeedback = async (
  course_id,
  interview_material_id,
  student_id,
  userRole,
  signature,
  data
) => {
  try {
    const response = await instance.post(
      `/course-tracking-service/v1/courses/${course_id}/interview-materials/${interview_material_id}/feedback?student_id=${student_id}`,
      {
        ...data,
        interview_material_id, // ✅ Added interview_material_id to request body
      },
      {
        headers: {
          "x-user-id": student_id,
          "x-user-role": userRole,
          "x-signature": signature,
        },
      }
    )
    return response.data
  } catch (error) {
    console.error("Error fetching interview material feedback:", error)
    throw new Error("Failed to fetch feedback")
  }
}
