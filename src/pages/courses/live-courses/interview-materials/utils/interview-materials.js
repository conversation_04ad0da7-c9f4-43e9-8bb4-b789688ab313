import { z } from "zod"

export const interviewMaterialSchema = z.object({
  title: z.string().min(3, { message: "Please enter a valid title" }),
  questions_count: z.string().min(1, { message: "Number of questions is required" }),
  // interview_material_link: z.string().optional(),
  docs: z.any().optional(),
  resource_type: z.string().min(1, { message: "Resource type is required" }), // Fixed
  interview_material_link: z
    .string()
    .optional()
    .refine(
      (val) => {
        // If empty or undefined, it's valid (optional)
        if (!val || val.trim() === "") return true
        // If has value, must be valid URL
        try {
          new URL(val)
          return true
        } catch {
          return false
        }
      },
      {
        message: "Please enter a valid URL (e.g., https://example.com)",
      }
    ),
  description: z.string().optional(),
  publish_status: z.string().optional(),
})

export const defaultInterviewMaterialsValues = {
  title: "",
  questions_count: "",
  // interview_material_link:"",
  docs: null,
  resource_type: "DOCUMENT",
  interview_material_link: "",
  description: "",
  publish_status: "",
}
