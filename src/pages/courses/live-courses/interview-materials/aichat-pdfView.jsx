import { Button } from "@/components/ui/button"
import PropTypes from "prop-types"
import ChatInterface from "./aiChat"
import PdfViewer from "./pdf"

const AichatpdfView = ({ userType, setView }) => {
  const renderUi = () => {
    switch (userType) {
      case "STUDENT":
        return (
          <>
            <PdfViewer />
            <ChatInterface />
          </>
        )
      case "TRAINER":
        return <ChatInterface />
      case "ADMIN":
        return <PdfViewer />
      default:
        return <div>No content available</div>
    }
  }

  return (
    <>
      <Button onClick={() => setView("")} variant="primary">
        Back
      </Button>

      <div className="flex gap-6 mt-3">{renderUi()}</div>
    </>
  )
}

AichatpdfView.propTypes = {
  userType: PropTypes.oneOf(["trainer", "student", "admin"]).isRequired,
  setView: PropTypes.func.isRequired,
}

export default AichatpdfView
