/* eslint-disable sonarjs/no-duplicate-string */
import { CustomSelect } from "@/components/custom/custom-select"
import { DeleteDialog } from "@/components/custom/dialogs/delete.dialog"
import { GeneralDialog } from "@/components/custom/dialogs/general.dialog"
import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import {
  useDeleteDialogHooks,
  useFilterHooks,
  useIsEditDialogHook,
  useIsUpdateHook,
  useOpenCloseHooks,
  usePaginationHooks,
  usePublishStatus,
} from "@/hooks/common.hooks"
import { useDebounce } from "@/hooks/useDebounce"
import { publishStatus, USER_ROLES } from "@/utils/constants"
import { handleCloseForm, isStudent } from "@/utils/helper"
import ct from "@constants/"
import { zodResolver } from "@hookform/resolvers/zod"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { useSelector } from "react-redux"
import CustomSidebar from "../../components/custom-sidebar"
import CreateInterviewMeterials from "./create-interview-meterials"
import InterviewMaterialUi from "./interview"
import {
  defaultInterviewMaterialsValues,
  interviewMaterialSchema,
} from "./utils/interview-materials"
import {
  useCreateInterViewMaterial,
  useDeleteInterViewMaterial,
  useEditInterViewMaterial,
  useFetchInterviewMaterials,
  useInterviewMaterialFeedBack,
  usePublishInterviewMaterial,
} from "./utils/interview-materials.query"

const Interview = () => {
  const course = useSelector((st) => st[ct.store.COURSES]?.activeCourseDetails)
  const loginData = useSelector((st) => st[ct.store.USER_STORE])
  const [studentFeedback, setStudentFeedBack] = useState([])
  const [rowStatus, setRowStatus] = useState({})
  const [rowId, setRowId] = useState(null)
  const [interviewMaterialId, setInterViewMaterialsId] = useState(null)
  const [interviewMaterialRowId, setInterViewMaterialRowId] = useState(null)
  const { sortBy, sortByField } = useFilterHooks()
  const { limit } = usePaginationHooks()
  const [searchingValue, setSearchingValue] = useState("")
  const debouncedSearchQuery = useDebounce(searchingValue, 500)
  const userType = loginData?.userRole
  const [feedbackLoading, setFeedbackLoading] = useState(false)

  const { id: userID, userRole } = useSelector((state) => state.user)
  const courseDet = useSelector((st) => st[ct.store.COURSES])
  const { isDelete, handleDelete, handleCancel } = useDeleteDialogHooks(false)
  const { isEditDialog, handleOpenEditDialog, handleResetEditDialog } = useIsEditDialogHook()
  const { publishedStatus, handleSetPublishStatus } = usePublishStatus()
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })

  const { open, setOpen, handleClose, handleOpen } = useOpenCloseHooks()
  const { isUpdate, handleResetUpdate, handleUpdate } = useIsUpdateHook()
  const form = useForm({
    resolver: zodResolver(interviewMaterialSchema),
    defaultValues: defaultInterviewMaterialsValues,
    mode: "onChange",
  })

  const course_id = course?.id
  const calculateOffset = (pageIndex, pageSize) => pageIndex * pageSize
  const { mutate: createInterViewMaterial } = useCreateInterViewMaterial()
  const { data: interViewMaterials } = useFetchInterviewMaterials({
    course_id,
    filters: {
      limit,
      offset: calculateOffset(pagination.pageIndex, pagination.pageSize),
      sort_by: sortBy,
      sort_by_field: sortByField,
      search_query: debouncedSearchQuery?.length > 0 ? debouncedSearchQuery : null,
      userID,
      userRole,
      signature: "SIGNATURE",
    },
  })

  const { mutate: editInterViewMaterial } = useEditInterViewMaterial()
  const { mutate: deleteInterViewMaterial } = useDeleteInterViewMaterial()

  const { mutate: PublicStatusUpdate } = usePublishInterviewMaterial()
  const { mutate: interviewMaterialFeedBack } = useInterviewMaterialFeedBack()

  useEffect(() => {
    setFeedbackLoading(false)
  }, [])

  useEffect(() => {
    if (studentFeedback.material_status && studentFeedback.student_feedback) {
      setFeedbackLoading(true)
      interviewMaterialFeedBack(
        {
          course_id,
          interview_material_id: interviewMaterialRowId,
          student_id: userID,
          userRole,
          signature: "SIGNATURE",
          data: studentFeedback,
        },
        {
          onSuccess: () => {
            setFeedbackLoading(false)
            successToast("Status Updated", "Interview material status updated successfully!")
            handleClose()
          },
          onError: () => {
            setFeedbackLoading(false)
            failureToast("Update Failed", "Failed to update interview material status.")
            handleClose()
          },
        }
      )
    }
  }, [studentFeedback])

  useEffect(() => {
    console.log("Feedback mutation loading:", feedbackLoading)
  }, [feedbackLoading])

  const handleUpdateStatus = () => {
    PublicStatusUpdate(
      {
        course_id,
        interview_material_id: interviewMaterialRowId,
        userID,
        userRole,
        signature: "signature",
        publish_status: publishedStatus,
      },
      {
        onSuccess: () => {
          handleResetEditDialog()
          successToast("Status Updated", "Interview material status updated successfully!")
        },
        onError: () => {
          handleResetEditDialog()
          failureToast("Update Failed", "Failed to update interview material status.")
        },
      }
    )

    // interviewMaterialFeedBack(
    //   {
    //     course_id,
    //     interview_material_id: interviewMaterialRowId,
    //     student_id: userID,
    //     userRole,
    //     signature: "SIGNATURE",
    //     data: studentFeedback,
    //   },
    //   {
    //     onSuccess: () => {
    //       successToast("Status Updated", "Interview material status updated successfully!")
    //       handleClose()
    //     },
    //     onError: () => {
    //       failureToast("Update Failed", "Failed to update interview material status.")
    //       handleClose()
    //     },
    //   }
    // )
  }

  const { handleSubmit, control, setValue, reset, watch } = form
  const handleCreateClass = () => {
    reset({
      title: "",
      questions_count: "",
      resource_type: "",
      interview_material_link: "",
      description: "",
    })

    setOpen(true)
  }

  const handleAddFeedBack = (row) => {
    const { id } = row?.original ?? {}

    // setProjectId({ ...projectId, project_id: id })
    setInterViewMaterialRowId(id)
    handleUpdate()

    handleOpen()
  }

  const handleCancelForm = () => {
    handleClose()
    handleCloseForm({ setOpen, reset })
  }

  useEffect(() => {
    if (rowId != null) {
      PublicStatusUpdate(
        {
          course_id,
          interview_material_id: rowId,
          userID,
          userRole,
          signature: "signature",
          publish_status: rowStatus[rowId],
        },
        {
          onSuccess: () => {
            handleResetEditDialog()
            successToast("Status Updated", "Interview material status updated successfully!")
          },
          onError: () => {
            handleResetEditDialog()
            failureToast("Update Failed", "Failed to update interview material status.")
          },
        }
      )
    }
  }, [course_id, userID, userRole, rowStatus])

  const handleCreateClassForm = async (data = {}) => {
    if (isUpdate) {
      editInterViewMaterial(
        {
          course_id,
          interview_material_id: interviewMaterialId,
          data,
          userID,
          userRole,
          signature: "signature",
        },
        {
          onSuccess: () => {
            handleClose()
            successToast(
              "Interview Material Updated",
              "The Interview Material has been successfully updated!"
            )
            handleResetUpdate()
            setInterViewMaterialsId(null)
          },
          onError: () => {
            handleResetUpdate()
            setInterViewMaterialsId(null)
            handleClose()
            failureToast(
              "Interview Material Update Failed",
              "An error occurred while updating the Interview Material. Please try again."
            )
          },
        }
      )
      return
    }

    createInterViewMaterial(
      {
        course_id,
        data,
        userID,
        userRole,
        signature: "signature",
      },
      {
        onSuccess: () => {
          handleClose()
          reset()

          successToast(
            "Interview Material Uploaded",
            "The Interview Material has been successfully Uploaded!"
          )
        },
        onError: (error) => {
          handleClose()

          if (error) {
            reset()
            failureToast(
              "Interview Material Upload Failed",
              "An error occurred Uploading the Interview Material. Please try again."
            )
          }
        },
      }
    )
  }

  const handleDeleteMaterials = (data) => {
    setInterViewMaterialsId(data?.original?.id)
    handleDelete()
  }

  const handleConfirmDelete = () => {
    deleteInterViewMaterial(
      {
        course_id,
        interview_material_id: interviewMaterialId,
        userID,
        userRole,
        signature: "signature",
      },
      {
        onSuccess: () => {
          handleCancel()
          setInterViewMaterialsId(null)
          successToast(
            "Interview Material Deleted",
            "The Interview Material has been successfully deleted!"
          )
        },
        onError: () => {
          handleCancel()
          setInterViewMaterialsId(null)
          failureToast(
            "Deletion Error",
            "Unable to delete the Interview Material. Please try again later."
          )
        },
      }
    )
  }
  const handleOpenInterViewStatusDialoge = (data) => {
    const { id, publish_status } = data?.original || {}

    setInterViewMaterialRowId(id)
    handleSetPublishStatus(publish_status)
    // setRowStatus((prev) => ({
    //   ...prev,
    //   [rowId]: newStatus,
    // }))

    handleOpenEditDialog()
  }
  const handleEdit = (row) => {
    const { title, questions_count, resource_type, interview_material_link, description, id } =
      row?.original ?? {}

    setInterViewMaterialsId(id)
    handleUpdate()
    reset({
      title: title?.toString(),
      questions_count: questions_count?.toString(),
      resource_type: resource_type?.toString(),
      interview_material_link: interview_material_link?.toString(),
      description: description?.toString(),
    })
    handleOpen()
  }

  const handleFeadBack = (e) => {
    e.stopPropagation()
    setOpen(true)
  }
  const CreateInterviewMeterialProps = {
    form,
    control,
    onClose: handleCancelForm,
    onCreateClassForm: handleCreateClassForm,
    handleSubmit,
    setValue,
    setStudentFeedBack,
    userType,
    watch,
    interviewMaterialId,
    isLoading: feedbackLoading,
    setInterViewMaterialRowId,
  }

  return (
    <div>
      <CustomSidebar
        isOpen={open}
        onClose={handleClose}
        title={
          [USER_ROLES.TRAINER, USER_ROLES.ADMIN].includes(userType)
            ? "Create Interview Material"
            : "Add Feedback"
        }
        description=""
        content={<CreateInterviewMeterials {...CreateInterviewMeterialProps} />}
      />

      <DeleteDialog
        onOpen={isDelete}
        onDelete={handleConfirmDelete}
        onCancel={handleCancel}
        title="Delete Interview Materials"
        content="Are you sure want to delete Interview Materials?"
      />
      <GeneralDialog
        onOpen={isEditDialog}
        title="Update Status"
        onClickCTA={handleUpdateStatus}
        ctaLabel="Update Status"
        onCancel={handleResetEditDialog}
        ctaPosition="justify-end"
        content={
          <CustomSelect
            publishedStatus={publishedStatus}
            setPublishedStatus={handleSetPublishStatus}
            iteratedData={publishStatus}
          />
        }
      />
      <InterviewMaterialUi
        setRowId={setRowId}
        handleDelete={handleDeleteMaterials}
        interViewMaterials={interViewMaterials}
        handleFeadBack={handleFeadBack}
        handleEdit={handleEdit}
        setIsSideBarOpen={handleCreateClass}
        rowStatus={rowStatus}
        setRowStatus={setRowStatus}
        handleAddFeedBack={handleAddFeedBack}
        interviewMaterialRowId={interviewMaterialRowId}
        pagination={pagination}
        setPagination={setPagination}
        handleOpenInterViewStatusDialoge={handleOpenInterViewStatusDialoge}
        setSearchingValue={setSearchingValue}
        searchingValue={searchingValue}
        isPaidUser={isStudent(courseDet?.activeCourse, userRole)}
      />
    </div>
  )
}

export default Interview
