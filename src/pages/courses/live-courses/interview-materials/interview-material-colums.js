export const student = [
  {
    accessorKey: "title",
    header: "Title",
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    accessorKey: "total_question",
    // eslint-disable-next-line sonarjs/no-duplicate-string
    header: "Total Questions",
  },
  {
    accessorKey: "link",
    header: "Reference",
  },
  {
    accessorKey: "status",
    header: "Status",
  },
  {
    accessorKey: "action",
    header: "Action",
  },
]

export const trainer = [
  {
    accessorKey: "title",
    header: "Title",
  },
  {
    accessorKey: "total_question",
    header: "Total Questions",
  },
  {
    accessorKey: "link",
    header: "Reference",
  },
  {
    accessorKey: "number_of_feedback",
    header: "Number of Feedback",
  },
  {
    accessorKey: "published",
    header: "Published Status",
  },
  {
    accessorKey: "action",
    header: "Action",
  },
]
export const vendor = [
  {
    accessorKey: "title",
    header: "Title",
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    accessorKey: "total_question",
    header: "Total Questions",
  },
  {
    accessorKey: "link",
    header: "Reference",
  },
  {
    accessorKey: "status",
    header: "Status",
  },

  // {
  //   accessorKey: "action",
  //   header: "Action",
  // },
]
export const Admin = [
  {
    accessorKey: "title",
    header: "Title",
  },
  {
    accessorKey: "total_question",
    header: "Total Questions",
  },
  {
    accessorKey: "link",
    header: "Reference",
  },
  {
    accessorKey: "number_of_feedback",
    header: "Number of Feedback",
  },
  {
    accessorKey: "published",
    header: "Published Status",
  },
  {
    accessorKey: "action",
    header: "Action",
  },
]

export const feedBackColoums = [
  {
    accessorKey: "student_name",
    header: "Student Name",
  },
  {
    accessorKey: "student_feedback",
    header: "Feedback",
  },
  {
    accessorKey: "material_status",
    header: "Material Status",
  },
  {
    accessorKey: "updated_at",
    header: "Updated At",
  },
]

export const getInterViewMeterialColumns = (role) => {
  switch (role.toUpperCase()) {
    case "TRAINER":
      return trainer
    case "STUDENT":
      return student
    case "ADMIN":
      return Admin
    case "VENDOR":
      return vendor
    default:
      return []
  }
}
