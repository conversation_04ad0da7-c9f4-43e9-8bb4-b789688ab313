import { useNavigate } from "react-router-dom"
import { ArrowLeft } from "lucide-react"
import ct from "@constants/"
import { But<PERSON> } from "@/components/ui/button"
import FeedbackUI from "./feedback.ui"

function Feedback() {
  const navigate = useNavigate()

  const handleNavigate = () => {
    navigate(
      ct.route.LIVE_COURSES.startsWith("/") ? ct.route.LIVE_COURSES : `/${ct.route.LIVE_COURSES}`,
      { replace: true }
    )
  }

  return (
    <div>
      <div className="flex items-center justify-between">
        <h6 className="text-primary">Feedback</h6>
        <Button className="flex items-center gap-x-1 font-semibold group" onClick={handleNavigate}>
          <ArrowLeft
            size={15}
            className="transform font-semibold transition-transform group-hover:-translate-x-1"
          />
          Back
        </Button>
      </div>
      <FeedbackUI />
    </div>
  )
}
export default Feedback
