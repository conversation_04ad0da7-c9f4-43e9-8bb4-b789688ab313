import DataTable from "@/components/custom/cutsom-table"
import { Action<PERSON>ell, DateCell, Link<PERSON>ell } from "@/components/custom/cutsom-table/table-cells"
import useTableConfig from "@/hooks/use-table.hooks"
import { flexRender } from "@tanstack/react-table"
import { useSelector } from "react-redux"
import ct from "@constants/"
import { USER_ROLES } from "@/utils/constants"
import courseContent from "../utils/course-content.json"

export const columns = [
  {
    id: "id",
    accessorKey: "id",
    header: "Feedback No",
  },
  {
    id: "name",
    accessorKey: "name",
    header: "Student Name",
  },
  {
    id: "feedback",
    accessorKey: "feedback",
    header: "Feedback",
  },
  {
    accessorKey: "date",
    header: "Created At",
    cell: ({ row }) => <div className="capitalize">{row.getValue("status")}</div>,
  },
  // {
  //   id: "actions",
  //   accessorKey: "actions",
  //   header: "Actions",
  // },
]

const FeedbackUI = ({ handleEdit, handleDelete }) => {
  const { userRole } = useSelector((st) => st[ct.store.USER_STORE])
  const renderCellContent = (cell, row) => {
    console.log("_table_cell", cell, row?.original)

    const { date, class: classes, topic, meet_link, trainer, notes, feedback } = row?.original || {}

    switch (cell.column.id) {
      case "date":
        return <DateCell value={date} />
      case "class":
        return <p>{classes}</p>
      case "topic":
        return <p>{topic}</p>
      case "meetlink":
        return <LinkCell value={meet_link} />
      case "trainer":
        return <p>{trainer}</p>
      case "notes":
        return <p>{notes}</p>
      case "feedback":
        return <p>{feedback}</p>
      case "actions":
        return (
          <div>
            {(() => {
              if (userRole === USER_ROLES.ADMIN) {
                return (
                  <ActionCell
                    label1="View"
                    label2="Edit"
                    label3="Delete Feedback"
                    row={row}
                    isEdit
                    isDelete
                    onDelete={handleDelete}
                    onEdit={handleEdit}
                  />
                )
              }
              // if (userRole === "trainer") {
              //   return (
              //     <ActionCell
              //       label1="View"
              //       label2="Edit"
              //       label3="Delete Feedback"
              //       row={row}
              //       isEdit
              //       isDelete
              //       onDelete={handleDelete}
              //       onEdit={handleEdit}
              //     />
              //   )
              // }
              // return <ActionCell label2="Edit Feedback" row={row} isEdit onEdit={handleEdit} />
            })()}
          </div>
        )
      default:
        return flexRender(cell.column.columnDef.cell, cell.getContext())
    }
  }
  const { table, found, pagination, pageCount } = useTableConfig(courseContent, columns)

  return (
    <div className="w-full">
      <DataTable
        renderCellContent={renderCellContent}
        columns={columns}
        table={table}
        found={found}
        pageCount={pageCount}
        pagination={pagination}
        pageName="Feedback"
      />
    </div>
  )
}

export default FeedbackUI
