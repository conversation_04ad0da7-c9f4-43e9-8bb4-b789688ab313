import CustomSearchbar from "@/components/custom/custom-search"
import DataTable from "@/components/custom/cutsom-table"
import {
  <PERSON><PERSON>ell,
  DateCell,
  RenderTableData,
  StatusUpdationCell,
} from "@/components/custom/cutsom-table/table-cells"

import { CustomSelect } from "@/components/custom/custom-select"
import { DeleteDialog } from "@/components/custom/dialogs/delete.dialog"
import { GeneralDialog } from "@/components/custom/dialogs/general.dialog"
import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import { Button } from "@/components/ui/button"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import {
  useDeleteDialogHooks,
  useFilterHooks,
  useIsEditDialogHook,
  useIsUpdateHook,
  useOpenCloseHooks,
  usePaginationHooks,
  usePublishStatus,
} from "@/hooks/common.hooks"
import useTableConfig from "@/hooks/use-table.hooks"
import { useDebounce } from "@/hooks/useDebounce"
import { publishStatus } from "@/utils/constants"
import { getOffset } from "@/utils/helper"
import ct from "@constants/"
import { zodResolver } from "@hookform/resolvers/zod"
import { flexRender } from "@tanstack/react-table"
import { ArrowLeft, FilePlus2 } from "lucide-react"
import moment from "moment"
import PropTypes from "prop-types"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { useSelector } from "react-redux"
import CustomSidebar from "../../../components/custom-sidebar"
import { taskSchema } from "../utils/project-schema"
import {
  useCreateTask,
  useDeletTask,
  useFetchProjectTasks,
  useTaskStatusUbdate,
  useUpdateTask,
} from "../utils/taskQuery"
import TaskCreation from "./task-creation"
import { getColumnsByUserType } from "./task.colum"

const Tasks = ({
  listOfProject,
  projectEndDate,
  setView,
  userType,
  handleCreateProject,
  projectId,
  setSelectedProjectId,
  handleProjectSubmit,
  setTaskId,
  taskId,
}) => {
  const course = useSelector((st) => st[ct.store.COURSES]?.activeCourseDetails)
  const { id: userID, userRole } = useSelector((state) => state.user)
  const { isEditDialog, handleOpenEditDialog, handleResetEditDialog } = useIsEditDialogHook()
  const { publishedStatus, handleSetPublishStatus } = usePublishStatus()
  const course_id = course?.id
  const [searchingValue, setSearchingValue] = useState("")
  const { sortBy, sortByField } = useFilterHooks()
  const { limit, offSet } = usePaginationHooks(0, 10)
  const { isDelete, handleDelete, handleCancel } = useDeleteDialogHooks(false)
  const { isUpdate, handleResetUpdate, handleUpdate } = useIsUpdateHook()
  const debouncedSearchQuery = useDebounce(searchingValue, 500)

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })

  const { data: tasks } = useFetchProjectTasks({
    data: { limit, offSet, sort_by: sortBy, sort_by_field: sortByField },
    project_id: projectId,
    filters: {
      limit,
      offset: getOffset(pagination.pageIndex, pagination.pageSize),
      sort_by: sortBy,
      sort_by_field: sortByField,
      search_query: debouncedSearchQuery?.length > 0 ? debouncedSearchQuery : null,
      ...(userType === "students" && { for_students: true }),
    },
    course_id,
    userID,
    userRole,
    signature: "SIGNATURE",
  })

  console.log("__task", tasks?.data)
  const { mutate: ubdateStatus } = useTaskStatusUbdate()
  const { mutate: CreateTask } = useCreateTask()
  const { mutate: EditTask } = useUpdateTask()
  const { mutate: deleteTask } = useDeletTask()
  const { table, found, pageCount } = useTableConfig(
    tasks?.data,
    getColumnsByUserType(userType),
    tasks?.metadata?.total_records,
    setPagination,
    pagination
  )
  const handleOpenProjectStatusDialog = (data) => {
    const { id, publish_status } = data?.original || {}
    handleSetPublishStatus(publish_status)
    setTaskId({
      ...taskId,
      task_id: id,
    })
    // setTaskId,
    // taskId,
    handleOpenEditDialog()
  }
  const { handleOpen, open, handleClose } = useOpenCloseHooks()
  // const [taskId, setTaskId] = useState(null)

  const form = useForm({
    resolver: zodResolver(taskSchema),
    defaultValues: null,
    mode: "onChange",
  })

  const { handleSubmit, control, setValue, reset } = form
  const handleCreateClassForm = async (data = {}) => {
    if (isUpdate) {
      EditTask(
        {
          data,
          project_id: projectId,
          task_id: taskId?.task_id,
          userID,
          userRole,
          signature: "SIGNATURE",
        },
        {
          onSuccess: () => {
            handleClose()
            successToast("Task Updated", "The Task has been successfully updated!")
            handleResetUpdate()
          },
          onError: () => {
            handleResetUpdate()
            handleClose()
            failureToast(
              "Task Update Failed",
              "An error occurred while updating the Task. Please try again."
            )
          },
        }
      )
      return
    }
    CreateTask(
      { course_id, project_id: projectId, data, userID, userRole, signature: "SIGNATURE" },
      {
        onSuccess: () => {
          handleClose()
          reset()
          successToast("Successfully Created!", "Task was successfully created!")
        },
        onError: () => {
          failureToast("Something went wrong", "Please try again later.")
        },
      }
    )
  }

  const handleEdit = (row) => {
    const { dead_line, passing_marks, total_marks, id, ...rest } = row?.original ?? {}

    setTaskId({ ...taskId, task_id: id })
    handleUpdate()
    reset({
      dead_line: new Date(dead_line),
      passing_marks: passing_marks?.toString(),
      total_marks: total_marks?.toString(),
      ...rest,
    })
    handleOpen()
  }

  const handleDeleteQuiz = (data) => {
    setTaskId({ ...taskId, task_id: data?.original?.id })
    handleDelete()
  }

  const handleConfirmDelete = () => {
    deleteTask(
      {
        project_id: projectId,
        task_id: taskId?.task_id,
        userID,
        userRole,
        signature: "SIGNATURE",
      },
      {
        onSuccess: () => {
          handleCancel()
          successToast("Task deleted successfully", "The Task has been removed.")
        },
        onError: () => {
          failureToast("Deletion Error", "Unable to delete the Task. Please try again later.")
        },
      }
    )
  }
  const CreateTaskProps = {
    form,
    control,
    onClose: handleClose,
    handleProjectSubmit,
    onCreateClassForm: handleCreateClassForm,
    handleSubmit,
    setValue,
    isUpdate,
  }
  // const [title, setTitle] = useState("")

  const getProjectTitle = (Project, Id) => {
    return Project?.data?.find((project) => project.id === Id)?.title || "Untitled"
  }
  const titleText = getProjectTitle(listOfProject, projectId)
  const truncateText = (text, maxLength = 25) => {
    if (!text) return ""
    if (text.length <= maxLength) return text
    return `${text.substring(0, maxLength)}...`
  }
  const renderCellContent = (cell, row) => {
    const {
      id,
      title,
      description,
      trainer_feedback,
      total_marks,
      feedback_student,
      publish_status,
      due_date,
    } = row?.original || {}

    switch (cell.column.id) {
      case "S_No":
        return (
          <RenderTableData
            content={cell.row.index + 1 + pagination.pageIndex * pagination.pageSize}
          />
        )
      case "task":
        return <RenderTableData content={title} />
      case "total_marks":
        return <RenderTableData content={total_marks} />
      case "description":
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <RenderTableData content={truncateText(description) || "-"} />
              </TooltipTrigger>
              <TooltipContent className="max-w-md">
                <p>{description || "-"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
      case "deadline":
        return <DateCell value={due_date || "-"} />
      case "feedback":
        return <RenderTableData content={feedback_student} />
      case "remarks":
        return <RenderTableData content={trainer_feedback} />
      case "status":
        return <StatusUpdationCell value={publish_status} key={id} />
      case "action":
        return userType === "ADMIN" || userType === "TRAINER" ? (
          <ActionCell
            row={row}
            label2="Edit"
            label1="Update Status"
            isEdit
            isDelete
            isView
            onView={handleOpenProjectStatusDialog}
            onDelete={handleDeleteQuiz}
            onEdit={handleEdit}
          />
        ) : (
          <ActionCell row={row} isEvaluate_trainer Evaluate_Trainer_label="Evaluate" />
        )

      default:
        return flexRender(cell.column.columnDef.cell, cell.getContext())
    }
  }

  const handleOpenCreate = () => {
    handleResetUpdate()
    reset({
      title: "",
      description: "",
      due_date: "",
      total_marks: "",
    })
    handleOpen()
  }
  const setPtojectView = () => {
    setSelectedProjectId(projectId)
    setView("projects")
  }

  const currentDate = moment().startOf("day") // Today at 00:00:00
  const deadLine = moment(projectEndDate, "YYYY-MM-DD", true).startOf("day")

  console.log("Current Date:", currentDate.format("YYYY-MM-DD"))
  console.log("Deadline:", deadLine.format("YYYY-MM-DD"))
  console.log("Is current date after deadline?", currentDate.isAfter(deadLine))

  const handleUpdateStatus = () => {
    ubdateStatus(
      {
        task_id: taskId?.task_id,
        project_id: projectId,
        publishedStatus,
      },
      {
        onSuccess: () => {
          handleResetEditDialog()
          successToast("Task Status Updated", "The Task status has been successfully Updated!")
        },
        onError: () => {
          handleResetEditDialog()
          failureToast(
            "Task Status Updation Failed",
            "An error occurred while update the Task status. Please try again."
          )
        },
      }
    )
  }

  console.log(projectEndDate, "deadline")
  return (
    <div className="">
      <Button onClick={setPtojectView} className="text-primary gap-x-2 items-center">
        <ArrowLeft className="text-primary" size={18} />
        <span className="text-lg text-primary font-semibold">{titleText}</span>
      </Button>

      <div className="flex justify-between mt-3">
        <CustomSearchbar
          inputSize="w-[20rem]"
          placeholder="Search by title..."
          searchedValue={searchingValue}
          setSearchedValue={(e) => setSearchingValue(e?.target.value)}
        />
        {userType !== "STUDENT" && userType !== "VENDOR" && (
          <Button variant="primary" className="gap-x-1" onClick={handleOpenCreate}>
            <FilePlus2 size={18} /> Create Task
          </Button>
        )}
        {userType === "STUDENT" && tasks?.data?.length > 0 && (
          <Button
            variant={currentDate.isAfter(deadLine) ? "secondary" : "primary"}
            className={currentDate.isAfter(deadLine) ? "text-gray-500 cursor-not-allowed" : ""}
            // disabled={currentDate.isAfter(deadLine)}
            onClick={() => {
              if (currentDate.isAfter(deadLine)) {
                failureToast("Submission deadline has passed!")
                return
              }
              handleCreateProject()
            }}
          >
            Submit
          </Button>
        )}
      </div>

      <DeleteDialog
        onOpen={isDelete}
        onDelete={handleConfirmDelete}
        onCancel={handleCancel}
        title="Delete Task"
        content="Are you sure want to delete this Task?"
      />
      <CustomSidebar
        isOpen={open}
        onClose={handleClose}
        title={isUpdate ? "Update Task" : "Create Task"}
        description=""
        content={<TaskCreation {...CreateTaskProps} />}
      />
      <GeneralDialog
        onOpen={isEditDialog}
        title="Update Status"
        onClickCTA={handleUpdateStatus}
        ctaLabel="Update Status"
        onCancel={handleResetEditDialog}
        ctaPosition="justify-end"
        content={
          <CustomSelect
            publishedStatus={publishedStatus}
            setPublishedStatus={handleSetPublishStatus}
            iteratedData={publishStatus}
          />
        }
      />
      <DataTable
        renderCellContent={renderCellContent}
        columns={getColumnsByUserType(userType)}
        table={table}
        found={found}
        pageCount={pageCount}
        pagination={pagination}
      />
    </div>
  )
}

Tasks.propTypes = {
  listOfProject: PropTypes.shape({
    data: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.number.isRequired,
        title: PropTypes.string.isRequired,
      })
    ),
  }),
  setView: PropTypes.func.isRequired,
  userType: PropTypes.string.isRequired,
  handleCreateProject: PropTypes.func.isRequired,
  projectId: PropTypes.number.isRequired,
  setSelectedProjectId: PropTypes.func.isRequired,
  handleProjectSubmit: PropTypes.func.isRequired,
  setTaskId: PropTypes.func.isRequired,
  taskId: PropTypes.shape({
    task_id: PropTypes.number,
  }),
  projectEndDate: PropTypes.string.isRequired,
}

export default Tasks
