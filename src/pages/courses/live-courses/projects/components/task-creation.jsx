import AppointmentScheduler from "@/components/custom/custom-forms/date-time-picker"
import FormInput from "@/components/custom/custom-forms/form-input"
import FormTextArea from "@/components/custom/custom-forms/form-textarea"
import { Button } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import PropTypes from "prop-types"

const TaskCreation = ({
  form,
  control,
  onClose,
  onCreateClassForm,
  handleSubmit,
  handleProjectSubmit,
  isUpdate,
}) => {
  return (
    <div>
      <Form {...form}>
        <form onSubmit={handleSubmit(onCreateClassForm)}>
          <div className="pt-5 flex gap-y-3 flex-col justify-end">
            <FormInput
              placeholder="Enter Title"
              label="Title"
              fieldControlName="title"
              isRequired
              control={control}
            />

            <FormInput
              label="Total Marks"
              fieldControlName="total_marks"
              isRequired
              control={control}
              isTypeNumer
            />

            <AppointmentScheduler
              dataTestID="test-id"
              dataTestIDError="error"
              fieldControlName="due_date"
              control={control}
              label="Dead Line"
              placeholder="Pick a date"
              futureDateDisabled={null}
              isRequired
            />

            <FormTextArea
              label="Description"
              fieldControlName="description"
              isRequired
              control={control}
            />

            <div className="pt-12 flex gap-x-3 justify-end">
              <Button onClick={onClose} variant="secondary" type="reset">
                Cancel
              </Button>
              <Button type="submit" variant="primary" onClick={handleProjectSubmit}>
                {isUpdate ? "Update Task" : "Create Task"}
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </div>
  )
}

TaskCreation.propTypes = {
  form: PropTypes.objectOf.isRequired,
  control: PropTypes.objectOf.isRequired,
  onClose: PropTypes.func.isRequired,
  onCreateClassForm: PropTypes.func.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  setValue: PropTypes.func.isRequired,
  handleProjectSubmit: PropTypes.func.isRequired,
  isUpdate: PropTypes.bool.isRequired,
}

export default TaskCreation
