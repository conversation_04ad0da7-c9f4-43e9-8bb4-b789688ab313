import DataTable from "@/components/custom/cutsom-table"
import {
  <PERSON><PERSON>ell,
  Link<PERSON><PERSON>,
  StatusUpdationCell,
} from "@/components/custom/cutsom-table/table-cells"
import { Button } from "@/components/ui/button"
import useTableConfig from "@/hooks/use-table.hooks"
import PropTypes from "prop-types"

const SubmitedTasks = ({ submitedTasks, setView, onUpdateStatus }) => {
  const columns = [
    {
      id: "task_name",
      accessorKey: "task_name",
      header: "Task Name",
    },
    {
      id: "description",
      accessorKey: "description",
      header: "Description",
    },
    {
      id: "acceptance_status",
      accessorKey: "acceptance_status",
      header: "Acceptance Status",
    },
    {
      id: "feedback_student",
      accessorKey: "feedback_student",
      header: "Student Feedback",
    },
    {
      id: "remark",
      accessorKey: "remark",
      header: "Remark",
    },
    {
      id: "marks_obtained",
      accessorKey: "marks_obtained",
      header: "Marks Obtained",
    },
    {
      id: "action",
      accessorKey: "action",
      header: "Action",
    },
  ]

  const { table, found, pagination, pageCount } = useTableConfig(submitedTasks?.data || [], columns)

  const renderCellContent = (cell, row) => {
    const {
      id,
      acceptance_status,
      description,
      feedback_student,
      marks_obtained,
      remark,
      task_name,
      meet_link,
    } = row?.original || {}

    switch (cell.column.id) {
      case "no":
        return <p>{id || "-"}</p>
      case "task_name":
        return <p>{task_name || "-"}</p>
      case "description":
        return <p>{description || "-"}</p>
      case "link":
        return <LinkCell value={meet_link || "-"} />
      case "acceptance_status":
        return <StatusUpdationCell value={acceptance_status} key={id} />

      case "feedback_student":
        return <p>{feedback_student || "-"}</p>
      case "marks_obtained":
        return <p>{marks_obtained || "-"}</p>
      case "remark":
        return <p>{remark || "-"}</p>
      case "action":
        return <ActionCell label1="Update Status" row={row} isView onView={onUpdateStatus} />

      default:
        return cell.column.columnDef.cell ? cell.column.columnDef.cell(cell) : null
    }
  }

  return (
    <div className="w-full">
      <Button variant="primary" onClick={() => setView("project")}>
        Back
      </Button>

      <DataTable
        renderCellContent={renderCellContent}
        columns={columns}
        table={table}
        found={found}
        pageCount={pageCount}
        pageName="Interview"
        pagination={pagination}
        notFoundPlaceholder="No Interview Materials Found"
      />
    </div>
  )
}

SubmitedTasks.propTypes = {
  setView: PropTypes.func.isRequired,
  submitedTasks: PropTypes.shape({
    data: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.number.isRequired,
        task_name: PropTypes.string.isRequired,
        description: PropTypes.string.isRequired,
        acceptance_status: PropTypes.string.isRequired,
        feedback_student: PropTypes.string.isRequired,
        remark: PropTypes.string.isRequired,
        marks_obtained: PropTypes.number.isRequired,
      })
    ).isRequired,
  }).isRequired,
  onUpdateStatus: PropTypes.func.isRequired,
}

export default SubmitedTasks
