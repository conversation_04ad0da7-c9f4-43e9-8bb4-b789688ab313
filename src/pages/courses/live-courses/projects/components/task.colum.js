export const trainer = [
  {
    id: "task",
    accessorKey: "task",
    header: "Task",
  },
  {
    id: "total_marks",
    accessorKey: "total_marks",
    // eslint-disable-next-line sonarjs/no-duplicate-string
    header: "Total Marks",
  },
  {
    id: "description",
    accessorKey: "description",
    header: "Description",
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Published Status",
  },

  {
    id: "deadline",
    accessorKey: "deadline",
    header: "Deadline",
  },

  {
    id: "action",
    accessorKey: "action",
    header: "Action",
  },
]

export const admin = [
  {
    id: "task",
    accessorKey: "task",
    header: "Task",
  },
  {
    id: "total_marks",
    accessorKey: "total_marks",
    // eslint-disable-next-line sonarjs/no-duplicate-string
    header: "Total Marks",
  },
  {
    id: "description",
    accessorKey: "description",
    header: "Description",
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Published Status",
  },

  {
    id: "deadline",
    accessorKey: "deadline",
    header: "Deadline",
  },

  {
    id: "action",
    accessor<PERSON>ey: "action",
    header: "Action",
  },
]

export const student = [
  {
    id: "task",
    accessorKey: "task",
    header: "Task",
  },
  {
    id: "total_marks",
    accessorKey: "total_marks",
    header: "Total Marks",
  },
  {
    id: "description",
    accessorKey: "description",
    header: "Description",
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Status",
  },
  {
    id: "feedback",
    accessorKey: "feedback",
    header: "Feedback",
  },
  {
    id: "remarks",
    accessorKey: "remarks",
    header: "Remarks",
  },
]

export const vendor = [
  {
    id: "task",
    accessorKey: "task",
    header: "Task",
  },
  {
    id: "total_marks",
    accessorKey: "total_marks",
    header: "Total Marks",
  },
  {
    id: "description",
    accessorKey: "description",
    header: "Description",
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Status",
  },
  {
    id: "feedback",
    accessorKey: "feedback",
    header: "Feedback",
  },
]

export const getColumnsByUserType = (userType) => {
  switch (userType.toUpperCase()) {
    case "TRAINER":
      return trainer
    case "STUDENT":
      return student
    case "VENDOR":
      return vendor
    case "ADMIN":
      return admin
    default:
      return [] // Return an empty array if the userType is invalid
  }
}
