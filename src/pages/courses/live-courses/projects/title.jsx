import PropTypes from "prop-types"
import { useState } from "react"
import { useNavigate } from "react-router-dom"

const Title = ({ topic }) => {
  const navigate = useNavigate()
  const [isHovered, setIsHovered] = useState(false)

  const handleNavigation = () => {
    navigate("/ProjectSubmission")
  }

  const handleMouseEnter = () => {
    setIsHovered(true)
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
  }

  const textStyle = {
    cursor: "pointer",
    color: isHovered ? "blue" : "black", // Change 'blue' to your desired hover color
    textDecoration: isHovered ? "underline" : "none", // Optional: Adds underline on hover
  }

  return (
    <div
      role="button"
      tabIndex={0}
      style={textStyle}
      onClick={handleNavigation}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          handleNavigation()
        }
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {topic}
    </div>
  )
}

Title.propTypes = {
  topic: PropTypes.string.isRequired,
}

export default Title
