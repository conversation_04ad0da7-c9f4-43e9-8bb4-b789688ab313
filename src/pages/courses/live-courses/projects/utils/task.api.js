import instance from "@/services/api"

export const fetchTasks = async (
  data,
  project_id,
  filters,
  course_id,
  userID,
  userRole,
  signature
) => {
  const params = {
    ...data,
    ...filters,
    ...(userRole !== "STUDENT" && userRole !== "VENDOR" ? { for_students: false } : {}),
  }

  console.log("Fetching tasks with params:", params)

  const response = await instance.get(
    `/course-service/v1/courses/${course_id}/projects/${project_id}/tasks`,
    {
      params,
      headers: {
        "x-user-id": userID,
        "x-user-role": userRole,
        "x-signature": signature,
      },
    }
  )

  return response.data
}

export const CreateTasks = (course_id, project_id, data) => {
  const response = instance.post(
    `/course-service/v1/courses/${course_id}/projects/${project_id}/tasks`,
    data
  )
  return response.data
}

export const deleteTask = async (project_id, task_id) => {
  try {
    const response = await instance.delete(
      `/course-service/v1/projects/${project_id}/tasks/${task_id}`
    )
    return response.data
  } catch (error) {
    return error
  }
}

export const EditTask = async (data, project_id, task_id) => {
  try {
    const response = await instance.put(
      `/course-service/v1/projects/${project_id}/tasks/${task_id}`,
      data
    )
    return response.data
  } catch (error) {
    return error
  }
}

export const SubmitTask = async (task_id, data, file) => {
  const formData = new FormData()

  // Create the data object
  const dataObject = {
    feedback_student: data.feedback_student,
    submission: data.submission,
    submission_type: data.submission_type || "",
  }
  if (file) {
    formData.append("file", file) // Append file if it exists
  }

  formData.append("submission", JSON.stringify(dataObject))

  const response = await instance.post(
    `/course-service/v1/courses/tasks/${task_id}/task_submission`,
    formData
  )
  return response.data
}

export const taskStatusUpdate = async ({ task_id }) => {
  const response = await instance.post(`/v1/projects/{project_id}/tasks/${task_id}`)
  return response.data
}

export const editTaskStatus = async (task_id, project_id, publishedStatus) => {
  const response = await instance.patch(
    `/course-service/v1/projects/${project_id}/tasks/${task_id}?publish_status=${publishedStatus}`
  )
  return response.data
}
