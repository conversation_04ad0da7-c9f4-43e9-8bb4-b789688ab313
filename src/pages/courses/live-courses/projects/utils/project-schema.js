import moment from "moment"
import { z } from "zod"

export const projectSchema = z
  .object({
    title: z.string().nonempty({ message: "Title is Required" }),
    description: z.string().min(15, { message: "Description must be at least 15 characters" }),
    project_link: z.string().nonempty({ message: "Project Link is Required" }),
    start_date: z
      .preprocess(
        (val) => (typeof val === "string" ? new Date(val) : val),
        z.date({ required_error: "Start date is Required" })
      )
      .refine((val) => moment(val).isSameOrAfter(moment(), "minute"), {
        message: "Start date cannot be in the past",
      }),
    end_date: z
      .preprocess(
        (val) => (typeof val === "string" ? new Date(val) : val),
        z.date({ required_error: "End date is required" })
      )
      .refine((val) => moment(val).isSameOrAfter(moment(), "minute"), {
        message: "Deadline cannot be in the past",
      }),
    total_marks: z
      .string()
      .nonempty({ message: "Total marks is Required" })
      .transform((val) => Number(val)),
    docs: z.any().optional(),
  })
  .superRefine((data, ctx) => {
    const { start_date, end_date } = data
    if (start_date instanceof Date && end_date instanceof Date) {
      if (end_date < start_date) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Deadline cannot be before start date",
          path: ["end_date"],
        })
      }
    }
  })
  .transform((data) => ({
    ...data,
    start_date: moment(data.start_date).format("YYYY-MM-DDTHH:mm:ss"),
    end_date: moment(data.end_date).format("YYYY-MM-DDTHH:mm:ss"),
  }))

export const taskSchema = z.object({
  title: z.string().nonempty({ message: "Title is required" }),
  description: z.string().nonempty({ message: "Description is Required" }),
  due_date: z
    .preprocess(
      (val) => (typeof val === "string" ? new Date(val) : val),
      z.date({ required_error: "Deadline is Required" })
    )
    .refine((val) => moment(val).isSameOrAfter(moment(), "minute"), {
      message: "Deadline cannot be in the past",
    })
    .transform((val) => moment(val).format("YYYY-MM-DDTHH:mm:ss")),
  total_marks: z
    .string()
    .nonempty({ message: "Total marks is Required" })
    .transform((val) => Number(val)),
})


export const studentProjectSchema = z.object({
  notes_link: z.string().optional(),
  feedback: z.string().optional(),
})