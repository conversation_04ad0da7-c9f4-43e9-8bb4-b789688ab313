import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { tanstackConfig } from "@/services/query/tanstack-config"
import {
  CreateTasks,
  deleteTask,
  EditTask,
  editTaskStatus,
  fetchTasks,
  SubmitTask,
} from "./task.api"

export const useFetchProjectTasks = ({
  data,
  project_id,
  filters,
  course_id,
  userID,
  userRole,
  signature,
}) => {
  return useQuery({
    queryKey: ["FEATCH_TASKS", data, project_id, filters, course_id, userID, userRole, signature],
    queryFn: () => fetchTasks(data, project_id, filters, course_id, userID, userRole, signature),
    enabled: !!project_id && !!course_id,
    ...tanstackConfig,
  })
}

export const useCreateTask = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "CREATE-Task",
    mutationFn: ({ course_id, project_id, data, userID, userRole, signature }) =>
      CreateTasks(course_id, project_id, data, userID, userRole, signature),
    onSuccess: () => {
      setTimeout(() => {
        queryClient.invalidateQueries([{ queryKey: "FEATCH_TASKS", exact: false }])
      }, 500)
    },
  })
}

export const useDeletTask = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ project_id, task_id, userID, userRole, signature }) =>
      deleteTask(project_id, task_id, userID, userRole, signature),
    onSuccess: () => {
      setTimeout(() => {
        queryClient.invalidateQueries([{ queryKey: "FEATCH_TASKS", exact: false }])
      }, 500)
    },
  })
}

export const useUpdateTask = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: ["UPDATE-TASK"],
    mutationFn: ({ data, project_id, task_id, userID, userRole, signature }) =>
      EditTask(data, project_id, task_id, userID, userRole, signature),
    onSuccess: () => {
      setTimeout(() => {
        queryClient.invalidateQueries([{ queryKey: "FEATCH_TASKS", exact: false }])
      }, 500)
    },
  })
}

export const useSubmitTask = () => {
  return useMutation({
    mutationKey: "SUBMIT-Task",
    mutationFn: ({ task_id, data, file = null, userID, userRole, signature }) =>
      SubmitTask(task_id, data, file, userID, userRole, signature),
  })
}

export const useTaskStatusUbdate = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: "UPDATE-TASK-STATUS",
    mutationFn: ({ task_id, project_id, publishedStatus }) =>
      editTaskStatus(task_id, project_id, publishedStatus),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "FEATCH_TASKS", exact: false }])
    },
  })
}
