import AppointmentScheduler from "@/components/custom/custom-forms/date-time-picker"
import FormInput from "@/components/custom/custom-forms/form-input"
import FormTextArea from "@/components/custom/custom-forms/form-textarea"
import FormsSelect from "@/components/custom/form-select"
import FileUploadCard from "@/components/custom/upload-file"
import { Button } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Textarea } from "@/components/ui/textarea"
import PropTypes from "prop-types"

const ProjectCreateSubmission = ({
  form,
  onClose,
  setValue,
  control,
  userType,
  handleSubmit,
  onCreateClassForm,
  setLinkData,
  setStudentFeedBack,
  studentFeedBack,
  setDocumentData,
  handleProjectSubmit,
  handleClearProjectSubmit,
  documentData,
  linkData,
  setFileTypes,
  fileTypes,
}) => {
  const selectFileTypes = ["FILE", "LINK"]
  return (
    <ScrollArea className="overflow-y-auto w-full rounded-md pr-2">
      <Form {...form}>
        <form className="" onSubmit={handleSubmit(onCreateClassForm)}>
          <div className=" flex gap-y-3 flex-col justify-end lg:mb-8">
            {userType !== "STUDENT" ? (
              <>
                <FormInput
                  placeholder="Enter title"
                  label="Title"
                  fieldControlName="title"
                  isRequired
                  control={control}
                />
                <FormInput
                  placeholder="https://example.com"
                  label="Project Link"
                  fieldControlName="project_link"
                  isRequired
                  control={control}
                />

                <AppointmentScheduler
                  dataTestID="test-id"
                  dataTestIDError="error"
                  fieldControlName="start_date"
                  control={control}
                  label="Start Date"
                  placeholder="Pick a start date"
                  isRequired
                />

                <AppointmentScheduler
                  dataTestID="test-id"
                  dataTestIDError="error"
                  fieldControlName="end_date"
                  control={control}
                  label="Dead Line"
                  placeholder="Pick a dead line"
                  futureDateDisabled={null}
                  isRequired
                />

                <FormInput
                  label="Total Marks"
                  fieldControlName="total_marks"
                  isRequired
                  placeholder="Total marks"
                  control={control}
                  isTypeNumer
                />

                <FormTextArea
                  label="Description"
                  fieldControlName="description"
                  isRequired
                  placeholder="Enter descriptions"
                  control={control}
                />
              </>
            ) : null}

            {/* Notes Link & File Upload (Always Rendered for Students) */}
            {userType === "STUDENT" && (
              <div>
                <div className="my-4">
                  <FormsSelect setValue={setFileTypes} value={selectFileTypes} />
                </div>

                <FormInput
                  placeholder="Add link"
                  label="Add Notes Link"
                  fieldControlName="notes_link"
                  control={control}
                  disabled={fileTypes === "FILE" || fileTypes === ""}
                />
                <p className="text-slate-400 text-xs my-4 font-semibold mx-auto w-5">(or)</p>
                <FileUploadCard
                  setValue={setValue}
                  onChange={setDocumentData}
                  value={documentData}
                  disables={fileTypes === "LINK" || fileTypes === ""}
                  fileType="zip"
                />

                <div className="flex flex-col mt-3 gap-2">
                  <Label>Feedback</Label>
                  <Textarea
                    placeholder="Add Feedback"
                    value={studentFeedBack}
                    onChange={(e) => setStudentFeedBack(e.target.value)}
                  />
                </div>
                <div className="pt-12 flex gap-x-3 justify-end">
                  <Button onClick={handleClearProjectSubmit} variant="secondary" type="reset">
                    Cancel
                  </Button>
                  <Button
                    type="button"
                    variant="primary"
                    onClick={handleProjectSubmit}
                    disabled={selectFileTypes === ""}
                  >
                    Submit Project
                  </Button>
                </div>
              </div>
            )}

            {userType !== "STUDENT" && (
              <div className="pt-12 flex gap-x-3 justify-end">
                <Button onClick={onClose} variant="secondary" type="reset">
                  Cancel
                </Button>
                <Button type="submit" variant="primary">
                  {userType === "STUDENT" ? "Submit File" : "Create"}
                </Button>
              </div>
            )}
          </div>
        </form>
      </Form>
    </ScrollArea>
  )
}

ProjectCreateSubmission.propTypes = {
  form: PropTypes.objectOf.isRequired,
  onClose: PropTypes.func.isRequired,
  setValue: PropTypes.func.isRequired,
  control: PropTypes.objectOf.isRequired,
  userType: PropTypes.string.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  onCreateClassForm: PropTypes.func.isRequired,
  setLinkData: PropTypes.func.isRequired,
  setStudentFeedBack: PropTypes.func.isRequired,
  setDocumentData: PropTypes.func.isRequired,
  handleProjectSubmit: PropTypes.func.isRequired,
  handleClearProjectSubmit: PropTypes.func.isRequired,
  documentData: PropTypes.string,
  linkData: PropTypes.string,
  studentFeedBack: PropTypes.string,
  setFileTypes: PropTypes.func.isRequired,
  fileTypes: PropTypes.string,
}

export default ProjectCreateSubmission