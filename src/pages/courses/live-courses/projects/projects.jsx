import CustomSearchbar from "@/components/custom/custom-search"
import { CustomSelect } from "@/components/custom/custom-select"
import DataTable from "@/components/custom/cutsom-table"
import {
  ActionCell,
  DateCell,
  LinkCell,
  StatusUpdationCell,
} from "@/components/custom/cutsom-table/table-cells"
import { GeneralDialog } from "@/components/custom/dialogs/general.dialog"
import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import { Button } from "@/components/ui/button"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useIsEditDialogHook, usePublishStatus, useSelectedIds } from "@/hooks/common.hooks"
import useTableConfig from "@/hooks/use-table.hooks"
import { useEditPublishedStatus } from "@/services/query/project-query"
import { publishStatus, USER_ROLES } from "@/utils/constants"
import { isStudent } from "@/utils/helper"
import ct from "@constants/"
import { flexRender } from "@tanstack/react-table"
import { FilePlus2 } from "lucide-react"
import PropTypes from "prop-types"
import { useState } from "react"
import { useSelector } from "react-redux"
import ProjectSubmission from "../project-submission/project-submission"
import Tasks from "./components/tasks"
import { getColumnsByUserType } from "./project-coloums"

const ProjectTable = ({
  handleCreateClass,
  handleCreateProject,
  handleEdit,
  handleDeleteProject,
  listOfProject,
  pagination,
  setPagination,
  isLoading,
  setProjectId,
  projectId,
  handleProjectSubmit,
  setTaskId,
  taskId,
  setSearchingValue,
  searchingValue,
}) => {
  const [selectedProjectId, setSelectedProjectId] = useState(null)
  const userType = useSelector((st) => st[ct.store.USER_STORE]?.userRole)
  const { publishedStatus, handleSetPublishStatus } = usePublishStatus()
  const [view, setView] = useState("projects")
  const { isEditDialog, handleOpenEditDialog, handleResetEditDialog } = useIsEditDialogHook()
  const [selectedProjectEndDate, setSelectedProjectEndDate] = useState(null)
  const { mutate: puplishedStatus } = useEditPublishedStatus()
  const { selectedIDs, setSelectedIDs } = useSelectedIds()
  const course = useSelector((st) => st[ct.store.COURSES]?.activeCourseDetails)
  const { id: userID, userRole } = useSelector((state) => state.user)
  const courseDet = useSelector((st) => st[ct.store.COURSES])

  const handleOpenProjectStatusDialog = (data) => {
    const { id, publish_status } = data?.original || {}
    handleSetPublishStatus(publish_status)
    setSelectedIDs({
      ...selectedIDs,
      project_id: id,
    })

    handleOpenEditDialog()
  }
  const dynamicHeight = listOfProject?.data?.length >= 7 ? "h-[55vh]" : undefined

  const truncateText = (text = "", wordLimit = 3) => {
    if (!text) return ""

    const words = text.split(" ")
    const truncatedText =
      words.length > wordLimit ? `${words.slice(0, wordLimit).join(" ")}...` : text

    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <p className="truncate cursor-pointer">{truncatedText}</p>
          </TooltipTrigger>
          <TooltipContent>{text}</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  const renderCellContent = (cell, row) => {
    const {
      id,
      title,
      end_date,
      project_link,
      total_marks,
      updated_at,
      marks_obtained,
      task,
      description,
      trainer_feedback,
      submission_status,
      submissions_count,
      task_count,
      publish_status,
      submission_count,
    } = row?.original || {}

    switch (cell.column.id) {
      case "S_No":
        return <p>{cell.row.index + 1 + pagination.pageIndex * pagination.pageSize}</p>
      case "title":
        return truncateText(title, 3)
      case "description":
        return truncateText(description, 5)
      case "link":
        return (
          <LinkCell
            value={project_link}
            isPaidUser={isStudent(courseDet?.activeCourse, userRole)}
          />
        )
      case "deadline":
        return <DateCell data={end_date} />
      case "submission":
        return (
          <Button
            variant="primary"
            onClick={(e) => {
              e.stopPropagation()

              handleCreateClass(id)
            }}
          >
            Submit
          </Button>
        )
      case "number_of_task":
        return userType === "TRAINER" || userType === "ADMIN" ? (
          <Button
            variant="secondary"
            size="xs"
            onClick={(e) => {
              e.stopPropagation()
              setView("tasks")
              setSelectedProjectId(id)
              setSelectedProjectEndDate(row?.original?.end_date) // Capture end_date here
            }}
          >
            View{" "}
            <span className="h-4 w-4 text-xs font-semibold bg-[hsl(var(--primary)/.1)] rounded-full ">
              {task_count ?? 0}
            </span>
          </Button>
        ) : (
          <p>{task_count ?? 0}</p>
        )
      case "task_submission":
        return (
          <Button
            variant="secondary"
            size="xs"
            onClick={(e) => {
              e.stopPropagation()
              setView("submitedTasks")
              setTaskId({ ...taskId, task_id: id })
              setProjectId({ ...projectId, project_id: id })
            }}
          >
            View{" "}
            <span className="h-4 w-4 text-xs font-semibold bg-[hsl(var(--primary)/.1)] rounded-full ">
              {submissions_count ?? 0}
            </span>
          </Button>
        )
      case "remarks":
        return trainer_feedback ? <p>{trainer_feedback}</p> : <p>-</p>
      case "obtained_marks":
        return <p>{marks_obtained ? `${marks_obtained}/${total_marks}` : "-"}</p>
      case "submissionCount":
        return (
          <Button
            variant="secondary"
            size="xs"
            onClick={() => {
              setProjectId({ ...projectId, project_id: id })
              setView("projectSubmission")
            }}
          >
            View{" "}
            <span className="h-4 w-4 text-xs font-semibold bg-[hsl(var(--primary)/.1)] rounded-full ">
              {submission_count ?? 0}
            </span>
          </Button>
        )
      case "task":
        return <p>{task}</p>
      case "status":
        return (
          <StatusUpdationCell
            value={(userType === "STUDENT" || userType === "VENDOR"
              ? submission_status
              : publish_status
            )?.replace(/_/g, " ")}
            key={id}
          />
        )

      case "updated_at":
        return <DateCell date={updated_at} />
      case "action":
        return userType === "STUDENT" ? (
          <Button
            variant="primary"
            onClick={(e) => {
              e.stopPropagation()
              handleCreateClass(id)
              setProjectId({ ...projectId, project_id: id })
            }}
          >
            Submit
          </Button>
        ) : (
          (userType === "ADMIN" || userType === "TRAINER") && (
            <ActionCell
              label1="Update Status"
              label2="Edit"
              label3="Delete"
              row={row}
              isEdit
              isDelete
              onDelete={handleDeleteProject}
              onEdit={handleEdit}
              isView
              onView={handleOpenProjectStatusDialog}
            />
          )
        )
      default:
        return flexRender(cell.column.columnDef.cell, cell.getContext())
    }
  }

  const handleRowClick = (row) => {
    setSelectedProjectId(row?.id)
    setProjectId({ ...projectId, project_id: row?.id })
    setSelectedProjectEndDate(row?.original?.end_date) // Capture end_date here
    setView("tasks")
  }

  const { table, found, pageCount } = useTableConfig(
    listOfProject?.data,
    getColumnsByUserType(userType),
    listOfProject?.metadata?.total_records,
    setPagination,
    pagination
  )

  const renderView = () => {
    switch (view) {
      case "projectSubmission":
        return (
          <ProjectSubmission
            listOfProject={listOfProject}
            setView={setView}
            userType={userType}
            setProjectId={setProjectId}
            projectId={projectId}
            isPaidUser={isStudent(courseDet?.activeCourse, userRole)}
          />
        )
      case "tasks":
        return (
          <Tasks
            listOfProject={listOfProject}
            Title="Tasks"
            setView={setView}
            userType={userType}
            handleCreateProject={handleCreateProject}
            projectId={selectedProjectId}
            setSelectedProjectId={setSelectedProjectId}
            handleProjectSubmit={handleProjectSubmit}
            setTaskId={setTaskId}
            taskId={taskId}
            projectEndDate={selectedProjectEndDate} // Pass it here
            isPaidUser={isStudent(courseDet?.activeCourse, userRole)}
          />
        )
      default:
        return (
          <DataTable
            renderCellContent={renderCellContent}
            columns={getColumnsByUserType(userType)}
            table={table}
            found={found}
            height={dynamicHeight}
            pageCount={pageCount}
            pageName="Projects"
            pagination={pagination}
            onRowClick={
              userType === USER_ROLES.STUDENT || userType === USER_ROLES.VENDOR
                ? handleRowClick
                : undefined
            }
            notFoundPlaceholder="No Projects Found"
            isLoading={isLoading}
            isPaidUser={isStudent(courseDet?.activeCourse, userRole)}
          />
        )
    }
  }

  const handleUpdateStatus = () => {
    puplishedStatus(
      {
        project_id: selectedIDs?.project_id,
        course_id: course?.id,
        publishedStatus,
        userID,
        userRole,
        signature: "signature",
      },
      {
        onSuccess: () => {
          handleResetEditDialog()
          successToast(
            "Project Status Updated",
            "The Project Status has been successfully Updated!"
          )
        },
        onError: () => {
          handleResetEditDialog()
          failureToast(
            "Project Status Updation Failed",
            "An error occurred while update the Project Status. Please try again."
          )
        },
      }
    )
  }

  return (
    <div>
      <div className="flex items-center justify-between">
        {view === "projects" && (
          <CustomSearchbar
            inputSize="w-[20rem]"
            placeholder="Search by title..."
            searchedValue={searchingValue}
            setSearchedValue={(e) => setSearchingValue(e?.target?.value)}
          />
        )}

        <GeneralDialog
          onOpen={isEditDialog}
          title="Update Status"
          onClickCTA={handleUpdateStatus}
          ctaLabel="Update Status"
          onCancel={handleResetEditDialog}
          ctaPosition="justify-end"
          content={
            <CustomSelect
              publishedStatus={publishedStatus}
              setPublishedStatus={handleSetPublishStatus}
              iteratedData={publishStatus}
            />
          }
        />
        {view === "projects" && (userType === "TRAINER" || userType === "ADMIN") && (
          <Button onClick={handleCreateProject} variant="primary">
            <FilePlus2 size={18} /> Create
          </Button>
        )}
      </div>
      <div className="w-full">{renderView()}</div>
    </div>
  )
}

ProjectTable.propTypes = {
  handleCreateClass: PropTypes.func.isRequired,
  handleCreateProject: PropTypes.func.isRequired,
  handleEdit: PropTypes.func.isRequired,
  handleDeleteProject: PropTypes.func.isRequired,
  listOfProject: PropTypes.shape({
    // eslint-disable-next-line react/forbid-prop-types
    data: PropTypes.arrayOf(PropTypes.object),
    metadata: PropTypes.shape({
      total_records: PropTypes.number,
    }),
  }).isRequired,
  pagination: PropTypes.shape({
    pageIndex: PropTypes.number.isRequired,
    pageSize: PropTypes.number.isRequired,
  }).isRequired,
  setPagination: PropTypes.func.isRequired,
  isLoading: PropTypes.bool.isRequired,
  setProjectId: PropTypes.func.isRequired,
  projectId: PropTypes.shape({
    project_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  }).isRequired,
  handleProjectSubmit: PropTypes.func.isRequired,
  setTaskId: PropTypes.func.isRequired,
  taskId: PropTypes.shape({
    task_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  }).isRequired,
  setSearchingValue: PropTypes.func.isRequired,
  searchingValue: PropTypes.string.isRequired,
}

export default ProjectTable
