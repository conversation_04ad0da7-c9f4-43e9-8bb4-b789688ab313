import { DeleteDialog } from "@/components/custom/dialogs/delete.dialog"
import { failureToast, successToast, warningToast } from "@/components/custom/toasts/tosters"
import {
  useDeleteDialogHooks,
  useFilterHooks,
  useIsUpdateHook,
  useOpenCloseHooks,
  usePaginationHooks,
} from "@/hooks/common.hooks"
import { useDebounce } from "@/hooks/useDebounce"
import ct from "@constants/"
import { zodResolver } from "@hookform/resolvers/zod"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { useSelector } from "react-redux"
import { useLocation } from "react-router-dom"
import {
  useCreateProject,
  useDeletProject,
  useFetchProject,
  useSubmitProject,
  useUpdateProject,
} from "../../../../services/query/project-query"
import CustomSidebar from "../../components/custom-sidebar"
import ProjectCreateSubmission from "./project-create-submission"
import ProjectTable from "./projects"
import { projectSchema, studentProjectSchema } from "./utils/project-schema"

function Projects() {
  const location = useLocation()
  const loginData = useSelector((st) => st[ct.store.USER_STORE])
  const course = useSelector((st) => st[ct.store.COURSES]?.activeCourseDetails)
  const [projectId, setProjectId] = useState({
    module_id: location?.state?.module_id,
    course_id: course?.id,
    project_id: 0,
  })
  const [taskId, setTaskId] = useState({
    project_id: projectId,
    task_id: 0,
  })

  const course_id = course?.id

  const { id: userID, userRole } = useSelector((state) => state.user)
  const userType = loginData?.userRole
  const { handleOpen, open, setOpen, handleClose } = useOpenCloseHooks()
  const { isDelete, handleDelete, handleCancel } = useDeleteDialogHooks(false)
  const { isUpdate, handleResetUpdate, handleUpdate } = useIsUpdateHook()
  const [documentData, setDocumentData] = useState(null) // State for document
  const [linkData, setLinkData] = useState("") // State for string
  const [searchingValue, setSearchingValue] = useState("")
  const { limit } = usePaginationHooks()
  const debouncedSearchQuery = useDebounce(searchingValue, 500)
  const { sortBy, sortByField } = useFilterHooks()

  const [studentFeedBack, setStudentFeedBack] = useState("")
  const { mutate: deleteProject } = useDeletProject()
  const { mutate: CreateProject } = useCreateProject()
  const { mutate: EditProject } = useUpdateProject()
  const { mutate: submitProject } = useSubmitProject()

  const [fileTypes, setFileTypes] = useState("")
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })

  const calculateOffset = (pageIndex, pageSize) => pageIndex * pageSize
  const { data: ProjectData, isLoading } = useFetchProject({
    course_id: course?.id,
    filters: {
      limit,
      offset: calculateOffset(pagination.pageIndex, pagination.pageSize),
      sort_by: sortBy,
      sort_by_field: sortByField,
      search_query: debouncedSearchQuery?.length > 0 ? debouncedSearchQuery : null,
      userID,
      userRole,
      signature: "SIGNATURE",
    },
  })

  const form = useForm({
    resolver: zodResolver(userType === "STUDENT" ? studentProjectSchema : projectSchema),
    defaultValues: null,
    mode: "onChange",
  })

  const { handleSubmit, control, setValue, reset } = form
  const handleCreateClass = (id) => {
    setProjectId({ ...projectId, project_id: id })
    reset({
      notes_link: "",
      feedback: "",
    })
    setOpen(true)
  }
  const handleCreateProject = () => {
    setOpen(true)
    reset({
      title: "",
      description: "",
      project_link: "",
      start_date: "",
      end_date: "",
      total_marks: "",
      docs: undefined, // If it's a file input
    })
  }

  const handleCancelForm = () => {
    // handleCloseForm({ setOpen, reset })
    setOpen(false)
    reset()
  }

  const handleCreateClassForm = async (data = {}) => {
    // eslint-disable-next-line no-undef

    if (isUpdate) {
      EditProject(
        {
          data,
          project_id: projectId?.project_id,
          course_id,
          userID,
          userRole,
          signature: "SIGNATURE",
        },
        {
          onSuccess: () => {
            handleClose()
            successToast("Project Updated", "The Project has been successfully updated!")
            handleResetUpdate()
          },
          onError: () => {
            handleResetUpdate()
            handleClose()
            failureToast(
              "Project Update Failed",
              "An error occurred while updating the Project. Please try again."
            )
          },
        }
      )
      return
    }

    CreateProject(
      { course_id, data, userID, userRole, signature: "SIGNATURE" },
      {
        onSuccess: () => {
          handleClose()
          reset()
          successToast("Successfully Created!", "Project was successfully created!")
        },
        onError: (error) => {
          if (error) {
            reset()
            failureToast("Something went wrong", "Please try again later.")
          }
        },
      }
    )
  }

  const handleConfirmDelete = () => {
    deleteProject(
      {
        project_id: projectId?.project_id,
        course_id,
      },
      {
        onSuccess: () => {
          handleCancel()
          successToast("Project deleted successfully", "The Project has been removed.")
        },
        onError: () => {
          handleCancel()
          failureToast("Deletion Error", "Unable to delete the Project. Please try again later.")
        },
      }
    )
  }
  const handleEdit = (row) => {
    const { title, description, start_date, end_date, total_marks, id, ...rest } =
      row?.original ?? {}

    setProjectId({ ...projectId, project_id: id })
    handleUpdate()
    reset({
      title: title?.toString(),
      description: description?.toString(),
      total_marks: total_marks?.toString(),
      start_date: start_date?.toString(),
      end_date: end_date?.toString(),
      ...rest,
    })
    handleOpen()
  }

  const handleDeleteProject = (data) => {
    setProjectId({ ...projectId, project_id: data?.original?.id })
    handleDelete()
  }

  const handleclear = () => {
    setStudentFeedBack("")
    setLinkData("")
    setDocumentData(null)
  }
  const handleProjectSubmit = () => {
    const formData = form.getValues()
    const linkData = formData.notes_link || ""
    const studentFeedBack = formData.feedback || ""

    console.log("__linkData", linkData, documentData)

    if (!linkData && !documentData) {
      warningToast("Please enter project link or upload file", "")
      return
    }

    submitProject(
      {
        project_id: projectId?.project_id,
        course_id,
        data: {
          submission: linkData || "",
          feedback_student: studentFeedBack,
          submission_type: fileTypes,
        },
        ...(documentData?.[0] ? { file: documentData[0] } : {}),
        userID,
        userRole,
        signature: "SIGNATURE",
      },
      {
        onSuccess: () => {
          handleClose()
          handleclear()
          successToast("Project Uploaded", "The Project has been successfully Uploaded!")
          handleResetUpdate()
        },
        onError: (error) => {
          handleResetUpdate()
          handleClose()
          handleclear()
          if (error) {
            failureToast(
              "Project Upload Failed",
              "An error occurred Uploading the Project. Please try again."
            )
          }
        },
      }
    )
  }

  const handleClearProjectSubmit = () => {
    setDocumentData(null)
    setLinkData("")
    setStudentFeedBack("")
    setOpen(false)
  }
  const CreateSubmitProjectProps = {
    form,
    control,
    onClose: handleCancelForm,
    onCreateClassForm: handleCreateClassForm,
    handleSubmit,
    setValue,
    userType,
    setDocumentData,
    setLinkData,
    handleProjectSubmit,
    handleClearProjectSubmit,
    documentData,
    linkData,
    setStudentFeedBack,
    studentFeedBack,
    setFileTypes,
    fileTypes,
  }

  return (
    <>
      <DeleteDialog
        onOpen={isDelete}
        onDelete={handleConfirmDelete}
        onCancel={handleCancel}
        title="Delete Project"
        content="Are you sure want to delete this Project?"
      />
      <CustomSidebar
        isOpen={open}
        onClose={handleClose}
        title={userType === "TRAINER" || userType === "ADMIN" ? "Create Project" : "Submit Project"}
        description=""
        content={<ProjectCreateSubmission {...CreateSubmitProjectProps} />}
      />

      <ProjectTable
        handleEdit={handleEdit}
        handleDeleteProject={handleDeleteProject}
        handleCreateProject={handleCreateProject}
        handleCreateClass={handleCreateClass}
        listOfProject={ProjectData?.data}
        isLoading={isLoading}
        pagination={pagination}
        setPagination={setPagination}
        setProjectId={setProjectId}
        projectId={projectId}
        setTaskId={setTaskId}
        taskId={taskId}
        searchingValue={searchingValue}
        // onUpdateStatus={handleOpenProjectStatusDialog}
        setSearchingValue={setSearchingValue}
      />
    </>
  )
}
export default Projects
