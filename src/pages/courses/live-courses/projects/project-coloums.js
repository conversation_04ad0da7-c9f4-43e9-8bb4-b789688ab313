export const trainer = [
  {
    id: "title",
    accessorKey: "title",
    header: "Title",
  },
  {
    id: "link",
    accessorKey: "link",
    header: "Reference",
  },

  {
    id: "number_of_task",
    accessorKey: "number_of_task",
    header: "Number of Task",
  },
  {
    id: "submissionCount",
    accessorKey: "submissionCount",
    header: "Submission",
  },

  {
    id: "status",
    accessor<PERSON>ey: "status",
    header: "Published Status",
  },
  {
    id: "action",
    accessor<PERSON>ey: "action",
    header: "Action",
  },
]

export const vendor = [
  {
    id: "title",
    accessorKey: "title",
    header: "Title",
  },
  {
    id: "description",
    accessorKey: "description",
    header: "Description",
  },
  {
    id: "link",
    accessorKey: "link",
    header: "Reference",
  },

  {
    id: "deadline",
    accessorKey: "deadline",
    header: "Deadline",
  },
  {
    id: "obtained_marks",
    accessorKey: "obtained_marks",
    header: "Obtained Marks",
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Submited Status",
  },
  {
    id: "remarks",
    accessorKey: "remarks",
    header: "Remarks",
  },
]

export const student = [
  {
    id: "title",
    accessorKey: "title",
    header: "Title",
  },
  {
    id: "description",
    accessorKey: "description",
    header: "Description",
  },
  {
    id: "link",
    accessorKey: "link",
    header: "Reference",
  },

  {
    id: "deadline",
    accessorKey: "deadline",
    header: "Deadline",
  },
  {
    id: "obtained_marks",
    accessorKey: "obtained_marks",
    header: "Obtained Marks",
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Submited Status",
  },
  {
    id: "remarks",
    accessorKey: "remarks",
    header: "Remarks",
  },
]

export const admin = [
  {
    id: "title",
    accessorKey: "title",
    header: "Title",
  },
  {
    id: "link",
    accessorKey: "link",
    header: "Reference",
  },
  {
    id: "number_of_task",
    accessorKey: "number_of_task",
    header: "Number of Task",
  },
  {
    id: "submissionCount",
    accessorKey: "submissionCount",
    header: "Submission",
  },

  {
    id: "status",
    accessorKey: "status",
    header: "Published Status",
  },
  {
    id: "action",
    accessorKey: "action",
    header: "Action",
  },
]

export const getColumnsByUserType = (userType) => {
  switch (userType.toUpperCase()) {
    case "TRAINER":
      return trainer
    case "STUDENT":
      return student
    case "VENDOR":
      return vendor
    case "ADMIN":
      return admin
    default:
      return [] // Return an empty array if the userType is invalid
  }
}
