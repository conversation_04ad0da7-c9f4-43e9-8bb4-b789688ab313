import CustomTabs from "@/components/custom/custom-tabs"
import { setActiveTab } from "@/services/store/slices/courses.slice"
import ct from "@constants/"
import { useSelector } from "react-redux"
import { useLocation } from "react-router-dom"
import Tabs from "./utils/Tabs.constant"

function LiveCourses() {
  const { activeTab } = useSelector((st) => st[ct.store.COURSES])
  const userType = useSelector((st) => st[ct.store.USER_STORE]?.userRole)
  const location = useLocation()
  const courseOverview = location.pathname?.includes("batch-course")
  // ✅ Filter out "course-materials" for non-ADMIN users
  const filteredTabs =
    userType === "ADMIN" && courseOverview ? Tabs : Tabs.filter((tab) => tab.id !== "batch")

  return (
    <CustomTabs
      tabs={filteredTabs} // ✅ Pass the filtered tabs
      size="w-full"
      activeTab={activeTab}
      defaultTab={filteredTabs[0]?.id} // ✅ Ensure the first tab exists
      setActiveTab={setActiveTab}
    />
  )
}

export default LiveCourses
