{"data": [{"id": 1, "quizNo": 2, "submission": "file", "student": "<PERSON><PERSON>", "task": "Complete JSX Introduction", "oppno": "12 / 10", "totno": 10, "sno": 1, "submissions": 5, "submission_count": 5, "assignment_no": 2547, "title": "React JS Basics", "description": "This session covers the fundamental concepts of React JS, including JSX, components, and the virtual DOM.", "deadline": "2025-01-30T23:59:59Z", "meet_link": "https://meet.example.com/1", "notes": "Introduction to React components.", "quiz": "What is JSX in React?", "CL": "Understand JSX and React components", "topic": ["JSX", "React Components", "Virtual DOM"], "trainer": "<PERSON>", "feedback": "Great session, very informative.", "mark_obtained": "9/10", "remarks": "Excellent grasp of JSX.", "task_number": 1, "total_questions": 10, "status": "Done on 22 January 2025", "updated_at": "2025-02-05", "isSubmitted": true, "feedback_count": 4}, {"id": 2, "assignment_no": 3891, "quizNo": 4, "submission": "link", "student": "<PERSON>", "task": "Write a blog on State vs Props", "sno": 2, "oppno": 34, "totno": 30, "submissions": 3, "submission_count": 3, "title": "State & Props", "description": "Learn about state and props in React, how they differ, and how to use them to manage data within components.", "deadline": "2025-04-15 ", "meet_link": "https://meet.example.com/2", "notes": "Understanding state and props in React.", "quiz": "What is the difference between state and props?", "CL": "Learn how state and props work in React", "topic": ["State", "Props", "Component Communication"], "trainer": "<PERSON>", "feedback": "Good explanations with practical examples.", "mark_obtained": "8/10", "remarks": "Needs more clarity on prop drilling.", "task_number": 2, "total_questions": 10, "status": "Done on 22 January 2025", "updated_at": "2025-02-05 ", "feedback_count": 5, "isSubmitted": false}, {"id": 3, "quizNo": 5, "sno": 3, "oppno": 56, "totno": 50, "student": "<PERSON>", "task": "Create a React Hooks project", "submissions": 2, "submission_count": 2, "assignment_no": 1456, "title": "React Hooks", "description": "Explore React hooks such as useState and useEffect to manage state and side effects in functional components.", "deadline": "2025-04-15 ", "meet_link": "https://meet.example.com/3", "notes": "Introduction to useState and useEffect.", "quiz": "What is the purpose of useState in React?", "CL": "Understand and implement useState and useEffect", "topic": ["useState", "useEffect", "<PERSON>s"], "trainer": "<PERSON>", "feedback": "Helpful session on hooks.", "mark_obtained": "7/10", "remarks": "Perfect understanding of hooks.", "task_number": 3, "total_questions": 10, "status": "Done on 22 January 2025", "updated_at": "2025-02-05 ", "feedback_count": 3, "isSubmitted": true}, {"id": 4, "quizNo": 6, "sno": 4, "submissions": 1, "submission_count": 1, "student": "<PERSON>", "task": "Build a React Router demo", "assignment_no": 7823, "title": "React Router", "description": "Understand how to set up and manage navigation in React applications using React Router.", "deadline": "2025-04-15 ", "meet_link": "https://meet.example.com/4", "notes": "Setting up navigation with React Router.", "quiz": "How do you define a route in React Router?", "CL": "Set up navigation using React Router", "topic": ["Routing", "Navigation", "React Router"], "trainer": "<PERSON>", "feedback": "Clear and concise explanation.", "mark_obtained": "7/10", "remarks": "Needs improvement in nested routing.", "task_number": 4, "total_questions": 10, "status": "Done on 22 January 2025", "updated_at": "2025-02-05 ", "feedback_count": 3}, {"id": 5, "assignment_no": 9234, "quizNo": 7, "sno": 5, "submissions": 4, "submission_count": 4, "student": "<PERSON>", "task": "Implement Redux in a project", "title": "Redux Basics", "description": "Introduction to Redux for state management, covering actions, reducers, and the Redux store.", "deadline": "2025-04-15 ", "meet_link": "https://meet.example.com/5", "notes": "Managing state with Redux.", "quiz": "What are the core principles of Redux?", "CL": "Learn the basics of Redux and state management", "topic": ["Redux", "State Management", "Actions & Reducers"], "trainer": "<PERSON>", "feedback": "Detailed explanation, but could use more examples.", "mark_obtained": "8/10", "remarks": "Good understanding, but practice needed.", "task_number": 5, "total_questions": 10, "status": "Done on 22 January 2025", "updated_at": "2025-02-05 ", "feedback_count": 3}], "meta": {"found": 10}}