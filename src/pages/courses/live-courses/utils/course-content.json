{"response": [{"id": 1, "date": "30 January 2025", "class": 10, "topic": "React JS Basics", "meet_link": "https://meet.example.com/1", "trainer": "<PERSON>", "notes": "Introduction to React components.", "type": "live", "url": "https://www.google.com/url?sa=i&url=https%3A%2F%2Fdev.to%2Fashallendesign%2F13-placeholder-avatar-image-websites-4g03&psig=AOvVaw21yeiK-Kw8ubOx9amRVN12&ust=1738417295677000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCPi7rJ6LoIsDFQAAAAAdAAAAABAE"}, {"id": 2, "date": "31 January 2025", "class": 11, "topic": "State & Props", "meet_link": "https://meet.example.com/2", "trainer": "<PERSON>", "notes": "Understanding state and props in React.", "type": "upcoming", "url": "https://www.google.com/url?sa=i&url=https%3A%2F%2Fdev.to%2Fashallendesign%2F13-placeholder-avatar-image-websites-4g03&psig=AOvVaw21yeiK-Kw8ubOx9amRVN12&ust=1738417295677000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCPi7rJ6LoIsDFQAAAAAdAAAAABAE"}, {"id": 3, "date": "1 February 2025", "class": 12, "topic": "React Hooks", "meet_link": "https://meet.example.com/3", "trainer": "<PERSON>", "notes": "Introduction to useState and useEffect.", "type": "completed", "url": "https://www.google.com/url?sa=i&url=https%3A%2F%2Fdev.to%2Fashallendesign%2F13-placeholder-avatar-image-websites-4g03&psig=AOvVaw21yeiK-Kw8ubOx9amRVN12&ust=1738417295677000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCPi7rJ6LoIsDFQAAAAAdAAAAABAE"}, {"id": 4, "date": "2 February 2025", "class": 13, "topic": "React Router", "meet_link": "https://meet.example.com/4", "trainer": "<PERSON>", "notes": "Setting up navigation with React Router.", "type": "completed", "url": "https://www.google.com/url?sa=i&url=https%3A%2F%2Fdev.to%2Fashallendesign%2F13-placeholder-avatar-image-websites-4g03&psig=AOvVaw21yeiK-Kw8ubOx9amRVN12&ust=1738417295677000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCPi7rJ6LoIsDFQAAAAAdAAAAABAE"}, {"id": 5, "date": "3 February 2025", "class": 14, "topic": "Redux Basics", "meet_link": "https://meet.example.com/5", "trainer": "<PERSON>", "notes": "Managing state with Redux.", "type": "completed", "url": "https://www.google.com/url?sa=i&url=https%3A%2F%2Fdev.to%2Fashallendesign%2F13-placeholder-avatar-image-websites-4g03&psig=AOvVaw21yeiK-Kw8ubOx9amRVN12&ust=1738417295677000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCPi7rJ6LoIsDFQAAAAAdAAAAABAE"}, {"id": 6, "date": "4 February 2025", "class": 15, "topic": "Redux Toolkit", "meet_link": "https://meet.example.com/6", "trainer": "<PERSON>", "notes": "Using Redux Toolkit for state management.", "type": "upcoming", "url": "https://www.google.com/url?sa=i&url=https%3A%2F%2Fdev.to%2Fashallendesign%2F13-placeholder-avatar-image-websites-4g03&psig=AOvVaw21yeiK-Kw8ubOx9amRVN12&ust=1738417295677000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCPi7rJ6LoIsDFQAAAAAdAAAAABAE"}, {"id": 7, "date": "5 February 2025", "class": 16, "topic": "Context API", "meet_link": "https://meet.example.com/7", "trainer": "<PERSON>", "notes": "Managing state using Context API.", "type": "upcoming", "url": "https://www.google.com/url?sa=i&url=https%3A%2F%2Fdev.to%2Fashallendesign%2F13-placeholder-avatar-image-websites-4g03&psig=AOvVaw21yeiK-Kw8ubOx9amRVN12&ust=1738417295677000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCPi7rJ6LoIsDFQAAAAAdAAAAABAE"}, {"id": 8, "date": "6 February 2025", "class": 17, "topic": "Custom Hooks", "meet_link": "https://meet.example.com/8", "trainer": "<PERSON>", "notes": "Creating reusable custom hooks in React.", "type": "upcoming", "url": "https://www.google.com/url?sa=i&url=https%3A%2F%2Fdev.to%2Fashallendesign%2F13-placeholder-avatar-image-websites-4g03&psig=AOvVaw21yeiK-Kw8ubOx9amRVN12&ust=1738417295677000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCPi7rJ6LoIsDFQAAAAAdAAAAABAE"}, {"id": 9, "date": "7 February 2025", "class": 18, "topic": "React Performance Optimization", "meet_link": "https://meet.example.com/9", "trainer": "<PERSON>", "notes": "Optimizing performance in React applications.", "type": "upcoming", "url": "https://www.google.com/url?sa=i&url=https%3A%2F%2Fdev.to%2Fashallendesign%2F13-placeholder-avatar-image-websites-4g03&psig=AOvVaw21yeiK-Kw8ubOx9amRVN12&ust=1738417295677000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCPi7rJ6LoIsDFQAAAAAdAAAAABAE"}, {"id": 10, "date": "8 February 2025", "class": 19, "topic": "Unit Testing in React", "meet_link": "https://meet.example.com/10", "trainer": "<PERSON>", "notes": "Writing test cases using Jest and React Testing Library.", "type": "upcoming", "url": "https://www.google.com/url?sa=i&url=https%3A%2F%2Fdev.to%2Fashallendesign%2F13-placeholder-avatar-image-websites-4g03&psig=AOvVaw21yeiK-Kw8ubOx9amRVN12&ust=1738417295677000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCPi7rJ6LoIsDFQAAAAAdAAAAABAE"}, {"id": 11, "date": "9 February 2025", "class": 20, "topic": "Deploying React Apps", "meet_link": "https://meet.example.com/11", "trainer": "<PERSON>", "notes": "Deployment strategies for React applications.", "type": "upcoming", "url": "https://www.google.com/url?sa=i&url=https%3A%2F%2Fdev.to%2Fashallendesign%2F13-placeholder-avatar-image-websites-4g03&psig=AOvVaw21yeiK-Kw8ubOx9amRVN12&ust=1738417295677000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCPi7rJ6LoIsDFQAAAAAdAAAAABAE"}, {"id": 12, "date": "10 February 2025", "class": 21, "topic": "Next.js Fundamentals", "meet_link": "https://meet.example.com/12", "trainer": "<PERSON>", "notes": "Introduction to Next.js framework.", "type": "upcoming", "url": "https://www.google.com/url?sa=i&url=https%3A%2F%2Fdev.to%2Fashallendesign%2F13-placeholder-avatar-image-websites-4g03&psig=AOvVaw21yeiK-Kw8ubOx9amRVN12&ust=1738417295677000&source=images&cd=vfe&opi=89978449&ved=0CBQQjRxqFwoTCPi7rJ6LoIsDFQAAAAAdAAAAABAE"}], "meta": {"found": 12}}