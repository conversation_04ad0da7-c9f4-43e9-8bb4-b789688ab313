import { Card } from "@/components/ui/card"
import ManageCourseBatchAssigneePage from "@/pages/manage-batch/manage-page"
import {
  BellRing,
  BookAudio,
  ChevronsLeftRightEllipsis,
  FileCode,
  FileCode2,
  <PERSON><PERSON><PERSON><PERSON>,
  Puzzle,
} from "lucide-react"
import { LuFolderCode } from "react-icons/lu"
import Announcements from "../announcement"
import Assignments from "../assignments"
import CourseContent from "../course-content"
import CourseMeterial from "../course-meterial"
import CourseOverview from "../course-overview"
import Interview from "../interview-materials"
import Projects from "../projects"
import Quiz from "../quiz"
import { COURSE_TABS_TITLES } from "./constants"

// Complete tabs configuration with consistent structure
const Tabs = [
  {
    id: "batch",
    label: COURSE_TABS_TITLES.BATCH,
    content: <ManageCourseBatchAssigneePage />,
    icon: <FileCode2 size={18} className="mr-2" />,
  },
  {
    id: "course-overview",
    label: COURSE_TABS_TITLES.COURSE_OVERVIEW,
    content: <CourseOverview />,
    icon: <FileCode2 size={18} className="mr-2" />,
  },
  {
    id: "announcement",
    label: COURSE_TABS_TITLES.ANNOUNCEMENT,
    content: <Announcements />,
    icon: <BellRing size={18} className="mr-2" />,
  },
  {
    id: "course-content",
    label: COURSE_TABS_TITLES.COURSE_CONTENT,
    content: <CourseContent />,
    icon: <FileCode size={18} className="mr-2" />,
  },
  {
    id: "quiz",
    label: COURSE_TABS_TITLES.QUIZ,
    content: (
      <Card className="border-0 outline-none shadow-none p-6 rounded-2xl ">
        <Quiz />
      </Card>
    ),
    icon: <Puzzle size={18} className="mr-2" />,
  },
  {
    id: "assignment",
    label: COURSE_TABS_TITLES.ASSIGNMENT,
    content: (
      <Card className="border-0 outline-none shadow-none p-6 rounded-2xl ">
        {" "}
        <Assignments />
      </Card>
    ),
    icon: <PcCase size={18} className="mr-2" />,
  },
  {
    id: "projects",
    label: COURSE_TABS_TITLES.PROJECTS,
    content: (
      <Card className="border-0 outline-none shadow-none p-6 rounded-2xl ">
        {" "}
        <Projects />
      </Card>
    ),
    icon: <LuFolderCode size={18} className="mr-2" />,
  },
  {
    id: "course-materials",
    label: COURSE_TABS_TITLES.COURSE_MATERIALS,
    content: (
      <Card className="border-0 outline-none shadow-none p-6 rounded-2xl ">
        {" "}
        <CourseMeterial />
      </Card>
    ),
    icon: <BookAudio size={18} className="mr-2" />,
  },
  {
    id: "interview-materials",
    label: COURSE_TABS_TITLES.INTERVIEW,
    content: (
      <Card className="border-0 outline-none shadow-none p-6 rounded-2xl ">
        {" "}
        <Interview />
      </Card>
    ),
    icon: <ChevronsLeftRightEllipsis size={18} className="mr-2" />,
  },
]

export default Tabs
