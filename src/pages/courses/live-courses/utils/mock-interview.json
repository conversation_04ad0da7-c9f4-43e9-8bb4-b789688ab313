{"response": [{"id": 1, "date": "30 January 2025", "title": "Software Engineer Google", "description": "Frontend Development Interview focusing on React ", "interview": "<PERSON>", "student": "<PERSON>", "remarks": "Excellent grasp of React components.", "status": "Completed", "interviewFeedback": "Demonstrated strong understanding of React concepts. Handled component lifecycle well. Could improve on performance optimization techniques.", "studentFeedback": "The interview was very insightful. Learned a lot about React best practices.", "selectedSkills": ["react", "javascript"], "overallRating": "excellent", "interviewLink": "https://meet.google.com/abc-defg-hij", "grade": "A", "duration": "60 mins"}, {"id": 2, "date": "31 January 2025", "title": "Software Engineer Microsoft", "description": "Full Stack Development Interview with focus on Node.js", "interview": "<PERSON>", "student": "<PERSON>", "remarks": "Good understanding of React state management.", "status": "Completed", "feedback": "Needs improvement in React state management.", "interviewLink": "https://meet.google.com/xyz-mnop-qrs", "grade": "B+", "duration": "45 mins"}, {"id": 3, "date": "1 February 2025", "title": "Software Engineer Facebook", "description": "Frontend System Design Interview", "interview": "<PERSON>", "student": "<PERSON>", "remarks": "Excellent problem-solving skills.", "status": "Scheduled", "feedback": "Great session on system design patterns.", "interviewLink": "https://meet.google.com/lmn-opq-rst", "grade": "A+", "duration": "90 mins"}, {"id": 4, "date": "2 February 2025", "title": "Software Engineer <PERSON>", "description": "Data Structures and Algorithms Interview", "interview": "<PERSON>", "student": "<PERSON>", "remarks": "Good understanding of DSA concepts.", "status": "Cancelled", "feedback": "Session cancelled due to technical issues.", "interviewLink": "https://meet.google.com/uvw-xyz-123", "grade": "N/A", "duration": "60 mins"}, {"id": 5, "date": "3 February 2025", "title": "Software Engineer <PERSON>", "description": "React Performance Optimization Interview", "interview": "<PERSON>", "student": "<PERSON>", "remarks": "Excellent grasp of React performance concepts.", "status": "Completed", "feedback": "Great understanding of performance optimization.", "interviewLink": "https://meet.google.com/456-789-abc", "grade": "A", "duration": "75 mins"}], "meta": {"found": 5}}