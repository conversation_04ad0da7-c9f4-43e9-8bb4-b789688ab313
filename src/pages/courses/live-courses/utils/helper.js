import { USER_ROLES } from "@/utils/constants"

export const displayTheHeadings = (
  userRole,
  isUpdate,
  editContent,
  createContent,
  sEditContent,
  sCreateContent
) => {
  const isTrainerOrAdmin = userRole === USER_ROLES.ADMIN || userRole === USER_ROLES.TRAINER

  if (isTrainerOrAdmin) {
    return isUpdate ? editContent : createContent
  }

  return isUpdate ? sEditContent : sCreateContent
}

export const roleRestrictions = (userRole, roleType) => {
  switch (roleType) {
    case "admin_trainer":
      return [USER_ROLES.ADMIN, USER_ROLES.TRAINER].includes(userRole)
    case USER_ROLES.ADMIN:
      return userRole === USER_ROLES.ADMIN
    case USER_ROLES.TRAINER:
      return userRole === USER_ROLES.TRAINER
    default:
      return false
  }
}
