import { Form } from "@/components/ui/form"
import { Button } from "@/components/ui/button"
import FormInput from "@/components/custom/custom-forms/form-input"
import FormTextArea from "@/components/custom/custom-forms/form-textarea"
import { formsProps } from "@/components/custom/custom-forms/utils/props-type"
import { USER_ROLES } from "@/utils/constants"

const AnnouncementCreationUpdationUI = ({
  form,
  control,
  onCreateOrUpdateAnnouncement,
  handleSubmit,
  isUpdate,
  onClose,
  userRole,
  isLoading,
}) => {
  const isTrainerOrAdmin = userRole === USER_ROLES.TRAINER || userRole === USER_ROLES.ADMIN
  const isStudentOrVendor = isUpdate
    ? [USER_ROLES.VENDOR, USER_ROLES.STUDENT].includes(userRole)
    : false
  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(onCreateOrUpdateAnnouncement)} className="space-y-5">
        <FormInput
          dataTestID="test-id"
          dataTestIDError="error"
          fieldControlName="title"
          control={control}
          label="Title"
          placeholder="Enter title"
          isRequired
          disabled={isStudentOrVendor}
        />
        <FormTextArea
          dataTestID="test-id"
          dataTestIDError="error"
          fieldControlName="content"
          control={control}
          label="Description"
          placeholder="Enter description"
          isRequired
          disabled={isStudentOrVendor}
        />

        <div className="p t-12 flex gap-x-3 justify-end">
          <Button onClick={onClose} variant="secondary" type="reset">
            Cancel
          </Button>
          {isTrainerOrAdmin && (
            <Button
              disabled={isLoading}
              type="submit"
              variant="primary"
              className={`${isLoading ? "opacity-75 cursor-not-allowed" : ""}`}
            >
              {isUpdate ? "Update" : "Create"}
              {/* {isLoading && <LoadingSpinner iconWidth={20} iconHeight={20} />} */}
            </Button>
          )}
        </div>
      </form>
    </Form>
  )
}
AnnouncementCreationUpdationUI.propTypes = formsProps

export default AnnouncementCreationUpdationUI
