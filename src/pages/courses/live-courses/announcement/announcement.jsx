import GradiantCard from "@/components/custom/custom-cards/graidiant.cards"
import PropTypes from "prop-types"

const AnnouncementUI = ({ onCardClick, listOfAnnouncements, onDelete, userRole, onEditStatus }) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
      {listOfAnnouncements?.length > 0 &&
        listOfAnnouncements?.map(
          ({ id, title, date, posted_by, publish_status, content }, index) => (
            <GradiantCard
              key={id}
              onCardClick={onCardClick}
              id={id}
              title={title}
              date={date}
              postedBy={posted_by}
              message={content}
              onDelete={onDelete}
              publish_status={publish_status}
              index={index}
              userRole={userRole}
              onEditStatus={onEditStatus}
            />
          )
        )}
    </div>
  )
}

AnnouncementUI.propTypes = {
  onCardClick: PropTypes.func,
  onEditStatus: PropTypes.func,
  listOfAnnouncements: PropTypes.arrayOf(PropTypes.objectof),
  onDelete: PropTypes.func,
  userRole: PropTypes.string,
}

export default AnnouncementUI
