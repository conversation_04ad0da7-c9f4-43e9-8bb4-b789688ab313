import CustomSearchbar from "@/components/custom/custom-search"
import { CustomSelect } from "@/components/custom/custom-select"
import { DeleteDialog } from "@/components/custom/dialogs/delete.dialog"
import { GeneralDialog } from "@/components/custom/dialogs/general.dialog"
import LoadingSpinner from "@/components/custom/LoadingSpinner"
import { deleteToast, failureToast, successToast } from "@/components/custom/toasts/tosters"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  useDeleteDialogHooks,
  useFilterHooks,
  useIsEditDialogHook,
  useIsLoading,
  useIsUpdateHook,
  useOpenCloseHooks,
  usePaginationHooks,
  usePublishStatus,
  useSelectedIds,
} from "@/hooks/common.hooks"
import { useDebounce } from "@/hooks/useDebounce"
import {
  useCreateAnnouncementMutaion,
  useDeleteAnnouncementMutation,
  useFetchAnnouncementsQuery,
  useUpdateAnnouncementMutation,
  useUpdateStatusAnnouncementMutation,
} from "@/services/query/announcements.query"
import { publishStatus, USER_ROLES } from "@/utils/constants"
import { titleChangesByRole } from "@/utils/helper"
import ct from "@constants/"
import { zodResolver } from "@hookform/resolvers/zod"
import { useCallback, useEffect, useRef, useState } from "react"
import { useForm } from "react-hook-form"
import { TbBellRinging } from "react-icons/tb"
import { useSelector } from "react-redux"
import CustomSidebar from "../../components/custom-sidebar"
import AnnouncementUI from "./announcement"
import AnnouncementCreationUpdationUI from "./announcement-creation-updations"
import { announcementFormDefaultValues, announcementSchema } from "./utils/announcement.schema"

function Announcements() {
  const userRole = useSelector((st) => st[ct.store.USER_STORE]?.userRole)
  const course = useSelector((st) => st[ct.store.COURSES]?.activeCourseDetails)

  // custom hooks
  const { handleOpen, handleClose, open } = useOpenCloseHooks()
  const { isUpdate, handleUpdate, handleResetUpdate } = useIsUpdateHook()
  const { isEditDialog, handleOpenEditDialog, handleResetEditDialog } = useIsEditDialogHook()
  const { isDelete, handleDelete, handleCancel } = useDeleteDialogHooks()
  const { isLoading, handleResetLoading, handleSetLoading } = useIsLoading()
  const { publishedStatus, handleSetPublishStatus } = usePublishStatus()
  const { selectedIDs, setSelectedIDs } = useSelectedIds()
  const { sortBy, sortByField } = useFilterHooks()

  const [searchingValue, setSearchingValue] = useState("")
  const [hasMore, setHasMore] = useState(true)
  const scrollAreaRef = useRef(null)
  const prevDataLengthRef = useRef(0)

  const debouncedSearchQuery = useDebounce(searchingValue, 500)
  const { limit, offset, setOffset } = usePaginationHooks()

  const [announcements, setAnnouncements] = useState([])
  const { data: listOfAnnouncements, isLoading: announcementLoading } = useFetchAnnouncementsQuery({
    limit,
    offset,
    course_id: course?.id,
    sort_by: sortBy,
    sort_by_field: sortByField,
    search_query: debouncedSearchQuery?.length > 0 ? debouncedSearchQuery : null,
  })

  // api's
  const createMutation = useCreateAnnouncementMutaion(setOffset)
  const updateMutation = useUpdateAnnouncementMutation()
  const deleteAnnouncementMutaion = useDeleteAnnouncementMutation()
  const updateStatusAnnouncementMutation = useUpdateStatusAnnouncementMutation()

  // hook form
  const form = useForm({
    resolver: zodResolver(announcementSchema),
    defaultValues: announcementFormDefaultValues,
    mode: "onChange",
  })

  const { handleSubmit, control, setValue, reset } = form

  const handleClickAnnouncementCard = (data) => {
    setSelectedIDs({
      ...selectedIDs,
      announceMentID: data?.id,
    })

    reset({
      title: data?.title,
      content: data?.message,
    })
    handleUpdate()
    handleOpen()
  }

  useEffect(() => {
    setAnnouncements([])
    setOffset(0)
    setHasMore(true)
    prevDataLengthRef.current = 0
  }, [sortBy, sortByField])

  useEffect(() => {
    if (!listOfAnnouncements?.data?.data) return

    // setOffset(0)
    const newData = listOfAnnouncements.data.data
    console.log("__announce", listOfAnnouncements?.data?.data)
    setAnnouncements((prev) => {
      // Prevent duplicate entries
      return offset === 0 ? newData : [...prev, ...newData]
    })

    prevDataLengthRef.current = newData.length
    setHasMore(newData.length >= limit)
  }, [listOfAnnouncements?.data, limit, offset])

  // First, memoize the handleScroll function
  const handleScroll = useCallback(() => {
    const scrollAreaElement = scrollAreaRef.current?.querySelector(
      "[data-radix-scroll-area-viewport]"
    )
    if (!scrollAreaElement) return

    const { scrollHeight, scrollTop, clientHeight } = scrollAreaElement

    // Add a small threshold before the bottom (e.g. 50px)
    const threshold = 100
    const isNearBottom = scrollTop + clientHeight >= scrollHeight - threshold
    if (isNearBottom && hasMore && !announcementLoading) {
      setOffset((prev) => prev + limit)
    }
  }, [hasMore, announcementLoading, scrollAreaRef, limit])

  // Then, properly clean up the event listener
  useEffect(() => {
    const scrollAreaElement = scrollAreaRef.current?.querySelector(
      "[data-radix-scroll-area-viewport]"
    )

    // Guard against null element
    if (!scrollAreaElement) return

    scrollAreaElement.addEventListener("scroll", handleScroll)

    // Clean up function
    // eslint-disable-next-line consistent-return
    return () => {
      scrollAreaElement.removeEventListener("scroll", handleScroll)
    }
  }, [handleScroll]) // Include handleScroll in dependencies

  const handleCreateOrUpdateAnnouncement = async (data = {}) => {
    if (isUpdate) {
      handleSetLoading()
      updateMutation.mutate(
        { course_id: course?.id, announcement_id: selectedIDs?.announceMentID, data },
        {
          onSuccess: () => {
            handleClose()
            handleResetUpdate()
            successToast("Successfully Updated!", "Announcement was successfully Updated!")
            handleResetLoading()
          },
          onError: () => {
            failureToast("Updations Failed", "Failed to update announcement")
            handleResetLoading()
          },
        }
      )
      return
    }
    handleSetLoading()
    createMutation.mutate(
      { course_id: course?.id, ...data },
      {
        onSuccess: () => {
          handleClose()
          successToast("Successfully Created!", "Announcement was successfully created!")
          handleResetLoading()
        },
        onError: () => {
          failureToast("Creations Failed", "Failed to create announcement")
          handleResetLoading()
        },
      }
    )
  }

  const handleCancelForm = () => {
    reset()
    handleClose()
  }

  const handleCreateAnnouncement = () => {
    reset({
      title: "",
      content: "",
    })
    handleResetUpdate()
    handleOpen()
  }

  const handleOpenDeleteDialog = (ids) => {
    console.log("__status", ids)
    setSelectedIDs({
      ...selectedIDs,
      announceMentID: ids,
    })
    handleDelete()
  }

  const handleEditStatus = (id, status) => {
    console.log("__status", status, id)
    handleSetPublishStatus(status)
    setSelectedIDs({
      ...selectedIDs,
      announceMentID: id,
    })

    handleOpenEditDialog()
  }
  console.log(selectedIDs?.announceMentID, "selectedIDs?.announceMentID,")
  const handleUpdateStatus = () => {
    updateStatusAnnouncementMutation.mutate(
      {
        course_id: course?.id,
        announcement_id: selectedIDs?.announceMentID,
        status: publishedStatus,
      },
      {
        onSuccess: () => {
          successToast("Publish Status", "Publish status has been successfully updated!")
          handleResetEditDialog()
        },
        onError: () => {
          failureToast(
            "Publish status failed",
            "An error occurred while updation the publish status. Please try again."
          )
          handleResetEditDialog()
        },
      }
    )
  }

  const handleConfirmDelete = () => {
    deleteAnnouncementMutaion.mutate(
      { course_id: course?.id, announcement_id: selectedIDs?.announceMentID },
      {
        onSuccess: () => {
          successToast("Announcement Delete", "The Announcement has been successfully deleted!")
          handleClose()
          handleCancel()
        },
        onError: () => {
          deleteToast(
            "Announcement Deletion Failed",
            "An error occurred while deletion the announcement. Please try again."
          )
          handleCancel()
        },
      }
    )
  }

  const announcementProps = {
    form,
    control,
    onCreateOrUpdateAnnouncement: handleCreateOrUpdateAnnouncement,
    handleSubmit,
    setValue,
    onClose: handleCancelForm,
    isUpdate,
    userRole,
    isLoading,
  }

  return (
    <div className="">
      <div className="flex mb-4 flex-col justify-between sm:flex-row items-center gap-4 w-full">
        <CustomSearchbar
          inputSize="w-[19rem] sm:w-[30rem] md:w-[20rem] lg:w-[24rem] "
          placeholder="Search by announcement name..."
          searchedValue={searchingValue}
          setSearchedValue={(e) => setSearchingValue(e?.target.value)}
        />
        {[USER_ROLES.ADMIN, USER_ROLES.TRAINER]?.includes(userRole) && (
          <Button
            variant="primary"
            className="w-full sm:w-auto px-4 md:px-6"
            onClick={handleCreateAnnouncement}
          >
            Create Announcement
          </Button>
        )}
      </div>

      <ScrollArea ref={scrollAreaRef} className="h-[44.4rem]  md:h-[54rem] lg:h-[42rem] flex pr-3">
        {announcementLoading ? (
          <LoadingSpinner />
        ) : (
          <div>
            {announcements?.length > 0 ? (
              <AnnouncementUI
                onCardClick={handleClickAnnouncementCard}
                listOfAnnouncements={announcements}
                onDelete={handleOpenDeleteDialog}
                userRole={userRole}
                onEditStatus={handleEditStatus}
              />
            ) : (
              <div className="flex justify-center flex-col text-secondary items-center h-full">
                <TbBellRinging className="h-36 w-36 mb-3" />
                <p className="font-semibold text-lg">No announcements found</p>
              </div>
            )}
          </div>
        )}
      </ScrollArea>

      <CustomSidebar
        title={titleChangesByRole(userRole, isUpdate, "Create Announcement", "Update Announcement")}
        description=""
        isOpen={open}
        onClose={handleClose}
        content={<AnnouncementCreationUpdationUI {...announcementProps} />}
      />

      <DeleteDialog
        onOpen={isDelete}
        onDelete={handleConfirmDelete}
        onCancel={handleCancel}
        title="Delete Announcement"
        content="Are you sure want to delete this announcement?"
      />

      <GeneralDialog
        onOpen={isEditDialog}
        title="Update Status"
        onClickCTA={handleUpdateStatus}
        ctaLabel="Update Status"
        onCancel={handleResetEditDialog}
        ctaPosition="justify-end"
        content={
          <CustomSelect
            publishedStatus={publishedStatus}
            setPublishedStatus={handleSetPublishStatus}
            iteratedData={publishStatus}
          />
        }
      />
    </div>
  )
}
export default Announcements
