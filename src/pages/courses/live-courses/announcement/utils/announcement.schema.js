import { z } from "zod"

export const announcementSchema = z.object({
  title: z
    .string({ message: "Title is required" })
    .nonempty({ message: "Title  must contain at least 1 character(s)" }),
  content: z
    .string({ message: "Description is required" })
    .nonempty({ message: "Description must contain at least 1 character(s)" }),
})

export const announcementFormDefaultValues = {
  title: "",
  content: "",
}
