import FormComboBox from "@/components/custom/custom-forms/combo-box.form"
import AppointmentScheduler from "@/components/custom/custom-forms/date-time-picker"
import FormInput from "@/components/custom/custom-forms/form-input"
import LoadingSpinner from "@/components/custom/LoadingSpinner"
import { Button } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { ScrollArea } from "@/components/ui/scroll-area"
import { formsProps } from "@/utils/props-types"

const ManageBatchUI = ({
  form,
  onCreations,
  handleSubmit,
  onClose,
  control,
  isUpdate,
  activeTab,
  iteratedData,
  isLoading,
}) => {
  const getButtonLabel = () => (isUpdate ? "Update" : "Create")

  return (
    <ScrollArea className="h-[700px] w-full rounded-md pr-2">
      <Form {...form}>
        <form onSubmit={handleSubmit(onCreations)} className="space-y-5 p-2 lg:mb-8">
          {!isUpdate && (
            <FormComboBox
              dataTestID="test-id"
              dataTestIDError="error"
              fieldControlName="parent_course_id"
              control={control}
              label="Parent Course"
              placeholder="Select Parent Course"
              iterateData={iteratedData || []}
            />
          )}
          <FormInput
            dataTestID="test-id"
            dataTestIDError="error"
            fieldControlName="new_title"
            control={control}
            label="Batch Name"
            placeholder="Enter Batch"
          // isRequired
          />

          <AppointmentScheduler
            dataTestID="test-id"
            dataTestIDError="error"
            fieldControlName="from_course_date"
            control={control}
            label="Stat Date"
            placeholder="Pick a date"
            isRequired
          />

          <AppointmentScheduler
            dataTestID="test-id"
            dataTestIDError="error"
            fieldControlName="to_course_date"
            control={control}
            label="End Date"
            placeholder="Pick a date"
            isRequired
          />
          {activeTab && (
            <FormInput
              dataTestID="test-id"
              dataTestIDError="error"
              fieldControlName="name"
              control={control}
              label="Name"
              placeholder="Enter Name"
            // isRequired
            />
          )}

          <div className="p t-12 flex gap-x-3 justify-end">
            <Button onClick={onClose} variant="secondary" type="reset">
              Cancel
            </Button>
            <Button type="submit" variant="primary" disabled={!!isLoading}>
              {isLoading ? (
                <LoadingSpinner iconWidth={30} iconHeight={30} color="bg-slate-100" />
              ) : (
                getButtonLabel()
              )}
            </Button>
          </div>
        </form>
      </Form>
    </ScrollArea>
  )
}

ManageBatchUI.propTypes = formsProps

export default ManageBatchUI
