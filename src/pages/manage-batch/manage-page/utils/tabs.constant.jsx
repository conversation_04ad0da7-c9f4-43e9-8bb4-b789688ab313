import { Users, GraduationCap } from "lucide-react"
import TrainerMangeBatch from "../tabs/trainer"
import StudentMangeBatch from "../tabs/student"

const BATCH_TABS_TITLES = {
  TRAINERS: "Trainers",
  STUDENTS: "Students",
}

const BatchTabs = [
  {
    id: "trainers",
    label: BATCH_TABS_TITLES.TRAINERS,
    content: <TrainerMangeBatch />,
    icon: <Users size={18} className="mr-2" />,
  },
  {
    id: "students",
    label: BATCH_TABS_TITLES.STUDENTS,
    content: <StudentMangeBatch />,
    icon: <GraduationCap size={18} className="mr-2" />,
  },
]

export default BatchTabs
