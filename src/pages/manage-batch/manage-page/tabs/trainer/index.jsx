import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { useFilterHooks, usePaginationHooks } from "@/hooks/common.hooks"
import useTableConfig from "@/hooks/use-table.hooks"
import { useDebounce } from "@/hooks/useDebounce"
import { useAttachStudentandTrainer } from "@/services/query/course-content.query"
import { useFetchUsersByRole } from "@/services/query/user.query"
import { getOffset } from "@/utils/helper"
import ct from "@constants/"
import { flexRender } from "@tanstack/react-table"
import PropTypes from "prop-types"
import { useEffect, useState } from "react"
import { useSelector } from "react-redux"
import { trainerTableColumns } from "../../columns/trainer-columns"
import TrainerUI from "./trainer.ui"

const TrainerMangeBatch = ({ setIsRefetchCourse }) => {
  // Get active course
  const { selectedBatchCourseDetails: course } = useSelector((st) => st[ct.store.COURSES])
  const [selectedOption, setSelectedOption] = useState({ label: "Attached", value: "ATTACHED" })
  const [listOfTrainers, setListOfTrainers] = useState([])

  const course_id = course?.id
  const existingTrainersIDSet = new Set(course?.trainer_ids || [])

  const [searchingValue, setSearchingValue] = useState("")

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })

  const { limit } = usePaginationHooks()
  const attachMutate = useAttachStudentandTrainer()
  // custom hooks
  const debouncedSearchQuery = useDebounce(searchingValue, 500)
  const { sortBy } = useFilterHooks()

  // Create filters object with conditional user_ids
  const filters = {
    limit,
    offset: getOffset(pagination.pageIndex, pagination.pageSize),
    order: sortBy,
    for_exclude: true,
    user_ids: course.trainer_ids,
    search_query: debouncedSearchQuery?.length > 0 ? debouncedSearchQuery : null,
    ...(selectedOption?.value === "ATTACHED" && course?.trainer_ids?.length > 0 && {
      for_exclude: false,
    })
  }

  // Debug logs
  console.log("selectedOption?.value:", selectedOption?.value)

  console.log("filters being passed:", filters)

  const { data: listOfTrainersData, isLoading } = useFetchUsersByRole({
    filter_role: "TRAINER",
    filters,
  })
  console.log("course?.trainer_ids:", listOfTrainersData)
  useEffect(() => {
    if (!listOfTrainersData?.data) return

    const withStatus = listOfTrainersData.data.map((t) => ({
      ...t,
      status: existingTrainersIDSet.has(t?.id) ? "ATTACHED" : "NOT_ATTACHED",
    }))

    setListOfTrainers(withStatus)
  }, [listOfTrainersData?.data])

  console.log("__TrainersWithStatus", selectedOption?.value)
  const filteredTrainers = selectedOption
    ? listOfTrainers.filter((t) => t?.status === selectedOption?.value)
    : listOfTrainers

  const renderCellContent = (cell, row) => {
    const isAttached = row.original?.status === "ATTACHED"
    if (cell.column.id === "status")
      return (
        <Badge
          className={`px-2 py-1 rounded-full text-xs font-medium ${isAttached ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
            }`}
        >
          {isAttached ? "Attached" : "Not Attached"}
        </Badge>
      )
    if (cell.column.id === "select")
      return (
        <Checkbox
          id={`select-row-${row.id}`}
          checked={row?.getIsSelected() || false}
          onCheckedChange={(value) => row?.toggleSelected(!!value)}
          disabled={existingTrainersIDSet.has(row.original?.id)}
        />
      )
    return flexRender(cell.column.columnDef.cell, cell.getContext())
  }
  const { table, pageCount, found } = useTableConfig(
    filteredTrainers,
    trainerTableColumns,
    listOfTrainersData?.metadata?.total_records,
    setPagination,
    pagination
  )

  const onAttach = () => {
    const filteredIDs = table.getSelectedRowModel().rows?.map((t) => t.id)
    if (!filteredIDs.length > 0) {
      return failureToast("Error", "No trainer selected for attachment.")
    }

    const payload = {
      assign_for: "TRAINER",
      students_ids: [],
      trainer_ids: filteredIDs,
    }

    attachMutate.mutate(
      { course_id, payload },
      {
        onSuccess: () => {
          table.resetRowSelection()
          setIsRefetchCourse(true)
          successToast(
            "Trainer Attached",
            "The trainer has been successfully assigned to the course."
          )
        },

        onError: (e) => {
          console.log("__error", e)
          const errMsg = e?.response.data.error.message
          if (["duplicate key", "already exists"].some((word) => errMsg.includes(word))) {
            failureToast("Duplicate Values", "Already Exists")
            return
          }
          failureToast(
            "Trainer Attachment Failed",
            errMsg || "An error occurred while attaching the trainer. Please try again."
          )
        },
      }
    )
    return null
  }

  return (
    <TrainerUI
      renderCellContent={renderCellContent}
      onAttach={onAttach}
      paignation={pagination}
      setPagination={setPagination}
      setSelectedLeads
      table={table}
      pageCount={pageCount}
      isLoading={isLoading}
      found={found}
      setSearchingValue={setSearchingValue}
      searchingValue={searchingValue}
      setSelectedOption={setSelectedOption}
      selectedOption={selectedOption}
    />
  )
}

TrainerMangeBatch.propTypes = {
  setIsRefetchCourse: PropTypes.func,
}

export default TrainerMangeBatch