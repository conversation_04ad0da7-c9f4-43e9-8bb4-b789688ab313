import CustomSearchbar from "@/components/custom/custom-search"
import DataTable from "@/components/custom/cutsom-table"
import { ReusableDropdown } from "@/components/custome-drop-down/drop-down"
import { Button } from "@/components/ui/button"
import PropTypes from "prop-types"
import { trainerTableColumns } from "../../columns/trainer-columns"

const TrainerUI = ({
  onAttach,
  renderCellContent,
  found,
  pagination,
  searchingValue,
  setSearchingValue,
  table,
  pageCount,
  isLoading,
  setSelectedOption,
  selectedOption,
}) => {
  console.log("__onAttach", onAttach)

  const options = [
    { label: "Attached", value: "ATTACHED" },
    { label: "Not Attached", value: "NOT_ATTACHED" },
  ]
  return (
    <div>
      <div className="flex justify-between mb-3">
        <CustomSearchbar
          inputSize="w-[20rem]"
          placeholder="Search by title..."
          searchedValue={searchingValue}
          setSearchedValue={(e) => setSearchingValue(e?.target.value)}
        />
        <div className="flex gap-2">
          <ReusableDropdown
            options={options}
            selected={selectedOption}
            onSelect={(option) => setSelectedOption(option)}
            placeholder="Choose level"
          />
          <Button variant="primary" type="button" onClick={onAttach}>
            Attach Trainers
          </Button>
        </div>
      </div>
      <div className="w-full">
        <DataTable
          renderCellContent={renderCellContent}
          columns={trainerTableColumns}
          table={table}
          found={found}
          pageCount={pageCount}
          pagination={pagination}
          isCheckbox
          isLoading={isLoading}
          notFoundPlaceholder="No Trainers Found"
        />
      </div>
    </div>
  )
}

TrainerUI.propTypes = {
  onAttach: PropTypes.func,
  renderCellContent: PropTypes.func,
  found: PropTypes.number,
  pagination: PropTypes.string,
  table: PropTypes.func,
  pageCount: PropTypes.number.isRequired,
  searchingValue: PropTypes.string,
  setSearchingValue: PropTypes.func.isRequired,
  isLoading: PropTypes.bool,
}

export default TrainerUI
