/* eslint-disable no-case-declarations */
import { DateCell } from "@/components/custom/cutsom-table/table-cells"
import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { useFilterHooks, usePaginationHooks } from "@/hooks/common.hooks"
import useTableConfig from "@/hooks/use-table.hooks"
import { useDebounce } from "@/hooks/useDebounce"
import { useAttachStudentandTrainer } from "@/services/query/course-content.query"
import { useFetchBatchCoursesByRole } from "@/services/query/mange-batch.query"
import { USER_ROLES } from "@/utils/constants"
import { getOffset } from "@/utils/helper"
import ct from "@constants/"
import { flexRender } from "@tanstack/react-table"
import PropTypes from "prop-types"
import { useEffect, useState, useMemo } from "react"
import { useSelector } from "react-redux"
import { useFetchUsersByRole } from "@/services/query/user.query"
import { studentTableColumns } from "../../columns/student-columns"
import StidentManageBatchUI from "./student.ui"

const StudentMangeBatch = ({ setIsRefetchCourse }) => {
  // Get active course
  const { selectedBatchCourseDetails: course } = useSelector((st) => st[ct.store.COURSES])
  const { id: course_id, parent_course_id } = course

  // Memoize the Set to prevent infinite re-renders
  const existingStudentsIDSet = useMemo(
    () => new Set(course?.student_ids || []),
    [course?.student_ids]
  )

  const [searchingValue, setSearchingValue] = useState("")
  const { limit } = usePaginationHooks()
  const { sortBy, sortByField } = useFilterHooks()
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })
  const [selectedOption, setSelectedOption] = useState({ label: "Attached", value: "ATTACHED" })
  const [studentList, setStudentList] = useState([])
  const attachMutate = useAttachStudentandTrainer()
  const debouncedSearchQuery = useDebounce(searchingValue, 500)

  // Memoize filters to prevent unnecessary re-renders
  const filters = useMemo(
    () => ({
      limit,
      offset: getOffset(pagination.pageIndex, pagination.pageSize),
      order: sortBy,
      search_query: debouncedSearchQuery?.length > 0 ? debouncedSearchQuery : null,
      // Conditional filtering based on selected option
      ...(selectedOption?.value === "ATTACHED" &&
        course?.student_ids?.length > 0 && {
        user_ids: course.student_ids,
        for_exclude: false, // Include only these students
      }),
      ...(selectedOption?.value === "NOT_ATTACHED" &&
        course?.student_ids?.length > 0 && {
        user_ids: course.student_ids,
        for_exclude: true, // Exclude these students
      }),
    }),
    [
      limit,
      pagination.pageIndex,
      pagination.pageSize,
      sortBy,
      debouncedSearchQuery,
      selectedOption?.value,
      course?.student_ids,
    ]
  )

  const { data: listOfStudents, isLoading } = useFetchUsersByRole({
    filter_role: "STUDENT",
    filters,
  })

  console.log("batchCourseData", listOfStudents)

  useEffect(() => {
    if (!listOfStudents?.data) return

    const withStatus = listOfStudents.data.map((student) => ({
      ...student,
      // Fixed: Use 'id' instead of 'student_id' since that's what the API returns
      status: existingStudentsIDSet.has(student?.id) ? "ATTACHED" : "NOT_ATTACHED",
      student_id: student.id, // Add student_id for backward compatibility
    }))

    console.log("__withStatus", withStatus, existingStudentsIDSet)
    setStudentList(withStatus)
  }, [listOfStudents?.data, existingStudentsIDSet])

  // Remove this filtering since we're now filtering at API level
  // const filteredStudents = selectedOption
  //   ? studentList.filter((t) => t?.status === selectedOption?.value)
  //   : studentList

  // Memoize renderCellContent to prevent unnecessary re-renders
  const renderCellContent = useMemo(
    () => (cell) => {
      const { column, row, getContext } = cell
      const columnID = column.id
      switch (columnID) {
        case "select": {
          const isAttached = existingStudentsIDSet.has(row.original?.id)
          return (
            <Checkbox
              id={`select-row-${row.id}`}
              checked={row.getIsSelected() || false}
              onCheckedChange={(value) => row.toggleSelected(!!value)}
              disabled={isAttached}
            />
          )
        }
        case "created_at":
          return <DateCell value={row.original?.created_at} />
        case "status": {
          const isAttached = row.original?.status === "ATTACHED"
          return (
            <Badge
              className={`px-2 py-1 rounded-full text-xs font-medium ${isAttached ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}
            >
              {isAttached ? "Attached" : "Not Attached"}
            </Badge>
          )
        }
        default:
          return flexRender(column.columnDef.cell, getContext())
      }
    },
    [existingStudentsIDSet]
  )

  const { table, pageCount, found } = useTableConfig(
    studentList, // Use studentList directly since filtering is done at API level
    studentTableColumns,
    listOfStudents?.metadata?.total_records,
    setPagination,
    pagination
  )

  const onAttach = () => {
    // Fixed: Use row.original.id instead of student_id
    const filteredIDs = table.getSelectedRowModel().rows?.map((t) => t.original.id)

    if (!filteredIDs.length > 0) {
      return failureToast("Error", "No student selected for attachment.")
    }

    const payload = {
      assign_for: "STUDENT",
      students_ids: filteredIDs,
      trainer_ids: [],
    }

    attachMutate.mutate(
      { course_id, payload },
      {
        onSuccess: () => {
          setIsRefetchCourse(true)
          table.resetRowSelection()
          successToast("Student Attached", "Student has been attached successfully.")
        },
        onError: (e) => {
          console.log("__error", e)
          const errMsg = e?.response.data.error.message
          if (["duplicate key", "already exists"].some((word) => errMsg.includes(word))) {
            failureToast("Duplicate Values", "Already Exists")
            return
          }
          failureToast("Error", e?.response?.data?.message || "Failed to attached students")
        },
      }
    )
    return null
  }

  return (
    <div>
      <StidentManageBatchUI
        setSearchingValue={setSearchingValue}
        searchingValue={searchingValue}
        renderCellContent={renderCellContent}
        onAttach={onAttach}
        pagination={pagination} // Fixed typo: was 'paignation'
        setPagination={setPagination}
        table={table}
        pageCount={pageCount}
        isLoading={isLoading}
        found={found}
        setSelectedOption={setSelectedOption}
        selectedOption={selectedOption}
      />
    </div>
  )
}

StudentMangeBatch.propTypes = {
  setIsRefetchCourse: PropTypes.func,
}

export default StudentMangeBatch
