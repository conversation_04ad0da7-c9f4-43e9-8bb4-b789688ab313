import CustomSearchbar from "@/components/custom/custom-search"
import DataTable from "@/components/custom/cutsom-table"
import { ReusableDropdown } from "@/components/custome-drop-down/drop-down"
import { Button } from "@/components/ui/button"
import PropTypes from "prop-types"
import { studentTableColumns } from "../../columns/student-columns"

const StidentManageBatchUI = ({
  onAttach,
  renderCellContent,
  pagination,
  table,
  searchingValue,
  pageCount,
  setSearchingValue,
  isLoading,
  found,
  setSelectedOption,
  selectedOption,
}) => {
  const options = [
    { label: "Attached", value: "ATTACHED" },
    { label: "Not Attached", value: "NOT_ATTACHED" },
  ]
  console.log("StidentManageBatchUI", table);

  return (
    <div className="">
      <div className="flex justify-between mb-2">
        <CustomSearchbar
          inputSize="w-[20rem]"
          placeholder="Search by title..."
          searchedValue={searchingValue}
          setSearchedValue={(e) => setSearchingValue(e?.target.value)}
        />
        <div className="flex gap-2">
          <ReusableDropdown
            options={options}
            selected={selectedOption}
            onSelect={(option) => setSelectedOption(option)}
            placeholder="Choose level"
          />
          <Button variant="primary" onClick={onAttach}>
            Attach Student
          </Button>
        </div>
      </div>
      <div className="w-full">
        <DataTable
          renderCellContent={renderCellContent}
          columns={studentTableColumns}
          table={table}
          found={found}
          pageCount={pageCount}
          pagination={pagination}
          isCheckbox
          isLoading={isLoading}
          notFoundPlaceholder="No Students Found"
        />
      </div>
    </div>
  )
}

StidentManageBatchUI.propTypes = {
  onAttach: PropTypes.func,
  renderCellContent: PropTypes.func,
  found: PropTypes.number,
  pagination: PropTypes.string,
  table: PropTypes.func.isRequired,
  searchingValue: PropTypes.string,
  setSearchingValue: PropTypes.func.isRequired,
  isLoading: PropTypes.bool.isRequired,
  pageCount: PropTypes.number,
}

export default StidentManageBatchUI
