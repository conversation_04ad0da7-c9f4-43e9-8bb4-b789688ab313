import { Checkbox } from "@/components/ui/checkbox"

export const trainerTableColumns = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        id="select-all"
        checked={table?.getIsAllRowsSelected() || false}
        onCheckedChange={(value) => table?.toggleAllRowsSelected(!!value)}
      />
    ),
    enableSorting: false,
    enableColumnFilter: false,
  },
  {
    id: "full_name",
    accessorKey: "full_name",
    header: "Trainer Name",
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Status",
  },
  {
    id: "email",
    accessorKey: "email",
    header: "Email",
  },
]
