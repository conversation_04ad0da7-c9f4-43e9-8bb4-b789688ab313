import { Checkbox } from "@/components/ui/checkbox"

export const studentTableColumns = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        id="select-all"
        checked={table?.getIsAllRowsSelected() || false}
        onCheckedChange={(value) => table?.toggleAllRowsSelected(!!value)}
      />
    ),
    enableSorting: false,
    enableColumnFilter: false,
  },
  {
    id: "student_name",
    accessorKey: "full_name",
    header: "Student Name",
  },
  {
    id: "student_email",
    accessorKey: "email",
    header: "Email",
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Status",
  },
]
