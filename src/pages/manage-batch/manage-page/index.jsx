import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger, TabsContent } from "@/components/ui/tabs"
import { Card } from "@/components/ui/card"
import ct from "@constants/"
import { ArrowLeft, GraduationCap, Users } from "lucide-react"
import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useFetchBatchCourse } from "@/services/query/live-course.query"
import { setBatchCourseDetails } from "@/services/store/slices/courses.slice"
import StudentMangeBatch from "./tabs/student"
import TrainerMangeBatch from "./tabs/trainer"

const BATCH_TABS_TITLES = {
  TRAINERS: "Trainers",
  STUDENTS: "Students",
}

function ManageCourseBatchAssigneePage() {
  const { selectedBatchCourseDetails: course } = useSelector((st) => st[ct.store.COURSES])
  const [activeTab, setActiveTab] = useState("trainer")

  // Refetch specific batch course on data change
  const [isFetchBatchCourse, setIsFetchBatchCourse] = useState(false)
  const { data: batchCourseData, isLoading: isBatchCourseLoading } = useFetchBatchCourse(
    {
      course_ids: [String(course?.id)],
      for_subscribed: true,
    },
    isFetchBatchCourse
  )

  const dispatch = useDispatch()
  useEffect(() => {
    if (isBatchCourseLoading) return

    const batchCourse = batchCourseData?.get_course?.courses?.at(0)
    if (isFetchBatchCourse && batchCourse) {
      dispatch(setBatchCourseDetails(batchCourse))
      setIsFetchBatchCourse(false)
    }
  }, [batchCourseData, isFetchBatchCourse])

  useEffect(() => {
    setIsFetchBatchCourse(true)
  }, [batchCourseData])

  const BatchTabs = [
    {
      id: "trainer",
      label: BATCH_TABS_TITLES.TRAINERS,
      content: <TrainerMangeBatch setIsRefetchCourse={setIsFetchBatchCourse} />,
      icon: <Users size={18} className="mr-2" />,
    },
    {
      id: "student",
      label: BATCH_TABS_TITLES.STUDENTS,
      content: <StudentMangeBatch setIsRefetchCourse={setIsFetchBatchCourse} />,
      icon: <GraduationCap size={18} className="mr-2" />,
    },
  ]

  return (
    <Card className="p-6 bg-white rounded-2xl shadow-md">
      <div className="flex flex-col gap-6">
        {/* Header with back button and batch name */}
        <div className="flex items-center  ">
          <ArrowLeft
            onClick={() => window.history.back()}
            size={18}
            className="mb-3  cursor-pointer text-primary"
          />

          <h2 className="text-lg ms-3   ">
            Batch: <span className="text-primary font-semibold ">{course?.title}</span>
          </h2>
        </div>

        {/* Tab navigation and content */}
        <div className="w-full">
          <Tabs
            value={activeTab}
            defaultValue="trainer"
            className="w-full"
            onValueChange={(val) => setActiveTab(val)}
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-medium text-gray-800">Manage Batch</h3>

              <TabsList className="bg-white p-1 rounded-md border border-gray-200">
                {BatchTabs.map((tab) => (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    className="flex items-center px-4 py-2 gap-2 rounded-md data-[state=active]:bg-blue-50 data-[state=active]:shadow-sm"
                  >
                    {tab.icon}
                    {tab.label}
                  </TabsTrigger>
                ))}
              </TabsList>
            </div>

            {BatchTabs.map((tab) => (
              <TabsContent key={tab.id} value={tab.id} className="mt-0">
                <div className="rounded-lg bg-white shadow-sm">{tab.content}</div>
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </div>
    </Card>
  )
}

export default ManageCourseBatchAssigneePage
