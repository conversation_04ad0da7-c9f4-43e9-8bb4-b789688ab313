import CustomSearchbar from "@/components/custom/custom-search"
import { CustomSelect } from "@/components/custom/custom-select"
import DataTable from "@/components/custom/cutsom-table"
import { ActionCell, DateCell, RenderTableData } from "@/components/custom/cutsom-table/table-cells"
import { DeleteDialog } from "@/components/custom/dialogs/delete.dialog"
import { GeneralDialog } from "@/components/custom/dialogs/general.dialog"
import { deleteToast, failureToast, successToast } from "@/components/custom/toasts/tosters"
import { ReusableDropdown } from "@/components/custome-drop-down/drop-down"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import useDebounce, {
  useDeleteDialogHooks,
  useFilterHooks,
  useIsEditDialogHook,
  useIsUpdateHook,
  useOpenCloseHooks,
  usePaginationHooks,
  usePublishStatus,
  useSelectedIds,
} from "@/hooks/common.hooks"
import useTableConfig from "@/hooks/use-table.hooks"
import { useUpdateCourse } from "@/services/query/create-course-form.query"
import { useFetchBatchCourse, useFetchCourseMinInfo } from "@/services/query/live-course.query"
import {
  useCreateCourseBatch,
  useDeletCourseBatch,
  useUpdateCourseBatch,
  useUpdateStatus,
} from "@/services/query/mange-batch.query"
import {
  setActiveCourseDetails,
  setActiveTab,
  setBatchCourseDetails,
} from "@/services/store/slices/courses.slice"
import { publishStatus } from "@/utils/constants"
import { getOffset } from "@/utils/helper"
import ct from "@constants/"
import { zodResolver } from "@hookform/resolvers/zod"
import { flexRender } from "@tanstack/react-table"
import { ArrowLeft, Plus } from "lucide-react"
import moment from "moment"
import { useEffect, useRef, useState } from "react"
import { useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { useNavigate } from "react-router-dom"
import CustomSidebar from "../courses/components/custom-sidebar"
import batchData from "./batch.json"
import { columns } from "./columns"
import ManageBatchUI from "./components/manage-batch.ui"
import { defaultMangeCourseValues, manageCourseSchema } from "./utils/form.schema"
import { filterParentCourse } from "./utils/helper"

const ManageCourseBatchPage = () => {
  const user = useSelector((st) => st[ct.store.USER_STORE])
  const dispatch = useDispatch()

  const [searchingValue, setSearchingValue] = useState("")
  const { open, handleClose, handleOpen } = useOpenCloseHooks()
  const { sortBy } = useFilterHooks()
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })
  const [selectedOption, setSelectedOption] = useState({ label: "published", value: "PUBLISHED" })
  const options = [
    { label: "published", value: "PUBLISHED" },
    { label: "archived", value: "ARCHIVED" },
  ]

  const { selectedIDs, setSelectedIDs } = useSelectedIds()

  const { publishedStatus, handleSetPublishStatus } = usePublishStatus()
  const { isEditDialog, handleOpenEditDialog, handleResetEditDialog } = useIsEditDialogHook()

  const handleOpenProjectStatusDialog = (data) => {
    const { id, publish_status } = data?.original || {}
    handleSetPublishStatus(publish_status)
    setSelectedIDs({
      ...selectedIDs,
      courseID: id,
    })

    handleOpenEditDialog()
  }

  const debouncedSearchQuery = useDebounce(searchingValue, 500)
  const { limit } = usePaginationHooks()
  // Fetch Batch Courses
  const { data: listOfBatchData, isLoading } = useFetchBatchCourse({
    course_type: "LIVE",
    limit,
    is_nano: true,
    offset: getOffset(pagination.pageIndex, pagination.pageSize),
    order: sortBy,
    search_query: debouncedSearchQuery?.length > 0 ? debouncedSearchQuery : null,
    for_subscribed: true,
    publish_status: selectedOption?.value,
  })
  const { data: coursesData } = useFetchCourseMinInfo({
    userID: user.id,
    courseType: "LIVE",
    for_subscribed: false,
    is_nano: true,
    publish_status: "PUBLISHED",
  })
  const { mutate: batchUpdate } = useUpdateCourse()
  const { mutate: onCreateBatchMuation, isPending: isCreationPending } = useCreateCourseBatch()
  const { mutate: onUpdateBatchMuation, isPending: isUpdationPending } = useUpdateCourseBatch()
  const { mutate: OnUpdateStatus } = useUpdateStatus()
  const onDeleteBatchMuation = useDeletCourseBatch()
  const [editBatchId, setEditBatchId] = useState(null)

  const { isDelete, handleCancel } = useDeleteDialogHooks()
  const { isUpdate, handleUpdate, handleResetUpdate } = useIsUpdateHook()

  const form = useForm({
    resolver: zodResolver(manageCourseSchema),
    defaultValues: defaultMangeCourseValues,
    mode: "onChange",
  })

  const { control, handleSubmit, reset } = form

  const navigate = useNavigate()
  const { table, found, pageCount } = useTableConfig(
    listOfBatchData?.get_course?.courses || [],
    columns,
    listOfBatchData?.get_course?.total_records,
    setPagination,
    pagination
  )

  const pageRef = useRef(null) // Ref to measure the page height
  const [shouldApplyHeight, setShouldApplyHeight] = useState(false)

  useEffect(() => {
    if (pageRef.current) {
      const pageHeight = pageRef.current.clientHeight // Measure the page height
      const viewportHeight = window.innerHeight // Get the viewport height

      // If the page height exceeds the viewport height, apply the height prop
      if (pageHeight > viewportHeight) {
        setShouldApplyHeight(true)
      } else {
        setShouldApplyHeight(false)
      }
    }
  }, [listOfBatchData]) // Re-run when table data changes

  const handleUpdateStatus = () => {
    OnUpdateStatus(
      {
        course_id: selectedIDs?.courseID,
        data: publishedStatus,
      },
      {
        onSuccess: () => {
          handleResetEditDialog()
          successToast("Batch Status Updated", "The Batch status has been successfully Updated!")
        },
        onError: () => {
          handleResetEditDialog()
          failureToast(
            "Batch Status Updation Failed",
            "An error occurred while update the Batch status. Please try again."
          )
        },
      }
    )
  }

  if (!batchData.response || batchData.response.length === 0) {
    return (
      <Card className="p-8 bg-white rounded-2xl">
        <div className="flex justify-center items-center h-40">
          <p>No data available</p>
        </div>
      </Card>
    )
  }

  const openManageBatchSidebar = () => {
    handleOpen()
    handleResetUpdate()
  }

  const onSubmitManageBatchForm = (data) => {
    console.log(data, "create batch")
    const formData = new FormData()
    const mutation = isUpdate ? onUpdateBatchMuation : onCreateBatchMuation
    const successMessage = isUpdate ? "Batch Updated" : "Batch Created"
    const errorMessage = isUpdate ? "Batch Updation Failed" : "Batch Creation Failed"

    formData.append(
      "data",
      JSON.stringify({
        for_batch_course: true,
        parent_data: {
          ...data,
        },
      })
    )

    if (isUpdate) {
      const EditformData = new FormData()
      EditformData.append(
        "data",
        JSON.stringify({
          course_type: "BATCH",
          batch_course_data: {
            title: data.new_title,
            from_course_date: data.from_course_date,
            to_course_date: data.to_course_date,
          },
        })
      )

      batchUpdate(
        { formData: EditformData, course_id: editBatchId },
        {
          onSuccess: () => {
            successToast(successMessage, `The Batch has been successfully updated!`)
            reset(null)
            handleClose()
            handleCancel()
          },
          onError: () => {
            failureToast(
              errorMessage,
              `An error occurred while updating the batch. Please try again.`
            )
            reset(null)
            handleClose()
            handleCancel()
          },
        }
      )
    } else {
      mutation(
        { data: formData },
        {
          onSuccess: () => {
            successToast(
              successMessage,
              `The Batch has been successfully ${isUpdate ? "updated" : "created"}!`
            )
            reset(null)
            handleClose()
            handleCancel()
          },
          onError: () => {
            failureToast(
              errorMessage,
              `An error occurred while ${isUpdate ? "updating" : "creating"} the batch. Please try again.`
            )
            reset(null)
            handleClose()
            handleCancel()
          },
        }
      )
    }
  }

  const handleEdit = (row) => {
    const { title, description, start_date, end_date, total_marks, id, ...rest } =
      row?.original ?? {}
    setEditBatchId(id)
    console.log(row?.original, "isUpdate")
    // setProjectId({ ...projectId, project_id: id })
    // handleUpdate()
    handleUpdate()
    reset({
      new_title: title?.toString(),
      description: description?.toString(),
      total_marks: total_marks?.toString(),
      start_date: start_date?.toString(),
      end_date: end_date?.toString(),
      ...rest,
    })
    handleOpen()
  }

  const onCloseMangeBatchForm = () => {
    reset()
    handleClose()
  }

  const renderCellContent = (cell, row) => {
    const {
      id,
      title,
      total_trainers,
      total_students,
      modified_at,
      publish_status,
      from_course_date,
      to_course_date,
    } = row?.original || {}
    switch (cell.column.id) {
      case "total_trainers":
        return <RenderTableData content={total_trainers} />
      case "total_students":
        return <RenderTableData content={total_students} />
      case "title":
        return <RenderTableData content={title} />
      case "end_date":
        return (
          <p>
            {to_course_date
              ? moment.parseZone(to_course_date).local().format("MMM D, YYYY • hh:mm A")
              : "-"}
          </p>
        )
      case "start_date":
        return (
          <p>
            {from_course_date
              ? moment.parseZone(from_course_date).local().format("MMM D, YYYY • hh:mm A")
              : "-"}
          </p>
        )
      case "modified_at":
        return <DateCell value={modified_at} />
      // case "status":
      //   return <StatusUpdationCell value={publish_status} />
      case "actions":
        return (
          <ActionCell
            row={row}
            label1="Update Status"
            label2="Edit"
            label3="Delete"
            isEdit
            onEdit={handleEdit}
            isView
            onView={handleOpenProjectStatusDialog}
          />
        )

      default:
        return flexRender(cell.column.columnDef.cell, cell.getContext())
    }
  }

  const handleConfirmDelete = () => {
    onDeleteBatchMuation.mutate(
      { course_id: 2 },
      {
        onSuccess: () => {
          successToast("Batch Delete", "The Batch has been successfully deleted!")
          handleClose()
          handleCancel()
        },
        onError: () => {
          deleteToast(
            "Batch Deletion Failed",
            "An error occurred while deletion the batch. Please try again."
          )
          handleCancel()
        },
      }
    )
  }

  const handleRowClick = (row) => {
    console.log(row?.original, "trtrtr")
    dispatch(setBatchCourseDetails(row?.original))
    dispatch(setActiveCourseDetails(row?.original))
    dispatch(setActiveTab("batch"))
    // navigate(`/live-courses/${row?.original?.id}${ct.route.COURSE_OVERVIEW}`)
    navigate(`/${ct.route.BATCH_COURSE}/${row?.original?.id}${ct.route.COURSE_OVERVIEW}`)
  }

  return (
    <Card className="px-8 py-6 bg-white rounded-2xl" ref={pageRef}>
      <div className="text-primary text-xl flex gap-3 items-center">
        <Button
          className="flex items-center gap-x-1 font-semibold group"
          onClick={() => navigate(-1)}
        >
          <ArrowLeft
            size={18}
            className="transform font-semibold transition-transform group-hover:-translate-x-1"
          />
        </Button>
        <h6 className="mb-0 text-primary">Manage Course Batch</h6>
      </div>
      <div className="flex justify-between items-center 2">
        <CustomSearchbar
          inputSize="w-[20rem]"
          placeholder="Search by title..."
          searchedValue={searchingValue}
          setSearchedValue={(e) => setSearchingValue(e?.target.value)}
        />
        <div className="flex gap-2">
          <ReusableDropdown
            options={options}
            selected={selectedOption}
            onSelect={(option) => setSelectedOption(option)}
            placeholder="Choose level"
          />
          <Button variant="primary" className="gap-1" onClick={openManageBatchSidebar}>
            <Plus size={15} /> Create Batch
          </Button>
        </div>
      </div>
      <DataTable
        pageName="Batches"
        renderCellContent={renderCellContent}
        columns={columns}
        table={table}
        found={found}
        height={shouldApplyHeight ? "h-[63vh]" : undefined} // Conditionally pass the height prop
        pageCount={pageCount}
        isLoading={isLoading}
        pagination={pagination}
        onRowClick={handleRowClick}
        notFoundPlaceholder="No Course Batch Found"
      />
      <CustomSidebar
        title={isUpdate ? "Edit Batch" : "Create Batch"}
        description=""
        isOpen={open}
        content={
          <ManageBatchUI
            form={form}
            onCreations={onSubmitManageBatchForm}
            handleSubmit={handleSubmit}
            onClose={onCloseMangeBatchForm}
            control={control}
            isUpdate={isUpdate}
            iteratedData={filterParentCourse(coursesData?.get_course?.courses)}
            isLoading={isUpdate ? isUpdationPending : isCreationPending}
          />
        }
      />

      <GeneralDialog
        onOpen={isEditDialog}
        title="Update Status"
        onClickCTA={handleUpdateStatus}
        ctaLabel="Update Status"
        onCancel={handleResetEditDialog}
        ctaPosition="justify-end"
        content={
          <CustomSelect
            publishedStatus={publishedStatus}
            setPublishedStatus={handleSetPublishStatus}
            iteratedData={publishStatus.filter((data) => data !== "UNLISTED" && data !== "DRAFT")}
          />
        }
      />

      <DeleteDialog
        onOpen={isDelete}
        onDelete={handleConfirmDelete}
        onCancel={handleCancel}
        title="Delete Batch"
        content="Are you sure want to delete this batch?"
      />
    </Card>
  )
}

export default ManageCourseBatchPage
