import moment from "moment"
import { z } from "zod"

const parent_msg = "Parent Course is required"
const new_title_msg = "Batch name is required"

export const manageCourseSchema = z
  .object({
    parent_course_id: z.union([
      z.string().nonempty({ message: parent_msg }),
      z.number().positive({ message: parent_msg }).or(z.nan()),
    ]),

    new_title: z.string().nonempty({ message: new_title_msg }),
    from_course_date: z
      .preprocess(
        (val) => (typeof val === "string" ? new Date(val) : val),
        z.date({ required_error: "Start date is Required" })
      )
      .refine((val) => moment(val).isSameOrAfter(moment(), "day"), {
        message: "Start date cannot be in the past",
      }),
    to_course_date: z
      .preprocess(
        (val) => (typeof val === "string" ? new Date(val) : val),
        z.date({ required_error: "End date is required" })
      )
      .refine((val) => moment(val).isSameOrAfter(moment(), "day"), {
        message: "End date cannot be in the past",
      }),
  })
  .superRefine((data, ctx) => {
    const startDate = data.from_course_date
    const endDate = data.to_course_date

    // Ensure we're working with Date objects for comparison
    // eslint-disable-next-line sonarjs/no-collapsible-if
    if (startDate instanceof Date && endDate instanceof Date) {
      if (endDate < startDate) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "End date cannot be before start date",
          path: ["end_date"],
        })
      }
    }
  })
  .transform((data) => ({
    ...data,
    // Transform dates to strings after validation
    start_date: moment(data.start_date).format("YYYY-MM-DD"),
    end_date: moment(data.end_date).format("YYYY-MM-DD"),
  }))

export const defaultMangeCourseValues = {
  parent_course_id: "",
  new_title: "",
  start_date: "",
  end_date: "",
}
