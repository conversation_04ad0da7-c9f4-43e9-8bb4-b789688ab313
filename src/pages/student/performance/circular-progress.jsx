import React from "react"

const CircularProgress = ({
    completed,
    total,
    size = 50,
    strokeWidth = 4,
    className = "",
    showLabel = true,
    colorScheme = "default",
}) => {
    const radius = (size - strokeWidth) / 2
    const circumference = radius * 2 * Math.PI
    const percentage = total > 0 ? (completed / total) * 100 : 0
    const strokeDasharray = `${(percentage * circumference) / 100} ${circumference}`

    // Color schemes for different metrics
    const colorSchemes = {
        default: {
            completed: "#10b981", // emerald-500
            background: "#e5e7eb", // gray-200
            text: "#374151", // gray-700
        },
        attendance: {
            completed: "#3b82f6", // blue-500
            background: "#dbeafe", // blue-100
            text: "#1e40af", // blue-800
        },
        quiz: {
            completed: "#8b5cf6", // violet-500
            background: "#ede9fe", // violet-100
            text: "#5b21b6", // violet-800
        },
        assignment: {
            completed: "#f59e0b", // amber-500
            background: "#fef3c7", // amber-100
            text: "#92400e", // amber-800
        },
        project: {
            completed: "#ef4444", // red-500
            background: "#fee2e2", // red-100
            text: "#b91c1c", // red-800
        },
        interview: {
            completed: "#06b6d4", // cyan-500
            background: "#cffafe", // cyan-100
            text: "#0e7490", // cyan-800
        },
    }

    const colors = colorSchemes[colorScheme] || colorSchemes.default

    return (
        <div className={`flex flex-col items-center justify-center ${className}`}>
            <div className="relative" style={{ width: size, height: size }}>
                <svg className="transform -rotate-90" width={size} height={size}>
                    {/* Background circle */}
                    <circle
                        cx={size / 2}
                        cy={size / 2}
                        r={radius}
                        stroke={colors.background}
                        strokeWidth={strokeWidth}
                        fill="transparent"
                    />
                    {/* Progress circle */}
                    <circle
                        cx={size / 2}
                        cy={size / 2}
                        r={radius}
                        stroke={colors.completed}
                        strokeWidth={strokeWidth}
                        fill="transparent"
                        strokeDasharray={strokeDasharray}
                        strokeDashoffset="0"
                        strokeLinecap="round"
                        className="transition-all duration-300 ease-in-out"
                    />
                </svg>

                {/* Center text */}
                {showLabel && (
                    <div className="absolute inset-0 flex items-center justify-center">
                        <span
                            className="text-xs font-semibold"
                            style={{ color: colors.text, fontSize: size > 40 ? "10px" : "8px" }}
                        >
                            {Math.round(percentage)}%
                        </span>
                    </div>
                )}
            </div>

            {/* Bottom label */}
            {showLabel && (
                <div className="text-xs text-gray-600 mt-1 text-center">
                    {completed}/{total}
                </div>
            )}
        </div>
    )
}

export default CircularProgress
