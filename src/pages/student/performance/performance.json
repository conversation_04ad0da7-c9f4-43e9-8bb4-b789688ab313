{"data": {"statistics": [{"student_name": "<PERSON>", "statistics": [{"type": "Quiz", "total": 10, "submitted": 3}, {"type": "Assignment", "total": 30, "submitted": 20}, {"type": "Project", "total": 40, "submitted": 20}, {"type": "Attendance", "total": 50, "submitted": 30}, {"type": "Interview", "total": 10, "submitted": 5}]}, {"student_name": "<PERSON>", "statistics": [{"type": "Quiz", "total": 20, "submitted": 13}, {"type": "Assignment", "total": 35, "submitted": 21}, {"type": "Project", "total": 43, "submitted": 22}, {"type": "Attendance", "total": 50, "submitted": 32}, {"type": "Interview", "total": 6, "submitted": 1}]}, {"student_name": "Zoyal", "statistics": [{"type": "Quiz", "total": 20, "submitted": 10}, {"type": "Assignment", "total": 9, "submitted": 4}, {"type": "Project", "total": 10, "submitted": 6}, {"type": "Attendance", "total": 32, "submitted": 15}, {"type": "Interview", "total": 5, "submitted": 2}]}]}, "metadata": {"request_id": "4336132b-7917-4986-9dd5-010eaeb373a6", "timestamp": "2025-03-27T16:04:09.032232", "message": "OK", "found": 3}}