import { Card } from "@/components/ui/card"
import DataTable from "@/components/custom/cutsom-table"
import useTableConfig from "@/hooks/use-table.hooks"
import { flexRender } from "@tanstack/react-table"

import { But<PERSON> } from "@/components/ui/button"

import { useState, useMemo, useEffect } from "react"

import { Label } from "@/components/ui/label"
import { useNavigate } from "react-router-dom"
import ct from "@constants/"
import { useSelector } from "react-redux"
import { USER_ROLES } from "@/utils/constants"

import CustomComboBox from "@/components/custom/custom-combo-box/custom-combo-box"
import { useFetchBatchCourseID } from "@/services/query/course-material.query"
import { useFetchStudentPerformance } from "@/services/query/course-video.query"

const StudentPerformancePage = () => {
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })
  const { data: batchCourseIdData, isLoading: batchCourseIdLoading } = useFetchBatchCourseID()
  const [selectedCourse, setSelectedCourse] = useState("")

  const { data: performanceResponse, isLoading } = useFetchStudentPerformance(selectedCourse?.id)

  const [studentPerformance, setStudentPerformance] = useState([])
  const [courseName, setCourseName] = useState([])

  const navigate = useNavigate()
  const userRole = useSelector((st) => st[ct.store.USER_STORE]?.userRole)

  useEffect(() => {
    if (!performanceResponse) return

    if (Array.isArray(performanceResponse)) {
      const processedData = performanceResponse.map((item) => item.data).filter(Boolean)
      setStudentPerformance(processedData)
    } else if (performanceResponse.data) {
      setStudentPerformance([performanceResponse.data])
    }
  }, [performanceResponse, isLoading])

  useEffect(() => {
    if (batchCourseIdData?.data?.data) {
      setCourseName(batchCourseIdData?.data?.data)
    }
  }, [batchCourseIdData, batchCourseIdLoading])

  const formattedPerformanceData = useMemo(() => {
    if (!studentPerformance || studentPerformance.length === 0) {
      return []
    }

    return studentPerformance.flatMap((courseData) => {
      const totalCounts = courseData?.data?.resourse_counts || {}
      return courseData?.data?.statistics?.map((student, index) => ({
        no: index + 1,
        studentId: student.student_id,
        studentName: student.student_name,
        classAttendance: `${student.attendance || 0}/${totalCounts.total_attendance || 0}`,
        quiz: `${student.quizzes_submitted || 0}/${totalCounts.total_quizzes || 0}`,
        assignment: `${student.assignments_submitted || 0}/${totalCounts.total_assignments || 0}`,
        project: `${student.projects_submitted || 0}/${totalCounts.total_projects || 0}`,
        interviewCompletion: `${student.completed_interview_questions || 0}/${totalCounts.total_interview_questions || 0}`,
        marketing: "N/A",
      }))
    })
  }, [studentPerformance])

  const columns = [
    {
      id: "no",
      accessorKey: "no",
      header: "No",
    },
    {
      id: "studentName",
      accessorKey: "studentName",
      header: "Student Name",
    },
    {
      id: "classAttendance",
      accessorKey: "classAttendance",
      header: "Class Attendance",
    },
    {
      id: "quiz",
      accessorKey: "quiz",
      header: "Quiz",
    },
    {
      id: "assignment",
      accessorKey: "assignment",
      header: "Assignment",
    },
    {
      id: "project",
      accessorKey: "project",
      header: "Project",
    },
    {
      id: "interviewCompletion",
      accessorKey: "interviewCompletion",
      header: "Interview completion",
    },
    {
      id: "marketing",
      accessorKey: "marketing",
      header: "Marketing Effort",
      cell: ({ row }) => (
        <Button
          variant="primary"
          size="sm"
          onClick={(e) => {
            e.stopPropagation()
            navigate("/student-dashboard")
          }}
        >
          View Marketing
        </Button>
      ),
    },
  ]

  const filteredColumns = columns.filter((col) => col.id !== "marketing")

  const renderCellContent = (cell) => {
    if (!cell) return null
    return flexRender(cell.column.columnDef.cell, cell.getContext())
  }

  const getColumns = () => {
    if (userRole === USER_ROLES.TRAINER) {
      return filteredColumns
    }
    return columns
  }

  const { table, found, pageCount } = useTableConfig(
    formattedPerformanceData || [],
    getColumns(),
    formattedPerformanceData?.length,
    setPagination,
    pagination
  )

  console.log("Performance Response:", performanceResponse)
  console.log("Processed Student Performance:", studentPerformance)
  console.log("Formatted Table Data:", formattedPerformanceData)

  const courseOptions = courseName
    .map((course) => ({
      id: course.id,
      name: course.title,
    }))
    .filter((m) => m.id && m.name)

  return (
    <Card className="p-8 bg-white rounded-2xl">
      <div className="flex justify-between items-center gap-9">
        <h1 className="text-2xl font-medium flex">Student Performance</h1>
        <div className="flex items-center gap-3">
          <Label htmlFor="course-select" className="text-right font-medium">
            Select Course:
          </Label>
          <CustomComboBox
            iterateData={courseOptions}
            selectedValue={selectedCourse?.name}
            setSelectedValue={(course) => setSelectedCourse(course)}
            notFoundMessage="modules"
            placeholder="Select module..."
            width="min-w-[285px]"
            loading={batchCourseIdLoading}
          />
        </div>
      </div>
      <div className="w-full py-5">
        <DataTable
          renderCellContent={renderCellContent}
          columns={getColumns()}
          table={table}
          found={found}
          pageCount={pageCount}
          pagination={pagination}
          notFoundPlaceholder="No Data Found"
        />
      </div>
    </Card>
  )
}

export default StudentPerformancePage
