import { Card } from "@/components/ui/card"
import DataTable from "@/components/custom/cutsom-table"
import useTableConfig from "@/hooks/use-table.hooks"
import { flexRender } from "@tanstack/react-table"

import { But<PERSON> } from "@/components/ui/button"

import { useState, useMemo, useEffect } from "react"

import { Label } from "@/components/ui/label"
import { useNavigate } from "react-router-dom"
import ct from "@constants/"
import { useSelector } from "react-redux"
import { USER_ROLES } from "@/utils/constants"

import CustomComboBox from "@/components/custom/custom-combo-box/custom-combo-box"
import { useFetchBatchCourseID } from "@/services/query/course-material.query"
import { useFetchStudentPerformance } from "@/services/query/course-video.query"
import CircularProgress from "./circular-progress"

const StudentPerformancePage = () => {
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })
  const { data: batchCourseIdData, isLoading: batchCourseIdLoading } = useFetchBatchCourseID()
  const [selectedCourse, setSelectedCourse] = useState("")

  const { data: performanceResponse, isLoading } = useFetchStudentPerformance(selectedCourse?.id)

  const [studentPerformance, setStudentPerformance] = useState([])
  const [courseName, setCourseName] = useState([])

  const navigate = useNavigate()
  const userRole = useSelector((st) => st[ct.store.USER_STORE]?.userRole)

  useEffect(() => {
    if (!performanceResponse) return

    if (Array.isArray(performanceResponse)) {
      const processedData = performanceResponse.map((item) => item.data).filter(Boolean)
      setStudentPerformance(processedData)
    } else if (performanceResponse.data) {
      setStudentPerformance([performanceResponse.data])
    }
  }, [performanceResponse, isLoading])

  useEffect(() => {
    if (batchCourseIdData?.data?.data) {
      setCourseName(batchCourseIdData?.data?.data)
    }
  }, [batchCourseIdData, batchCourseIdLoading])

  // Reset pagination when course changes
  useEffect(() => {
    setPagination((prev) => ({
      ...prev,
      pageIndex: 0,
    }))
  }, [selectedCourse])

  const formattedPerformanceData = useMemo(() => {
    if (!studentPerformance || studentPerformance.length === 0) {
      return []
    }

    return studentPerformance.flatMap((courseData) => {
      const totalCounts = courseData?.data?.resourse_counts || {}
      return courseData?.data?.statistics?.map((student, index) => ({
        no: index + 1,
        studentId: student.student_id,
        studentName: student.student_name || "Unknown",
        attendanceCompleted: student.attendance || 0,
        attendanceTotal: totalCounts.total_attendance || 0,
        quizCompleted: student.quizzes_submitted || 0,
        quizTotal: totalCounts.total_quizzes || 0,
        assignmentCompleted: student.assignments_submitted || 0,
        assignmentTotal: totalCounts.total_assignments || 0,
        projectCompleted: student.projects_submitted || 0,
        projectTotal: totalCounts.total_projects || 0,
        interviewCompleted: student.completed_interview_questions || 0,
        interviewTotal: totalCounts.total_interview_questions || 0,
        marketing: "N/A",
      }))
    })
  }, [studentPerformance])

  // Calculate paginated data
  const paginatedData = useMemo(() => {
    const startIndex = pagination.pageIndex * pagination.pageSize
    const endIndex = startIndex + pagination.pageSize
    return formattedPerformanceData.slice(startIndex, endIndex)
  }, [formattedPerformanceData, pagination])

  // Calculate total count for pagination
  const totalCount = formattedPerformanceData.length
  const pageCount = Math.ceil(totalCount / pagination.pageSize)

  const columns = [
    {
      id: "no",
      accessorKey: "no",
      header: "No",
      cell: ({ row }) => {
        const globalIndex = pagination.pageIndex * pagination.pageSize + row.index + 1
        return <div className="text-start ms-3 font-medium">{globalIndex}</div>
      },
    },
    {
      id: "studentName",
      accessorKey: "studentName",
      header: "Student Name",
      cell: ({ row }) => <div>{row.original.studentName}</div>,
    },
    {
      id: "classAttendance",
      accessorKey: "attendanceCompleted",
      header: "Class Attendance",
      cell: ({ row }) => (
        <div className="flex justify-center">
          <CircularProgress
            completed={row.original.attendanceCompleted}
            total={row.original.attendanceTotal}
            size={45}
            strokeWidth={3}
            colorScheme="attendance"
            showLabel
          />
        </div>
      ),
    },
    {
      id: "quiz",
      accessorKey: "quizCompleted",
      header: "Quiz",
      cell: ({ row }) => (
        <div className="flex justify-center">
          <CircularProgress
            completed={row.original.quizCompleted}
            total={row.original.quizTotal}
            size={45}
            strokeWidth={3}
            colorScheme="quiz"
            showLabel
          />
        </div>
      ),
    },
    {
      id: "assignment",
      accessorKey: "assignmentCompleted",
      header: "Assignment",
      cell: ({ row }) => (
        <div className="flex justify-center">
          <CircularProgress
            completed={row.original.assignmentCompleted}
            total={row.original.assignmentTotal}
            size={45}
            strokeWidth={3}
            colorScheme="assignment"
            showLabel
          />
        </div>
      ),
    },
    {
      id: "project",
      accessorKey: "projectCompleted",
      header: "Project",
      cell: ({ row }) => (
        <div className="flex justify-center">
          <CircularProgress
            completed={row.original.projectCompleted}
            total={row.original.projectTotal}
            size={45}
            strokeWidth={3}
            colorScheme="project"
            showLabel
          />
        </div>
      ),
    },
    {
      id: "interviewCompletion",
      accessorKey: "interviewCompleted",
      header: "Interview Completion",
      cell: ({ row }) => (
        <div className="flex justify-center">
          <CircularProgress
            completed={row.original.interviewCompleted}
            total={row.original.interviewTotal}
            size={45}
            strokeWidth={3}
            colorScheme="interview"
            showLabel
          />
        </div>
      ),
    },
    {
      id: "marketing",
      accessorKey: "marketing",
      header: "Marketing Effort",
      cell: ({ row }) => (
        <div className="flex justify-center">
          <Button
            variant="primary"
            size="sm"
            onClick={(e) => {
              e.stopPropagation()
              navigate("https://ai-jobs-9b4c6.web.app/login")
            }}
          >
            View Marketing
          </Button>
        </div>
      ),
    },
  ]

  const filteredColumns = columns.filter((col) => col.id !== "marketing")

  const renderCellContent = (cell) => {
    if (!cell) return null
    return flexRender(cell.column.columnDef.cell, cell.getContext())
  }

  const getColumns = () => {
    if (userRole === USER_ROLES.TRAINER) {
      return filteredColumns
    }
    return columns
  }

  // Use paginated data instead of full data
  const { table, found } = useTableConfig(
    paginatedData || [],
    getColumns(),
    totalCount, // Pass total count for proper pagination info
    setPagination,
    pagination
  )

  console.log("Performance Response:", performanceResponse)
  console.log("Processed Student Performance:", studentPerformance)
  console.log("Formatted Table Data:", formattedPerformanceData)
  console.log("Paginated Data:", paginatedData)
  console.log("Pagination State:", pagination)
  console.log("Total Count:", totalCount)
  console.log("Page Count:", pageCount)

  const courseOptions = courseName
    .map((course) => ({
      id: course.id,
      name: course.title,
    }))
    .filter((m) => m.id && m.name)

  return (
    <Card className="p-8 bg-white rounded-2xl shadow-lg">
      <div className="flex justify-between items-center gap-9 mb-6">
        <h1 className="text-2xl font-semibold text-gray-800 flex items-center">
          Student Performance Dashboard
        </h1>
        <div className="flex items-center gap-3">
          <Label htmlFor="course-select" className="text-right font-medium text-gray-700">
            Select Course:
          </Label>
          <CustomComboBox
            iterateData={courseOptions}
            selectedValue={selectedCourse?.name}
            setSelectedValue={(course) => setSelectedCourse(course)}
            notFoundMessage="modules"
            placeholder="Select module..."
            width="min-w-[285px]"
            loading={batchCourseIdLoading}
          />
        </div>
      </div>

      {/* Performance Summary Cards */}
      {formattedPerformanceData.length > 0 && (
        <div className="grid grid-cols-5 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="text-sm font-medium text-gray-600 mb-1">Avg Attendance</div>
            <CircularProgress
              completed={Math.round(
                formattedPerformanceData.reduce(
                  (acc, student) =>
                    acc +
                    (student.attendanceTotal > 0
                      ? (student.attendanceCompleted / student.attendanceTotal) * 100
                      : 0),
                  0
                ) / formattedPerformanceData.length
              )}
              total={100}
              size={60}
              strokeWidth={4}
              colorScheme="attendance"
              showLabel
            />
          </div>
          <div className="text-center">
            <div className="text-sm font-medium text-gray-600 mb-1">Avg Quiz</div>
            <CircularProgress
              completed={Math.round(
                formattedPerformanceData.reduce(
                  (acc, student) =>
                    acc +
                    (student.quizTotal > 0 ? (student.quizCompleted / student.quizTotal) * 100 : 0),
                  0
                ) / formattedPerformanceData.length
              )}
              total={100}
              size={60}
              strokeWidth={4}
              colorScheme="quiz"
              showLabel
            />
          </div>
          <div className="text-center">
            <div className="text-sm font-medium text-gray-600 mb-1">Avg Assignment</div>
            <CircularProgress
              completed={Math.round(
                formattedPerformanceData.reduce(
                  (acc, student) =>
                    acc +
                    (student.assignmentTotal > 0
                      ? (student.assignmentCompleted / student.assignmentTotal) * 100
                      : 0),
                  0
                ) / formattedPerformanceData.length
              )}
              total={100}
              size={60}
              strokeWidth={4}
              colorScheme="assignment"
              showLabel
            />
          </div>
          <div className="text-center">
            <div className="text-sm font-medium text-gray-600 mb-1">Avg Project</div>
            <CircularProgress
              completed={Math.round(
                formattedPerformanceData.reduce(
                  (acc, student) =>
                    acc +
                    (student.projectTotal > 0
                      ? (student.projectCompleted / student.projectTotal) * 100
                      : 0),
                  0
                ) / formattedPerformanceData.length
              )}
              total={100}
              size={60}
              strokeWidth={4}
              colorScheme="project"
              showLabel
            />
          </div>
          <div className="text-center">
            <div className="text-sm font-medium text-gray-600 mb-1">Avg Interview</div>
            <CircularProgress
              completed={Math.round(
                formattedPerformanceData.reduce(
                  (acc, student) =>
                    acc +
                    (student.interviewTotal > 0
                      ? (student.interviewCompleted / student.interviewTotal) * 100
                      : 0),
                  0
                ) / formattedPerformanceData.length
              )}
              total={100}
              size={60}
              strokeWidth={4}
              colorScheme="interview"
              showLabel
            />
          </div>
        </div>
      )}

      <div className="w-full">
        <DataTable
          renderCellContent={renderCellContent}
          columns={getColumns()}
          table={table}
          found={found}
          pageCount={pageCount}
          pagination={pagination}
          notFoundPlaceholder="No Data Found"
          isLoading={isLoading}
        />
      </div>
    </Card>
  )
}

export default StudentPerformancePage
