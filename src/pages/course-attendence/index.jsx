import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import { Card } from "@/components/ui/card"
import {
  useDateHook,
  useModuleAndTopicsList,
  useOpenCloseHooks,
  usePaginationHooks,
  useSelectedModule,
} from "@/hooks/common.hooks"
import { useDebounce } from "@/hooks/useDebounce"
import {
  useFetchCourseAttendance,
  useUpdateAttendance,
} from "@/services/query/course-overview.query"
import ct from "@constants/"
import { ArrowLeft } from "lucide-react"
import { useEffect, useState } from "react"
import { useSelector } from "react-redux"
import { useLocation } from "react-router-dom"
import CourseAttendanceUI from "./course-attendance.ui"

const CourseAttendance = () => {
  const userRole = useSelector((st) => st[ct.store.USER_STORE]?.userRole)
  const user = useSelector((st) => st[ct.store.USER_STORE])
  const course = useSelector((st) => st[ct.store.COURSES]?.activeCourseDetails)
  console.log("coursssssssssse", course.id)
  let isLoading
  const location = useLocation()
  const { coursesContentData } = location?.state || {}

  const { handleOpen, handleClose, open } = useOpenCloseHooks()
  const { selectedModule, setSelectedModule, selectedTopic, setSelectedTopic } = useSelectedModule()
  const { listOfModules, setListOfModules, setListOfTopics, listOfTopics } =
    useModuleAndTopicsList()

  const { date, setDate } = useDateHook({
    from: new Date(),
    to: null,
  })
  const [selectedOption, setSelectedOption] = useState(null)
  const [selectedResource, setSelectedResource] = useState(null)
  // hooks
  const [searchingValue, setSearchingValue] = useState("")
  const [isEditMode, setIsEditMode] = useState(false)

  // Track changes made during edit mode
  const [pendingChanges, setPendingChanges] = useState([])
  const [selectedStudents, setSelectedStudents] = useState([])
  const [data, setData] = useState([])
  const { limit } = usePaginationHooks()
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })
  const [selectedDate, setSelectedDate] = useState(null)
  // custom hooks
  const debouncedSearchQuery = useDebounce(searchingValue, 500)

  const currentPage = 0

  const start_date = date?.from?.toLocaleDateString("en-CA")
  const end_date = date?.to?.toLocaleDateString("en-CA")

  const currentDate = new Date().toISOString().split("T")[0] // format: 'YYYY-MM-DD'
  const calculateOffset = (pageIndex, pageSize) => pageIndex * pageSize
  const { data: listOfAttendance, isLoading: attendanceLoading } = useFetchCourseAttendance({
    course_id: course?.id,
    modules_id: selectedOption?.value,
    resource_id: selectedResource?.value,
    resourse_date: selectedDate,
    filters: {
      limit,
      offset: calculateOffset(pagination.pageIndex, pagination.pageSize),
    },
  })

  // const { data: coursesContentData, isLoading } = useGetCourseContent(
  //   {
  //     userID: user.id,
  //     userRole: user?.userRole,
  //     course_id: course?.id, // Move course_id inside the object
  //     signature: "SIGNATURE",
  //   },
  //   debouncedSearchQuery?.length > 0 ? debouncedSearchQuery : "", // Search query
  //   currentPage,
  //   pageSize
  // )

  const { mutate: updateAttendance } = useUpdateAttendance()
  useEffect(() => {
    if (
      !coursesContentData?.get_module_resource ||
      coursesContentData?.get_module_resource?.total_records === 0
    ) {
      return
    }

    // Extract module names
    const filteredModules = coursesContentData?.get_module_resource?.modules?.map((item) => ({
      id: item.id,
      name: item.module_name,
    }))

    setListOfModules(filteredModules)
  }, [coursesContentData?.get_module_resource?.modules])

  useEffect(() => {
    if (!selectedModule?.id) return

    // Extract resources where module ID matches selectedModule.id
    const filteredTopics =
      coursesContentData?.get_module_resource?.modules
        ?.filter((item) => item.id === selectedModule.id) // Find matching module
        ?.flatMap(
          (item) =>
            item.resources?.map((topic) => ({
              id: topic.id,
              name: topic.resource_name,
            })) || []
        ) || []

    setListOfTopics(filteredTopics)
  }, [selectedModule, coursesContentData])

  const handleAttendance = async () => {
    // if (!selectedModule?.id || !selectedTopic?.id) {
    //   const errorMsg = !selectedModule?.id ? "Please select module" : "Please select topic"

    //   failureToast("", errorMsg)
    //   return
    // }

    // Object.entries(pendingChanges).forEach(([key, value]) => {
    //   const [rowIndex, columnId] = key.split("|")
    //   if (data[rowIndex]) {
    //     data[rowIndex] = {
    //       ...data[rowIndex],
    //       [columnId]: value,
    //     }
    //   }
    // })

    //   return newData
    // })

    // course_id, resource_id, session_date, data
    console.log("___values", selectedStudents)
    updateAttendance(
      {
        course_id: course?.id,
        resource_id: selectedResource?.value,
        session_date: selectedDate,
        data: selectedStudents,
      },
      {
        onSuccess: () => {
          setIsEditMode(false)
          setPendingChanges({})
          setSelectedStudents([])
          successToast("Attandance Added", "The attendance has been successfully added!")
        },
        onError: () => {
          setIsEditMode(false)
          setPendingChanges({})
          setSelectedStudents([])
          failureToast(
            "Failed to add Attendance ",
            "An error occurred while update the attendance. Please try again."
          )
        },
      }
    )
  }

  return (
    <Card className="p-8 bg-white rounded-2xl">
      <div className="flex gap-2">
        <h6 className="text-lg text-primary font-semibold mb-6 items-center flex gap-2">
          {" "}
          <ArrowLeft size={18} className="cursor-pointer " onClick={() => window.history.back()} />
          Course Attendance
        </h6>
      </div>

      <CourseAttendanceUI
        selectedDate={selectedDate}
        setSelectedDate={setSelectedDate}
        setSelectedOption={setSelectedOption}
        setSelectedResource={setSelectedResource}
        selectedOption={selectedOption}
        selectedResource={selectedResource}
        coursesContentData={coursesContentData}
        handleClose={handleClose}
        handleOpen={handleOpen}
        open={open}
        pagination={pagination}
        setPagination={setPagination}
        userRole={userRole}
        searchingValue={searchingValue}
        setSearchingValue={setSearchingValue}
        isEditMode={isEditMode}
        setIsEditMode={setIsEditMode}
        pendingChanges={pendingChanges}
        setPendingChanges={setPendingChanges}
        data={data}
        setData={setData}
        isLoading={isLoading}
        listOfModules={listOfModules}
        selectedModule={selectedModule}
        setSelectedModule={setSelectedModule}
        listOfTopics={listOfTopics}
        setSelectedTopic={setSelectedTopic}
        selectedTopic={selectedTopic}
        handleAttendance={handleAttendance}
        setDate={setDate}
        date={date}
        listOfAttendance={listOfAttendance?.data}
        attendanceLoading={attendanceLoading}
        setSelectedStudents={setSelectedStudents}
      />
    </Card>
  )
}

export default CourseAttendance
