// Create a new component: AttendanceDisplay.jsx
import { Check, X, Calendar, TentTree } from "lucide-react"
import PropTypes from "prop-types"

const AttendanceDisplay = ({ attendance }) => {
    const getStatusIcon = (status) => {
        switch (status) {
            case "Present":
                return <Check className="mr-2 h-4 w-4 text-green-500" />
            case "Absent":
                return <X className="mr-2 h-4 w-4 text-red-500" />
            case "Late":
                return <Calendar className="mr-2 h-4 w-4 text-yellow-500" />
            case "Holiday":
                return <TentTree className="mr-2 h-4 w-4 text-blue-500" />
            default:
                return null
        }
    }

    if (!attendance) {
        return <span className="text-gray-400">-</span>
    }

    const formattedValue = attendance.charAt(0).toUpperCase() + attendance.slice(1).toLowerCase()

    return (
        <div className="flex items-center">
            {getStatusIcon(formattedValue)}
            <span className="font-medium text-sm">{formattedValue}</span>
        </div>
    )
}

AttendanceDisplay.propTypes = {
    attendance: PropTypes.string,
}

export default AttendanceDisplay
