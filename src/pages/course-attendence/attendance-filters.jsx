import { formsProps } from "@/components/custom/custom-forms/utils/props-type"
import { ScrollArea } from "@/components/ui/scroll-area"
import CustomComboBox from "@/components/custom/custom-combo-box/custom-combo-box"
import { Label } from "@/components/ui/label"
import CustomMultipleDateSelect from "@/components/custom/custom-multi-date-select"

const AttendanceFilters = (props) => {
  const {
    listOfModules,
    selectedModule,
    setSelectedModule,
    moduleLoading,
    listOfTopics,
    setSelectedTopic,
    selectedTopic,
    setDate,
    date,
  } = props
  return (
    <ScrollArea className="overflow-auto w-full rounded-md pr-2">
      <div className="gap-4">
        <div>
          <Label className="block  mb-1.5">Modules</Label>
          <CustomComboBox
            iterateData={listOfModules}
            selectedValue={selectedModule?.name}
            setSelectedValue={(module) => setSelectedModule(module)}
            notFoundMessage="module"
            placeholder="Select module..."
            width="min-w-full"
            loading={moduleLoading}
          />
        </div>
        <div className="my-5">
          <Label className="block  mb-1.5">Topics</Label>
          <CustomComboBox
            iterateData={listOfTopics}
            selectedValue={selectedTopic?.name}
            setSelectedValue={(topic) => setSelectedTopic(topic)}
            notFoundMessage="topic"
            placeholder="Select topic..."
            width="min-w-full"
            loading={moduleLoading}
          />
        </div>
        <div>
          <Label className="block  mb-1.5">Select Date</Label>
          <CustomMultipleDateSelect width="min-w-full" date={date} setDate={setDate} />
        </div>
      </div>
    </ScrollArea>
  )
}

AttendanceFilters.propTypes = formsProps

export default AttendanceFilters
