import DataTable from "@/components/custom/cutsom-table"
import AttendanceCell from "@/components/custom/cutsom-table/cells-editable-table/components/attendance-cell"
import { ActionCell, RenderTableData } from "@/components/custom/cutsom-table/table-cells"
import { ReusableDropdown } from "@/components/custome-drop-down/drop-down"
import DatePicker from "@/components/state-date-picker"
import { But<PERSON> } from "@/components/ui/button"
import useTableConfig from "@/hooks/use-table.hooks"
import { flexRender } from "@tanstack/react-table"
import { Check, X, Calendar, TentTree } from "lucide-react" // Add these imports
import PropTypes from "prop-types"
import { useEffect, useMemo } from "react"

import CustomSidebar from "../courses/components/custom-sidebar"
import AttendanceFilters from "./attendance-filters"

const CourseAttendanceUI = ({
  setSelectedDate,
  selectedDate,
  setSelectedOption,
  setSelectedResource,
  selectedOption,
  selectedResource,
  coursesContentData,
  pagination,
  setPagination,
  setSearchingValue,
  searchingValue,
  pendingChanges,
  setPendingChanges,
  setData,
  isEditMode,
  setIsEditMode,
  handleClose,
  handleOpen,
  open,
  listOfModules,
  selectedModule,
  setSelectedModule,
  listOfTopics,
  setSelectedTopic,
  selectedTopic,
  handleAttendance,
  setDate,
  date,
  listOfAttendance,
  attendanceLoading,
  setSelectedStudents,
}) => {


  const deduplicatedAttendanceData = useMemo(() => {
    if (!listOfAttendance?.data || !Array.isArray(listOfAttendance.data)) {
      return []
    }

    // Remove duplicates based on unique combination of student_id + resource_id
    const uniqueData = listOfAttendance.data.filter((item, index, self) => {
      return index === self.findIndex(t =>
        t.student_id === item.student_id &&
        t.resource_id === item.resource_id &&
        (t.date === item.date || (!t.date && !item.date))
      )
    })

    console.log('Original data length:', listOfAttendance.data.length)
    console.log('Deduplicated data length:', uniqueData.length)

    return uniqueData
  }, [listOfAttendance?.data])

  const trackChange = (rowIndex, columnId, value, id, student_id) => {
    const changeKey = `${rowIndex}|${columnId}`
    console.log("__trackrow", id, value, student_id)
    setSelectedStudents((pre) => [...pre, { student_id, attendance: value?.toUpperCase() }])
    setPendingChanges((prev) => ({
      ...prev,
      [changeKey]: value,
      student_id,
      id,
    }))
  }

  const ModuleOptions =
    coursesContentData?.get_module_resource?.modules.map((data) => {
      return { label: data.module_name, value: data.id }
    }) || []

  const ResourceOption =
    coursesContentData?.get_module_resource?.modules
      .find((data) => selectedOption?.value === data.id)
      ?.resources?.map((res) => ({
        label: res.resource_name,
        value: res.id,
      })) || []

  // Helper function to get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case "Present":
        return <Check className="mr-2 h-4 w-4 text-green-500" />
      case "Absent":
        return <X className="mr-2 h-4 w-4 text-red-500" />
      case "Late":
        return <Calendar className="mr-2 h-4 w-4 text-yellow-500" />
      case "Holiday":
        return <TentTree className="mr-2 h-4 w-4 text-blue-500" />
      default:
        return null
    }
  }

  // Helper function to format attendance display
  const formatAttendanceDisplay = (attendanceValue) => {
    if (!attendanceValue) return <span className="text-gray-400">-</span>

    const formattedValue = attendanceValue.charAt(0).toUpperCase() + attendanceValue.slice(1).toLowerCase()

    return (
      <div className="flex items-center">
        {getStatusIcon(formattedValue)}
        <span className="font-medium text-sm">{formattedValue}</span>
      </div>
    )
  }

  const renderCellContent = (cell, row) => {
    const { student_name, module_name, attendance, resource_name, resource_id } =
      row?.original || {}

    switch (cell.column.id) {
      case "student_name":
        return <RenderTableData content={student_name} />
      case "module_name":
        return <RenderTableData content={module_name} />
      case "resource_name":
        return <RenderTableData content={resource_name} />
      case "attendance":
        return (
          <div>
            {isEditMode ? (
              <AttendanceCell
                rowIndex={row.index}
                columnId={cell.column.id}
                initialValue={
                  attendance
                    ? attendance.charAt(0).toUpperCase() + attendance.slice(1).toLowerCase()
                    : ""
                }
                pendingChanges={pendingChanges}
                trackChange={trackChange}
                isEditMode={isEditMode}
                id={resource_id}
                student_id={cell?.row.original?.student_id}
                row={cell?.row.original}
              />
            ) : (
              formatAttendanceDisplay(attendance)
            )}
          </div>
        )

      case "actions":
        return (
          <ActionCell
            label1="Update Attendance"
            label2="Edit"
            label3="Delete"
            row={row}
            isEdit
            isDelete
            isView
          />
        )

      default:
        return flexRender(cell.column.columnDef.cell, cell.getContext())
    }
  }

  const columns = useMemo(
    () => [
      {
        id: "student_name",
        accessorKey: "student_name",
        header: "Student Name",
      },
      {
        id: "module_name",
        accessorKey: "module_name",
        header: "Module",
      },
      {
        id: "resource_name",
        accessorKey: "resource_name",
        header: "Topic",
      },
      {
        id: "attendance",
        accessorKey: "attendance",
        header: "Attendance",
      },
    ],
    [isEditMode, pendingChanges]
  )

  // const { table, found } = useEditableTableConfig(
  //   listOfAttendance?.data,
  //   columns,
  //   listOfAttendance?.metadata?.total_records, // Adjust as needed
  //   null
  // )
  const { table, found, pageCount } = useTableConfig(
    deduplicatedAttendanceData,
    columns,
    deduplicatedAttendanceData.length,
    setPagination,
    pagination
  )

  // Function to toggle edit mode
  const toggleEditMode = () => {
    setIsEditMode((prevMode) => !prevMode)
    // Clear pending changes if exiting edit mode without saving
    if (isEditMode) {
      setPendingChanges([])
    }
  }

  const filterProps = {
    listOfModules,
    selectedModule,
    setSelectedModule,
    listOfTopics,
    setSelectedTopic,
    selectedTopic,
    setDate,
    date,
  }

  useEffect(() => {
    // Reset selectedResource when selectedOption changes
    setSelectedResource(null)
  }, [selectedOption])

  return (
    <div>
      <div className="flex justify-between gap-4">
        {/* <CustomSearchbar
          inputSize="lg:w-[20rem] w-[13rem]"
          placeholder="Search by title..."
          searchedValue={searchingValue}
          setSearchedValue={(e) => setSearchingValue(e?.target.value)}
        /> */}

        <div className="flex flex-col md:flex-row flex-wrap justify-end items-start md:items-center gap-3 md:gap-4 mb-4">
          {/* <div className="hidden xl:flex items-center gap-2">
            <AttendanceFilters {...filterProps} />
          </div> */}
          {/* <AppointmentScheduler
            dataTestID="test-id"
            dataTestIDError="error"
            fieldControlName="due_date"
            label="Dead Line"
            placeholder="Pick a date"
            futureDateDisabled={null}
            isRequired
          /> */}

          <div>
            <DatePicker value={selectedDate} onChange={(selected) => setSelectedDate(selected)} />
          </div>

          <ReusableDropdown
            label="Select Module"
            options={ModuleOptions}
            selected={selectedOption}
            onSelect={(option) => setSelectedOption(option)}
            placeholder="Select Module"
          />
          <ReusableDropdown
            label="Select Topic"
            options={ResourceOption}
            selected={selectedResource}
            onSelect={(option) => setSelectedResource(option)}
            placeholder="Select Topic"
          />
          <Button
            variant={isEditMode ? "outline" : "primary"}
            className=""
            onClick={toggleEditMode}
          >
            {isEditMode ? "Cancel Edit" : "Edit Attendance"}
          </Button>

          {isEditMode && (
            <Button variant="primary" className="" onClick={handleAttendance}>
              Update Changes
            </Button>
          )}

          {/* {isEditMode && Object.keys(pendingChanges)?.length > 0 && (
              <span>({Object.keys(pendingChanges)?.length} pending changes)</span>
            )} */}

          {/* <Button
            variant="outline"
            onClick={() => handleOpen()}
            className="text-primary px-2.5 items-center gap-2"
          >
            <Filter size={16} />
          </Button> */}
        </div>
      </div>

      <CustomSidebar
        title="Attendance Filters"
        description=""
        isOpen={open}
        onClose={handleClose}
        content={<AttendanceFilters {...filterProps} />}
      />

      {/* <CellEditableTable
        renderCellContent={renderCellContent}
        columns={columns}
        table={table}
        found={found}
        pageCount={pageCount}
        pagination={pagination}
        notFoundPlaceholder="No data available"
        setData={setData}
        setIsEditMode={setIsEditMode}
        isEditMode={isEditMode}
        pendingChanges={pendingChanges}
        setPendingChanges={setPendingChanges}
        handleAttendance
        isLoading={attendanceLoading}
      /> */}

      <DataTable
        renderCellContent={renderCellContent}
        columns={columns}
        table={table}
        found={found}
        pageCount={pageCount}
        pagination={pagination}
        pageName="Attendance"
        notFoundPlaceholder="No data available"
      />
    </div>
  )
}


CourseAttendanceUI.propTypes = {
  coursesContentData: PropTypes.shape({
    get_module_resource: PropTypes.shape({
      modules: PropTypes.arrayOf(
        PropTypes.shape({
          id: PropTypes.number.isRequired,
          module_name: PropTypes.string,
          resources: PropTypes.arrayOf(
            PropTypes.shape({
              id: PropTypes.number.isRequired,
              resource_name: PropTypes.string,
            })
          ),
        })
      ),
    }),
  }).isRequired,
  setSearchingValue: PropTypes.func,
  searchingValue: PropTypes.string,
  pendingChanges: PropTypes.objectOf(PropTypes.string),
  setPendingChanges: PropTypes.func,
  setData: PropTypes.func,
  isEditMode: PropTypes.bool,
  setIsEditMode: PropTypes.func,
  handleClose: PropTypes.func,
  handleOpen: PropTypes.func,
  open: PropTypes.bool,
  listOfModules: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      name: PropTypes.string.isRequired,
    })
  ),
  selectedModule: PropTypes.shape({
    id: PropTypes.number.isRequired,
    name: PropTypes.string.isRequired,
  }),
  setSelectedModule: PropTypes.func,
  listOfTopics: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      name: PropTypes.string.isRequired,
    })
  ),
  setSelectedTopic: PropTypes.func,
  selectedTopic: PropTypes.shape({
    id: PropTypes.number.isRequired,
    name: PropTypes.string.isRequired,
  }),
  handleAttendance: PropTypes.func,
  setDate: PropTypes.func,
  date: PropTypes.string,
  listOfAttendance: PropTypes.shape({
    data: PropTypes.arrayOf(
      PropTypes.shape({
        student_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        resource_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        date: PropTypes.string,
        attendance: PropTypes.string,
        module_name: PropTypes.string,
        resource_name: PropTypes.string,
        student_name: PropTypes.string,
      })
    ),
  }),
  pageCount: PropTypes.number,
  attendanceLoading: PropTypes.bool,
  setSelectedStudents: PropTypes.func,
}

export default CourseAttendanceUI