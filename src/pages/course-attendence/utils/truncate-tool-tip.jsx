import { Tooltip, Toolt<PERSON>Content, TooltipTrigger } from "@/components/ui/tooltip"
import PropTypes from "prop-types"

export const TruncatedTextWithTooltip = ({ text, maxLength = 15 }) => {
  if (!text) return "-"
  const shouldTruncate = text.length > maxLength
  const truncatedText = shouldTruncate ? `${text.substring(0, maxLength)}...` : text

  return shouldTruncate ? (
    <Tooltip>
      <TooltipTrigger>
        <span className="truncate">{truncatedText}</span>
      </TooltipTrigger>
      <TooltipContent>
        <p>{text}</p>
      </TooltipContent>
    </Tooltip>
  ) : (
    <span>{text}</span>
  )
}

TruncatedTextWithTooltip.propTypes = {
  text: PropTypes.string.isRequired,
  maxLength: PropTypes.number,
}

