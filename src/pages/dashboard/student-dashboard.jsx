import ct from "@constants/"
import { useSelector } from "react-redux"

const StudentDashboard = () => {
  const userRole = useSelector((st) => st[ct.store.USER_STORE]?.userRole)

  const dashboardData = {
    leetcode: {
      total: 150,
      easy: 70,
      medium: 60,
      hard: 20,
    },
    quizzes: {
      total: 10,
      completed: 8,
      avgScore: 85,
      bestScore: 95,
    },
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Student Dashboard</h1>
      {/* TODO: IF user is not student, then it is getting accessed by other user, so we can show latest cv */}
      {userRole !== "student" ? <h4>Latest CV: Check Here</h4> : null}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Leetcode Stats */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Leetcode Progress</h2>
          <div className="space-y-2">
            <p>Total Solved: {dashboardData.leetcode.total}</p>
            <p>Easy: {dashboardData.leetcode.easy}</p>
            <p>Medium: {dashboardData.leetcode.medium}</p>
            <p>Hard: {dashboardData.leetcode.hard}</p>
          </div>
        </div>

        {/* Quiz Stats */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Quiz Performance</h2>
          <div className="space-y-2">
            <p>Total Quizzes: {dashboardData.quizzes.total}</p>
            <p>Completed: {dashboardData.quizzes.completed}</p>
            <p>Average Score: {dashboardData.quizzes.avgScore}%</p>
            <p>Best Score: {dashboardData.quizzes.bestScore}%</p>
          </div>
        </div>

        {/* Progress Card */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Overall Progress</h2>
          <div className="flex items-center justify-center h-32">
            <div className="text-4xl font-bold text-green-600">
              {Math.round((dashboardData.quizzes.completed / dashboardData.quizzes.total) * 100)}%
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default StudentDashboard
