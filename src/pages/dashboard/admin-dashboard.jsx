import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card"
import { Users, GraduationCap, DollarSign, BookOpen, FolderGit2 } from "lucide-react"

const AdminDashboard = () => {
  const dashboardData = {
    totalStudents: 150,
    dailyNewStudents: 5,
    totalPayments: 25000,
    totalCourses: 12,
    activeProjects: 8,
  }

  const kpiCards = [
    {
      title: "Total Students",
      value: dashboardData.totalStudents,
      icon: <Users className="h-6 w-6 text-muted-foreground" />,
    },
    {
      title: "New Students Today",
      value: dashboardData.dailyNewStudents,
      icon: <GraduationCap className="h-6 w-6 text-muted-foreground" />,
    },
    {
      title: "Total Payments",
      value: `$${dashboardData.totalPayments.toLocaleString()}`,
      icon: <DollarSign className="h-6 w-6 text-muted-foreground" />,
    },
    {
      title: "Total Courses",
      value: dashboardData.totalCourses,
      icon: <BookOpen className="h-6 w-6 text-muted-foreground" />,
    },
    {
      title: "Active Projects",
      value: dashboardData.activeProjects,
      icon: <FolderGit2 className="h-6 w-6 text-muted-foreground" />,
    },
  ]

  return (
    <div className="p-6 space-y-6">
      {/* Welcome Section */}
      <div>
        <h2 className="text-3xl font-bold tracking-tight text-gray-900">Welcome back  👋 </h2>
      </div>

      {/* KPI Cards
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {kpiCards.map((kpi, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{kpi.title}</CardTitle>
              {kpi.icon}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{kpi.value}</div>
            </CardContent>
          </Card>
        ))}
      </div> */}
    </div>
  )
}

export default AdminDashboard
