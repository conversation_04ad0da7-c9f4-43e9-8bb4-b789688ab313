import InterviewCard from "@/components/custom/interview-card"
import ct from "@constants/"
import { useEffect, useState } from "react"
import { useSelector } from "react-redux"

import { useFeatchUsers } from "@/services/query/leads-detail.query.js"

import { <PERSON><PERSON> } from "@/components/ui/button"

import { successToast } from "@/components/custom/toasts/tosters"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useFetchBatchCourseID } from "@/services/query/course-material.query"
import { useGetCourseByBatch } from "@/services/query/live-course.query"
import {
  useGetMockInterviews,
  usePatchMockInterview,
  usePostMockInterview,
} from "@/services/query/mock-interview.query"
import { USER_ROLES } from "@/utils/constants"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import CustomSidebar from "../courses/components/custom-sidebar"
import InterviewF<PERSON>, { InterviewSchema } from "./sidebar"
import InterviewRequestForm from "./student-request"

const MockInterview = () => {
  const { id: userID, userRole, isSessionValid } = useSelector((st) => st[ct.store.USER_STORE])
  const [courseName, setCourseName] = useState([])
  const [studentSearchQuery, setStudentSearchQuery] = useState("")
  const [interviewerSearchQuery, setInterviewerSearchQuery] = useState("")
  const { data: studentsData } = useFeatchUsers("STUDENT", { search_query: studentSearchQuery })
  const { data: trainersData } = useFeatchUsers("TRAINER", { search_query: interviewerSearchQuery })
  const [students, setStudents] = useState([])
  const [trainers, setTrainers] = useState([])

  console.log(studentSearchQuery, "searchQuery")
  const [mockInterviewData, setMockInterviewData] = useState([])
  const [batchCourses, setBatchCourses] = useState([])
  const { data: batchCourseData, isLoading: batchCourseLoading } = useGetCourseByBatch()
  const { data: batchCourseIdData, isLoading: batchCourseIdLoading } = useFetchBatchCourseID()
  const { data: MockInterviewData, refetch: refetchInterviews } = useGetMockInterviews()
  const [selectedCourse, setSelectedCourse] = useState(null)
  const [editData, setEditData] = useState(null)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [isEdit, setIsEdit] = useState(false)
  const [showRequestForm, setShowRequestForm] = useState(false)
  const isTrainerOrStudent = userRole === USER_ROLES.TRAINER || userRole === USER_ROLES.STUDENT
  const isStudent = userRole === USER_ROLES.STUDENT
  const [editStuent, setEditStaudent] = useState(null)
  const [editTrainer, setEditTrainer] = useState(null)
  const { mutate: createInterview } = usePostMockInterview()
  const { mutate: updateInterview } = usePatchMockInterview()

  const form = useForm({
    resolver: zodResolver(InterviewSchema),
    defaultValues: {
      title: "",
      description: "",
      interviewer: "",
      student: "",
      date: new Date(),
      duration: "",
      status: isStudent ? "REQUESTED" : "SCHEDULED",
      remarks: "",
      feedback: "",
      grade: "",
      interviewLink: "",
      interviewFeedback: "",
      studentFeedback: "",
    },
    mode: "onChange",
  })

  const { handleSubmit, control, reset, getValues, setValue } = form

  const handleCardClick = (interview) => {
    console.log("handleCardClick", interview)

    // Map API response fields to form fields
    const formattedInterview = {
      title: interview.title || "",
      description: interview.description || "",
      interviewer: interview.interviewer_id?.toString() || "",
      student: interview.student_id?.toString() || "",
      date: interview.interview_date ? new Date(interview.interview_date) : new Date(),
      duration: interview.interview_duration?.toString() || "",
      status: interview.interview_status || (isStudent ? "REQUESTED" : "SCHEDULED"),
      remarks: interview.remarks || "",
      feedback: interview.student_feedback || "",
      grade: interview.interview_grade || "",
      interviewLink: interview.interview_link || "",
      interviewFeedback: interview.interviewer_feedback || "",
      studentFeedback: interview.student_feedback || "",
      selectedSkills: interview.skills || [],
      overallRating: interview.rating || "",
      id: interview.id,
      course_id: interview.course_id,
    }

    setEditData(formattedInterview)
    setIsEdit(true)
    setShowRequestForm(false)

    // Set form values based on the mapped interview data
    Object.keys(formattedInterview).forEach((key) => {
      if (key === "date") {
        setValue(key, new Date(formattedInterview[key]))
      } else if (form.getValues(key) !== undefined) {
        // Only set values for fields that exist in the form
        setValue(key, formattedInterview[key])
      }
    })

    // If there's a course ID in the response, set the selected course
    if (interview.course_id) {
      const course =
        courseOptions.find((c) => c.id === interview.course_id) ||
        comboBoxData.find((c) => c.id === interview.course_id)
      if (course) {
        setSelectedCourse(course)
      }
    }

    setSidebarOpen(true)
  }

  useEffect(() => {
    if (batchCourseIdData?.data?.data) {
      setCourseName(batchCourseIdData?.data?.data)
    }
    if (studentsData?.data) {
      setStudents(studentsData.data || [])
    }
    if (trainersData?.data) {
      setTrainers(trainersData.data || [])
    }
    if (MockInterviewData?.data?.data) {
      setMockInterviewData(MockInterviewData.data.data)
    }
    if (batchCourseData?.get_course?.courses) {
      setBatchCourses(batchCourseData?.get_course?.courses || [])
    }
  }, [
    batchCourseIdData,
    batchCourseIdLoading,
    studentsData,
    trainersData,
    MockInterviewData,
    batchCourseData,
    batchCourseLoading,
  ])

  // Reset form when editing changes
  useEffect(() => {
    if (editData) {
      reset({
        title: editData?.title || "",
        description: editData?.description || "",
        interviewer: editData?.interviewer || "",
        student: editData?.student || "",
        date: editData?.date ? new Date(editData.date) : new Date(),
        duration: editData?.duration || "",
        status: editData?.status || (isStudent ? "REQUESTED" : "SCHEDULED"),
        remarks: editData?.remarks || "",
        feedback: editData?.feedback || "",
        grade: editData?.grade || "",
        interviewLink: editData?.interviewLink || "",
        interviewFeedback: editData?.interviewFeedback || "",
        studentFeedback: editData?.studentFeedback || "",
        selectedSkills: editData?.selectedSkills || [],
        overallRating: editData?.overallRating || "",
      })
    }
  }, [editData, reset, isStudent])

  console.log(editData, "editData")

  const courseOptions = courseName
    .map((course) => ({
      id: course.id,
      name: course.title,
    }))
    .filter((m) => m.id && m.name)

  const studentOptions = students
    .map((student) => ({
      id: student.id,
      name: student.user_name,
      role: student.user_role,
    }))
    .filter((m) => m.id && m.name)

  const trainerOptions = trainers
    .map((trainer) => ({
      id: trainer.id,
      name: trainer.user_name,
      role: trainer.user_role,
    }))
    .filter((m) => m.id && m.name)

  const getDefaultInterviewValues = () => ({
    title: "",
    description: "",
    interviewer: "",
    student: "",
    date: null, // Now defaulting to null
    duration: "",
    status: "SCHEDULED",
    remarks: "",
    feedback: "",
    grade: "",
    interviewLink: "",
    interviewFeedback: "",
    studentFeedback: "",
    selectedSkills: [],
    overallRating: "",
  })

  const handleFormSubmit = (data) => {
    // Format data for submission
    const formattedData = {
      student_id: data?.student || null,
      interviewer_id: data?.interviewer || null,
      course_id: selectedCourse?.id || editData?.course_id || null,
      title: data.title,
      description: data.description || null,
      interview_date: data.date,
      interview_duration: parseInt(data.duration, 10) || null,
      interview_link: data.interviewLink || null,
      student_feedback: data.studentFeedback || null,
      interviewer_feedback: data.interviewFeedback || null,
      interview_grade: data.grade || null,
      interview_status: isStudent && !isEdit ? "REQUESTED" : data.status || "SCHEDULED",
    }

    if (isEdit && editData?.id) {
      // Make sure to include the id for PATCH operations
      updateInterview(
        { ...formattedData, id: editData.id },
        {
          onSuccess: () => {
            successToast("Successfully Updated!", "Interview was successfully Updated!")
            refetchInterviews()
            setSidebarOpen(false)
            setEditData(null)
            reset(getDefaultInterviewValues())
            setIsEdit(false)
            setShowRequestForm(false)
          },
          onError: (error) => {
            console.error("Error updating interview:", error)
          },
        }
      )
    } else {
      createInterview(formattedData, {
        onSuccess: () => {
          successToast("Successfully Created!", "Interview was successfully Created!")
          refetchInterviews()
          setSidebarOpen(false)
          setEditData(null)
          reset(getDefaultInterviewValues())
          setIsEdit(false)
          setShowRequestForm(false)
        },
        onError: (error) => {
          console.error("Error creating interview:", error)
          reset(getDefaultInterviewValues())
        },
      })
    }
  }

  const handleInterviewRequest = (requestData) => {
    console.log("handleInterviewRequest", requestData)

    // Format data for a basic request
    const formattedData = {
      student_id: requestData.student || null,
      interviewer_id: requestData.interviewer || null,
      course_id: selectedCourse?.id || null,
      title: requestData.title || "Mock Interview title",
      description: requestData.description || "Mock Interview description",
      interview_date: requestData.date || new Date(),
      interview_duration: parseInt(requestData.duration, 10) || 30,
      interview_status: "REQUESTED",
      interview_link: requestData.interviewLink || "link",
      student_feedback: requestData.studentFeedback || null,
      interviewer_feedback: requestData.interviewFeedback || null,
      interview_grade: requestData.grade || null,
    }

    console.log("Submitting request:", formattedData)
    createInterview(formattedData, {
      onSuccess: () => {
        console.log("Interview request created successfully")
        refetchInterviews()
        setSidebarOpen(false)
        setShowRequestForm(false)
        reset(getDefaultInterviewValues())
      },
      onError: (error) => {
        console.error("Error creating interview request:", error)
        reset(getDefaultInterviewValues())
      },
    })
  }

  const handleSidebarClose = () => {
    setSidebarOpen(false)
    setEditData(null)
    reset()
    setIsEdit(false)
    setShowRequestForm(false)
  }

  // Handle new interview button click
  const handleNewInterview = () => {
    setIsEdit(false)

    // For students, show the request form instead of the full form
    if (isStudent && selectedCourse) {
      setShowRequestForm(true)
      setSidebarOpen(true)
      return
    }

    reset({
      title: "",
      description: "",
      interviewer: "",
      student: "",
      date: null,
      duration: "",
      status: isStudent ? "REQUESTED" : "SCHEDULED",
      remarks: "",
      feedback: "",
      grade: "",
      interviewLink: "",
      interviewFeedback: "",
      studentFeedback: "",
    })
    setSidebarOpen(true)
  }

  const transformedBatchCourses = batchCourses.map((course) => ({
    id: course.id,
    name: course.title,
  }))
  const comboBoxData = isTrainerOrStudent ? transformedBatchCourses : courseOptions
  const comboBoxLoading = isTrainerOrStudent ? batchCourseLoading : batchCourseIdLoading

  // Create props for interview form
  const interviewFormProps = {
    form,
    control,
    onClose: handleSidebarClose,
    handleSubmit,
    onSubmit: handleFormSubmit,
    userRole,
    isEdit,
    getValues,
    setValue,
    courseOptions,
    selectedCourse,
    batchCourseIdLoading,
    setSelectedCourse,
    studentOptions,
    trainerOptions,
    batchCourses,
    batchCourseLoading,
    comboBoxData,
    comboBoxLoading,
    editData,
    userID,
    setStudentSearchQuery,
    setInterviewerSearchQuery,
  }

  // Create props for request form
  const requestFormProps = {
    selectedCourse,
    onSubmit: handleInterviewRequest,
    onClose: handleSidebarClose,
    trainerOptions,
  }

  return (
    <div className="p-4 sm:p-6 md:p-8">
      {/* Header Section */}
      <div className="flex flex-col gap-4 md:flex-row md:justify-between md:items-center mb-6">
        <h1 className="text-lg sm:text-xl md:text-2xl font-semibold">Mock Interviews</h1>

        <div className="flex flex-col md:flex-row md:items-center gap-3 w-full md:w-auto">
          {/* <div className="flex flex-col sm:flex-row sm:items-center gap-2 w-full sm:w-auto">
            <Label htmlFor="course-select" className="font-medium text-sm sm:text-base">
              Select Course:
            </Label>
            <CustomComboBox
              iterateData={comboBoxData}
              selectedValue={selectedCourse?.name}
              setSelectedValue={(course) => setSelectedCourse(course)}
              notFoundMessage="modules"
              placeholder="Select module..."
              width="w-full sm:min-w-[260px] md:min-w-[285px]"
              loading={comboBoxLoading}
            />
          </div> */}

          <Button
            variant="primary"
            className="rounded-md py-2 px-4 text-sm sm:py-3 sm:px-6 sm:text-base w-full sm:w-auto"
            onClick={handleNewInterview}
          >
            {isStudent ? "Request Interview" : "Schedule Interview"}
          </Button>
        </div>
      </div>

      {/* Cards Grid Section */}
      <ScrollArea className="h-[60vh] sm:h-[65vh]">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {mockInterviewData.map((interview) => (
            <InterviewCard key={interview.id} interview={interview} onClick={handleCardClick} />
          ))}
        </div>
      </ScrollArea>

      {/* Sidebar */}
      <CustomSidebar
        isOpen={sidebarOpen}
        onClose={handleSidebarClose}
        description=""
        title={
          isEdit
            ? "Edit Interview Details"
            : showRequestForm
              ? "Request a Mock Interview"
              : "Schedule a Mock Interview"
        }
        content={
          showRequestForm ? (
            <InterviewRequestForm {...requestFormProps} />
          ) : (
            <InterviewForm {...interviewFormProps} />
          )
        }
      />
    </div>
  )
}

export default MockInterview
