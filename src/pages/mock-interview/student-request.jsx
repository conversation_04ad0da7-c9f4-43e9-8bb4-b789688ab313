import FormInput from "@/components/custom/custom-forms/form-input"
import FormTextArea from "@/components/custom/custom-forms/form-textarea"
import { Button } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { ScrollArea } from "@/components/ui/scroll-area"
import ct from "@constants/"
import { zodResolver } from "@hookform/resolvers/zod"
import PropTypes from "prop-types"
import { useForm } from "react-hook-form"
import { useSelector } from "react-redux"
import { z } from "zod"

// Define schema for interview request validation - removed optional fields
const InterviewRequestSchema = z.object({
  title: z.string().min(3, { message: "Title must be at least 3 characters" }),
  description: z.string().optional(),
})

export const InterviewRequestForm = ({ selectedCourse, onSubmit, onClose }) => {
  const store = useSelector((st) => st[ct.store.USER_STORE])
  const form = useForm({
    resolver: zod<PERSON>esolver(InterviewRequestSchema),
    defaultValues: {
      title: "",
      description: "",
    },
    mode: "onChange",
  })

  const { handleSubmit, control } = form
  console.log("onFormSubmit", store)
  const onFormSubmit = (data) => {
    onSubmit({
      ...data,
      student: store?.id,
      course_id: selectedCourse?.id,
    })
  }

  return (
    <Form {...form}>
      <ScrollArea className="h-[calc(100vh-200px)]">
        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
          <div className="space-y-4">
            <div className="border p-4 rounded-md bg-blue-50">
              <p className="text-sm text-blue-700">
                You are requesting a mock interview for the course:{" "}
                <strong>{selectedCourse?.name}</strong>
              </p>
            </div>

            <FormInput
              placeholder="Enter interview request title"
              label="Title"
              fieldControlName="title"
              isRequired
              control={control}
            />

            <FormTextArea
              placeholder="Describe your interview request, skills you want to focus on, etc."
              label="Description"
              fieldControlName="description"
              control={control}
            />
          </div>

          <div className="flex justify-end gap-3 pt-6">
            <Button onClick={onClose} variant="secondary" type="reset">
              Cancel
            </Button>
            <Button type="submit" variant="primary">
              Request Interview
            </Button>
          </div>
        </form>
      </ScrollArea>
    </Form>
  )
}

InterviewRequestForm.propTypes = {
  selectedCourse: PropTypes.shape({
    id: PropTypes.number,
    name: PropTypes.string,
  }),
  onSubmit: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired,
}

export default InterviewRequestForm
