import CustomComboBox from "@/components/custom/custom-combo-box/custom-combo-box"
import AppointmentScheduler from "@/components/custom/custom-forms/date-time-picker"
import FormInput from "@/components/custom/custom-forms/form-input"
import FormSelect from "@/components/custom/custom-forms/form-select"
import FormTextArea from "@/components/custom/custom-forms/form-textarea"
import { Button } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { ScrollArea } from "@/components/ui/scroll-area"
import { USER_ROLES } from "@/utils/constants"
import moment from "moment"
import PropTypes from "prop-types"
import { useEffect, useState } from "react"
import { z } from "zod"

// Define schema for interview form validation
export const InterviewSchema = z.object({
  title: z.string().min(3, { message: "Title must be at least 3 characters" }),
  description: z
    .string()
    .min(15, { message: "Description must be at least 15 characters" })
    .optional(),
  interviewer: z.string().min(1, { message: "Interviewer is required" }).optional(),
  student: z.string().min(1, { message: "Student is required" }),
  date: z
    .preprocess(
      (val) => (typeof val === "string" ? new Date(val) : val),
      z.date({ required_error: "Date is Required" })
    )
    .refine((val) => moment(val).isSameOrAfter(moment(), "minute"), {
      message: "Date cannot be in the past",
    }),
  duration: z.string().min(1, { message: "Duration is required" }),
  status: z
    .enum(["SCHEDULED", "COMPLETED", "REQUESTED", "CANCELLED"], {
      message: "Please select a valid status",
    })
    .optional()
    .default("SCHEDULED"),
  remarks: z.string().optional(),
  feedback: z.string().optional(),
  grade: z.string().optional(),
  interviewLink: z
    .string()
    .url({ message: "Valid URL is required" })
    .optional()
    .or(z.string().length(0)),
  interviewFeedback: z.string().optional(),
  studentFeedback: z.string().optional(),
  selectedSkills: z.array(z.string()).optional(),
  overallRating: z.string().optional(),
})

// Status options for the dropdown - updated to match API enum values
const statusOptions = [
  { value: "SCHEDULED", label: "Scheduled" },
  { value: "COMPLETED", label: "Completed" },
  { value: "REQUESTED", label: "Requested" },
  { value: "CANCELLED", label: "Cancelled" },
]

// Grade options for the dropdown - updated to match API enum values
const gradeOptions = [
  { value: "EXCELLENT", label: "Excellent" },
  { value: "GOOD", label: "Good" },
  { value: "AVERAGE", label: "Average" },
  { value: "POOR", label: "Poor" },
]

// Interview form component
const InterviewForm = ({
  form,
  onSubmit,
  onClose,
  userRole,
  isEdit,
  control,
  getValues,
  handleSubmit,
  comboBoxData,
  selectedCourse,
  comboBoxLoading,
  setSelectedCourse,
  studentOptions,
  trainerOptions,
  setValue,
  editData,
  userID,
  setStudentSearchQuery,
  setInterviewerSearchQuery,
}) => {
  const [selectedStudentObj, setSelectedStudentObj] = useState(null)
  const [selectedInterviewerObj, setSelectedInterviewerObj] = useState(null)
  const canEditInterviewerFeedback =
    userRole === USER_ROLES.TRAINER || userRole === USER_ROLES.ADMIN
  const canEditStudentFeedback = userRole === USER_ROLES.STUDENT || userRole === USER_ROLES.ADMIN
  const canEditAll = userRole === USER_ROLES.ADMIN
  const canEditGradeAndLink = userRole === USER_ROLES.TRAINER || userRole === USER_ROLES.ADMIN
  const isStudent = userRole === USER_ROLES.STUDENT

  useEffect(() => {
    if (userID && userRole === "TRAINER") {
      setValue("interviewer", userID)
    }
  }, [userID, setValue])

  useEffect(() => {
    if (isEdit) {
      const studentName = editData?.student
        ? Object.values(studentOptions).find((s) => s.id === editData?.student)
        : ""
      const TrainerName = editData?.interviewer
        ? Object.values(trainerOptions).find((s) => s.id === editData?.interviewer)
        : ""

      setTimeout(() => {
        setSelectedStudentObj(studentName)
        setSelectedInterviewerObj(TrainerName)
      }, 100)
    }
  }, [editData, isEdit])

  const isCompletedInterview = getValues("status") === "COMPLETED"
  const shouldShowStudentFeedbackOnly = isStudent && isEdit && isCompletedInterview
  const shouldDisableAllFields =
    isCompletedInterview && (userRole === USER_ROLES.TRAINER || userRole === USER_ROLES.ADMIN)

  const buttonText = () => {
    switch (true) {
      case isEdit:
        return "Update"
      case isStudent:
        return "Request"
      default:
        return "Schedule"
    }
  }

  // Get currently selected values
  useEffect(() => {
    const studentId = getValues("student")
    const interviewerId = getValues("interviewer")

    if (studentId) {
      const foundStudent = studentOptions.find((s) => s.id.toString() === studentId.toString())
      if (foundStudent) {
        setSelectedStudentObj(foundStudent)
      }
    }

    if (interviewerId) {
      const foundInterviewer = trainerOptions.find(
        (t) => t.id.toString() === interviewerId.toString()
      )
      if (foundInterviewer) {
        setSelectedInterviewerObj(foundInterviewer)
      }
    }
  }, [getValues, studentOptions, trainerOptions])

  // Handle student selection
  const handleStudentSelection = (student) => {
    setValue("student", student.id.toString())
    setSelectedStudentObj(student)
  }

  // Handle interviewer selection
  const handleInterviewerSelection = (interviewer) => {
    setValue("interviewer", interviewer.id.toString())
    setSelectedInterviewerObj(interviewer)
  }

  // Special form for students with completed interviews
  if (shouldShowStudentFeedbackOnly) {
    return (
      <Form {...form}>
        <ScrollArea className="h-[calc(100vh-200px)]">
          <form
            onSubmit={handleSubmit(onSubmit, (errs) => {
              console.log("Validation errors:", errs)
            })}
            className="space-y-6"
          >
            <FormInput
              placeholder="Enter title"
              label="Title"
              fieldControlName="title"
              isRequired
              control={control}
              disabled={editData?.status === "COMPLETED"}
            />

            <FormTextArea
              placeholder="Interview description"
              label="Description"
              fieldControlName="description"
              control={control}
              disabled={editData?.status === "COMPLETED"}
            />

            <div className="space-y-4 pt-4">
              <FormTextArea
                placeholder="Enter your feedback about the interview..."
                label="Student Feedback"
                fieldControlName="studentFeedback"
                control={control}
                className="h-32"
              />
              <FormTextArea
                placeholder="Provide detailed feedback about the interview..."
                label="Interviewer Feedback"
                fieldControlName="interviewFeedback"
                control={control}
                className="h-32"
                disabled={!canEditInterviewerFeedback} // Only enable for trainers and admins
              />
            </div>

            <div className="flex justify-end gap-3 pt-6">
              <Button onClick={onClose} variant="secondary" type="reset">
                Cancel
              </Button>
              <Button type="submit" variant="primary">
                {buttonText()}
              </Button>
            </div>
          </form>
        </ScrollArea>
      </Form>
    )
  }

  return (
    <Form {...form}>
      <ScrollArea className="h-[calc(100vh-200px)]">
        <form
          onSubmit={handleSubmit(onSubmit, (errs) => {
            console.log("Validation errors:", errs)
          })}
          className="space-y-6"
        >
          {/* Only show course selection if not in edit mode */}
          {!isEdit && (
            <>
              <h2 className="font-medium text-sm -mb-4">Select Course</h2>
              <CustomComboBox
                iterateData={comboBoxData}
                selectedValue={selectedCourse?.name}
                setSelectedValue={(course) => setSelectedCourse(course)}
                notFoundMessage="modules"
                placeholder="Select module..."
                width="min-w-[320px]"
                loading={comboBoxLoading}
              />
            </>
          )}

          {/* Always show student selection for admin or show for trainers */}
          {(canEditAll || (userRole === USER_ROLES.TRAINER && !isStudent)) && (
            <>
              {/* Always show interviewer selection for admin or show for students */}
              {(canEditAll || (userRole === USER_ROLES.STUDENT && !isEdit)) && (
                <div>
                  <h2 className="font-medium text-sm">Select Interviewer</h2>
                  <CustomComboBox
                    setSearchQuery={setInterviewerSearchQuery}
                    selectedValue={selectedInterviewerObj?.name || ""}
                    setSelectedValue={handleInterviewerSelection}
                    iterateData={trainerOptions}
                    notFoundMessage="trainer"
                    placeholder="Select Interviewer..."
                    width="min-w-[320px]"
                    disabled={editData?.status === "COMPLETED"}
                  />
                </div>
              )}
              <div>
                <h2 className="font-medium text-sm">Select Student</h2>
                <CustomComboBox
                  setSearchQuery={setStudentSearchQuery}
                  selectedValue={selectedStudentObj?.name || ""}
                  setSelectedValue={handleStudentSelection}
                  iterateData={studentOptions}
                  notFoundMessage="Students"
                  placeholder="Select student..."
                  width="min-w-[320px]"
                  disabled={editData?.status === "COMPLETED"}
                />
              </div>
            </>
          )}
          {editData?.status !== "COMPLETED" && (
            <>
              <FormInput
                placeholder="Enter title"
                label="Title"
                fieldControlName="title"
                isRequired
                control={control}
                disabled={editData?.status === "COMPLETED"}
              />

              <FormTextArea
                placeholder="Enter interview description..."
                label="Description"
                fieldControlName="description"
                isRequired={!isStudent}
                control={control}
                disabled={editData?.status === "COMPLETED"}
              />
            </>
          )}

          {/* <div className="grid grid-cols-2 gap-4"> */}
          {editData?.status !== "COMPLETED" && (
            <>
              {userRole !== USER_ROLES.STUDENT && (
                <>
                  <AppointmentScheduler
                    fieldControlName="date"
                    control={control}
                    label="Date"
                    placeholder="Pick a date"
                    futureDateDisabled={null}
                  />

                  <FormInput
                    placeholder="Enter duration in minutes"
                    label="Duration (minutes)"
                    fieldControlName="duration"
                    control={control}
                    isTypeNumer
                    disabled={editData?.status === "COMPLETED"}
                  />
                </>
              )}
              {/* Only show meeting link for trainers and admins */}
              {canEditGradeAndLink && (
                <FormInput
                  placeholder="https://meet.google.com/..."
                  label="Meeting Link"
                  fieldControlName="interviewLink"
                  control={control}
                  disabled={editData?.status === "COMPLETED"}
                />
              )}
            </>
          )}
          {/* </div> */}
          {/* Only show grade field for trainers and admins in EDIT mode */}
          {canEditGradeAndLink && isEdit && (
            <FormSelect
              fieldControlName="grade"
              control={control}
              label="Grade"
              getValues={getValues}
              iterateData={gradeOptions}
              placeholder="Select grade"
              disabled={editData?.status === "COMPLETED"}
            />
          )}

          {/* Feedback sections based on user role and edit mode */}
          {isEdit && (
            <div className="border-t pt-6">
              {/* Show interviewer feedback field for trainers and admins in edit mode */}

              <div>
                <FormTextArea
                  placeholder="Provide detailed feedback about the interview..."
                  label="Interviewer Feedback"
                  fieldControlName="interviewFeedback"
                  control={control}
                  className="h-32"
                  disabled={userRole === USER_ROLES.STUDENT || editData?.status === "COMPLETED"}
                />
              </div>

              {/* Show student feedback section for everyone in edit mode */}
              <div className="pt-4">
                {editData?.status === "COMPLETED" && (
                  <FormTextArea
                    placeholder="Enter your feedback about the interview..."
                    label="Student Feedback"
                    fieldControlName="studentFeedback"
                    control={control}
                    className="h-32"
                    disabled={userRole !== USER_ROLES.STUDENT}
                  />
                )}
              </div>

              {/* Only show status field for trainers and admins */}
              {(userRole === USER_ROLES.TRAINER || userRole === USER_ROLES.ADMIN) && (
                <FormSelect
                  fieldControlName="status"
                  control={control}
                  label="Status"
                  getValues={getValues}
                  iterateData={statusOptions}
                  placeholder="Select status"
                  disabled={editData?.status === "COMPLETED"}
                />
              )}
            </div>
          )}

          {/* Button section */}
          <div className="flex justify-end gap-3 pt-6">
            <Button onClick={onClose} variant="secondary" type="reset">
              Cancel
            </Button>
            <div
              title={
                editData?.status === "COMPLETED" ? "Cannot edit if interview is completed " : ""
              }
              className={editData?.status === "COMPLETED" ? "cursor-not-allowed" : ""}
            >
              <Button
                disabled={editData?.status === "COMPLETED"}
                type="submit"
                variant="primary"
                className={editData?.status === "COMPLETED" ? "bg-gray-400 hover:bg-gray-400" : ""}
              >
                {buttonText()}
              </Button>
            </div>
          </div>
        </form>
      </ScrollArea>
    </Form>
  )
}

InterviewForm.propTypes = {
  form: PropTypes.shape({
    title: PropTypes.string,
    description: PropTypes.string,
  }).isRequired,
  comboBoxData: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      name: PropTypes.string.isRequired,
    })
  ).isRequired,
  comboBoxLoading: PropTypes.bool.isRequired,
  onSubmit: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired,
  userRole: PropTypes.string.isRequired,
  editData: PropTypes.shape({
    interviewer: PropTypes.string,
    student: PropTypes.string,
  }),
  isEdit: PropTypes.bool.isRequired,
  control: PropTypes.shape({
    register: PropTypes.func,
    watch: PropTypes.func,
    handleSubmit: PropTypes.func,
  }).isRequired,
  getValues: PropTypes.func.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  setValue: PropTypes.func.isRequired,
  courseOptions: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      name: PropTypes.string.isRequired,
    })
  ),
  selectedCourse: PropTypes.shape({
    id: PropTypes.number,
    name: PropTypes.string,
  }),
  batchCourseIdLoading: PropTypes.bool,
  setSelectedCourse: PropTypes.func.isRequired,
  studentOptions: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
      name: PropTypes.string.isRequired,
    })
  ).isRequired,
  trainerOptions: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
      name: PropTypes.string.isRequired,
    })
  ).isRequired,
}

export default InterviewForm
