export const mockJobData = {
  id: "jd123",
  title: "Senior Frontend Developer",
  company_name: "TechCorp Solutions",
  location: "San Francisco, CA",
  min_work_exp: 5,
  max_work_exp: 8,
  skills: "React.js, TypeScript, Next.js, GraphQL, CSS3, JavaScript",
  salary: "$120,000 - $150,000",
  modified_at: "2024-01-15T10:30:00Z",
  application_count: 45,
  post_link: "https://example.com/job-post",
  jd_summary: `We are looking for a Senior Frontend Developer to join our dynamic team. The ideal candidate will have:

• Strong experience with React.js and modern JavaScript
• Expertise in building responsive and performant web applications
• Experience with state management solutions (Redux, MobX, etc.)
• Knowledge of modern frontend build tools and workflows
• Strong problem-solving abilities and attention to detail

Responsibilities:
- Lead frontend development initiatives
- Mentor junior developers
- Implement best practices and coding standards
- Collaborate with UI/UX designers and backend teams
- Optimize application performance

Benefits:
- Competitive salary and equity
- Health, dental, and vision insurance
- Flexible work hours and remote options
- Professional development budget
- Regular team events and activities`,
  meta: {
    "Employment Type": "Full-time",
    Industry: "Technology",
    Education: "Bachelor's Degree",
    "Remote Work": "Hybrid",
  },
}
