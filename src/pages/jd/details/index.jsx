import CustomTabs from "@/components/custom/custom-tabs"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useJdDetailsJDs } from "@/services/query/jd.query"
import { USER_ROLES } from "@/utils/constants"
import ct from "@constants/"
import { ArrowUpRight, Briefcase, Calendar, Hash, IndianRupee, Loader, MapPin } from "lucide-react"
import { useState } from "react"
import { useSelector } from "react-redux"
import { useLocation } from "react-router-dom"
import CandidatesTab from "./tabs/candidates-tab"
import DescriptionTab from "./tabs/description-tab"
import RelationshipTab from "./tabs/relationship-tab"

// Helper function to format salary
const formatSalary = (salary) => {
  if (!salary) return "Not specified"
  return `₹${salary.toLocaleString()}/-`
}

export default function JDDetailsPage() {
  const location = useLocation()
  const id = location.state?.jobId
  const userRole = useSelector((st) => st[ct.store.USER_STORE]?.userRole)
  const [activeTab, setActiveTab] = useState("description")

  const { data: jobDetailsResponse, isLoading: isLoadingJD } = useJdDetailsJDs(id)

  // Map the API response to match the expected format
  const metaData = jobDetailsResponse?.metadata
  const jobData = jobDetailsResponse?.data
  const relationshipData = jobDetailsResponse
    ? {
      ...jobDetailsResponse.data,
      min_work_exp: jobDetailsResponse.data.min_experience,
      max_work_exp: jobDetailsResponse.data.max_experience,
      salary: formatSalary(jobDetailsResponse.data.salary),
      application_count: jobDetailsResponse.metadata?.total_record || 0,
      employment_type: jobDetailsResponse.data.type,
      modified_at: jobDetailsResponse.data.meta?.modified_at || new Date().toISOString(),
    }
    : null
  console.log("relationshipData", relationshipData)

  if (isLoadingJD) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gradient-to-br from-violet-50 to-white">
        <Loader className="animate-spin text-violet-600 w-12 h-12" />
      </div>
    )
  }

  if (!jobData) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gradient-to-br from-violet-50 to-white">
        <div className="text-center space-y-4">
          <h2 className="text-3xl font-bold text-gray-700">JD Not Found</h2>
          <p className="text-gray-500">
            The JD details you&apos;re looking for might have been removed or is temporarily
            unavailable.
          </p>
          <Button variant="primary" onClick={() => window.history.back()}>
            Back to JDs
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-gradient-to-br from-violet-50 to-white py-12 px-4 sm:px-6 lg:px-8">
      <ScrollArea className="h-[70vh]">
        <div className="mx-auto space-y-8">
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
            {/* Job Overview Card */}
            <Card className="xl:col-span-2 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300 dark:border-gray-800">
              <div className="p-6 space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-3xl font-bold text-gray-800 dark:text-gray-100 flex items-center gap-3">
                      {relationshipData.title}
                      <Badge variant="secondary" className="text-xs">
                        {relationshipData.employment_type}
                      </Badge>
                    </h2>
                    <p className="text-gray-600 dark:text-gray-400 mt-2 flex items-center gap-2">
                      <Briefcase className="w-4 h-4" />
                      {relationshipData.company_name} •
                      <MapPin className="w-4 h-4" />
                      {relationshipData.location}
                    </p>
                  </div>
                  {relationshipData.post_link && (
                    <Button
                      variant="outline"
                      className="group"
                      onClick={() => window.open(relationshipData.post_link, "_blank")}
                    >
                      View Post
                      <ArrowUpRight className="ml-2 w-4 h-4 group-hover:rotate-45 transition-transform" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                  <div className="bg-violet-50 p-3 rounded-lg">
                    <div className="flex items-center gap-2 text-violet-600">
                      <Calendar className="w-4 h-4" />
                      <span className="text-xs font-semibold">Experience</span>
                    </div>
                    <p className="font-bold text-sm mt-1">
                      {relationshipData.min_work_exp} - {relationshipData.max_work_exp} years
                    </p>
                  </div>
                  <div className="bg-green-50 p-3 rounded-lg">
                    <div className="flex items-center  text-green-600">
                      <IndianRupee className="w-4 h-4" />
                      <span className="text-xs font-semibold">Salary</span>
                    </div>
                    <p className="font-bold text-sm mt-1">{relationshipData.salary}</p>
                  </div>
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <div className="flex items-center gap-2 text-blue-600">
                      <Hash className="w-4 h-4" />
                      <span className="text-xs font-semibold">Applications</span>
                    </div>
                    <p className="font-bold text-sm mt-1">
                      {relationshipData.application_count} Candidates
                    </p>
                  </div>
                  <div className="bg-orange-50 p-3 rounded-lg">
                    <div className="flex items-center gap-2 text-orange-600">
                      <Calendar className="w-4 h-4" />
                      <span className="text-xs font-semibold">Posted On</span>
                    </div>
                    <p className="font-bold text-sm mt-1">
                      {new Date(relationshipData.modified_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                <div className="mt-6">
                  <h5 className="font-semibold mb-3 text-gray-700 dark:text-gray-200">
                    Required Skills
                  </h5>
                  <div className="flex flex-wrap gap-2">
                    {relationshipData?.skills?.split(",")?.map((skill) => (
                      <Badge
                        key={skill}
                        variant="secondary"
                        className="bg-blue-50 text-primary border border-violet-200 dark:bg-gray-800 dark:text-violet-400 px-2 py-0.5"
                      >
                        {skill.trim()}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </Card>

            {/* Additional Job Details Card */}
            <Card className="xl:col-span-1 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300 dark:border-gray-800">
              <CardHeader>
                <CardTitle className="text-xl font-bold">Job Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {relationshipData.company_website && (
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium text-gray-700">Company Website</span>
                    <a
                      href={relationshipData.company_website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      Visit Website
                    </a>
                  </div>
                )}
                {Object.entries(relationshipData.meta || {}).map(([key, value]) => (
                  <div
                    key={key}
                    className="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
                  >
                    <span className="font-medium text-gray-700">{key}</span>
                    <span className="text-gray-600 font-semibold">{value}</span>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Tabs Section */}
          <CustomTabs
            tabs={[
              {
                id: "description",
                label: "Job Description",
                content: <DescriptionTab jobData={jobData} />,
              },
              {
                id: "candidates",
                label: userRole === USER_ROLES.STUDENT ? "Relationships" : "Candidates",
                content:
                  userRole === USER_ROLES.STUDENT ? (
                    <RelationshipTab metaData={metaData} relationshipData={relationshipData} />
                  ) : (
                    <CandidatesTab jobId={id} CandidateData={relationshipData?.candidates} />
                  ),
              },
            ]}
            defaultTab="description"
            size="w-full"
            setActiveTab={setActiveTab}
            activeTab={activeTab}
          />
        </div>
      </ScrollArea>
    </div>
  )
}
