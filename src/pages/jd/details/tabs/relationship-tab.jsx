import { Card } from "@/components/ui/card"
import PropTypes from "prop-types"
import DataTable from "@/components/custom/cutsom-table"
import useTableConfig from "@/hooks/use-table.hooks"
import { flexRender } from "@tanstack/react-table"
import { DateCell, LinkCell } from "@/components/custom/cutsom-table/table-cells"
import { useState } from "react"
import { columns } from "./relationship-columns"

export default function RelationshipTab({ relationshipData }) {
  console.log("jobDetailsResponse", relationshipData?.relations)
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })

  const renderCellContent = (cell, row) => {
    const { updated_at, linkedin } = row?.original || {}
    switch (cell.column.id) {
      case "updated_at":
        return <DateCell value={updated_at} />

      case "linkedin":
        return linkedin ? <LinkCell value={linkedin} type="linkedin" /> : null
      default:
        return flexRender(cell.column.columnDef.cell, cell.getContext())
    }
  }

  const { table, found, pageCount } = useTableConfig(
    relationshipData?.relations,
    columns,
    relationshipData?.relations?.length,
    setPagination,
    pagination
  )

  if (!relationshipData.relations || relationshipData.relations.length === 0) {
    return (
      <Card className="p-8 bg-white rounded-2xl">
        <div className="flex justify-center items-center h-40">
          <p>No relationships available</p>
        </div>
      </Card>
    )
  }

  return (
    <Card className="p-8 bg-white rounded-2xl">
      <h2 className="text-2xl font-medium mb-5">Relationship Details</h2>
      <div className="w-full">
        <DataTable
          renderCellContent={renderCellContent}
          columns={columns}
          table={table}
          found={found}
          pageCount={pageCount}
          pagination={pagination}
        />
      </div>
    </Card>
  )
}
RelationshipTab.propTypes = {
  relationshipData: PropTypes.shape({
    relations: PropTypes.arrayOf(
      PropTypes.shape({
        updated_at: PropTypes.string,
        linkedin: PropTypes.string,
      })
    ),
  }).isRequired,
}
