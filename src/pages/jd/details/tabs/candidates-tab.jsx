import { useState, useMemo, useEffect } from "react"
import PropTypes from "prop-types"
import { But<PERSON> } from "@/components/ui/button"
import CandidateCard from "@/components/cv-card"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { Search, RefreshCw } from "lucide-react"
import { Input } from "@/components/ui/input"
import { motion, AnimatePresence } from "framer-motion"
import { Label } from "@/components/ui/label"
import { useJdDetailsJDs } from "@/services/query/jd.query"

function CandidatesTab({ jobId, initialCandidateData }) {
  const [candidateFilter, setCandidateFilter] = useState("recommended")
  const [currentPage, setCurrentPage] = useState(1)
  const [searchTerm, setSearchTerm] = useState("")
  const [sortBy] = useState("relevance")

  // Map the filter state to the API's expected job_type values
  const getJobTypeForApi = (filter) => {
    if (filter === "applied") return "APPLIED"
    if (filter === "recommended") return "RECOMMENDED"
    return "RECOMMENDED"
  }

  // Use the custom hook with the job_type based on current filter
  const {
    data: jobDetailsResponse,
    isLoading,
    refetch,
  } = useJdDetailsJDs(jobId, getJobTypeForApi(candidateFilter))

  // Get candidates from either the prop or the fetched data
  const [candidates, setCandidates] = useState([])
  console.log("JDCard", candidates)
  useEffect(() => {
    if (jobDetailsResponse?.data?.candidates) {
      // Transform API response to match CandidateCard expected fields
      const transformedCandidates = jobDetailsResponse.data.candidates.map((candidate) => ({
        id: candidate.student_id,
        name: candidate.name,
        email: candidate.email,
        distance: candidate.distance || 1,
        primary_skills: candidate.primary_skills,
        secondary_skills: candidate.secondary_skills,
        designation: candidate.title,
        company: candidate.job_role,
        total_years_of_exp: candidate.total_experience,
        cv_link: candidate.cv_link,
        modified_at: candidate.modified_at,
        applied_at: candidate.applied_at,
        resume_id: candidate.resume_id,
      }))
      setCandidates(transformedCandidates)
    } else if (initialCandidateData) {
      // Use the initial data if API data is not available
      setCandidates(
        initialCandidateData.map((candidate) => ({
          id: candidate.student_id,
          name: candidate.name,
          email: candidate.email,
          distance: candidate.distance || 1,
          primary_skills: candidate.primary_skills,
          secondary_skills: candidate.secondary_skills,
          designation: candidate.title,
          company: candidate.job_role,
          total_years_of_exp: candidate.total_experience,
          cv_link: candidate.cv_link,
          modified_at: candidate.modified_at,
          applied_at: candidate.applied_at,
        }))
      )
    }
  }, [jobDetailsResponse, initialCandidateData])

  const itemsPerPage = 10
  const totalItems = candidates.length
  const totalPages = Math.ceil(totalItems / itemsPerPage)

  const filteredAndSortedCandidates = useMemo(() => {
    return candidates
      .filter(
        (candidate) =>
          candidate.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          candidate.primary_skills.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .sort((a, b) => {
        switch (sortBy) {
          case "experience":
            return b.total_years_of_exp - a.total_years_of_exp
          case "name":
            return a.name.localeCompare(b.name)
          default:
            return 0
        }
      })
  }, [candidates, searchTerm, sortBy])

  const paginatedCandidates = filteredAndSortedCandidates.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  const handlePageChange = (page, e) => {
    e.preventDefault()
    setCurrentPage(page)
  }

  const handleCVAppliedClicked = (candidateId, isApplied) => {
    console.log(`Candidate ${candidateId} applied status: ${isApplied}`)
  }

  const handleRefresh = () => {
    const icon = document.querySelector(".refresh-icon")
    if (icon) {
      icon.classList.add("animate-spin")
      refetch().then(() => {
        setTimeout(() => icon.classList.remove("animate-spin"), 1000)
      })
    }
  }
  const handleFilterChange = (filter) => {
    handleRefresh()
    setCandidateFilter(filter)
    // Reset to first page when changing filters
    setCurrentPage(1)
  }
  return (
    <div className="bg-white shadow-sm border border-gray-100 rounded-3xl p-6 space-y-6">
      {/* Filter and Search Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 justify-between">
        <div className="relative flex gap-2">
          <Label className="text-md font-semibold mt-2 text-gray-500">Search :</Label>
          <Input
            icon={<Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />}
            placeholder="Search candidates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 focus:border-primary focus:ring-2 w-auto focus:ring-primary/20 transition-all"
          />
        </div>

        <div className="flex gap-4 items-center justify-end">
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button
              variant={candidateFilter === "applied" ? "primary-outline" : "secondary"}
              className="text-sm cursor-pointer transition-all hover:bg-primary/10"
              onClick={() => handleFilterChange("applied")}
            >
              Applied
            </Button>
          </motion.div>
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button
              variant={candidateFilter === "recommended" ? "primary-outline" : "secondary"}
              className="text-sm cursor-pointer transition-all hover:bg-primary/10"
              onClick={() => handleFilterChange("recommended")}
            >
              Recommended
            </Button>
          </motion.div>
        </div>
      </div>

      {/* Candidates Summary */}
      <div className="flex justify-between items-center bg-gray-50 rounded-xl p-4">
        <p className="text-sm text-gray-600 font-medium">
          Showing {paginatedCandidates.length} of {totalItems} candidates
        </p>
        <Button
          variant="ghost"
          size="sm"
          className="text-primary hover:text-primary"
          onClick={handleRefresh}
        >
          <RefreshCw className="h-4 w-4 mr-2 refresh-icon" />
          Refresh List
        </Button>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex justify-center items-center py-12">
          <RefreshCw className="h-6 w-6 animate-spin text-primary" />
          <span className="ml-2 text-gray-600">Loading candidates...</span>
        </div>
      )}

      {/* Empty State */}
      {!isLoading && paginatedCandidates.length === 0 && (
        <div className="flex flex-col justify-center items-center py-12 bg-gray-50 rounded-xl">
          <p className="text-gray-500 mb-4">No candidates found</p>
        </div>
      )}

      {/* Candidates Grid */}
      {!isLoading && paginatedCandidates.length > 0 && (
        <AnimatePresence>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6"
          >
            {paginatedCandidates.map((candidate) => (
              <motion.div
                key={candidate.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                <CandidateCard
                  candidate={candidate}
                  jobId={jobId}
                  handleCVAppliedClicked={handleCVAppliedClicked}
                  showApplied
                  onArchiveClicked={(id) => console.log(`Archive clicked for ${id}`)}
                  candidateFilter={candidateFilter}
                  refetch={refetch}
                />
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-6 flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={(e) => handlePageChange(currentPage - 1, e)}
                  disabled={currentPage === 1}
                />
              </PaginationItem>

              {Array.from({ length: Math.min(totalPages, 5) }).map((_, idx) => (
                <PaginationItem key={`page-${idx + 1}`}>
                  <PaginationLink
                    onClick={(e) => handlePageChange(idx + 1, e)}
                    isActive={currentPage === idx + 1}
                  >
                    {idx + 1}
                  </PaginationLink>
                </PaginationItem>
              ))}

              {totalPages > 5 && currentPage < totalPages - 2 && (
                <PaginationItem>
                  <span className="px-2 text-gray-500">...</span>
                </PaginationItem>
              )}

              <PaginationItem>
                <PaginationNext
                  onClick={(e) => handlePageChange(currentPage + 1, e)}
                  disabled={currentPage === totalPages}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  )
}

CandidatesTab.propTypes = {
  jobId: PropTypes.string.isRequired, // Make jobId required
  initialCandidateData: PropTypes.arrayOf(
    PropTypes.shape({
      student_id: PropTypes.number.isRequired,
      name: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      distance: PropTypes.number,
      primary_skills: PropTypes.string.isRequired,
      secondary_skills: PropTypes.string,
      title: PropTypes.string.isRequired,
      job_role: PropTypes.string,
      total_experience: PropTypes.number.isRequired,
      cv_link: PropTypes.string,
      modified_at: PropTypes.string,
      applied_at: PropTypes.string,
    })
  ),
}

export default CandidatesTab
