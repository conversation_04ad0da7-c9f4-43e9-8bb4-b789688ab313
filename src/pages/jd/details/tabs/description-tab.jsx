import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { FileText, Target, Layers, Lightbulb } from "lucide-react"
import ReactMarkdown from "react-markdown"
import { <PERSON>rism as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-syntax-highlighter"
import { materialDark } from "react-syntax-highlighter/dist/esm/styles/prism"
import remarkGfm from "remark-gfm"
import PropTypes from "prop-types"

export default function DescriptionTab({ jobData }) {
  // Transform the job description and skills into structured markdown
  const enhancedMarkdown = `
## Job Overview
${jobData.description || "No job description provided."}
### Required Skills
${
  jobData.skills
    ?.split(",")
    ?.map((skill) => `- ${skill.trim()}`)
    .join("\n") || "No specific skills listed."
}

### Additional Information
> This is an exciting opportunity to grow and innovate in a dynamic technology environment.
  `.trim()

  return (
    <Card className="shadow-sm hover:shadow-md transition-shadow duration-300">
      <CardHeader className="pb-0">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-3">
            <FileText className="h-6 w-6 text-primary" />
            Job Description
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent className="p-6 pt-4">
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          components={{
            h2: ({ node, ...props }) => (
              <h2
                className="text-xl font-bold text-primary mt-6 mb-4 flex items-center gap-2 border-b pb-2"
                {...props}
              >
                <Lightbulb className="h-5 w-5 text-primary/70" />
                {props.children}
              </h2>
            ),
            h3: ({ node, ...props }) => (
              <h3 className="text-lg font-semibold text-gray-800 mt-4 mb-3" {...props} />
            ),
            p: ({ node, ...props }) => (
              <p className="text-gray-600 leading-relaxed mb-4" {...props} />
            ),
            ul: ({ node, ...props }) => (
              <ul className="list-disc list-inside space-y-2 pl-4 text-gray-600 mb-4" {...props} />
            ),
            ol: ({ node, ...props }) => (
              <ol
                className="list-decimal list-inside space-y-2 pl-4 text-gray-600 mb-4"
                {...props}
              />
            ),
            blockquote: ({ node, ...props }) => (
              <blockquote
                className="border-l-4 border-primary/50 pl-4 py-2 my-4 bg-gray-50 italic text-gray-700"
                {...props}
              />
            ),
            code: ({ node, inline, className, children, ...props }) => {
              const match = /language-(\w+)/.exec(className || "")
              return !inline && match ? (
                <SyntaxHighlighter style={materialDark} language={match[1]} PreTag="div" {...props}>
                  {String(children).replace(/\n$/, "")}
                </SyntaxHighlighter>
              ) : (
                <code className="bg-gray-100 rounded px-1.5 py-0.5 text-sm font-mono" {...props}>
                  {children}
                </code>
              )
            },
          }}
        >
          {enhancedMarkdown}
        </ReactMarkdown>
      </CardContent>
    </Card>
  )
}

DescriptionTab.propTypes = {
  jobData: PropTypes.shape({
    description: PropTypes.string,
    skills: PropTypes.string,
    type: PropTypes.string,
  }).isRequired,
}
