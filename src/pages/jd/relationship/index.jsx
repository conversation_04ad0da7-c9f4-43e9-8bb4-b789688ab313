import CustomSearchbar from "@/components/custom/custom-search"
import DataTable from "@/components/custom/cutsom-table"
import { ActionCell, LinkCell } from "@/components/custom/cutsom-table/table-cells"
import { DeleteDialog } from "@/components/custom/dialogs/delete.dialog"
import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import {
  useDeleteDialogHooks,
  useFilterHooks,
  useIsUpdateHook,
  useOpenCloseHooks,
  usePaginationHooks,
} from "@/hooks/common.hooks"
import useTableConfig from "@/hooks/use-table.hooks"
import { useDebounce } from "@/hooks/useDebounce"
import CustomSidebar from "@/pages/courses/components/custom-sidebar"
import {
  useCreateLeads,
  useDeleteLead,
  useFetchLeads,
  useUpdateLead,
  useUploadLeadFile,
} from "@/services/query/leads-management.query"
import ct from "@constants/"
import { zodResolver } from "@hookform/resolvers/zod"
import { flexRender } from "@tanstack/react-table"
import { Plus } from "lucide-react"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { useSelector } from "react-redux"
import { useLocation, useNavigate } from "react-router-dom"
import AddLeadsSideBar from "./add-leads-side-bar"
import { columns } from "./columns"
import { leadHeadings } from "./utils/helper"
import { defaultLeadValues, leadsSchema } from "./utils/leads.schema"

const RelationshipPage = () => {
  const userRole = useSelector((st) => st[ct.store.USER_STORE]?.userRole)

  const [searchingValue, setSearchingValue] = useState("")
  const debouncedSearchQuery = useDebounce(searchingValue, 500)
  const { handleOpen, handleClose, open } = useOpenCloseHooks()
  const { isUpdate, handleResetUpdate, handleUpdate } = useIsUpdateHook()
  const { isDelete, handleDelete, handleCancel } = useDeleteDialogHooks(false)
  const { sortBy, sortByField } = useFilterHooks()
  const { limit } = usePaginationHooks()
  const [file, setFile] = useState(null)

  const location = useLocation()
  const navigate = useNavigate()

  const [leadIDs, setLeadIDs] = useState({
    //   module_id: location?.state?.module_id,
    //   course_id: location?.state?.course_id,
    lead_id: 2,
  })

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })

  const calculateOffset = (pageIndex, pageSize) => pageIndex * pageSize

  const form = useForm({
    resolver: zodResolver(leadsSchema),
    defaultValues: defaultLeadValues,
    mode: "onChange",
  })

  const { mutate: CreateLeads } = useCreateLeads()
  const { mutate: upLoadLeads } = useUploadLeadFile()
  const { mutate: deleteLead } = useDeleteLead()
  const { mutate: EditLead } = useUpdateLead()

  const { data: leadsdata, isLoading } = useFetchLeads({
    filters: {
      limit,
      offSet: calculateOffset(pagination.pageIndex, pagination.pageSize),
      sort_by: sortBy,
      sort_by_field: sortByField,
      search_query: debouncedSearchQuery?.length > 0 ? debouncedSearchQuery : null,
    },
  })

  const {
    handleSubmit,
    control,
    reset,
    setValue,
    formState: { errors },
    getValues,
  } = form

  console.log("_errors", errors, getValues())

  const handleCancelForm = () => {
    handleClose()
    reset({ name: "", email: "", designation: "", phone: "", company_name: "", linkedin: "" })
  }
  const handleCreateClass = () => {
    handleResetUpdate()
    reset({ name: "", email: "", designation: "", phone: "", company_name: "", linkedin: "" })
    handleOpen()
  }

  const handleCreateClassForm = async (data) => {
    console.log("handleLeadsCreation", data)

    if (isUpdate) {
      EditLead(
        {
          leadId: leadIDs?.lead_id,
          updatedData: data,
        },
        {
          onSuccess: () => {
            handleClose()
            successToast("Successfully Edited!", "Lead was successfully Edited!")
            handleResetUpdate()
          },
          onError: (error) => {
            handleClose()
            failureToast("Failed to update lead")
            handleResetUpdate()
          },
        }
      )
    } else {
      CreateLeads(
        { data },
        {
          onSuccess: () => {
            handleClose()
            successToast("Successfully Created!", "Lead was successfully created!")
            handleResetUpdate()
          },
          onError: (error) => {
            handleResetUpdate()
            failureToast("Something went wrong, please try again")
            handleClose()
          },
        }
      )
    }
  }

  const handleEdit = (row) => {
    const { id, ...rest } = row?.original ?? {}
    setLeadIDs({ ...leadIDs, lead_id: id })
    handleUpdate()
    reset({ ...rest })
    handleOpen()
  }

  const handleDeleteLead = (data) => {
    setLeadIDs({ ...leadIDs, lead_id: data?.original?.id })
    handleDelete()
  }

  const handleUploadFile = async () => {
    console.log(file[0], "filefile")
    upLoadLeads(file[0], {
      onSuccess: () => {
        handleCancelForm()
        successToast("File uploaded successfully")
      },
      onError: (error) => {
        handleCancelForm()
        failureToast("Failed to upload file")
      },
    })
  }

  const handleConfirmDelete = () => {
    console.log(leadIDs.lead_id, "Attempting to delete lead")
    deleteLead(
      { leadId: leadIDs.lead_id },
      {
        onSuccess: () => {
          handleClose()
          successToast("Lead deleted successfully", "The lead has been removed.")
          handleCancel()
        },
        onError: (error) => {
          handleClose()
          failureToast("Deletion Error", "Unable to delete the lead. Please try again later.")
          handleCancel()
        },
      }
    )
  }

  const addLeadsProps = {
    form,
    control,
    onClose: handleCancelForm,
    onCreateClassForm: handleCreateClassForm,
    handleSubmit,
    setValue,
    setFile,
    file,
    handleUploadFile,
    isUpdate,
    userRole,
  }

  const renderCellContent = (cell, row) => {
    const { name, email, phone, linkedin, designation, company_name } = row?.original || {}

    switch (cell.column.id) {
      case "name":
        return <p>{name}</p>

      case "email":
        return <p>{email}</p>
      case "designation":
        return <p>{designation}</p>

      case "phonenumber":
        return <p>{phone}</p>
      case "companyname":
        return <p>{company_name}</p>
      case "linkedin":
        return <LinkCell value={linkedin} />
      case "actions":
        return (
          <ActionCell
            label2="Edit"
            label3="Delete"
            isEdit
            isDelete
            onDelete={() => handleDeleteLead(row)}
            onEdit={() => handleEdit(row)}
          />
        )
      default:
        return flexRender(cell.column.columnDef.cell, cell.getContext())
    }
  }

  const { table, found, pageCount } = useTableConfig(
    leadsdata?.data?.data,
    columns,
    leadsdata?.data?.metadata?.total_record,
    setPagination,
    pagination
  )

  const handleRowClick = (row) => {
    navigate(`/leads-management/relationship`, {
      state: {
        id: row.original.id,
        company_name: row.original.company_name,
        designation: row.original.designation,
        email: row.original.email,
        name: row.original.name,
      },
    })
  }

  return (
    <div>
      <DeleteDialog
        onOpen={isDelete}
        onDelete={handleConfirmDelete}
        onCancel={handleCancel}
        title="Delete Leads"
        content="Are you sure want to delete this Lead?"
      />
      <CustomSidebar
        isOpen={open}
        onClose={handleClose}
        title={leadHeadings(userRole, isUpdate, "Update Leads", "Create Leads")}
        description=""
        content={<AddLeadsSideBar {...addLeadsProps} />}
      />
      <Card className="p-8 bg-white rounded-2xl">
        <div className="flex justify-between items-center mb-6">
          <div className="flex flex-col gap-2">
            <h1 className="text-xl font-medium text-primary">Lead Management</h1>
            <CustomSearchbar
              inputSize="w-[20rem]"
              placeholder="Search by title..."
              searchedValue={searchingValue}
              setSearchedValue={(e) => setSearchingValue(e?.target.value)}
            />
          </div>
          <Button variant="primary" onClick={handleCreateClass}>
            <Plus className="" />
            Add Lead
          </Button>
        </div>
        <div className="w-full">
          <DataTable
            renderCellContent={renderCellContent}
            columns={columns}
            table={table}
            found={found || 0}
            pageName="Leads"
            pageCount={pageCount}
            pagination={pagination}
            onRowClick={handleRowClick}
            isLoading={isLoading}
            notFoundPlaceholder="Leads Not Found"
          />
        </div>
      </Card>
    </div>
  )
}

export default RelationshipPage
