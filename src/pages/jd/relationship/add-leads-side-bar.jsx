import FormInput from "@/components/custom/custom-forms/form-input"
import FileUploadCard from "@/components/custom/upload-file"
import { But<PERSON> } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { Switch } from "@/components/ui/switch" // ✅ Import Toggle Switch
import PropTypes from "prop-types"
import { useEffect, useState } from "react"

const AddLeadsSideBar = ({
  form,
  control,
  onClose,
  onCreateClassForm,
  handleSubmit,
  setValue,
  editData,
  setFile,
  file,
  handleUploadFile,
}) => {
  const [formView, setFormView] = useState(true) // ✅ Toggle between form and upload

  useEffect(() => {
    if (editData) {
      Object.keys(editData).forEach((key) => {
        setValue(key, editData[key]) // Prefill the form with existing data
      })
    }
  }, [editData, setValue])

  return (
    <div>
      <div className="flex justify-end items-center mb-4">
        {/* <h2 className="text-lg font-semibold">{formView ? "Add Lead" : "Upload Leads"}</h2> */}
        <div className="flex items-center gap-2">
          <span className="text-sm">Form</span>
          <Switch checked={!formView} onCheckedChange={() => setFormView(!formView)} />
          <span className="text-sm">Upload</span>
        </div>
      </div>

      <Form encType="multipart/form-data" {...form}>
        <form encType="multipart/form-data" className="flex flex-col space-y-2">
          {formView ? (
            <>
              <FormInput
                placeholder="name"
                label="Name"
                fieldControlName="name"
                isRequired
                control={control}
              />
              <FormInput
                placeholder="Designation"
                label="Designation"
                fieldControlName="designation"
                control={control}
              />
              <FormInput
                placeholder="Phone"
                label="Phone"
                fieldControlName="phone"
                control={control}
              />
              <FormInput
                placeholder="<EMAIL>"
                label="Email"
                fieldControlName="email"
                isRequired
                control={control}
              />
              <FormInput
                placeholder="Enter company name"
                label="Company Name"
                fieldControlName="company_name"
                control={control}
              />
              <FormInput
                placeholder="Enter LinkedIn link"
                label="LinkedIn Link"
                fieldControlName="linkedin"
                control={control}
              />
            </>
          ) : (
            <>
              <FileUploadCard
                setValue={setValue}
                onChange={setFile}
                value={file}
                fileType="csv-excel"
              />
              <div className="pt-12 flex gap-x-3 justify-end">
                <Button variant="secondary" type="reset" onClick={onClose}>
                  Cancel
                </Button>
                <Button onClick={handleUploadFile} type="button" variant="primary">
                  Upload Leads
                </Button>
              </div>
            </>
          )}

          <div className="pt-12 flex gap-x-3 justify-end">
            {formView && (
              <>
                <Button onClick={onClose} variant="secondary" type="reset">
                  Cancel
                </Button>
                <Button onClick={handleSubmit(onCreateClassForm)} type="submit" variant="primary">
                  Save
                </Button>
              </>
            )}
          </div>
        </form>
      </Form>
    </div>
  )
}

AddLeadsSideBar.propTypes = {
  form: PropTypes.objectOf.isRequired,
  control: PropTypes.objectOf.isRequired,
  onClose: PropTypes.func.isRequired,
  onCreateClassForm: PropTypes.func.isRequired,
  handleSubmit: PropTypes.func.isRequired,

  setValue: PropTypes.func.isRequired,
  handleUploadFile: PropTypes.func.isRequired,
  setFile: PropTypes.func.isRequired,
  file: PropTypes.shape({
    name: PropTypes.string,
    size: PropTypes.number,
    type: PropTypes.string,
  }),
  editData: PropTypes.shape({
    name: PropTypes.string,
    designation: PropTypes.string,
    phone: PropTypes.string,
    email: PropTypes.string,
    company_name: PropTypes.string,
    linkedin: PropTypes.string,
  }),
}

export default AddLeadsSideBar
