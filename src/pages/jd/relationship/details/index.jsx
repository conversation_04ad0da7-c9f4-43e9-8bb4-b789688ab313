import DataTable from "@/components/custom/cutsom-table"
import { ActionCell, DateCell } from "@/components/custom/cutsom-table/table-cells"
import { DeleteDialog } from "@/components/custom/dialogs/delete.dialog"
import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import {
  useDeleteDialogHooks,
  useFilterHooks,
  useIsUpdateHook,
  useOpenCloseHooks,
  usePaginationHooks,
} from "@/hooks/common.hooks"
import useTableConfig from "@/hooks/use-table.hooks"
import { useDebounce } from "@/hooks/useDebounce"
import CustomSidebar from "@/pages/courses/components/custom-sidebar"
import {
  useAllJds,
  useCreateLeadRelationShip,
  useDeletLeads,
  useFeatchUsers,
  useFetchLeadDetails,
  useUpdateLeadRelationShip,
} from "@/services/query/leads-detail.query.js"
import { zodResolver } from "@hookform/resolvers/zod"
import { flexRender } from "@tanstack/react-table"
import { Plus } from "lucide-react"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { useLocation } from "react-router-dom"
import { columns } from "./columns"
import CreateRelationship from "./create-relationship"
import detailsData from "./details.json"
import { leadRelationShip } from "./utils/lead-relationship"

const RelationshipDetailsPage = () => {
  // const { id } = useParams()
  const location = useLocation()
  const { id, company_name, designation, email, name } = location.state || {}
  const { isDelete, handleDelete, handleCancel } = useDeleteDialogHooks(false)
  const { mutate: deleteMutation } = useDeletLeads()
  const { data: users } = useFeatchUsers("STUDENT")
  const [relationShipId, setRelationShipId] = useState(null)
  const [searchingValue, setSearchingValue] = useState("")
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })

  const { handleOpen, open, setOpen, handleClose } = useOpenCloseHooks()
  const { mutate: mutateCreateRelationship, isLoading } = useCreateLeadRelationShip()
  const { isUpdate, handleResetUpdate, handleUpdate } = useIsUpdateHook()

  const { data: jds } = useAllJds()
  const { mutate: ubdateLeditRelationShip } = useUpdateLeadRelationShip()
  const { limit } = usePaginationHooks()
  const debouncedSearchQuery = useDebounce(searchingValue, 500)
  const { sortBy, sortByField } = useFilterHooks()
  const handleDeleteLeadRelationSHip = (data) => {
    setRelationShipId(data?.original?.id)
    handleDelete()
  }

  const form = useForm({
    resolver: zodResolver(leadRelationShip),
    defaultValues: null,
    mode: "onChange",
  })

  const { handleSubmit, control, setValue, reset, getValues } = form

  const handleEdit = (row) => {
    const { jd_name, relationship, shared_to, id } = row?.original ?? {}

    // setTaskId({ ...taskId, task_id: id })
    setRelationShipId(id)
    handleUpdate()
    reset({
      userId: shared_to?.toString(),
      relationshipType: relationship?.toString(),
      jobids: jd_name?.toString(),
      // dead_line: new Date(dead_line),
      // passing_marks: passing_marks?.toString(),
      // total_marks: total_marks?.toString(),
      // ...rest,
    })
    handleOpen()
  }

  const handleCancelForm = () => {
    // handleCloseForm({ setOpen, reset })
    handleResetUpdate()
    setOpen(false)
    reset({
      userId: "",
      relationshipType: "",
      jobids: "",
    })
  }
  const handleCancelDelete = () => {
    // handleCloseForm({ setOpen, reset })
    setOpen(false)
    reset()
  }
  const handleConfirmDelete = () => {
    deleteMutation(
      relationShipId,

      {
        onSuccess: () => {
          handleCancel()
          successToast("Relationship deleted successfully", "The Relationship has been removed.")
        },
        onError: () => {
          handleCancel()
          failureToast(
            "Deletion Error",
            "Unable to delete the Relationship. Please try again later."
          )
        },
      }
    )
  }

  const createLeadRelationShip = (values) => {
    const { userId, relationshipType, jobids } = values

    if (isUpdate) {
      ubdateLeditRelationShip(
        {
          relation_id: relationShipId,
          data: { job_id: jobids, shared_to: userId, relationship: relationshipType },
        },
        {
          onSuccess: () => {
            handleClose()
            successToast("RelationShip Updated", "The RelationShip has been successfully updated!")
            handleResetUpdate()
          },
          onError: () => {
            handleResetUpdate()
            handleClose()
            failureToast(
              "RelationShip Update Failed",
              "An error occurred while updating the RelationShip. Please try again."
            )
          },
        }
      )
      return
    }

    mutateCreateRelationship(
      {
        lead_id: id,
        data: { job_id: jobids, shared_to: userId, relationship: relationshipType },
      },
      {
        onSuccess: () => {
          handleClose()
          successToast("RelationShip Updated", "The RelationShip has been successfully Updated!")
        },
        onError: () => {
          handleClose()
          failureToast(
            "RelationShip Updation Failed",
            "An error occurred while update the RelationShip. Please try again."
          )
        },
      }
    )
  }

  const renderCellContent = (cell, row) => {
    const { id, contact_id, student_name, jd_name, job_id, relationship, shared_to, modified_at } =
      row?.original || {}

    switch (cell.column.id) {
      case "id":
        return <p>{cell.row.index + 1 + pagination.pageIndex * pagination.pageSize}</p>
      case "contact_id":
        return <p>{contact_id}</p>
      case "student_name":
        return <p>{student_name}</p>
      case "jd_name":
        return <p>{jd_name}</p>
      case "job_id":
        return <p>{job_id}</p>
      case "relationship":
        return (
          <p>
            {relationship
              ? relationship.charAt(0).toUpperCase() + relationship.slice(1).toLowerCase()
              : "-"}
          </p>
        )

      case "shared_to":
        return <p>{student_name}</p>
      case "updatedAt":
        return <DateCell date={modified_at} />
      case "action":
        return (
          <ActionCell
            label2="Edit"
            label3="Delete"
            row={row}
            isEdit
            onEdit={handleEdit}
            isDelete
            onDelete={handleDeleteLeadRelationSHip}
            // onEdit={handleEdit}
            // isView
            // onView={handleOpenProjectStatusDialog}
          />
        )
      default:
        return flexRender(cell.column.columnDef.cell, cell.getContext())
    }
  }

  const calculateOffset = (pageIndex, pageSize) => pageIndex * pageSize
  const { data: leadsDetails } = useFetchLeadDetails({
    lead_id: id,
    filters: {
      limit,
      offset: calculateOffset(pagination.pageIndex, pagination.pageSize),
      sort_by: sortBy,
      sort_by_field: sortByField,
      search_query: debouncedSearchQuery?.length > 0 ? debouncedSearchQuery : null,
    },
  })

  const { table, found, pageCount } = useTableConfig(
    leadsDetails?.data || [],
    columns,
    leadsDetails?.metadata?.total_record,
    setPagination,
    pagination
  )

  const handleAddClick = () => {
    handleOpen()
    reset({
      userId: "",
      relationshipType: "",
      jobids: "",
    })
    if (isUpdate) {
      handleResetUpdate()
    }
  }

  const relationshipProps = {
    users,
    jds: jds?.data,
    createLeadRelationShip,
    handleSubmit,
    control,
    form,
    getValues,
    handleCancelForm,
    // handleSubmit,
    // control,
  }

  if (!detailsData.response || detailsData.response.length === 0) {
    return (
      <Card className="p-8 bg-white rounded-2xl">
        <div className="flex justify-center items-center h-40">
          <p>No data available</p>
        </div>
      </Card>
    )
  }

  return (
    <Card className="p-8 bg-white rounded-2xl">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-medium">{name}</h1>
          <p>
            <span className="text-xs">
              {designation} @ {company_name} | {email}
            </span>
          </p>
        </div>
        <Button variant="primary" onClick={handleAddClick}>
          <Plus className="h-4 w-4 mr-2" />
          Share with Student
        </Button>
      </div>
      <DeleteDialog
        onOpen={isDelete}
        onDelete={handleConfirmDelete}
        onCancel={handleCancel}
        title="Delete Relatoionship"
        content="Are you sure want to delete this Relatoionship?"
      />
      <CustomSidebar
        isOpen={open}
        onClose={handleClose}
        title={isUpdate ? "Update RelationShip" : "Create Relationship"}
        description=""
        content={<CreateRelationship {...relationshipProps} />}
      />
      {/* <CustomSearchbar
        inputSize="w-[20rem]"
        placeholder="Search by title..."
        searchedValue={searchingValue}
        setSearchedValue={(e) => setSearchingValue(e?.target?.value)}
      /> */}

      <div className="w-full">
        <DataTable
          renderCellContent={renderCellContent}
          columns={columns}
          table={table}
          found={found}
          pageCount={pageCount}
          pagination={pagination}
        />
      </div>
    </Card>
  )
}

export default RelationshipDetailsPage
