import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import PropTypes from "prop-types"

const UserSelect = ({
  dataTestID = "test-id",
  dataTestIDError = "error",
  fieldControlName,
  control,
  label,
  iterateData,
  placeholder,
  isRequired,
  defaultValue = "",
}) => {
  return (
    <FormField
      control={control}
      name={fieldControlName}
      render={({ field }) => (
        <FormItem className="">
          <FormLabel htmlFor={label} className="block">
            {label} {isRequired && <span className="text-red-600">*</span>}
          </FormLabel>

          <FormControl>
            <Select
              data-testid={dataTestID}
              onValueChange={field.onChange}
              defaultValue={field.value || defaultValue}
            >
              <SelectTrigger>
                <SelectValue placeholder={placeholder} />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  {iterateData &&
                    iterateData.map((jd) => (
                      <SelectItem key={jd.id} value={jd.id.toString()}>
                        {jd.title}
                      </SelectItem>
                    ))}
                </SelectGroup>
              </SelectContent>
            </Select>
          </FormControl>
          <FormMessage data-testid={dataTestIDError} className="text-xs font-medium mt-1" />
        </FormItem>
      )}
    />
  )
}

// Prop Validations
UserSelect.propTypes = {
  dataTestID: PropTypes.string, // Test ID for the select component
  dataTestIDError: PropTypes.string, // Test ID for the error message
  fieldControlName: PropTypes.string.isRequired, // Name of the field (required)
  control: PropTypes.object.isRequired, // Control object from react-hook-form (required)
  label: PropTypes.string.isRequired, // Label for the select field (required)
  iterateData: PropTypes.arrayOf(
    // Array of data to iterate over
    PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired, // ID of the item
      full_name: PropTypes.string.isRequired, // Display name of the item
    })
  ).isRequired,
  placeholder: PropTypes.string, // Placeholder text for the select field
  isRequired: PropTypes.bool, // Whether the field is required
  defaultValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number]), // Default value for the select field
}

// Default Props
UserSelect.defaultProps = {
  dataTestID: "test-id",
  dataTestIDError: "error",
  placeholder: "Select an option",
  isRequired: false,
  defaultValue: "",
}

export default UserSelect
