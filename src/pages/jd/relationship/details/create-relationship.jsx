import { Button } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import PropTypes from "prop-types"
import FormIdSelect from "./utils/form-id-select"
import RelationShipDropDown from "./utils/relation-ship"
import UserSelect from "./utils/user-dropdown"

const CreateAssignment = ({
  getValues,
  form,
  onClose,
  control,
  handleSubmit,
  createLeadRelationShip,
  handleCancelForm,
  users,
  jds,
}) => {
  // Toggle function to switch between LINK and FILE
  const relationshipTypes = [
    { label: "Alumni", value: "ALUMNI" },
    { label: "Colleague", value: "COLLEAGUE" },
    { label: "Mentor", value: "MENTOR" },
    { label: "Recruiter", value: "RECRUITER" },
    { label: "Industry Contact", value: "INDUSTRY_CONTACT" },
  ]
  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(createLeadRelationShip)}>
        <div className="flex gap-y-4 flex-col justify-end">
          <FormIdSelect
            dataTestID="userId"
            dataTestIDError="userId"
            fieldControlName="userId"
            control={control}
            placeholder="Select User"
            label="Users"
            getValues={getValues}
            iterateData={users?.data}
          />
          <UserSelect
            dataTestID="relationshipType"
            dataTestIDError="relationshipType"
            fieldControlName="jobids"
            control={control}
            label="JD"
            placeholder="Select Jd"
            getValues={getValues}
            iterateData={jds}
          />

          <RelationShipDropDown
            dataTestID="jobids"
            dataTestIDError="jobids"
            fieldControlName="relationshipType"
            control={control}
            label="Relationship"
            placeholder="Select Relationship"
            getValues={getValues}
            iterateData={relationshipTypes}
          />

          <div className="pt-12 flex gap-x-3 justify-end">
            <Button variant="secondary" type="reset" onClick={handleCancelForm}>
              Cancel
            </Button>
            <Button type="submit" variant="primary">
              Create
            </Button>
          </div>
          {/* <RelationShip
            dataTestID="acceptance-status"
            dataTestIDError="acceptance-status-error"
            fieldControlName="acceptance_status"
            control={control}
            label="Relationship"
            getValues={getValues}
            iterateData={relationshipTypes}
          /> */}
        </div>
      </form>
    </Form>
  )
}

CreateAssignment.propTypes = {
  getValues: PropTypes.func.isRequired,
  form: PropTypes.objectOf.isRequired,
  onClose: PropTypes.func.isRequired,
  control: PropTypes.objectOf.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  createLeadRelationShip: PropTypes.func.isRequired,
  users: PropTypes.shape({
    data: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
        full_name: PropTypes.string.isRequired,
      })
    ),
  }),
  jds: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
      full_name: PropTypes.string.isRequired,
    })
  ),
}

export default CreateAssignment
