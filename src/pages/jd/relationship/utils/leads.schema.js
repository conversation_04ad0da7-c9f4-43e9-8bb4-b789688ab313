import { z } from "zod"

export const leadsSchema = z.object({
  name: z
    .string()
    .min(3, { message: "Name must be at least 3 characters" })
    .nonempty({ message: "Name is required" }),

  email: z
    .string()
    .email({ message: "Please enter a valid email" })
    .nonempty({ message: "Email is required" }),

  designation: z.string().optional(),

  phone: z.string().optional(),

  company_name: z.string().optional(),

  linkedin: z.string().optional(),
})

export const defaultLeadValues = {
  name: "",
  email: "",
  designation: "",
  phone: "",
  company_name: "",
  linkedin: "",
}
