import { JDSelect } from "@/components/custom/custom-select"
import DataTable from "@/components/custom/cutsom-table"
import {
  Action<PERSON>ell,
  DateCell,
  StatusUpdationCell,
} from "@/components/custom/cutsom-table/table-cells"
import { DeleteDialog } from "@/components/custom/dialogs/delete.dialog"
import { GeneralDialog } from "@/components/custom/dialogs/general.dialog"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import {
  useDeleteDialogHooks,
  useIsEditDialogHook,
  useOpenCloseHooks,
  usePublishStatus,
  useSelectedIds,
} from "@/hooks/common.hooks"
import useTableConfig from "@/hooks/use-table.hooks"
import CustomSidebar from "@/pages/courses/components/custom-sidebar"
import {
  useDeleteJobApplication,
  useGetJobApplications,
  usePostJobApplication,
  useUpdateJobApplicationStatus,
} from "@/services/query/jd.query"
import { flexRender } from "@tanstack/react-table"
import { Plus } from "lucide-react"
import { useEffect, useState } from "react"

import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import CreateJobApplicationForm from "@/pages/manage-course/create"
import applicationData from "./applications.json"
import { columns } from "./columns"

const ApplicationsPage = () => {
  const { data, isLoading, refetch } = useGetJobApplications()
  const [appId, setAppId] = useState("")
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })
  const { open, handleOpen, handleClose } = useOpenCloseHooks()
  const { selectedIDs, setSelectedIDs } = useSelectedIds()
  const { publishedStatus, handleSetPublishStatus } = usePublishStatus()
  const { isEditDialog, handleOpenEditDialog, handleResetEditDialog } = useIsEditDialogHook()
  const { isDelete, handleDelete, handleCancel } = useDeleteDialogHooks(false)
  const [jobApplications, setJobApplications] = useState([])
  const { mutate: updateJobApplicationStatusMutate } = useUpdateJobApplicationStatus()
  const { mutate: deleteJobApplicationMutate } = useDeleteJobApplication()
  const { mutate } = usePostJobApplication()
  const [totalData, setTotalData] = useState([])

  const handleOpenApplicationStatusDialog = (dialogData) => {
    const { notes, application_status, id } = dialogData?.original || {}
    handleSetPublishStatus(application_status)
    setSelectedIDs({
      notes: notes || "",
      id,
    })

    handleOpenEditDialog()
  }

  const handleConfirmDelete = () => {
    deleteJobApplicationMutate({ application_id: appId })
    handleCancel()
    successToast("Job Application Deleted", "Job Application Deleted Successfully")
    refetch()
  }

  const handleUpdateStatus = () => {
    const updateData = {
      application_status: publishedStatus,
      notes: selectedIDs?.notes || "",
    }
    updateJobApplicationStatusMutate({
      application_id: selectedIDs?.id,
      data: updateData,
    })
    handleResetEditDialog()
    successToast("Job Application Updated", "Job Application Updated Successfully")
  }

  const handleDeleteApplication = (del) => {
    setAppId(del?.original?.id)
    handleDelete()
    console.log("_useFetchQuizDetails", del?.original)
  }

  const handleCreateTopic = () => {
    handleOpen()
  }
  const onCreateClassForm = (job) => {
    const formattedData = {
      student_id: job.studentName,
      job_id: job.jobTitle,
      notes: job.notes || "",
    }
    mutate(formattedData, {
      onSuccess: () => {
        successToast("Successfully Created!", "Job Application Created Successfully")
        refetch()
        handleClose()
      },
      onError: (error) => {
        console.error(error)
        failureToast("Error!", "Failed to create Job Application")
      },
    })
  }

  const renderCellContent = (cell, row) => {
    const { applied_at, student_name, job_name, notes, student_feedback, application_status, id } =
      row.original || {}

    switch (cell.column.id) {
      case "student_name":
        return <p>{student_name || "-"}</p>
      case "job_name":
        return <p>{job_name || "-"}</p>
      case "applied_at":
        return <DateCell value={applied_at} />
      case "application_status":
        return <StatusUpdationCell value={application_status} key={id} />
      case "notes":
        return <p>{notes || "-"}</p>
      case "student_feedback":
        return <p>{student_feedback || "-"}</p>
      case "actions":
        return (
          <ActionCell
            label1="update status & Edit"
            label3="Delete"
            row={row}
            isDelete
            isView
            onView={handleOpenApplicationStatusDialog}
            onDelete={handleDeleteApplication}
          />
        )
      default:
        return flexRender(cell.column.columnDef.cell, cell.getContext())
    }
  }
  useEffect(() => {
    if (data?.data) {
      setJobApplications(data)
      setTotalData(data?.metadata.total_record)
    }
  }, [data])
  const { table, found, pageCount } = useTableConfig(
    jobApplications?.data || [],
    columns,
    totalData,
    setPagination,
    pagination
  )

  if (!applicationData.response || applicationData.response.length === 0) {
    return (
      <Card className="p-8 bg-white rounded-2xl">
        <div className="flex justify-center items-center h-40">
          <p>No data available</p>
        </div>
      </Card>
    )
  }

  return (
    <Card className="p-8 bg-white rounded-2xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-medium">Job Applications</h1>
        <Button variant="primary" onClick={handleCreateTopic}>
          <Plus className="h-4 w-4 " />
          Add Application
        </Button>
      </div>
      <div className="w-full">
        <DataTable
          renderCellContent={renderCellContent}
          columns={columns}
          table={table}
          found={found}
          pageCount={pageCount}
          pagination={pagination}
          isLoading={isLoading}
        />
      </div>
      <GeneralDialog
        onOpen={isEditDialog}
        title="Update Status and Notes"
        onClickCTA={handleUpdateStatus}
        ctaLabel="Update"
        onCancel={handleResetEditDialog}
        ctaPosition="justify-end"
        content={
          <JDSelect
            notes={selectedIDs?.notes || ""}
            setNotes={(value) => {
              setSelectedIDs({ ...selectedIDs, notes: value || "" })
            }}
            publishedStatus={publishedStatus}
            setPublishedStatus={handleSetPublishStatus}
          />
        }
      />
      <DeleteDialog
        onOpen={isDelete}
        onDelete={handleConfirmDelete}
        onCancel={handleCancel}
        title="Delete Quiz"
        content="Are you sure want to delete this Quiz?"
      />
      <CustomSidebar
        title="Create Job Application"
        description=""
        isOpen={open}
        onClose={handleClose}
        content={<CreateJobApplicationForm onCreateClassForm={onCreateClassForm} />}
      />
    </Card>
  )
}

export default ApplicationsPage
