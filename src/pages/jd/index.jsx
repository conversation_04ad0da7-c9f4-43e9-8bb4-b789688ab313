import CustomPagination from "@/components/custom/custom-pagination"
import CustomSearchbar from "@/components/custom/custom-search"
import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Sheet, Sheet<PERSON>ontent, She<PERSON>Header, She<PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from "@/components/ui/sheet"
import { useArchiveJD, useAvailableJDs, useToggleApplyStatus } from "@/services/query/jd.query"
import { USER_ROLES } from "@/utils/constants"
import ct from "@constants/"
import { Filter } from "lucide-react"
import { useEffect, useState } from "react"
import { useSelector } from "react-redux"
import { useNavigate } from "react-router-dom"
import JobListContent from "./components/job-content"

// const PROVIDER_MAP = {
//   ALL: "All",
//   LINKEDIN: "LinkedIn",
//   MONSTER: "Monster",
//   INDEED: "Indeed",
//   DICE: "Dice",
//   WEBSITE: "Website",
//   TALGY: "Talgy",
// }

const JOB_STATUS_OPTIONS = {
  ALL: { value: "all", label: "All Jobs", apiValue: "ALL" },
  RECOMMENDED: { value: "recommended", label: "Recommended", apiValue: "RECOMMENDED" },
  APPLIED: { value: "applied", label: "Applied", apiValue: "APPLIED" },
}

export default function JDPage() {
  const navigate = useNavigate()
  const { mutateAsync: toggleApply } = useToggleApplyStatus()
  const userRole = useSelector((st) => st[ct.store.USER_STORE]?.userRole)
  const archiveJDMutation = useArchiveJD()
  const [jdCardList, setJDCardList] = useState([])
  const [searchQuery, setSearchQuery] = useState("")
  const [totalData, setTotalData] = useState(0)
  const [offset, setOffset] = useState(0)
  const [error, setError] = useState(null)
  const limit = 10

  // Initialize state with the ALL option
  const [selectedFilter, setSelectedFilter] = useState(JOB_STATUS_OPTIONS.ALL.value)
  const [filterOpen, setFilterOpen] = useState(false)

  const { data, isLoading, refetch } = useAvailableJDs({
    jobType: JOB_STATUS_OPTIONS[selectedFilter.toUpperCase()]?.apiValue || "ALL",
    searchQuery,
    offset,
    limit,
  })

  useEffect(() => {
    if (data) {
      setJDCardList(data?.data)
      setTotalData(data?.metadata?.total_record)
    }
  }, [data])

  const handleFilterChange = (value) => {
    setSelectedFilter(value)
    refetch()
    setOffset(0)
    setFilterOpen(false)
  }

  const handleSearchChange = (e) => {
    e.preventDefault()
    setSearchQuery(e.target.value)
    setOffset(0)
    refetch()
  }

  const handlePageChange = (newOffset) => {
    setOffset(newOffset)
  }

  const handleViewDetailsClick = (jobData) => {
    navigate(`/jd/${jobData.id}`)
  }

  const handleApplyToggle = async (jobId, resumeId) => {
    const job_id = jobId
    const resume_id = resumeId
    try {
      await toggleApply({ job_id, resume_id })
      successToast("Application Status Updated", "Job application status updated successfully")
      refetch()
    } catch (err) {
      if (err?.response.data.error.code === "INTEGRITY_ERROR") {
        setError(err?.response.data.error.code)
        failureToast("Failed to  Duplicate apply status", "You have already applied for this job")
      }
    }
  }

  const handleArchiveJD = async (id) => {
    try {
      await archiveJDMutation.mutateAsync(id)
      refetch()
    } catch (erro) {
      console.error("Failed to archive JD:", erro)
    }
  }

  return (
    <div className="">
      <div className="">
        <div className="flex justify-between mb-1">
          <div className="flex items-center space-x-2">
            <CustomSearchbar
              isRightIcon={false}
              placeholder="Search by JD..."
              inputSize="w-[16rem]"
              searchedValue={searchQuery}
              setSearchedValue={handleSearchChange}
            />

            <div className="mx-2" />
            {userRole === USER_ROLES.STUDENT && (
              <Sheet open={filterOpen} onOpenChange={setFilterOpen}>
                <SheetTrigger asChild>
                  <Button variant="outline">
                    <Filter className="mr-2 h-4 w-4" />
                    {JOB_STATUS_OPTIONS[selectedFilter.toUpperCase()]?.label || "Apply Filters"}
                  </Button>
                </SheetTrigger>
                <SheetContent>
                  <SheetHeader>
                    <SheetTitle>Filter Jobs</SheetTitle>
                  </SheetHeader>
                  <div className="grid gap-4 py-4">
                    <div className="space-y-2">
                      <Label>Job Status</Label>
                      <Select value={selectedFilter} onValueChange={handleFilterChange}>
                        <SelectTrigger>
                          <SelectValue>
                            {JOB_STATUS_OPTIONS[selectedFilter.toUpperCase()]?.label}
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                          {Object.values(JOB_STATUS_OPTIONS).map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
            )}
          </div>
          <div className="flex items-center font-semibold gap-10 ">
            <h3 className="text-lg font-semibold mt-2 ">
              Total Available JDs :
              <span className="text-primary font-semibold text-xl ms-2">{totalData}</span>
            </h3>
            {(userRole === USER_ROLES.MARKETER ||
              userRole === USER_ROLES.ADMIN ||
              userRole === USER_ROLES.VENDOR) && (
                <Button variant="primary" onClick={() => navigate("/jd/add-JD")}>
                  Create JD
                </Button>
              )}
          </div>
        </div>
        <div className="h-[70vh] overflow-y-auto">
          <JobListContent
            isLoading={isLoading}
            jdCardList={jdCardList}
            onViewDetailsClick={handleViewDetailsClick}
            onArchiveClicked={handleArchiveJD}
            handleJDAppliedClicked={handleApplyToggle}
            userRole={userRole}
            selectedFilter={selectedFilter}
            error={error}
          />
        </div>
      </div>
      {/* <Separator /> */}
      <div className="flex justify-end mt-3">
        <CustomPagination
          paginationPosition="end"
          onPageChange={handlePageChange}
          currentOffset={offset}
          totalItems={totalData}
          limit={limit}
        />
      </div>
    </div>
  )
}
