import * as z from "zod"

export const jobDescriptionSchema = z.object({
  provider: z.enum(["LINKEDIN", "MONST<PERSON>", "INDEED", "DICE", "WEBSITE", "TALGY"], {
    required_error: "Please select a JD provider",
  }),
  title: z
    .string()
    .min(5, {
      message: "Title must be at least 5 characters",
    })
    .max(255, {
      message: "Title must not exceed 255 characters",
    }),
  location: z
    .string()
    .min(1, {
      message: "Location is required",
    })
    .max(255, {
      message: "Location must not exceed 255 characters",
    }),
  type: z
    .string()
    .min(1, {
      message: "Type is required",
    })
    .max(255, {
      message: "Type must not exceed 255 characters",
    }),
  description: z.string().min(5, {
    message: "Description must be at least 5 characters",
  }),
  min_experience: z.number().int().min(1).max(20, {
    message: "Minimum experience must be between 0 and 20 years",
  }),
  max_experience: z.number().int().min(0).nullable(),
  salary: z.number().int().positive({
    message: "Salary must be a positive number",
  }),
  skills: z.string().min(1, {
    message: "Skills are required",
  }),
  company_name: z
    .string()
    .min(1, {
      message: "Company name is required",
    })
    .max(255, {
      message: "Company name must not exceed 255 characters",
    }),
  post_link: z
    .string()
    .min(1, {
      message: "Post link is required",
    })
    .max(500, {
      message: "Post link must not exceed 500 characters",
    }),
  company_website: z.string().url().nullable(),
})

export const defaultValues = {
  provider: undefined,
  title: "",
  description: "",
  location: "",
  type: "",
  min_experience: 0,
  max_experience: 0,
  salary: undefined,
  skills: "",
  post_link: "",
  company_name: "",
  company_website: "",
}

// Job type options
export const jobTypes = [
  { value: "FULL_TIME", label: "Full-Time" },
  { value: "PART_TIME", label: "Part-Time" },
  { value: "CONTRACT", label: "Contract" },
]

// Provider badge colors
export const providerColors = {
  LINKEDIN: "bg-blue-100 text-blue-800 hover:bg-blue-200",
  MONSTER: "bg-purple-100 text-purple-800 hover:bg-purple-200",
  INDEED: "bg-yellow-100 text-yellow-800 hover:bg-yellow-200",
  DICE: "bg-red-100 text-red-800 hover:bg-red-200",
  WEBSITE: "bg-green-100 text-green-800 hover:bg-green-200",
  TALGY: "bg-orange-100 text-orange-800 hover:bg-orange-200",
}
