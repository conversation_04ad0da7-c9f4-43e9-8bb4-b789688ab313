import { Form<PERSON>ield, Form<PERSON>tem, FormLabel, FormMessage } from "@/components/ui/form"
import { cn } from "@/lib/utils"
import PropTypes from "prop-types"
import { useCallback } from "react"
import { providerColors } from "./job-description-schema"

export function ProviderSelector({ control, setValue }) {
  const handleSelect = useCallback(
    (provider) => {
      setValue("provider", provider)
    },
    [setValue]
  )

  return (
    <FormField
      control={control}
      name="provider"
      render={({ field }) => (
        <FormItem>
          <FormLabel className="font-medium flex items-center gap-1">
            JD Provider <span className="text-red-500">*</span>
          </FormLabel>
          <div className="flex flex-wrap gap-2">
            {Object.keys(providerColors).map((provider) => (
              <div
                key={provider}
                role="button"
                tabIndex={0}
                className={cn(
                  "cursor-pointer py-2 border rounded-sm px-3 text-sm font-medium transition-colors",
                  field.value === provider ? providerColors[provider] : "bg-gray-100"
                )}
                onClick={() => handleSelect(provider)}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") {
                    e.preventDefault()
                    handleSelect(provider)
                  }
                }}
              >
                {provider}
              </div>
            ))}
          </div>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}

ProviderSelector.propTypes = {
  control: PropTypes.shape({
    register: PropTypes.func.isRequired,
    unregister: PropTypes.func.isRequired,
    getFieldState: PropTypes.func.isRequired,
    formState: PropTypes.shape({
      isDirty: PropTypes.bool,
      isValid: PropTypes.bool,
      errors: PropTypes.shape({}),
    }).isRequired,
  }).isRequired,
  setValue: PropTypes.func.isRequired,
}
