import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import LetterCUI from "@/pages/courses/components/letter-ui"
import PropTypes from "prop-types"

export function CompanyInfoSection({ form }) {
  return (
    <div className="rounded-md p-4">
      <div className="space-y-1">
        <h1 className="text-xl font-bold flex items-center leading-tight m-0">
          Company Information
        </h1>
        <FormLabel className=" font-medium flex items-center -mt-1">
          Add Company Detail here
        </FormLabel>
      </div>

      <div className="my-5">
        <LetterCUI letter="C" />
      </div>

      <div className="grid grid-cols-1 gap-6">
        {/* Company Name */}
        <FormField
          control={form.control}
          name="company_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="font-medium flex items-center gap-1">
                Company Name <span className="text-red-500">*</span>
              </FormLabel>

              <FormControl>
                <Input
                  placeholder="Acme Inc."
                  {...field}
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Company Website */}
        <FormField
          control={form.control}
          name="company_website"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="font-medium flex items-center gap-1">Company Website</FormLabel>
              <FormControl>
                <Input
                  placeholder="https://company.com"
                  {...field}
                  value={field.value || null}
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </FormControl>
              {/* <FormDescription className="text-gray-500">Optional</FormDescription> */}
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="post_link"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="font-medium flex items-center gap-1">
                Job Post Link <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="https://example.com/job/123"
                  {...field}
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </FormControl>
              {/* <FormDescription className="text-gray-500">
                URL where candidates can apply for this position
              </FormDescription> */}
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Post Link */}
    </div>
  )
}
CompanyInfoSection.propTypes = {
  form: PropTypes.shape({
    control: PropTypes.shape({}).isRequired,
  }).isRequired,
}
