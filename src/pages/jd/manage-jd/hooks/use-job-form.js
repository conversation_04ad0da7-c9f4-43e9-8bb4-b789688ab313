import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useNavigate } from "react-router-dom"
import { useCreateJD } from "@/services/query/jd.query"
import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import { useMemo } from "react"
import ct from "@constants/"
import { jobDescriptionSchema, defaultValues } from "../job-description-schema"

export function useJobForm() {
  const { mutate, isLoading } = useCreateJD()
  const navigate = useNavigate()

  const form = useForm({
    resolver: zodResolver(jobDescriptionSchema),
    defaultValues,
  })

  const handleSubmit = (data) => {
    // Convert string inputs to numbers where needed
    const formattedData = {
      ...data,
      min_experience: Number(data.min_experience),
      max_experience: data.max_experience ? Number(data.max_experience) : null,
      salary: String(data.salary),
    }

    mutate(formattedData, {
      onSuccess: () => {
        successToast("Successfully Created!", "Job Description Created Successfully")
        navigate(ct.route.JD_PAGE)
      },
      onError: (error) => {
        console.error(error)
        failureToast("Error!", "Failed to create Job Description")
      },
    })
  }

  const handleValidationError = (validationErrors) => {
    console.log("Validation failed:", validationErrors)
    failureToast("Validation Error", validationErrors[Object.keys(validationErrors)[0]].message)
  }

  const submitHandler = useMemo(
    () => form.handleSubmit(handleSubmit, handleValidationError),
    [form]
  )

  return {
    form,
    isLoading,
    submitHandler,
    navigate,
  }
}
