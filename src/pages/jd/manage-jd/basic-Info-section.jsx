import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import PropTypes from "prop-types"

import { jobTypes } from "./job-description-schema"
import { ProviderSelector } from "./provider-selector"

export function BasicInfoSection({ form }) {
  return (
    <div className="rounded-md px-4 mb-2">
      <div className="space-y-1 mb-4">
        <h1 className="text-xl font-bold flex items-center leading-tight m-0">
          Basic Job Information
        </h1>
        <FormLabel className="font-medium flex items-center -mt-1">
          Update your basic job details here
        </FormLabel>
      </div>

      <ProviderSelector control={form.control} setValue={form.setValue} />
      <div className="grid grid-cols-2 gap-4 w-full mt-2">
        {/* Job Title */}
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem className="w-full">
              <FormLabel className="font-medium flex items-center gap-1">
                Job Title <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="Senior Software Engineer"
                  {...field}
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 w-full"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Job Type */}
        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem className="w-full">
              <FormLabel className="font-medium flex items-center gap-1">
                Job Type <span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 w-full">
                    <SelectValue placeholder="Select job type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {jobTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={form.control}
        name="location"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="font-medium flex items-center gap-1">
              Location <span className="text-red-500">*</span>
            </FormLabel>
            <FormControl>
              <Input
                placeholder="New York, NY"
                {...field}
                className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      {/* <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6"> */}
      {/* Location */}

      {/* </div> */}
    </div>
  )
}

BasicInfoSection.propTypes = {
  form: PropTypes.shape({
    control: PropTypes.shape({}).isRequired,
    setValue: PropTypes.func.isRequired,
  }).isRequired,
}
