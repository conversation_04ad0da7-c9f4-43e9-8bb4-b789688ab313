import { FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import PropTypes from "prop-types"
import { useState } from "react"

export function SkillsField({ control }) {
  const [inputValue, setInputValue] = useState("")

  const handleKeyDown = (e, onChange, value) => {
    if (e.key === "Enter" || e.key === ",") {
      e.preventDefault()
      if (e.target.value.trim()) {
        // eslint-disable-next-line no-use-before-define
        addSkill(onChange, value, e.target.value.trim())
        setInputValue("")
      }
    }
  }

  const handleBlur = (e, onChange, value) => {
    if (e.target.value.trim()) {
      // eslint-disable-next-line no-use-before-define
      addSkill(onChange, value, e.target.value.trim())
      setInputValue("")
    }
  }

  const addSkill = (onChange, currentValue, newSkill) => {
    const newValue = currentValue ? `${currentValue},${newSkill}` : newSkill
    onChange(newValue)
  }

  const removeSkill = (index, onChange, value) => {
    const skills = value.split(",").filter(Boolean)
    skills.splice(index, 1)
    onChange(skills.join(","))
  }

  return (
    <FormField
      control={control}
      name="skills"
      render={({ field }) => (
        <FormItem>
          <FormLabel className="font-medium flex items-center gap-1">
            Skills <span className="text-red-500">*</span>
          </FormLabel>

          <div className="border border-gray-300 rounded-md bg-white overflow-hidden focus-within:ring-2">
            <div className="flex flex-wrap items-center p-1 gap-0">
              {field.value
                .split(",")
                .filter(Boolean)
                .map((skill, index) => (
                  // eslint-disable-next-line react/no-array-index-key
                  <div key={index} className="flex items-center m-1">
                    <span className="bg-blue-50 border border-blue-200 text-blue-700 px-2 py-1 text-sm rounded-md flex items-center">
                      {skill.trim()}
                      <button
                        type="button"
                        className="ml-1 text-red-500 hover:text-blue-700"
                        onClick={() => removeSkill(index, field.onChange, field.value)}
                      >
                        ×
                      </button>
                    </span>
                  </div>
                ))}

              <Input
                type="text"
                className="flex-1 outline-none border-0 focus:ring-0 min-w-20 p-2 text-sm"
                placeholder={field.value ? "Add more skills..." : "Add skills..."}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={(e) => handleKeyDown(e, field.onChange, field.value)}
                onBlur={(e) => handleBlur(e, field.onChange, field.value)}
              />
            </div>
          </div>

          <FormDescription className="text-sm mt-2">
            Press Enter or comma to add a skill
          </FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}

SkillsField.propTypes = {
  control: PropTypes.shape({
    register: PropTypes.func.isRequired,
    unregister: PropTypes.func.isRequired,
    getFieldState: PropTypes.func.isRequired,
    formState: PropTypes.shape({
      isDirty: PropTypes.bool,
      isValid: PropTypes.bool,
      errors: PropTypes.objectOf(
        PropTypes.shape({
          type: PropTypes.string,
          message: PropTypes.string,
        })
      ),
    }).isRequired,
    setValue: PropTypes.func.isRequired,
    getValues: PropTypes.func.isRequired,
    trigger: PropTypes.func.isRequired,
    control: PropTypes.shape({
      register: PropTypes.func.isRequired,
      unregister: PropTypes.func.isRequired,
      getFieldState: PropTypes.func.isRequired,
      formState: PropTypes.shape({
        isDirty: PropTypes.bool,
        isValid: PropTypes.bool,
        errors: PropTypes.objectOf(
          PropTypes.shape({
            type: PropTypes.string,
            message: PropTypes.string,
          })
        ),
      }).isRequired,
      setValue: PropTypes.func.isRequired,
      getValues: PropTypes.func.isRequired,
      trigger: PropTypes.func.isRequired,
      watch: PropTypes.func.isRequired,
      resetField: PropTypes.func.isRequired,
      clearErrors: PropTypes.func.isRequired,
      setError: PropTypes.func.isRequired,
    }).isRequired,
    watch: PropTypes.func.isRequired,
    resetField: PropTypes.func.isRequired,
    clearErrors: PropTypes.func.isRequired,
    setError: PropTypes.func.isRequired,
  }).isRequired,
}
