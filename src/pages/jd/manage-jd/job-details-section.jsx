import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { DollarSign } from "lucide-react"
import PropTypes from "prop-types"
import { useCallback } from "react"

export function JobDetailsSection({ form }) {
  const handleNumberChange = useCallback((e, onChange) => {
    onChange(e.target.value ? parseInt(e.target.value, 10) : null)
  }, [])

  const handleKeyDown = (event) => {
    const allowedKeys = [
      "0",
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "Backspace",
      "Tab",
      "ArrowLeft",
      "ArrowRight",
    ]

    if (!allowedKeys.includes(event.key)) {
      event.preventDefault()
    }
  }

  return (
    <div className="rounded-md p-4">
      <FormField
        control={form.control}
        name="description"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="font-medium flex items-center gap-1">
              Job Description <span className="text-red-500">*</span>
            </FormLabel>
            <FormControl>
              <Textarea
                placeholder="Detailed job description..."
                className="min-h-20 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                {...field}
              />
            </FormControl>
            {/* <FormDescription className="text-gray-500">
              Provide a comprehensive description of the role, responsibilities, and requirements.
            </FormDescription> */}
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 ">
        {/* Min Experience */}
        <FormField
          control={form.control}
          name="min_experience"
          render={({ field }) => (
            <FormItem className="bg-white py-3 rounded-md shadow-sm">
              <FormLabel className="font-medium flex items-center gap-1">
                Min Experience <span className="text-red-500">*</span>
              </FormLabel>
              <div className="flex items-center">
                <FormControl>
                  <Input
                    type="text"
                    min="0"
                    max="50"
                    placeholder="Experience in years"
                    className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    {...field}
                    onKeyDown={handleKeyDown}
                    onChange={(e) => handleNumberChange(e, field.onChange)}
                  />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Max Experience */}
        <FormField
          control={form.control}
          name="max_experience"
          render={({ field }) => (
            <FormItem className="bg-white py-3 rounded-md shadow-sm">
              <FormLabel className="font-medium">Max Experience</FormLabel>
              <div className="flex items-center">
                <FormControl>
                  <Input
                    type="text"
                    min="0"
                    placeholder="Experience in years"
                    className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    {...field}
                    onKeyDown={handleKeyDown}
                    onChange={(e) => handleNumberChange(e, field.onChange)}
                    value={field.value || ""}
                  />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Salary */}
        <FormField
          control={form.control}
          name="salary"
          render={({ field }) => (
            <FormItem className="bg-white py-3 rounded-md shadow-sm">
              <FormLabel className="font-medium flex items-center gap-1">
                <DollarSign className="h-4 w-4" /> Salary <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  type="text"
                  min="1"
                  placeholder="Annual salary"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  {...field}
                  onKeyDown={handleKeyDown}
                  onChange={(e) => handleNumberChange(e, field.onChange)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
JobDetailsSection.propTypes = {
  form: PropTypes.shape({
    control: PropTypes.shape({
      register: PropTypes.func.isRequired,
      handleSubmit: PropTypes.func.isRequired,
      watch: PropTypes.func.isRequired,
      formState: PropTypes.shape({
        isDirty: PropTypes.bool,
        isValid: PropTypes.bool,
        errors: PropTypes.objectOf(PropTypes.shape({})),
      }).isRequired,
    }).isRequired,
  }).isRequired,
}
