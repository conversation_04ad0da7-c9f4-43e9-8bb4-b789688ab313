import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Form } from "@/components/ui/form"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Loader2 } from "lucide-react"
import { FaChevronLeft } from "react-icons/fa"
import { BasicInfoSection } from "./basic-Info-section"
import { CompanyInfoSection } from "./company-Info-section"
import { useJobForm } from "./hooks/use-job-form"
import { JobDetailsSection } from "./job-details-section"
import SkillsSection from "./skills-section"

export default function ManageJD() {
  const { form, isLoading, submitHandler, navigate } = useJobForm()

  return (
    <div className="px-4 md:px-6 lg:px-10">
      <div className="flex items-center gap-1">
        <Button onClick={() => navigate(-1)}>
          <FaChevronLeft className="text-primary mb-5 h-8" />
        </Button>
        <h1 className="text-2xl md:text-3xl font-bold mb-5 text-primary">New Job Posting</h1>
      </div>
      <Separator className="bg-gray-300 h-[1px] w-full my-4" />

      <div className="">
        <ScrollArea className="h-[70vh] pr-4">
          <Form {...form}>
            <form onSubmit={submitHandler} className="space-y-6 pb-8">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 w-full">
                {/* Company info card */}
                <Card className="p-4 lg:col-span-1">
                  <div className="w-full">
                    <CompanyInfoSection form={form} />
                  </div>
                </Card>

                {/* Main content card */}
                <Card className="p-4 md:px-5 lg:col-span-2">
                  <div className="flex flex-col w-full">
                    <BasicInfoSection form={form} />
                    <SkillsSection form={form} />
                    <JobDetailsSection form={form} />
                  </div>

                  <div className="flex flex-col sm:flex-row justify-end m-2 md:m-4 gap-3">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => navigate("#")}
                      className="w-full sm:w-auto sm:min-w-32"
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      variant="primary"
                      disabled={isLoading}
                      className="w-full sm:w-auto sm:min-w-32"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Creating...
                        </>
                      ) : (
                        "Post Job"
                      )}
                    </Button>
                  </div>
                </Card>
              </div>
            </form>
          </Form>
        </ScrollArea>
      </div>
    </div>
  )
}
