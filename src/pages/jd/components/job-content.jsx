import LoadingSpinner from "@/components/custom/LoadingSpinner"
import <PERSON><PERSON><PERSON> from "@/components/jd-card"
import { USER_ROLES } from "@/utils/constants"
import { Layers } from "lucide-react"
import PropTypes from "prop-types"

// Map for provider strings to numbers to match the existing PROVIDER_MAP in JDCard
const PROVIDER_STRING_TO_NUMBER = {
  LINKEDIN: 1,
  MONSTER: 2,
  INDEED: 3,
  DICE: 4,
  WEBSITE: 5,
  TALGY: 6,
}

// Helper function to format salary
const formatSalary = (salary) => {
  if (!salary) return "Not specified"
  return `₹${salary.toLocaleString()}/year`
}

function JobListContent({
  isLoading,
  jdCardList,
  onViewDetailsClick,
  onArchiveClicked,
  handleJDAppliedClicked,
  userRole,
  selectedFilter,
  error,
}) {
  if (isLoading) {
    return (
      <div>
        <LoadingSpinner />
      </div>
    )
  }

  if (!jdCardList?.length) {
    return (
      <div className="text-center flex flex-col items-center justify-center py-8 text-gray-500">
        <Layers className="text-gray-300 mx-auto  h-24 w-24" />
        <p className="text-xl mt-5 text-gray-300 font-semibold">No Jobs Found</p>
      </div>
    )
  }

  const mappedJobs = jdCardList.map((job) => ({
    id: job.id,
    title: job.title,
    resume_id: job.resume_id,
    company_name: job.company_name,
    min_work_exp: job.min_experience,
    max_work_exp: job.max_experience,
    skills: job.skills,
    salary: formatSalary(job.salary),
    location: job.location,
    post_link: job.post_link,
    provider: PROVIDER_STRING_TO_NUMBER[job.provider] || 5,
    modified_at: job.meta?.modified_at || new Date().toISOString(),
    posted_at: job.meta?.posted_at || new Date().toISOString(),
    distance: job.distance || 1,
    applied_at: job.applied_at,
    application_status: job.application_status,
  }))

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
      {mappedJobs.map((jobData) => (
        <JDCard
          key={jobData.id}
          jobData={jobData}
          onViewDetailsClick={() => onViewDetailsClick(jobData)}
          onArchiveClicked={onArchiveClicked}
          handleJDAppliedClicked={handleJDAppliedClicked}
          showMatch={userRole === USER_ROLES.STUDENT}
          showApplied={userRole === USER_ROLES.STUDENT}
          isApplied={!!jobData.applied_at}
          allowFeedback={userRole === USER_ROLES.STUDENT}
          showApplicationStatus={userRole === USER_ROLES.STUDENT}
          showArchive={userRole !== USER_ROLES.STUDENT}
          userRole={userRole}
          selectedFilter={selectedFilter}
          error={error}
        />
      ))}
    </div>
  )
}

JobListContent.propTypes = {
  isLoading: PropTypes.bool.isRequired,
  jdCardList: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      title: PropTypes.string.isRequired,
      company_name: PropTypes.string.isRequired,
      min_work_exp: PropTypes.number,
      max_work_exp: PropTypes.number,
      skills: PropTypes.arrayOf(PropTypes.string),
      salary: PropTypes.string,
      location: PropTypes.string,
      post_link: PropTypes.string,
      provider: PropTypes.number.isRequired,
      modified_at: PropTypes.string,
      posted_at: PropTypes.string,
      distance: PropTypes.number,
      applied_at: PropTypes.string,
      application_status: PropTypes.string,
    })
  ).isRequired,
  onViewDetailsClick: PropTypes.func.isRequired,
  onArchiveClicked: PropTypes.func.isRequired,
  handleJDAppliedClicked: PropTypes.func.isRequired,
  userRole: PropTypes.string.isRequired,
}

export default JobListContent
