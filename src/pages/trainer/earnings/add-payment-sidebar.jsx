/* eslint-disable no-underscore-dangle */
import LoadingSpinner from "@/components/custom/LoadingSpinner"
import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import FileUploadCard from "@/components/custom/upload-file"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>etContent, SheetHeader, SheetTitle } from "@/components/ui/sheet"
import { useStoreTrainerPayment } from "@/services/query/payment.query"
import PropTypes from "prop-types"
import { useState } from "react"

const AddPaymentSidebar = ({ isOpen, onOpenChange, selectedIDs, trainerID, trainerName }) => {
  const initialFormData = {
    payment: "",
    paymentDate: "",
    referenceFile: null,
  }
  const initialFormErrors = {
    paymentErr: null,
    paymentDateErr: null,
  }

  const [formData, setFormData] = useState({ ...initialFormData })
  const [formErrors, setFormErrors] = useState({ ...initialFormErrors })

  const handleFileChange = (files) => {
    if (!files?.length) {
      setFormData((prev) => ({ ...prev, referenceFile: null }))
      return
    }

    setFormData((prev) => ({ ...prev, referenceFile: files[0] }))
  }

  const handleInputChange = (field, value, errField) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
    if (value) setFormErrors((prev) => ({ ...prev, [errField]: "" }))
  }

  // Reset Form
  const resetForm = () => {
    setFormData({ ...initialFormData })
  }

  // Validation
  const validateValues = () => {
    let isValid = true
    const errors = { ...initialFormErrors }
    if (!formData.payment) {
      isValid = false
      errors.paymentErr = "Required"
    }
    if (!formData.paymentDate) {
      isValid = false
      errors.paymentDateErr = "Required"
    }

    setFormErrors(errors)
    return isValid
  }
  const { mutate: storePaymentMutate, isPending } = useStoreTrainerPayment()
  const handleSubmit = (e) => {
    e.preventDefault()

    if (!validateValues()) return
    setFormErrors({ ...initialFormErrors })

    const payload = new FormData()
    if (formData.referenceFile) payload.append("file", formData.referenceFile)

    const data = {
      trainer_earning_ids: selectedIDs,
      trainer_id: trainerID,
      trainer_name: trainerName,
      amount_paid: formData.payment,
      payment_date: formData.paymentDate,
    }
    payload.append("data", JSON.stringify(data))

    storePaymentMutate(payload, {
      onSuccess: () => {
        successToast("Payment Added", "The payment has been successfully added!")
        resetForm()
        onOpenChange(false)
      },
      onError: () => {
        failureToast("Failed to add payment", "")
      },
    })
  }

  const getLabel = (htmlFor, label, errField) => (
    <Label htmlFor={htmlFor} className={formErrors[errField] && "text-red-500"}>
      {label}
      <span className={formErrors[errField] ? "text-red-500" : "text-primary"}> *</span>
    </Label>
  )

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="w-[400px]">
        <SheetHeader>
          <SheetTitle>Add New Payment</SheetTitle>
          <p>{selectedIDs.length} Earning(s) Selected</p>
        </SheetHeader>
        <form onSubmit={handleSubmit} className="space-y-6 mt-4">
          {/* Payment(Amount) */}
          <div className="space-y-2">
            {getLabel("payment", "Payment", "paymentErr")}
            <Input
              id="payment"
              type="number"
              value={formData.payment}
              onChange={(e) => handleInputChange("payment", e.target.value, "paymentErr")}
            />
          </div>

          {/* Payment Date */}
          <div className="space-y-2">
            {getLabel("paid-data", "Paid Date", "paymentDateErr")}
            <Input
              id="paid-date"
              type="date"
              value={formData.paymentDate}
              onChange={(e) => handleInputChange("paymentDate", e.target.value, "paymentDateErr")}
            />
          </div>

          {/* Upload Reference Image */}
          <div className="space-y-2">
            <Label>Reference Image</Label>
            <FileUploadCard
              setValue={(_, files) => handleFileChange(files)}
              onChange={(files) => handleFileChange(files)}
            />
          </div>

          <div className="flex gap-3 mt-6">
            <Button variant="outline" onClick={() => onOpenChange(false)} type="button">
              Cancel
            </Button>
            <Button variant="primary" type="submit">
              {isPending ? <LoadingSpinner /> : "Add"}
            </Button>
          </div>
        </form>
      </SheetContent>
    </Sheet>
  )
}

AddPaymentSidebar.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onOpenChange: PropTypes.func.isRequired,
  selectedIDs: PropTypes.arrayOf(PropTypes.oneOf([PropTypes.number, PropTypes.string])).isRequired,
  trainerID: PropTypes.string,
  trainerName: PropTypes.string,
}

export default AddPaymentSidebar
