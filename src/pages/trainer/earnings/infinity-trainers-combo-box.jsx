import InfiniteCustomComboBox from "@/components/dropdowns/infinite-custom-combo-box"
import { useGetAllUsers } from "@/services/query/user.query"
import { USER_ROLES } from "@/utils/constants"
import PropTypes from "prop-types"
import { useEffect, useState } from "react"

const LIMIT = 10

export default function InfinityTrainersComboBox({
  selectedTrainer,
  handleTrainerChange,
  keyName = "trainer",
  isFetchEnabled = true,
}) {
  const [trainerOptions, setTrainerOptions] = useState([])
  const [trainerPageIndex, setTrainerPageIntex] = useState(0)
  const [hasMoreTrainer, setHashMoreTrainer] = useState(true)

  // Fetch Trainers
  const params = {
    filter_role: USER_ROLES.TRAINER,
    offset: trainerPageIndex,
    limit: LIMIT,
  }
  const { data: trainersResponse, isLoading: isTrainersLoading } = useGetAllUsers(
    params,
    isFetchEnabled
  )
  useEffect(() => {
    if (isTrainersLoading) return

    const trainers = trainersResponse?.data || []
    const trainersLen = trainerOptions.length + trainers.length
    if (trainers?.length > 0) {
      const options = trainers.map(({ id, user_name }) => ({ value: id, label: user_name }))
      if (trainerPageIndex === 0) setTrainerOptions(options)
      else setTrainerOptions((prev) => [...prev, ...options])
    }
    // Calculate Hash More
    const total = trainersResponse?.metadata?.total_records
    setHashMoreTrainer(trainersLen < total)
  }, [trainersResponse])

  const loadMoreTrainers = () => {
    setTrainerPageIntex((prev) => prev + 1)
  }

  return (
    <InfiniteCustomComboBox
      selected={{
        label: selectedTrainer?.label,
        value: selectedTrainer?.value,
      }}
      options={trainerOptions}
      onSelectChange={(option, key) => handleTrainerChange(option, key)}
      keyName={keyName}
      hasMore={hasMoreTrainer}
      isLoading={isTrainersLoading}
      loadMoreOptions={loadMoreTrainers}
    />
  )
}

InfinityTrainersComboBox.propTypes = {
  selectedTrainer: PropTypes.shape({
    label: PropTypes.oneOf([PropTypes.number, PropTypes.string]),
    value: PropTypes.oneOf([PropTypes.number, PropTypes.string]),
  }).isRequired,
  handleTrainerChange: PropTypes.func.isRequired,
  keyName: PropTypes.string,
  isFetchEnabled: PropTypes.bool,
}
