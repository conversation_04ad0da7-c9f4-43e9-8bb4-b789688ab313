import DataTable from "@/components/custom/cutsom-table"
import InfiniteCustomComboBox from "@/components/dropdowns/infinite-custom-combo-box"
import { Card } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import useTableConfig from "@/hooks/use-table.hooks"
import {
  useFetchTrainerEarnings,
  useFetchTrainerPaymentStats,
} from "@/services/query/payment.query"
import { useGetAllUsers } from "@/services/query/user.query"
import { STATUS_COLOURS_TEXT, USER_ROLES } from "@/utils/constants"
import ct from "@constants/"
import { flexRender } from "@tanstack/react-table"
import { useEffect, useState } from "react"
import { useSelector } from "react-redux"
import AddPaymentEarningButtons from "./add-payment-earnings-button"
import { adminColumns, commonColumns } from "./columns"

const LIMIT = 10

const TrainerEarningPage = () => {
  const userRole = useSelector((st) => st[ct.store.USER_STORE]?.userRole)

  const [trainerOptions, setTrainerOptions] = useState([])
  const [trainerPageIndex, setTrainerPageIntex] = useState(0)
  const [hasMoreTrainer, setHashMoreTrainer] = useState(true)
  const [selectedTrainer, setSelectedTrainer] = useState(null)
  const [paymentStats, setPaymentStats] = useState({
    total_earnings: "-",
    total_completed_payments: "-",
    total_pending_payments: "-",
  })
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })

  // Fetch Trainers For Admin View
  let params = {
    filter_role: USER_ROLES.TRAINER,
    offset: 0,
    limit: 10,
  }
  const { data: trainersResponse, isLoading: isTrainersLoading } = useGetAllUsers(
    params,
    userRole === USER_ROLES.ADMIN
  )
  useEffect(() => {
    if (isTrainersLoading) return

    const trainers = trainersResponse?.data || []
    const trainersLen = trainerOptions.length + trainers.length
    if (trainers?.length > 0) {
      const options = trainers.map(({ id, user_name }) => ({ value: id, label: user_name }))
      if (trainerPageIndex === 0) setTrainerOptions(options)
      else setTrainerOptions((prev) => [...prev, ...options])
    }
    // Calculate Hash More
    const total = trainersResponse?.metadata?.total_records
    setHashMoreTrainer(trainersLen < total)
  }, [trainersResponse])

  const loadMoreTrainers = () => {
    setTrainerPageIntex((prev) => prev + 1)
  }

  // Fetch Trainer Earning-Stats
  params = {
    from_date: null,
    to_date: null,
  }
  const { data: paymentStatsResponse, isLoading: isPaymentStatsLoading } =
    useFetchTrainerPaymentStats(params, userRole === USER_ROLES.TRAINER)

  useEffect(() => {
    if (isPaymentStatsLoading) return
    setPaymentStats({ ...paymentStatsResponse?.data })
  }, [paymentStatsResponse])

  // Fetch Trainer Earnings
  params = {
    trainer_id: selectedTrainer?.id,
    offset: pagination.pageIndex * pagination.pageSize,
    limit: LIMIT,
  }
  if (userRole === USER_ROLES.ADMIN) params.payment_user = USER_ROLES.TRAINER
  const { data: earningsResponse, isLoading } = useFetchTrainerEarnings(params)

  const renderCellContent = (cell, row) => {
    if (!cell) return null

    if (cell.column.id === "select")
      return (
        <Checkbox
          id={`select-row-${row.id}`}
          checked={Boolean(row?.getIsSelected())}
          onCheckedChange={(value) => row?.toggleSelected(!!value)}
          disabled={false}
        />
      )
    if (cell.column.id === "payment_status") {
      const { payment_status } = row.original
      return (
        <span className={`${STATUS_COLOURS_TEXT[payment_status]} font-medium text-sm`}>
          {payment_status}
        </span>
      )
    }

    if (cell.column.id === "sno") {
      return <p>{cell.row.index + 1 + pagination.pageIndex * pagination.pageSize}</p>
    }

    return flexRender(cell.column.columnDef.cell, cell.getContext())
  }

  const columns = userRole === USER_ROLES.ADMIN ? adminColumns : commonColumns
  const { table, found, pageCount } = useTableConfig(
    earningsResponse?.data || [],
    columns,
    earningsResponse?.metadata?.total_records,
    setPagination,
    pagination
  )

  const handleTrainerChange = (option) => {
    if (selectedTrainer?.id === option.value)
      // Unselect
      setSelectedTrainer(null)
    else
      // Select
      setSelectedTrainer({
        id: option.value,
        name: option.label,
      })
  }

  return (
    <Card className="p-8 bg-white rounded-2xl">
      <div className="grid grid-cols-[2fr,1fr] gap-6 mb-4">
        <div>
          <h1 className="text-2xl font-medium">Trainer Earnings</h1>
          {userRole === USER_ROLES.ADMIN && (
            <div className="w-72">
              <InfiniteCustomComboBox
                selected={{
                  label: selectedTrainer?.name,
                  value: selectedTrainer?.id,
                }}
                options={trainerOptions}
                onSelectChange={handleTrainerChange}
                keyName="trainer"
                hasMore={hasMoreTrainer}
                isLoading={isTrainersLoading}
                loadMoreOptions={loadMoreTrainers}
              />
            </div>
          )}

          {userRole === USER_ROLES.TRAINER && (
            <p className="font-medium">Date: 07-01-2024 - 07-02-2024</p>
          )}
        </div>
        {userRole === USER_ROLES.TRAINER && (
          <div className="space-y-4">
            <div className="flex gap-5 justify-end">
              <div className="text-sm">
                <p className="text-gray-500">Total Earning</p>
                <p className="font-medium text-center">{paymentStats?.total_earnings ?? "-"}</p>
              </div>
              <div className="text-sm">
                <p className="text-gray-500">Total Paid</p>
                <p className="font-medium text-center">
                  {paymentStats?.total_completed_payments ?? "-"}
                </p>
              </div>
              <div className="text-sm">
                <p className="text-gray-500">Pending</p>
                <p className="font-medium text-center">
                  {paymentStats?.total_pending_payments ?? "-"}
                </p>
              </div>
            </div>
          </div>
        )}

        {userRole === USER_ROLES.ADMIN && (
          <div className="flex justify-end gap-4">
            <AddPaymentEarningButtons table={table} selectedTrainer={selectedTrainer} />
          </div>
        )}
      </div>

      <div className="w-full">
        <DataTable
          renderCellContent={renderCellContent}
          columns={columns}
          table={table}
          found={found}
          pageCount={pageCount}
          pagination={pagination}
          isLoading={isLoading}
          notFoundPlaceholder={selectedTrainer?.id ? "" : "Select Trainer to see earnings"}
        />
      </div>
    </Card>
  )
}

export default TrainerEarningPage
