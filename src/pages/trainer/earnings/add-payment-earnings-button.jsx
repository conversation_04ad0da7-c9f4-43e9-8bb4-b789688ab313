/* eslint-disable react/prop-types */
import GetStartedButton from "@/components/buttons/get-started-button"
import { failureToast } from "@/components/custom/toasts/tosters"
import { DollarSign, Plus } from "lucide-react"
import { useState } from "react"
import AddEarningSidebar from "./add-earning-sidebar"
import AddPaymentSidebar from "./add-payment-sidebar"

export default function AddPaymentEarningButtons({ table, selectedTrainer }) {
  const [earningSidebarOpen, setEarningSidebarOpen] = useState(false)
  const [paymentSidebarOpen, setPaymentSidebarOpen] = useState(false)
  const [selectedRowIDs, setSelectedRowIDs] = useState([])

  const onPaymentSidebarChange = (value) => {
    if (value === false) setPaymentSidebarOpen(false)
    else {
      const selectedIDs = table?.getSelectedRowModel().rows.map((row) => row.original.id)
      setSelectedRowIDs(selectedIDs)
      if (selectedIDs.length === 0)
        failureToast("No Trainer Selected", "Please select at least one Trainer to add payment")
      else setPaymentSidebarOpen(true)
    }
  }

  return (
    <>
      <GetStartedButton
        text="Add Earning"
        onClick={() => setEarningSidebarOpen(true)}
        icon={Plus}
      />
      <GetStartedButton
        text="Add Payment"
        onClick={() => onPaymentSidebarChange(true)}
        icon={DollarSign}
      />

      <AddEarningSidebar isOpen={earningSidebarOpen} onOpenChange={setEarningSidebarOpen} />
      <AddPaymentSidebar
        isOpen={paymentSidebarOpen}
        onOpenChange={onPaymentSidebarChange}
        selectedIDs={selectedRowIDs}
        trainerID={selectedTrainer?.id}
        trainerName={selectedTrainer?.name}
      />
    </>
  )
}
