import { DateCell } from "@/components/custom/cutsom-table/table-cells"
import { Checkbox } from "@/components/ui/checkbox"

export const commonColumns = [
  {
    id: "sno",
    accessorKey: "sno",
    header: "S no",
  },

  {
    id: "course_name",
    accessorKey: "course_name",
    header: "Course",
  },
  {
    id: "module_name",
    accessorKey: "module_name",
    header: "Module",
  },
  {
    id: "resource_name",
    accessorKey: "resource_name",
    header: "Topic",
  },
  {
    id: "amount",
    accessorKey: "amount",
    header: "Payment",
  },
  {
    id: "hours",
    accessorKey: "hours",
    header: "Hours",
  },
  {
    id: "work_date",
    accessorKey: "work_date",
    header: "Date",
    cell: ({ row }) => <DateCell value={row.original?.work_date} />,
  },
  {
    id: "payment_status",
    accessorKey: "payment_status",
    header: "Status",
  },
]

// export const trainerColumns = [
//   {
//     id: "select",
//     header: ({ table }) => (
//       <Checkbox
//         id="select-all"
//         checked={table?.getIsAllRowsSelected() || false}
//         onCheckedChange={(value) => table?.toggleAllRowsSelected(!!value)}
//       />
//     ),
//     enableSorting: false,
//     enableColumnFilter: false,
//   },
//   ...commonColumns,
// ]
export const adminColumns = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        id="select-all"
        checked={table?.getIsAllRowsSelected() || false}
        onCheckedChange={(value) => table?.toggleAllRowsSelected(!!value)}
      />
    ),
    enableSorting: false,
    enableColumnFilter: false,
  },
  {
    id: "sno",
    accessorKey: "sno",
    header: "S no",
  },
  {
    id: "trainer_name",
    accessorKey: "trainer_name",
    header: "Trainer Name",
  },

  ...commonColumns.filter((column) => column.id !== "sno"),
]
