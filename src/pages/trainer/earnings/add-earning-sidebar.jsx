/* eslint-disable no-underscore-dangle */
import LoadingSpinner from "@/components/custom/LoadingSpinner"
import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { She<PERSON>, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet"
import { Switch } from "@/components/ui/switch"
import {
  useFetchCourseRourseMinInfo,
  useGetCourseByBatch,
} from "@/services/query/live-course.query"
import { useCreateTrainerEarning } from "@/services/query/payment.query"
import { debounce } from "lodash"
import PropTypes from "prop-types"
import { useEffect, useState } from "react"
import InfiniteCustomComboBox from "../../../components/dropdowns/infinite-custom-combo-box"
import InfinityTrainersComboBox from "./infinity-trainers-combo-box"

const LIMIT = 15
const INTERVIEW = "interview"
const CLASS = "class"

const getUniqueOptions = (existingOptions, newOptions) => {
  const optionsSet = new Set(existingOptions.map(({ value }) => value))
  return newOptions.filter(({ value }) => !optionsSet.has(value))
}

const AddEarningSidebar = ({ isOpen, onOpenChange }) => {
  const [courseOptions, setCourseOptions] = useState([])
  const [moduleOptions, setModuleOptions] = useState([])
  const [topicOptions, setTopicOptions] = useState([])
  const [trainerOptions, setTrainerOptions] = useState([])
  const [modules, setModules] = useState([])

  const [courseSearch, setcourseSearch] = useState(null)
  const [coursePageIndex, setCoursePageIndex] = useState(0)
  const [hasMore, setHashMore] = useState(true)
  const [isInterview, setIsInterview] = useState(false)
  const initialFormData = {
    type: CLASS,
    courseID: "",
    courseName: "",
    moduleID: "",
    moduleName: "",
    topicID: "",
    topicName: "",
    trainerID: "",
    trainerName: "",
    date: "",
    payment: "",
    hours: "",
    mockInterviewID: "",
    mockInterViewName: "",
  }
  const initialFormErrors = {
    courseErr: null,
    moduleErr: null,
    topicErr: null,
    trainerErr: null,
    dateErr: null,
    paymentErr: null,
    hoursErr: null,
    mockInterviewErr: null,
  }

  const [formData, setFormData] = useState({ ...initialFormData })
  const [formErrors, setFormErrors] = useState({ ...initialFormErrors })
  const entities = {
    course: ["courseID", "courseName", "courseErr"],
    module: ["moduleID", "moduleName", "moduleErr"],
    topic: ["topicID", "topicName", "topicErr"],
    trainer: ["trainerID", "trainerName", "trainerErr"],
    mockInterview: ["mockInterviewID", "mockInterViewName", "mockInterviewErr"],
  }

  // Fetch Batch Courses
  const { data: courseResponse, isLoading: courseLoading } = useGetCourseByBatch({
    course_type: "LIVE",
    search_query: courseSearch,
    publish_status: "PUBLISHED",
    offset: coursePageIndex,
    limit: LIMIT,
    order: "DESC",
    for_subscribed: true,
  })

  useEffect(() => {
    if (courseLoading) return
    const courses = courseResponse?.get_course?.courses || []
    const coursesLen = courses.length + courseOptions.length
    if (coursesLen > 0) {
      const options = courses.map(({ id, title }) => ({ value: id, label: title }))
      if (coursePageIndex === 0) setCourseOptions(options)
      else {
        const newFilterdOptions = getUniqueOptions(courseOptions, options)
        setCourseOptions([...courseOptions, ...newFilterdOptions])
      }
    }
    // Calculate Hash More
    const total = courseResponse?.get_course?.total_records
    setHashMore(coursesLen < total)
  }, [courseResponse])

  const loadMoreCourses = () => {
    setCoursePageIndex((prev) => prev + 1)
  }

  // Fetch Selected Course's Modules, Topics and Trainers
  const { data: courseModuleResponse, isLoading: courseMouleLoading } = useFetchCourseRourseMinInfo(
    { course_id: formData.courseID },
    Boolean(formData.courseID)
  )

  useEffect(() => {
    if (courseMouleLoading) return

    // Load Modules
    const _modules = courseModuleResponse?.get_module_resource?.modules
    if (_modules?.length > 0) {
      const options = _modules.map(({ id, module_name }) => ({ value: id, label: module_name }))
      setModuleOptions(options)
      setModules(_modules)
    }

    // Load Trainers
    const _trainers = courseModuleResponse?.get_module_resource?.trainers
    if (_trainers?.length > 0) {
      const options = _trainers.map(({ id, name }) => ({ value: id, label: name }))
      setTrainerOptions(options)
    }

    // Reset Module & Topic & Trainer Fields On Course Change
    setFormData((prev) => ({
      ...prev,
      moduleID: "",
      moduleName: "",
      topicID: "",
      topicName: "",
      trainerID: "",
      trainerName: "",
    }))
  }, [courseModuleResponse])

  // Load Topics After Selecting Module
  useEffect(() => {
    if (!formData.moduleID || modules?.length < 1) {
      setTopicOptions([])
      return
    }

    const selectedModule = modules.find(({ id }) => id === formData.moduleID)
    const { resources } = selectedModule
    if (!resources) return

    const options = resources.map(({ id, resource_name }) => ({ value: id, label: resource_name }))
    setTopicOptions(options)

    // Reset Topic Fields
    setFormData((prev) => ({
      ...prev,
      topicID: "",
      topicName: "",
    }))
  }, [formData.moduleID])

  // Load Triners When Form Type Changed to 'interview'
  useEffect(() => {}, [formData.type])

  const handleCourseSearch = debounce((value) => {
    setCoursePageIndex(0)
    setcourseSearch(value)
  }, 250)

  const handleSelectChange = (option, keyName) => {
    const [idField, nameField, errField] = entities[keyName]
    console.log(idField, nameField, errField, option, keyName)
    if (formData[idField] === option.value) {
      // Unselect
      setFormData((prev) => ({
        ...prev,
        [nameField]: "",
        [idField]: "",
      }))
      if (keyName === "course") {
        setModuleOptions([])
        setTopicOptions([])
        setTrainerOptions([])
      } else if (keyName === "module") setTopicOptions([])
    } else
      // Select
      setFormData((prev) => ({
        ...prev,
        [nameField]: option.label,
        [idField]: option.value,
      }))

    setFormErrors((prev) => ({ ...prev, [errField]: "" }))
  }

  const handleInputChange = (keyName, value, errField) => {
    setFormData((prev) => ({
      ...prev,
      [keyName]: value,
    }))

    if (value) setFormErrors((prev) => ({ ...prev, [errField]: "" }))
  }

  // Reset Form
  const resetForm = () => {
    setFormData({ ...initialFormData })
  }

  console.log(courseModuleResponse, "trainerOptions")

  // Validation
  const validateValues = () => {
    let isValid = true
    const errors = { ...initialFormErrors }
    if (formData.type === CLASS) {
      if (!formData.courseName) {
        isValid = false
        errors.courseErr = "Required"
      }
      if (!formData.moduleName) {
        isValid = false
        errors.moduleErr = "Required"
      }
      if (!formData.topicName) {
        isValid = false
        errors.topicErr = "Required"
      }
    } else if (!formData.mockInterViewName) {
      isValid = false
      errors.mockInterviewErr = "Required"
    }

    if (!formData.trainerName) {
      isValid = false
      errors.trainerErr = "Required"
    }
    if (!formData.date) {
      isValid = false
      errors.dateErr = "Required"
    }
    if (!formData.payment) {
      isValid = false
      errors.paymentErr = "Required"
    }
    if (!formData.hours) {
      isValid = false
      errors.hoursErr = "Required"
    }

    setFormErrors(errors)
    return isValid
  }

  const { mutate: createEarningMutate, isPending } = useCreateTrainerEarning()
  const handleSubmit = (e) => {
    e.preventDefault()

    if (!validateValues()) return
    setFormErrors({ ...initialFormErrors })

    // Prepare Params
    const payload = {
      trainer_id: formData.trainerID,
      work_date: formData.date,
      amount: formData.payment,
      hours: formData.hours,
    }

    if (formData.type === INTERVIEW) payload.mock_interview_id = formData.mockInterviewID
    else {
      payload.course_id = formData.courseID
      payload.course_name = formData.courseName
      payload.module_id = formData.moduleID
      payload.module_name = formData.moduleName
      payload.resource_id = formData.topicID
      payload.resource_name = formData.topicName
    }

    createEarningMutate(payload, {
      onSuccess: () => {
        successToast("Earning Added", "The earning has been successfully added!")
        resetForm()
        onOpenChange(false)
      },
      onError: () => {
        failureToast("Failed to add earning", "")
      },
    })
  }

  const getLabel = (htmlFor, label, errField) => (
    <Label htmlFor={htmlFor} className={formErrors[errField] && "text-red-500"}>
      {label}
      <span className={formErrors[errField] ? "text-red-500" : "text-primary"}> *</span>
    </Label>
  )

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="w-[400px]">
        <SheetHeader>
          <SheetTitle>Add New Earning</SheetTitle>
        </SheetHeader>
        <form onSubmit={handleSubmit} className="space-y-4 mt-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-x-2">
              <Switch
                id="payment-type"
                checked={isInterview}
                onCheckedChange={(checked) => {
                  setIsInterview(checked)
                  resetForm()
                  setFormData((prev) => ({
                    ...prev,
                    type: checked ? INTERVIEW : CLASS,
                  }))
                }}
                className="w-10 h-5"
              />
              <Label htmlFor="payment-type">Pay for Interview</Label>
            </div>
            <Button
              className="text-primary underline cursor-pointer"
              onClick={() => resetForm()}
              type="button"
            >
              clear
            </Button>
          </div>

          {isInterview ? (
            <>
              {/* ALL TRAINERS */}
              <div className="space-y-2">
                {getLabel("trainer", "Trainer", "trainerErr")}
                <InfinityTrainersComboBox
                  selectedTrainer={{
                    label: formData.trainerName,
                    value: formData.trainerID,
                  }}
                  handleTrainerChange={handleSelectChange}
                  isFetchEnabled={formData.type === INTERVIEW}
                />
              </div>
              <div className="space-y-2">
                {getLabel("mockInterviewName", "Interview Type", "mockInterviewErr")}
                <InfiniteCustomComboBox
                  selected={{
                    label: formData.mockInterViewName,
                    value: formData.mockInterviewID,
                  }}
                  options={[]}
                  onSelectChange={(option, keyName) => handleSelectChange(option, keyName)}
                  handleSearchChange={handleCourseSearch}
                  keyName="mockInterview"
                  hasMore={hasMore}
                  isLoading={courseLoading}
                  loadMoreOptions={loadMoreCourses}
                />
              </div>
            </>
          ) : (
            <>
              {/* COURSE */}
              <div className="space-y-2">
                {getLabel("courseName", "Course Name", "courseErr")}
                <InfiniteCustomComboBox
                  selected={{
                    label: formData.courseName,
                    value: formData.courseID,
                  }}
                  options={courseOptions}
                  onSelectChange={(course, keyName) => handleSelectChange(course, keyName)}
                  handleSearchChange={handleCourseSearch}
                  keyName="course"
                  hasMore={hasMore}
                  isLoading={courseLoading}
                  loadMoreOptions={loadMoreCourses}
                />
              </div>

              {/* MODULE */}
              <div className="space-y-2">
                {getLabel("classModule", "Class Module", "moduleErr")}
                <InfiniteCustomComboBox
                  selected={{
                    label: formData.moduleName,
                    value: formData.moduleID,
                  }}
                  options={moduleOptions}
                  onSelectChange={(module, keyName) => handleSelectChange(module, keyName)}
                  keyName="module"
                />
              </div>

              {/* TOPIC */}
              <div className="space-y-2">
                {getLabel("classTopic", "Class Topic", "topicErr")}
                <InfiniteCustomComboBox
                  selected={{
                    label: formData.topicName,
                    value: formData.topicID,
                  }}
                  options={topicOptions}
                  onSelectChange={handleSelectChange}
                  keyName="topic"
                />
              </div>

              {/* COURSE RELATED TRAINERS */}
              <div className="space-y-2">
                {getLabel("trainer", "Trainer", "trainerErr")}
                <InfiniteCustomComboBox
                  selected={{
                    label: formData.trainerName,
                    value: formData.trainerID,
                  }}
                  options={trainerOptions}
                  onSelectChange={handleSelectChange}
                  keyName="trainer"
                />
              </div>
            </>
          )}

          <div className="space-y-2">
            {getLabel("date", "Date", "dateErr")}
            <Input
              id="date"
              type="date"
              value={formData.date}
              onChange={(e) => handleInputChange(e.target.id, e.target.value, "dateErr")}
            />
          </div>

          <div className="space-y-2">
            {getLabel("payment", "Payment", "paymentErr")}
            <Input
              id="payment"
              type="number"
              value={formData.payment}
              onChange={(e) => handleInputChange(e.target.id, e.target.value, "paymentErr")}
            />
          </div>

          <div className="space-y-2">
            {getLabel("hours", "Hours", "hoursErr")}
            <Input
              id="hours"
              type="number"
              value={formData.hours}
              onChange={(e) => handleInputChange(e.target.id, e.target.value, "hoursErr")}
            />
          </div>

          <div className="flex gap-3 mt-6">
            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button variant="primary" type="submit" className="w-full">
              {isPending ? <LoadingSpinner /> : "Add"}
            </Button>
          </div>
        </form>
      </SheetContent>
    </Sheet>
  )
}

AddEarningSidebar.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onOpenChange: PropTypes.func.isRequired,
}

export default AddEarningSidebar
