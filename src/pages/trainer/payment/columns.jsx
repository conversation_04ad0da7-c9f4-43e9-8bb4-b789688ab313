import { DateCell } from "@/components/custom/cutsom-table/table-cells"

export const commonColumns = [
  {
    id: "amount_paid",
    accessorKey: "amount_paid",
    header: "Paid Amount",
  },
  {
    id: "paid_by",
    accessorKey: "paid_by",
    header: "Paid By",
  },
  {
    id: "payment_method",
    accessorKey: "payment_method",
    header: "Paid Method",
  },
  {
    id: "reference",
    accessorKey: "reference",
    header: "Reference",
  },
  {
    id: "payment_date",
    accessorKey: "payment_date",
    header: "Date",
    cell: ({ row }) => <DateCell value={row.original?.payment_date} />,
  },
]

export const adminColumns = [
  {
    id: "id",
    accessorKey: "id",
    header: "ID",
  },
  {
    id: "trainer_name",
    accessorKey: "trainer_name",
    header: "Trainer Name",
  },
  ...commonColumns,
]

export const trainerColumns = [
  {
    id: "id",
    accessorKey: "id",
    header: "ID",
  },
  ...commonColumns,
]
