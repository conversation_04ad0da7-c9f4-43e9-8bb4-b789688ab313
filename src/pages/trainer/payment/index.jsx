/* eslint-disable no-case-declarations */
import { Card } from "@/components/ui/card"
import DataTable from "@/components/custom/cutsom-table"
import useTableConfig from "@/hooks/use-table.hooks"
import { flexRender } from "@tanstack/react-table"
import { useSelector } from "react-redux"
import ct from "@constants/"
import { USER_ROLES } from "@/utils/constants"
import CustomSearchbar from "@/components/custom/custom-search"
import { useState } from "react"
import { useFetchPayments } from "@/services/query/payment.query"
import { openPublicUrl } from "@/services/api/project-api."
import { Button } from "@/components/ui/button"
import { FaFileDownload } from "react-icons/fa"
import { trainerColumns, adminColumns } from "./columns"

const LIMIT = 10

const TrainerPaymentPage = () => {
  const { userRole } = useSelector((st) => st[ct.store.USER_STORE])

  const [searchingValue, setSearchingValue] = useState("")
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })

  // Preapare search params
  const params = {
    search_query: searchingValue,
    offset: pagination.pageIndex * pagination.pageSize,
    limit: LIMIT,
  }
  if (userRole === USER_ROLES.ADMIN) params.payment_user = USER_ROLES.TRAINER

  const { data: paymentsData, isLoading } = useFetchPayments(params)

  const columns = userRole === USER_ROLES.ADMIN ? adminColumns : trainerColumns

  const downloadFile = (cloud_path) => {
    openPublicUrl(cloud_path)
  }

  const renderCellContent = (cell, row) => {
    switch (cell?.column.id) {
      case "id":
        return <span>{row.index + 1 + pagination.pageIndex * pagination.pageSize}</span>
      case "reference":
        const { reference } = row?.original || {}
        return reference ? (
          <Button onClick={() => downloadFile(reference)}>
            <FaFileDownload className="w-5 h-5 text-yellow-500" />
          </Button>
        ) : (
          "-"
        )

      default:
        return flexRender(cell.column.columnDef.cell, cell.getContext())
    }
  }

  const { table, found, pageCount } = useTableConfig(
    paymentsData?.data || [],
    columns,
    paymentsData?.metadata?.total_records,
    setPagination,
    pagination
  )

  return (
    <Card className="p-8 bg-white rounded-2xl">
      <div className="grid grid-cols-[2fr,1fr] gap-6">
        <div>
          <h1 className="text-2xl font-medium">Trainer Payments</h1>
        </div>
      </div>
      {userRole === USER_ROLES.ADMIN && (
        <div className="flex justify-between mb-3">
          <CustomSearchbar
            inputSize="w-[20rem]"
            placeholder="Search by Trainer name..."
            searchedValue={searchingValue}
            setSearchedValue={(e) => setSearchingValue(e?.target.value)}
          />
        </div>
      )}
      <div className="w-full">
        <DataTable
          renderCellContent={renderCellContent}
          columns={columns}
          table={table}
          found={found}
          pageCount={pageCount}
          pagination={pagination}
          isLoading={isLoading}
        />
      </div>
    </Card>
  )
}

export default TrainerPaymentPage
