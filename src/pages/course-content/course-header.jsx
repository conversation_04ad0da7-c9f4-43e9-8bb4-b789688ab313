import { List } from "lucide-react"
import { Button } from "@/components/ui/button"
import PropTypes from "prop-types"

const CourseHeader = ({ showResources, showNotes, setShowNotes, setShowResources, courseData }) => {
  return (
    <div className="bg-blue-600  text-white px-6 rounded-tr-2xl border-t-2 flex items-center justify-between shadow-md">
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          className="lg:hidden hover:bg-blue-700 p-2 rounded-lg transition-colors"
          onClick={() => setShowNotes(!showNotes)}
        >
          <List size={24} />
        </Button>

        <h1 className="text-xl text-white mt-4 font-semibold">
          {courseData?.videoSection?.title} : {courseData?.videoSection?.lessons[0]?.title}
        </h1>
      </div>
      <div className="flex items-center space-x-4">
        <Button
          variant="link"
          onClick={() => setShowResources(!showResources)}
          className="text-sm border border-white/30 px-4 py-2 rounded-xl text-white transition-colors"
        >
          Resources
        </Button>
      </div>
    </div>
  )
}

CourseHeader.propTypes = {
  showNotes: PropTypes.bool.isRequired,
  setShowNotes: PropTypes.func.isRequired,
  showResources: PropTypes.bool.isRequired,
  setShowResources: PropTypes.func.isRequired,
  courseData: PropTypes.shape({
    videoSection: PropTypes.shape({
      title: PropTypes.string,
      lessons: PropTypes.arrayOf(
        PropTypes.shape({
          title: PropTypes.string,
        })
      ),
    }),
  }).isRequired,
}

export default CourseHeader
