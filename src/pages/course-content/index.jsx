import { useEffect, useRef, useState } from "react"
import { useLocation, useNavigate } from "react-router-dom"

import { useFetchResources } from "@/services/query/course-video.query"
import CourseHeader from "./course-header"
import CourseResourcesPanel from "./course-resources-panel"
import CourseSidebar from "./course-sidebar"
import CourseVideoPlayer from "./course-video-player"

const CourseContent = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const muxPlayerRef = useRef(null)
  const [courseData, setCourseData] = useState({})
  const [resources, setResources] = useState([])
  const id = location.state?.id

  const { courseId, moduleId, moduleName } = location.state || {}
  const {
    data: resourceData,
    isLoading: isLoadingResources,
    refetch: fetchResources,
  } = useFetchResources(courseId, moduleId)

  const [currentVideo, setCurrentVideo] = useState({
    sectionKey: "",
    lessonIndex: 0,
    playing: false,
  })

  const [expandedSection, setExpandedSection] = useState("")
  const [showNotes, setShowNotes] = useState(false)
  const [showResources, setShowResources] = useState(false)
  const [lastWatchedTime, setLastWatchedTime] = useState({})
  const [isMuted, setIsMuted] = useState(false)
  const [currentResourceType, setCurrentResourceType] = useState(null)
  const [currentDocumentUrl, setCurrentDocumentUrl] = useState("")

  // New state for mobile view detection
  const [isMobileView, setIsMobileView] = useState(false)

  // Listen for window resize to detect mobile view
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(window.innerWidth < 640)
    }

    // Set initial value
    handleResize()

    // Listen for window resize
    window.addEventListener("resize", handleResize)

    // Clean up
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  // Process resources data when it loads (existing code)
  useEffect(() => {
    if (resourceData?.data?.data) {
      const resourcesData = resourceData.data.data
      setResources(resourcesData)

      // Build course data structure from resources
      const newCourseData = {}

      // Group resources by type
      const videoResources = resourcesData.filter((r) => r.resource_type === "VIDEO")
      const documentResources = resourcesData.filter((r) => r.resource_type === "DOCUMENT")
      const linkResources = resourcesData.filter((r) => r.resource_type === "LINK")

      // Create a section for video content
      if (videoResources.length > 0) {
        newCourseData.videoSection = {
          title: moduleName || "Video Content",
          progress: `${videoResources.filter((r) => r.is_completed).length}/${videoResources.length}`,
          completed: videoResources.every((r) => r.is_completed),
          lessons: videoResources.map((resource) => ({
            id: `video-${resource.id}`,
            title: resource.resource_name,
            description: resource.resource_description,
            duration: formatTime(resource.total_duration || 0),
            completed: resource.is_completed || false,
            playbackId: `video-${resource.id}`,
            type: "video",
            watched:
              resource.total_duration && resource.watched_duration
                ? (resource.watched_duration / resource.total_duration) * 100
                : 0,
            isPlaying: false,
            resources: [], // Will be populated with associated documents
            resourceId: resource.id, // Store the original resource ID
          })),
        }
      }

      // Associate documents with videos for resources panel
      if (videoResources.length > 0 && (documentResources.length > 0 || linkResources.length > 0)) {
        newCourseData.videoSection.lessons = newCourseData.videoSection.lessons.map((lesson) => {
          return {
            ...lesson,
            resources: [...documentResources, ...linkResources].map((resource) => ({
              id: `resource-${resource.id}`,
              name: resource.resource_name,
              type: resource.resource_type,
              link: resource.resource_link,
            })),
          }
        })
      }

      setCourseData(newCourseData)

      // Set initial expanded section and current video
      if (Object.keys(newCourseData).length > 0) {
        const firstSectionKey = Object.keys(newCourseData)[0]
        setExpandedSection(firstSectionKey)

        // Find a video to start with - preferably one that's not completed or has progress
        let startSectionKey = firstSectionKey
        let startLessonIndex = 0

        for (const [sectionKey, section] of Object.entries(newCourseData)) {
          if (section.title === "Video Content") {
            const incompleteIndex = section.lessons.findIndex(
              (lesson) => !lesson.completed && lesson.type === "video"
            )

            if (incompleteIndex !== -1) {
              startSectionKey = sectionKey
              startLessonIndex = incompleteIndex
              break
            }
          }
        }

        setCurrentVideo({
          sectionKey: startSectionKey,
          lessonIndex: startLessonIndex,
          playing: false,
        })

        // Set the initial resource type based on the first active lesson
        const initialLesson = newCourseData[startSectionKey]?.lessons[startLessonIndex]
        if (initialLesson) {
          setCurrentResourceType(initialLesson.type)
          if (initialLesson.type === "document" && initialLesson.link) {
            setCurrentDocumentUrl(initialLesson.link)
          }
        }
      }
    }
  }, [resourceData, moduleName])

  // Helper functions (formatTime, etc.) - same as before
  const formatTime = (seconds) => {
    if (!seconds) return "0:00"
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, "0")}`
  }

  // Other functions (checkSectionCompletion, toggleLessonPlayback, etc.) - same as before
  const checkSectionCompletion = (sectionKey) => {
    const section = courseData[sectionKey]
    if (!section) return

    const allLessonsCompleted = section.lessons.every((lesson) => lesson.completed)

    if (allLessonsCompleted && !section.completed) {
      setCourseData((prev) => ({
        ...prev,
        [sectionKey]: {
          ...prev[sectionKey],
          completed: true,
        },
      }))

      const sectionKeys = Object.keys(courseData)
      const currentIndex = sectionKeys.indexOf(sectionKey)
      const nextSectionKey = sectionKeys[currentIndex + 1]

      if (nextSectionKey) {
        setExpandedSection(nextSectionKey)
      }
    }
  }

  const toggleLessonPlayback = (sectionKey, lessonIndex) => {
    const isCurrentLesson =
      currentVideo.sectionKey === sectionKey && currentVideo.lessonIndex === lessonIndex

    if (!isCurrentLesson) {
      setCurrentVideo({
        sectionKey,
        lessonIndex,
        playing: true,
      })
    } else if (muxPlayerRef.current) {
      if (muxPlayerRef.current.paused) {
        muxPlayerRef.current.play()
      } else {
        muxPlayerRef.current.pause()
      }
    }

    setCourseData((prev) => ({
      ...prev,
      [sectionKey]: {
        ...prev[sectionKey],
        lessons: prev[sectionKey].lessons.map((lesson, idx) => ({
          ...lesson,
          isPlaying: idx === lessonIndex ? !lesson.isPlaying : false,
        })),
      },
    }))
  }

  const handleLoadedMetadata = (event, sectionKey, lessonIndex) => {
    if (!event.target || !sectionKey || typeof lessonIndex !== "number") return

    const { duration } = event.target

    setCourseData((prevData) => ({
      ...prevData,
      [sectionKey]: {
        ...prevData[sectionKey],
        lessons: prevData[sectionKey].lessons.map((lesson, index) =>
          index === lessonIndex ? { ...lesson, duration: formatTime(duration) } : lesson
        ),
      },
    }))

    // Also update the resources array with total duration
    if (resources.length > 0 && sectionKey && duration) {
      const currentLesson = courseData[sectionKey]?.lessons[lessonIndex]
      if (currentLesson && currentLesson.resourceId) {
        setResources((prev) =>
          prev.map((resource) =>
            resource.id === currentLesson.resourceId
              ? { ...resource, total_duration: duration }
              : resource
          )
        )
      }
    }
  }

  const handleVideoComplete = () => {
    if (!currentVideo.sectionKey || !courseData[currentVideo.sectionKey]) return

    const currentSection = courseData[currentVideo.sectionKey]
    const currentLesson = currentSection.lessons[currentVideo.lessonIndex]

    // Update current video as completed
    setCourseData((prev) => {
      const updatedSection = {
        ...prev[currentVideo.sectionKey],
        lessons: prev[currentVideo.sectionKey].lessons.map((lesson, index) =>
          index === currentVideo.lessonIndex
            ? { ...lesson, completed: true, watched: 100, isPlaying: false }
            : lesson
        ),
      }

      const completedCount = updatedSection.lessons.filter((l) => l.completed).length
      updatedSection.progress = `${completedCount}/${updatedSection.lessons.length}`

      return {
        ...prev,
        [currentVideo.sectionKey]: updatedSection,
      }
    })

    // Update resources as completed
    if (currentLesson && currentLesson.resourceId) {
      setResources((prev) =>
        prev.map((resource) =>
          resource.id === currentLesson.resourceId
            ? {
              ...resource,
              is_completed: true,
              watched_duration: resource.total_duration || 0,
            }
            : resource
        )
      )
    }

    // Check if the section is completed
    checkSectionCompletion(currentVideo.sectionKey)

    // Find next video to play
    const nextSectionKeys = Object.keys(courseData)
    const currentSectionIndex = nextSectionKeys.indexOf(currentVideo.sectionKey)
    const currentLessons = courseData[currentVideo.sectionKey].lessons

    if (currentVideo.lessonIndex < currentLessons.length - 1) {
      // Next lesson in same section
      setCurrentVideo({
        sectionKey: currentVideo.sectionKey,
        lessonIndex: currentVideo.lessonIndex + 1,
        playing: true,
      })
    } else if (currentSectionIndex < nextSectionKeys.length - 1) {
      // First lesson in next section
      const nextSectionKey = nextSectionKeys[currentSectionIndex + 1]
      setCurrentVideo({
        sectionKey: nextSectionKey,
        lessonIndex: 0,
        playing: true,
      })
      setExpandedSection(nextSectionKey)
    }

    // Refetch resources after video completion
    fetchResources()
  }

  const handleBack = () => {
    navigate(-1)
  }

  const renderContent = () => {
    if (currentResourceType === "video") {
      return (
        <CourseVideoPlayer
          muxPlayerRef={muxPlayerRef}
          courseData={courseData}
          currentVideo={currentVideo}
          isMuted={isMuted}
          handleLoadedMetadata={handleLoadedMetadata}
          handleVideoComplete={handleVideoComplete}
          setLastWatchedTime={setLastWatchedTime}
          setCourseData={setCourseData}
          resources={resources}
        />
      )
    }
    if (currentResourceType === "document" && currentDocumentUrl) {
      const BASE_URL = "https://training10xapi.10xscale.ai"
      return (
        <div className="flex-1 overflow-hidden bg-gray-100 rounded-t-lg sm:rounded-tr-2xl relative">
          <iframe
            src={`${BASE_URL}/${currentDocumentUrl}#toolbar=0`}
            className="w-full h-full border-0"
            title="Document Viewer"
          />
        </div>
      )
    }

    // Default fallback
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-100 rounded-t-lg sm:rounded-tr-2xl">
        <p className="text-gray-500">Select a resource to view</p>
      </div>
    )
  }

  return (
    <div className="flex flex-col sm:flex-row h-[calc(100vh-3.5rem)]  sm:h-[calc(100vh-7rem)] bg-gray-50 sm:rounded-2xl overflow-hidden relative">
      {isLoadingResources ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="w-12 h-12 border-4 border-t-blue-500 border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin mx-auto mb-4" />
            <p className="text-gray-600">Loading course resources...</p>
          </div>
        </div>
      ) : (
        <>
          {/* Use ResponsiveCourseSidebar */}
          <CourseSidebar
            handleBack={handleBack}
            courseData={courseData}
            expandedSection={expandedSection}
            setExpandedSection={setExpandedSection}
            currentVideo={currentVideo}
            setCurrentVideo={setCurrentVideo}
            toggleLessonPlayback={toggleLessonPlayback}
            showNotes={showNotes}
            setShowNotes={setShowNotes}
            muxPlayerRef={muxPlayerRef}
          />

          {/* Main content area - adjusted for better mobile and tablet display */}
          <div
            className={`flex-1 flex flex-col  transition-all duration-300 ${showNotes && isMobileView ? "opacity-20" : "opacity-100"}`}
          >
            <CourseHeader
              showNotes={showNotes}
              setShowNotes={setShowNotes}
              showResources={showResources}
              setShowResources={setShowResources}
              currentVideo={currentVideo}
              courseData={courseData}
              muxPlayerRef={muxPlayerRef}
            />

            {/* Content container - adjusted for mobile view */}
            <div className="flex-1 flex flex-col relative">
              {renderContent()}

              {showResources && (
                <CourseResourcesPanel
                  courseData={courseData}
                  currentVideo={currentVideo}
                  setShowResources={setShowResources}
                  resources={resources.filter((r) => r.resource_type !== "LINK")}
                />
              )}
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default CourseContent
