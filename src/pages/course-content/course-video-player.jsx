import { useEffect, useRef, useState, useMemo } from "react"
import PropTypes from "prop-types"
import { usePostTrackVideo } from "@/services/query/course-video.query"

const BASE_URL = "https://training10xapi.10xscale.ai"

const CourseVideoPlayer = ({
  muxPlayerRef,
  currentVideo,
  courseData,
  setCourseData,
  handleLoadedMetadata,
  handleVideoComplete,
  setLastWatchedTime,
  resources = [],
  isMuted = false,
}) => {
  const videoRef = useRef(null)
  const containerRef = useRef(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const currentVideoIdRef = useRef(null)
  const fetchTimeoutRef = useRef(null)
  const progressTrackingIntervalRef = useRef(null)
  const lastTrackedPositionRef = useRef(0)

  const handleContextMenu = (e) => {
    console.log("CourseVideoPlayer_Loading", e)
    e.preventDefault()
    return false
  }

  // Memoize currentResource to avoid recalculating on every render
  const currentResource = useMemo(() => {
    return resources.find(
      (resource) =>
        resource.resource_type === "VIDEO" &&
        currentVideo &&
        courseData[currentVideo.sectionKey]?.lessons[currentVideo.lessonIndex] &&
        resource.id ===
        courseData[currentVideo.sectionKey].lessons[currentVideo.lessonIndex].resourceId
    )
  }, [resources, currentVideo, courseData])

  // Memoize videoSrc to avoid recalculating on every render
  const videoSrc = useMemo(() => {
    return currentResource
      ? `${BASE_URL}/video-service/v1/videos/stream?cloud_path=${encodeURIComponent(currentResource.resource_link)}`
      : ""
  }, [currentResource])

  // Get the current resource ID
  const currentResourceId = currentResource?.id

  // Get the mutation hook for tracking video progress
  const { mutate: trackVideoProgress } = usePostTrackVideo(currentResourceId)

  // Function to track current position
  const trackCurrentPosition = () => {
    if (!videoRef.current || !currentResourceId) return

    const currentPosition = Math.floor(videoRef.current.currentTime || 0)

    // Only send tracking request if position has changed significantly (at least 1 second)
    if (Math.abs(currentPosition - lastTrackedPositionRef.current) >= 1) {
      console.log(
        `Sending tracking request for video ID: ${currentResourceId}, position: ${currentPosition}s`
      )
      trackVideoProgress({ current_position: currentPosition })
      lastTrackedPositionRef.current = currentPosition
    }
  }

  // Function to start tracking progress
  const startProgressTracking = () => {
    // Clear any existing interval first
    if (progressTrackingIntervalRef.current) {
      clearInterval(progressTrackingIntervalRef.current)
    }

    // Only start tracking if we have a valid resource ID
    if (currentResourceId) {
      console.log(`Starting progress tracking for video ID: ${currentResourceId}`)

      // Send initial tracking request immediately
      trackCurrentPosition()

      // Set up interval for every 15 seconds
      progressTrackingIntervalRef.current = setInterval(() => {
        trackCurrentPosition()
      }, 15000) // Track every 15 seconds
    }
  }

  useEffect(() => {
    const currentContainer = containerRef.current
    // eslint-disable-next-line no-shadow
    const currentVideo = videoRef.current

    if (currentContainer) {
      currentContainer.addEventListener("contextmenu", handleContextMenu)
    }

    if (currentVideo) {
      currentVideo.addEventListener("contextmenu", handleContextMenu)
    }

    return () => {
      if (currentContainer) {
        currentContainer.removeEventListener("contextmenu", handleContextMenu)
      }

      if (currentVideo) {
        currentVideo.removeEventListener("contextmenu", handleContextMenu)
      }
    }
  }, [])

  // Function to stop tracking progress
  const stopProgressTracking = () => {
    if (progressTrackingIntervalRef.current) {
      console.log("Stopping progress tracking")
      clearInterval(progressTrackingIntervalRef.current)
      progressTrackingIntervalRef.current = null

      // Send one last update when stopping
      trackCurrentPosition()
    }
  }

  useEffect(() => {
    if (videoRef.current) {
      muxPlayerRef.current = videoRef.current
    }
  }, [muxPlayerRef])

  useEffect(() => {
    if (!videoRef.current || !videoSrc) return

    // Create a unique identifier for the current video to prevent duplicate fetches
    const currentVideoId = `${currentVideo?.sectionKey}-${currentVideo?.lessonIndex}`

    // Skip if this is the same video we're already playing
    if (currentVideoIdRef.current === currentVideoId && videoRef.current.src) {
      // If we have the same video but are just toggling play/pause, handle that here
      if (currentVideo?.playing && videoRef.current.paused) {
        videoRef.current.play().catch((e) => {
          console.warn("Autoplay prevented:", e)
        })
      } else if (!currentVideo?.playing && !videoRef.current.paused) {
        videoRef.current.pause()
      }
      return
    }

    // Clear any pending timeouts
    if (fetchTimeoutRef.current) {
      clearTimeout(fetchTimeoutRef.current)
    }

    // Stop existing progress tracking when changing videos
    stopProgressTracking()

    // Reset last tracked position for new video
    lastTrackedPositionRef.current = 0

    // Update the current video ID reference
    currentVideoIdRef.current = currentVideoId

    setIsLoading(true)
    setError(null)

    // Debounce the fetch video function to avoid multiple API calls
    fetchTimeoutRef.current = setTimeout(() => {
      const fetchVideo = async () => {
        try {
          // Only set the src if it's different from the current src
          if (videoRef.current.src !== videoSrc) {
            videoRef.current.src = videoSrc
            videoRef.current.load()
          }

          // Apply muted state
          videoRef.current.muted = isMuted

          // Set last position if available
          if (currentResource && currentResource.last_position) {
            videoRef.current.currentTime = currentResource.last_position
            lastTrackedPositionRef.current = Math.floor(currentResource.last_position)
          }

          // Handle play state changes
          const handlePlay = () => {
            console.log("Video started playing")
            startProgressTracking()
          }

          const handlePause = () => {
            console.log("Video paused")
            stopProgressTracking()
          }

          const handleEnded = () => {
            console.log("Video ended")
            stopProgressTracking()

            // Send one final tracking request when video completes
            if (currentResourceId) {
              trackVideoProgress({
                current_position: Math.floor(videoRef.current.duration || 0),
              })
            }

            if (handleVideoComplete) {
              handleVideoComplete()
            }
          }

          // Remove existing event listeners first
          videoRef.current.removeEventListener("play", handlePlay)
          videoRef.current.removeEventListener("pause", handlePause)
          videoRef.current.removeEventListener("ended", handleEnded)

          // Add new event listeners
          videoRef.current.addEventListener("play", handlePlay)
          videoRef.current.addEventListener("pause", handlePause)
          videoRef.current.addEventListener("ended", handleEnded)

          videoRef.current.oncanplay = () => {
            setIsLoading(false)

            if (currentVideo?.playing) {
              videoRef.current.play().catch((e) => {
                console.warn("Autoplay prevented:", e)
              })
            }

            if (currentVideo && handleLoadedMetadata) {
              handleLoadedMetadata(
                {
                  target: videoRef.current,
                },
                currentVideo.sectionKey,
                currentVideo.lessonIndex
              )
            }
          }

          videoRef.current.ontimeupdate = () => {
            if (!videoRef.current) return

            const { currentTime } = videoRef.current
            const { duration } = videoRef.current

            if (duration > 0) {
              const watchedPercentage = (currentTime / duration) * 100

              if (currentVideo && setCourseData && watchedPercentage > 0) {
                setCourseData((prev) => {
                  // Guard against potential null access
                  if (!prev[currentVideo.sectionKey]?.lessons) return prev

                  const updatedLessons = [...prev[currentVideo.sectionKey].lessons]
                  updatedLessons[currentVideo.lessonIndex] = {
                    ...updatedLessons[currentVideo.lessonIndex],
                    watched: watchedPercentage,
                  }

                  return {
                    ...prev,
                    [currentVideo.sectionKey]: {
                      ...prev[currentVideo.sectionKey],
                      lessons: updatedLessons,
                    },
                  }
                })

                // Debounce setLastWatchedTime to reduce updates
                if (!videoRef.current.updateTimeoutId) {
                  videoRef.current.updateTimeoutId = setTimeout(() => {
                    setLastWatchedTime((prev) => ({
                      ...prev,
                      [`${currentVideo.sectionKey}-${currentVideo.lessonIndex}`]: currentTime,
                    }))
                    videoRef.current.updateTimeoutId = null
                  }, 1000) // Update at most once per second
                }
              }
            }
          }
        } catch (err) {
          console.error("Video loading error:", err)
          setError(err.message)
          setIsLoading(false)
        }
      }

      fetchVideo()
    }, 300) // 300ms debounce to avoid rapid changes triggering multiple API calls

    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current)
      }

      stopProgressTracking()

      if (videoRef.current) {
        // Remove event listeners
        videoRef.current.removeEventListener("play", startProgressTracking)
        videoRef.current.removeEventListener("pause", stopProgressTracking)

        // Clear the timeout on cleanup
        if (videoRef.current.updateTimeoutId) {
          clearTimeout(videoRef.current.updateTimeoutId)
          videoRef.current.updateTimeoutId = null
        }

        videoRef.current.oncanplay = null
        videoRef.current.ontimeupdate = null
        videoRef.current.onended = null
        videoRef.current.pause()
      }
    }
  }, [
    videoSrc,
    currentVideo,
    currentResource,
    currentResourceId,
    setCourseData,
    handleLoadedMetadata,
    handleVideoComplete,
    setLastWatchedTime,
    trackVideoProgress,
    isMuted,
  ])

  // Make sure to stop tracking when component unmounts
  useEffect(() => {
    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current)
      }

      stopProgressTracking()

      if (videoRef.current) {
        if (videoRef.current.updateTimeoutId) {
          clearTimeout(videoRef.current.updateTimeoutId)
        }
        videoRef.current.src = ""
        videoRef.current.load()
      }
    }
  }, [])

  // Check if we need to start tracking immediately (if video is already playing)
  useEffect(() => {
    if (videoRef.current && !videoRef.current.paused && currentResourceId) {
      startProgressTracking()
    }

    return () => {
      stopProgressTracking()
    }
  }, [currentResourceId])

  // Apply muted state when it changes
  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.muted = isMuted
    }
  }, [isMuted])

  return (
    <div
      ref={containerRef}
      className="flex-1 overflow-hidden bg-black rounded-tr-2xl relative z-0"
      onContextMenu={handleContextMenu}
    >
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 text-white">
          <div className="bg-red-800 p-4 rounded-lg max-w-md text-center">
            <p className="font-semibold mb-2">Error loading video</p>
            <p className="text-sm">{error}</p>
          </div>
        </div>
      )}

      <video
        ref={videoRef}
        className="w-full h-full object-contain"
        controls
        playsInline
        controlsList="nodownload"
        onContextMenu={handleContextMenu}
      >
        <track kind="captions" src="path/to/captions.vtt" srcLang="en" label="English" />
        Your browser does not support the video tag.
      </video>

      {/*    {videoProgress > 0 && videoProgress < 100 && (
        <div className="absolute bottom-16 left-0 right-0 px-4">
          <div className="bg-gray-700 h-1 rounded-full overflow-hidden">
            <div
              className="bg-blue-500 h-full rounded-full"
              style={{ width: `${videoProgress}%` }}
            />
          </div>
        </div>
      )} */}
    </div>
  )
}

CourseVideoPlayer.propTypes = {
  currentVideo: PropTypes.shape({
    sectionKey: PropTypes.string.isRequired,
    lessonIndex: PropTypes.number.isRequired,
    playing: PropTypes.bool,
  }),
  muxPlayerRef: PropTypes.shape({
    current: PropTypes.instanceOf(Element),
  }),

  courseData: PropTypes.shape({
    [PropTypes.string]: PropTypes.shape({
      lessons: PropTypes.arrayOf(
        PropTypes.shape({
          resourceId: PropTypes.string,
          watched: PropTypes.number,
        })
      ),
    }),
  }),
  setCourseData: PropTypes.func,
  handleLoadedMetadata: PropTypes.func,
  handleVideoComplete: PropTypes.func,
  setLastWatchedTime: PropTypes.func,
  resources: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      resource_type: PropTypes.string.isRequired,
      resource_link: PropTypes.string.isRequired,
      last_position: PropTypes.number,
      watched_duration: PropTypes.number,
      total_duration: PropTypes.number,
    })
  ),
  isMuted: PropTypes.bool,
}

export default CourseVideoPlayer
