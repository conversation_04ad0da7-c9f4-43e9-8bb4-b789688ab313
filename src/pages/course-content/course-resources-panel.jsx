import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  <PERSON>et<PERSON>ontent,
  SheetDescription,

  <PERSON>etHeader,
  SheetTit<PERSON>,
} from "@/components/ui/sheet"
import { openPublicUrl } from "@/services/api/project-api."
import { File, FileText, Download } from "lucide-react"
import PropTypes from "prop-types"

const CourseResourcesPanel = ({ courseData, currentVideo }) => {
  const BASE_URL = "https://training10xapi.10xscale.ai"

  const currentLesson =
    currentVideo && courseData[currentVideo.sectionKey]?.lessons[currentVideo.lessonIndex]

  // Get resources associated with the current lesson
  const lessonResources =
    currentLesson?.resources.filter((resource) => resource.type !== "LINK") || []

  const getResourceIcon = (type) => {
    switch (type) {
      case "DOCUMENT":
        return <FileText size={18} className="text-blue-500" />
      case "VIDEO":
        return <File size={18} className="text-green-500" />
      default:
        return <File size={18} className="text-gray-500" />
    }
  }

  const handleResourceClick = (resource) => {
    if (resource.type === "DOCUMENT") {
      // Open document in a new tab
      window.open(`${BASE_URL}/${resource.link}`, "_blank")
    }
  }

  const handleDownload = (e, resource) => {
    e.stopPropagation()
    openPublicUrl(resource.link)
  }

  return (
    <Sheet defaultOpen>
      <SheetContent className="absolute right-0 top-18 w-72 bg-white border-l h-[calc(100%-4rem)] shadow-xl">
        <SheetHeader>
          <div className="flex items-center justify-between">
            <SheetTitle className="text-lg font-semibold text-gray-800">Resources</SheetTitle>
          </div>
        </SheetHeader>
        <SheetDescription className="mt-6">
          {lessonResources.length > 0 ? (
            <div className="space-y-2">
              {lessonResources.map((resource, index) => (
                <div
                  key={resource.id || index}
                  role="button"
                  tabIndex="0"
                  className="flex items-center py-3 px-4 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors mb-2 text-gray-700"
                  onClick={() => handleResourceClick(resource)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      handleResourceClick(resource)
                    }
                  }}
                >
                  <div className="mr-3">{getResourceIcon(resource.type)}</div>
                  <div className="flex-1">
                    <div className="font-medium">{resource.name}</div>
                    <div className="text-xs text-gray-500">{resource.type.toLowerCase()}</div>
                  </div>
                  {resource.type === "DOCUMENT" && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="p-1"
                      onClick={(e) => handleDownload(e, resource)}
                      title="Download document"
                    >
                      <Download size={16} />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No resources available for this lesson
            </div>
          )}
        </SheetDescription>

      </SheetContent>
    </Sheet>
  )
}

CourseResourcesPanel.propTypes = {
  courseData: PropTypes.shape({
    sectionKey: PropTypes.string,
    lessons: PropTypes.arrayOf(
      PropTypes.shape({
        resources: PropTypes.arrayOf(
          PropTypes.shape({
            id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
            type: PropTypes.string,
            link: PropTypes.string,
            name: PropTypes.string,
          })
        ),
      })
    ),
  }).isRequired,
  currentVideo: PropTypes.shape({
    sectionKey: PropTypes.string.isRequired,
    lessonIndex: PropTypes.number.isRequired,
  }),
}

export default CourseResourcesPanel
