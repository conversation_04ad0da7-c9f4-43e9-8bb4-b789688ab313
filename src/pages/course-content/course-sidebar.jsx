import { Search, X, Minus, Plus, CirclePlay, CirclePause, ArrowLeft, <PERSON>u } from "lucide-react"
import { PiCheckCircleBold } from "react-icons/pi"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import PropTypes from "prop-types"
import { useEffect, useState } from "react"

const CourseSidebar = ({
  courseData,
  expandedSection,
  setExpandedSection,
  currentVideo,
  setCurrentVideo,
  toggleLessonPlayback,
  showNotes,
  setShowNotes,
  muxPlayerRef,
  handleBack,
}) => {
  // State to track current video time for active video
  const [currentVideoTime, setCurrentVideoTime] = useState(0)
  console.log("muxPlayerRef", muxPlayerRef.current)

  // Set up interval to update current video time
  useEffect(() => {
    const updateCurrentTime = () => {
      if (muxPlayerRef?.current) {
        setCurrentVideoTime(Math.floor(muxPlayerRef.current.currentTime || 0))
      }
    }

    // Update every second
    const intervalId = setInterval(updateCurrentTime, 1000)

    // Initial update
    updateCurrentTime()

    return () => clearInterval(intervalId)
  }, [muxPlayerRef])

  // Helper function to format time in MM:SS format
  const formatTime = (seconds) => {
    if (!seconds && seconds !== 0) return "0:00"
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, "0")}`
  }

  // Helper function to check if a video is currently playing
  const isVideoPlaying = (sectionKey, lessonIndex) => {
    return (
      currentVideo?.sectionKey === sectionKey &&
      currentVideo?.lessonIndex === lessonIndex &&
      muxPlayerRef?.current &&
      !muxPlayerRef.current.paused
    )
  }


  const truncateText = (text, maxLength = 30) => {
    if (!text) return ""
    return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text
  }


  const SectionTitle = ({ title, progress }) => {
    const shouldShowTooltip = title && title.length > 30

    if (shouldShowTooltip) {
      return (
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
              <span className="font-semibold text-sm sm:text-base text-gray-700 truncate">
                {truncateText(title, 30)}
              </span>
              <span className="text-xs sm:text-sm text-gray-400 flex-shrink-0">{progress}</span>
            </div>
          </TooltipTrigger>
          <TooltipContent side="top" align="start" className="max-w-xs z-50">
            <p className="text-sm font-medium">{title}</p>
          </TooltipContent>
        </Tooltip>
      )
    }

    return (
      <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
        <span className="font-semibold text-sm sm:text-base text-gray-700 truncate">{title}</span>
        <span className="text-xs sm:text-sm text-gray-400 flex-shrink-0">{progress}</span>
      </div>
    )
  }


  const LessonTitle = ({ title, type, completed }) => {
    const shouldShowTooltip = title && title.length > 30

    if (shouldShowTooltip) {
      return (
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex flex-col min-w-0 flex-1">
              <span
                className={`text-sm font-medium truncate ${completed ? "text-blue-500" : "text-gray-700"
                  }`}
              >
                {truncateText(title, 30)}
              </span>
              <span className="text-xs text-gray-400">{type}</span>
            </div>
          </TooltipTrigger>
          <TooltipContent className="max-w-xs z-0 ms-4">
            <div className="space-y-1">
              <p className="text-sm font-medium">{title}</p>
              <p className="text-xs text-gray-400 capitalize">{type}</p>
            </div>
          </TooltipContent>
        </Tooltip>
      )
    }

    return (
      <div className="flex flex-col min-w-0 flex-1">
        <span
          className={`text-sm font-medium truncate ${completed ? "text-blue-500" : "text-gray-700"
            }`}
        >
          {title}
        </span>
        <span className="text-xs text-gray-400">{type}</span>
      </div>
    )
  }

  return (
    <TooltipProvider delayDuration={300}>
      {/* Mobile Menu Toggle - Fixed at the top of the screen for mobile */}
      <button
        type="button"
        onClick={() => setShowNotes(true)}
        className="fixed top-4 left-4 z-50 bg-white rounded-full p-2 shadow-md sm:hidden"
        aria-label="Open course menu"
      >
        <Menu size={24} />
      </button>

      {/* Overlay for mobile when sidebar is open */}
      {showNotes && (
        <button
          type="button"
          className="fixed inset-0 bg-black/40 z-20 sm:hidden"
          onClick={() => setShowNotes(false)}
        />
      )}

      {/* Main Sidebar */}
      <div
        className={`
          fixed inset-y-0 left-0 z-30 w-full max-w-xs bg-white transition-all duration-300 overflow-y-auto
          shadow-lg border-r border-gray-200
          sm:w-64 md:w-72 lg:w-80 xl:w-96
          ${showNotes ? "translate-x-0" : "-translate-x-full"}
          sm:relative sm:translate-x-0
          h-full 
        `}
      >
        <div className="flex flex-col h-full z-50">
          <div className="p-3 sm:p-4 md:p-6 flex-shrink-0">
            <div className="flex items-center justify-between mb-4 sm:mb-6">
              <h2 className="text-lg sm:text-xl flex items-center font-bold text-gray-800 min-w-0 flex-1">
                <button
                  type="button"
                  onClick={handleBack}
                  className="border rounded-full p-1 me-2 transition-transform duration-200 hover:scale-110 flex-shrink-0"
                  aria-label="Go back"
                >
                  <ArrowLeft size={16} />
                </button>
                <span className="truncate">Course Video</span>
              </h2>
              <Button
                onClick={() => setShowNotes(!showNotes)}
                className="sm:hidden p-1.5 h-auto flex-shrink-0"
                variant="ghost"
                aria-label="Close course menu"
              >
                <X size={20} />
              </Button>
            </div>

            <div className="relative mb-4 sm:mb-6">
              <Input
                type="text"
                placeholder="Search Lesson"
                className="w-full pl-10 pr-4 py-2 sm:py-3 rounded-xl sm:rounded-2xl border border-gray-200 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 text-xs sm:text-sm"
              />
              <Search className="absolute left-3 top-2.5 sm:top-3.5 text-gray-400" size={16} />
            </div>
          </div>

          <div className="flex-grow overflow-y-auto px-3 sm:px-4 md:px-6 pb-4 sm:pb-6 ">
            {Object.entries(courseData).map(([sectionKey, section]) => (
              <div key={sectionKey} className="mb-4 sm:mb-6">
                <div
                  role="button"
                  tabIndex={0}
                  className="flex items-center justify-between cursor-pointer py-2 px-2 sm:py-3 hover:bg-gray-50 rounded-lg transition-colors duration-200"
                  onClick={() =>
                    setExpandedSection(expandedSection === sectionKey ? null : sectionKey)
                  }
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      setExpandedSection(expandedSection === sectionKey ? null : sectionKey)
                    }
                  }}
                >
                  <SectionTitle title={section.title} progress={section.progress} />
                  <span className="text-gray-400 flex-shrink-0 ml-2">
                    {expandedSection === sectionKey ? <Minus size={16} /> : <Plus size={16} />}
                  </span>
                </div>

                {expandedSection === sectionKey && (
                  <div className=" space-y-2 mt-2">
                    {section.lessons.map((lesson, index) => {
                      // Determine if this is the active video
                      const isActive =
                        currentVideo.sectionKey === sectionKey && currentVideo.lessonIndex === index

                      // Get video duration from the current resources if available
                      const totalDuration =
                        isActive && muxPlayerRef?.current?.duration
                          ? muxPlayerRef.current.duration
                          : null

                      // Current time to display for active video only
                      const currentTime = isActive ? currentVideoTime : null

                      // Format time for display
                      const timeDisplay =
                        isActive && currentTime !== null && totalDuration
                          ? `${formatTime(currentTime)} / ${formatTime(totalDuration)}`
                          : lesson.duration

                      // Calculate real-time watched percentage for active video
                      const watchedPercentage =
                        isActive && totalDuration && currentTime
                          ? Math.round((currentTime / totalDuration) * 100)
                          : Math.round(lesson.watched || 0)

                      return (
                        <div
                          key={lesson.id}
                          role="button"
                          tabIndex={0}
                          className={`flex items-center justify-between p-3 cursor-pointer rounded-lg transition-all duration-200
                        ${isActive ? "bg-blue-50 text-blue-600" : "hover:bg-gray-50"}`}
                          onClick={() => {
                            setCurrentVideo({
                              sectionKey,
                              lessonIndex: index,
                              playing: true,
                            })
                          }}
                          onKeyDown={(e) => {
                            if (e.key === "Enter" || e.key === " ") {
                              setCurrentVideo({
                                sectionKey,
                                lessonIndex: index,
                                playing: true,
                              })
                            }
                          }}
                        >
                          <div className="flex items-center space-x-3 min-w-0 flex-1">
                            {lesson.completed ? (
                              <PiCheckCircleBold
                                size={20}
                                className="text-blue-500 flex-shrink-0"
                              />
                            ) : (
                              <button
                                type="button"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  toggleLessonPlayback(sectionKey, index)
                                }}
                                className="focus:outline-none flex-shrink-0"
                              >
                                {isVideoPlaying(sectionKey, index) ? (
                                  <CirclePause size={18} className="text-gray-400" />
                                ) : (
                                  <CirclePlay size={18} className="text-gray-400" />
                                )}
                              </button>
                            )}
                            <LessonTitle
                              title={lesson.title}
                              type={lesson.type}
                              completed={lesson.completed}
                            />
                          </div>
                          <div className="flex items-center space-x-3 flex-shrink-0 ml-3">
                            <span className="text-xs text-gray-400 whitespace-nowrap">
                              {timeDisplay}
                            </span>
                            {watchedPercentage > 0 &&
                              watchedPercentage < 100 &&
                              !lesson.completed && (
                                <span className="text-xs px-2 py-1 bg-blue-100 text-blue-600 rounded-full whitespace-nowrap">
                                  {watchedPercentage}%
                                </span>
                              )}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </TooltipProvider>
  )
}

CourseSidebar.propTypes = {
  courseData: PropTypes.oneOfType([PropTypes.object]),
  expandedSection: PropTypes.string,
  setExpandedSection: PropTypes.func.isRequired,
  currentVideo: PropTypes.shape({
    sectionKey: PropTypes.string.isRequired,
    lessonIndex: PropTypes.number.isRequired,
    playing: PropTypes.bool.isRequired,
  }).isRequired,
  setCurrentVideo: PropTypes.func.isRequired,
  toggleLessonPlayback: PropTypes.func.isRequired,
  showNotes: PropTypes.bool.isRequired,
  setShowNotes: PropTypes.func.isRequired,
  handleBack: PropTypes.func.isRequired,
  muxPlayerRef: PropTypes.shape({
    current: PropTypes.objectOf(PropTypes.number),
  }),
}

export default CourseSidebar
