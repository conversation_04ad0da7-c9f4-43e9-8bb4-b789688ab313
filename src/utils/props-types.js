import PropTypes from "prop-types"

export const sidebarMenusPropsTypes = {
  isExpanded: PropTypes.bool.isRequired,
  userRole: PropTypes.oneOf(["admin", "user", "trainer", "vendor", "marketing"]).isRequired,
}

export const sidebarGroupLablesPropsTypes = {
  content: PropTypes.string.isRequired,
  icon: PropTypes.element.isRequired,
}
export const formsProps = {
  dataTestID: PropTypes.string,
  dataTestIDError: PropTypes.string,
  fieldControlName: PropTypes.string.isRequired,
  control: PropTypes.objectOf,
  label: PropTypes.string.isRequired,
  placeholder: PropTypes.string,
  isRequired: PropTypes.bool,
  type: PropTypes.string,
  onChange: PropTypes.func,
}

export const functionsPropTypes = {
  onDeleteTopic: PropTypes.func,
  onEditTopic: PropTypes.func,
  onEditModule: PropTypes.func,
  onDeleteModule: PropTypes.func,
  onCreateTopic: PropTypes.func,
  onUpdateStatus: PropTypes.func,
  userRole: PropTypes.string,
  onTopicStatus: PropTypes.func,
}

export const isPaidUserPropTypes = {
  class: PropTypes.string,
  paidUser: PropTypes.bool,
}
