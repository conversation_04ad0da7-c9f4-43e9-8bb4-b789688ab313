/* eslint-disable sonarjs/no-duplicate-string */
export const publishStatus = ["PUBLISHED", "DRAFT", "ARCHIVED", "UNLISTED"]

export const fileType = [
  { value: "VIDEO", label: "Video" },
  { value: "DOCUMENT", label: "Document" },
  { value: "LINK", label: "Link" },
]

export const applicationStatus = [
  "APPLIED",
  "UNDER_REVIEW",
  "INTERVIEW_SCHEDULED",
  "OFFERED",
  "REJECTED",
  "WITHDRAWN",
]

export const quizStatus = [
  { value: "ACCEPTED", label: "Accepted" },
  { value: "PENDING", label: "Pending" },
  { value: "REJECTED", label: "Rejected" },
]

export const USER_ROLES = {
  ADMIN: "ADMIN",
  TRAINER: "TRAINER",
  STUDENT: "STUDENT",
  MARKETER: "MARKETER",
  VENDOR: "VENDOR",
}

export const USER_ROLE_OPTIONS = [
  { value: USER_ROLES.ADMIN, label: "Admin" },
  { value: USER_ROLES.TRAINER, label: "Trainer" },
  { value: USER_ROLES.STUDENT, label: "Student" },
  { value: USER_ROLES.MARKETER, label: "Marketer" },
  { value: USER_ROLES.VENDOR, label: "Vendor User" },
]

export const STATUS_COLOURS_TEXT = {
  RED: "text-red-400",
  GREEN: "text-green-400",
  ORANGE: "text-orange-400",
  ACTIVE: "text-green-400",
  COMPLETED: "text-green-400",
  INACTIVE: "text-red-400",
  FAILED: "text-red-400",
  REJECTED: "text-red-400",
  PENDING: "text-orange-400",
  IN_PROGRESS: "text-orange-400",
}

export const STATUS_COLOURS_BG = {
  RED: "bg-red-300",
  GREEN: "bg-green-300",
  ORANGE: "bg-orange-300",
  ACTIVE: "bg-green-300",
  COMPLETED: "bg-green-300",
  INACTIVE: "bg-red-300",
  FAILED: "bg-red-300",
  REJECTED: "bg-red-300",
  PENDING: "bg-orange-300",
  IN_PROGRESS: "bg-orange-300",
}

export const PAYMENT_OPTIONS = {
  STRIPE: "STRIPE",
  PAYPAL: "PAYPAL",
  RAZORPAY: "RAZORPAY",
  MANUAL: "MANUAL",
}

export const PAYMENT_PERCENTAGES = {
  "100%": 100,
  "50%": 50,
}
