import axios from "axios"

// Map of country codes to currencies
export const CURRENCY_CODES = {
  IN: "INR",
  in: "INR",
  US: "USD",
  us: "USD",
}

export const CURRENCY_SYMBOLS = {
  INR: "₹",
  inr: "₹",
  USD: "$",
  usd: "$",
}

export const detectCurrency = async () => {
  if ("currency" in localStorage) {
    return localStorage.getItem("currency")
  }

  try {
    // Using a free IP geolocation API
    const response = await axios.get("https://ipapi.co/json/")
    const { currency, country_code } = response.data
    const detectedCurrency = currency || CURRENCY_CODES[country_code] || "USD"

    localStorage.setItem("currency", detectedCurrency)
    return detectedCurrency
  } catch (error) {
    console.error("Error detecting country:", error)
    return "USD" // Default fallback on error
  }
}
