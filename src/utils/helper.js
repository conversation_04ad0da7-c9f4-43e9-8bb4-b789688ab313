/* eslint-disable sonarjs/no-duplicate-string */
import { jwtDecode } from "jwt-decode"
import { USER_ROLES } from "./constants"

export const truncate = (str, maxLength = 25) => {
  if (str?.length > maxLength) {
    return `${str?.slice(0, maxLength)}...`
  }
  return str
}

export const handleCloseForm = (setOpen, reset) => {
  setOpen(false)
  reset(null)
}

export const findRolesAndRenderUI = ({
  userRole,
  trainerColumns = [],
  studentColumns = [],
  adminColumns = [],
}) => {
  let columns = []
  switch (userRole) {
    case USER_ROLES.ADMIN:
      columns = adminColumns
      break
    case USER_ROLES.TRAINER:
      columns = trainerColumns
      break
    case "student":
      columns = studentColumns
      break
    default:
      return []
  }
  return columns
}
export const badgeColor = (status) => {
  const colors = {
    PUBLISHED: "bg-green-100 text-green-500",
    ACTIVE: "bg-green-100 text-green-500",
    ARCHIVED: "bg-gray-100 text-gray-500",
    UNLISTED: "bg-yellow-100 text-yellow-500",
    DRAFT: "bg-blue-100 text-blue-500",
    REJECTED: "bg-red-100 text-red-500",
    UNDER_REVIEW: "bg-yellow-100 text-yellow-500",
    INTERVIEW_SCHEDULED: "bg-blue-100 text-blue-500",
    OFFERED: "bg-green-100 text-green-500",
    WITHDRAWN: "bg-red-100 text-red-500",
    SUBMITTED: "bg-green-100 text-green-500",
    GRADED: "bg-green-100 text-green-500",
    LIVE: "bg-red-600 dark:bg-red-600 dark:text-white text-white rounded-full",
    SHORT: "bg-blue-600 text-white rounded-full",
    RECORDED: "bg-green-600 text-white rounded-full",

    "NOT SUBMITTED": "bg-red-100 text-red-500",
  }

  return colors[status] || "bg-gray-200 text-gray-600"
}

export const titleChangesByRole = (
  role,
  isUpdate,
  createTitle = "Create",
  updateTitle = "Update"
) => {
  let announcementTitle = createTitle

  if (isUpdate) {
    announcementTitle = ["vendor", "student"].includes(role) ? "Announcement" : updateTitle
  }

  return announcementTitle
}

export const getOffset = (pageIndex, pageSize) => pageIndex * pageSize

export const roleRestrictions = (role, roleType) => {
  switch (roleType) {
    case "admin_trainer":
      return [USER_ROLES.ADMIN, USER_ROLES.TRAINER].includes(role)
    case USER_ROLES.ADMIN:
      return role === USER_ROLES.ADMIN
    case USER_ROLES.TRAINER:
      return role === USER_ROLES.TRAINER
    default:
      return false
  }
}

let navigateFn = null

export const setNavigate = (navigate) => {
  navigateFn = navigate
}

export const navigateTo = (path) => {
  if (navigateFn) navigateFn(path)
  else console.log("Navigate function not set")
}

export const getStatusColor = (status) => {
  console.log("__status", status)
  const colors = {
    PUBLISHED: "text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900",
    DRAFT: "text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-800",
    ACTIVE: "text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900",
    ARCHIVED: "text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900",
    UNLISTED: "text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900",
  }
  return colors[status] || "text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-800"
}

export const isStudent = (course, role) => {
  const isPaidUser = course === "Live courses" && role === USER_ROLES.STUDENT
  return {
    paidUser: isPaidUser,
    class: `disabled:cursor-not-allowed ${isPaidUser ? "cursor-not-allowed" : ""}`,
  }
}

/**
 * Checks if a given JWT token has expired.
 *
 * @param {string} token - The JWT token to check for expiry.
 * @returns {boolean} - Returns true if the token is expired, otherwise false.
 */
export const checkTokenExpiry = (token) => {
  try {
    // Decode the jwt token
    const decodedToken = jwtDecode(token)

    const currentTime = Math.floor(Date.now() / 1000)
    console.log("expiry time", decodedToken.exp, "current time", currentTime)
    if (currentTime >= decodedToken.exp) return true
  } catch (error) {
    console.log("error in checking token expiry", error)
  }
  return false
}
