import { initializeApp } from "firebase/app"
import { getMessaging, getToken, onMessage } from "firebase/messaging"

// Firebase configuration for the messaging app
export const firebaseMessagingConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
}

// Initialize Firebase
const app = initializeApp(firebaseMessagingConfig)
export const messaging = getMessaging(app)

// Function to request notification permission and get FCM token
export const generateFCMToken = async () => {
  try {
    const permission = await Notification.requestPermission()
    const vapidKey = import.meta.env.VITE_FIREBASE_VAPID_KEY
    console.log("permission", permission)
    if (permission === "granted") {
      // Get FCM Token
      const token = await getToken(messaging, { vapidKey })
      console.log("FCM Token:", token)
      return token
    }
    throw new Error("Permission denied")
  } catch (error) {
    console.error("Error while generating FCM Token : ", error)
    throw error
  }
}

// Function to handle incoming messages
export const onMessageListener = () =>
  new Promise((resolve) => {
    onMessage(messaging, (payload) => {
      console.log(" onMessage_messaging firebase message", payload)
      resolve(payload)
    })
  })
