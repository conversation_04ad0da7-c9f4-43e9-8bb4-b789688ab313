import { getCourseGQLClient } from "@/lib/graphql-client"
import { useQuery } from "@tanstack/react-query"
import { useMemo } from "react"

import {
  FETCH_COURSES,
  GET_BATCH_COURSE,
  GET_BATCH_COURSEID,
  GET_COURSE,
  GET_COURSES_MIN_INFO,
  GET_COURSE_CONTENTS,
  GET_COURSE_CONTENT_MIN_INFO,
  GET_MODULE_NAME,
} from "../grphql-query/live-course.gql"
import { tanstackConfig } from "./tanstack-config"

const FETCH_POLICY = "cache-first"

export const useGetCourse = (
  {
    userID,
    courseType,
    limit,
    offset,
    order,
    search_query,
    for_subscribed,
    publish_status,
    base_currency,
    from_price,
    to_price,
    nano_course_ids,
  },
  categoryIds = []
) => {
  const filters = useMemo(() => {
    const baseFilters = {
      ...(() => {
        switch (courseType) {
          case "Live courses":
            return { course_type: "LIVE" }
          case "Short courses":
            return { course_type: "SHORT" }
          case "Nano courses":
            return { course_type: "NANO" }
          case "My Courses":
            return { for_subscribed: true }
          default:
            return {}
        }
      })(),
      limit,
      offset,
      order,
      search_query,
      for_subscribed,
      publish_status,
      base_currency,
      from_price,
      to_price,
    }
    if (nano_course_ids?.length > 0) {
      baseFilters.nano_course_ids = nano_course_ids
    }

    // Always add category_ids if present, for all course types
    if (categoryIds?.length > 0) {
      baseFilters.category_ids = categoryIds
    }

    return baseFilters
  }, [
    courseType,
    categoryIds,
    limit,
    offset,
    order,
    search_query,
    base_currency,
    from_price,
    to_price,
    nano_course_ids,
  ])

  return useQuery({
    queryKey: ["GET_COURSE", courseType, filters, userID],
    queryFn: ({ signal }) => {
      const client = getCourseGQLClient()
      return client.request(GET_COURSE, { filters }, { signal, fetchPolicy: FETCH_POLICY })
    },
    ...tanstackConfig,
  })
}

export const useGetCourseByBatch = (filters = { for_subscribed: true }) => {
  return useQuery({
    queryKey: ["GET_COURSE_BY_BATCH", filters],
    queryFn: ({ signal }) => {
      const client = getCourseGQLClient()
      return client.request(GET_BATCH_COURSEID, { filters }, { signal, fetchPolicy: FETCH_POLICY })
    },
    ...tanstackConfig,
  })
}

export const useFetchCourseMinInfo = (
  {
    userID,
    courseType,
    limit,
    offset,
    order,
    search_query,
    for_subscribed,
    publish_status,
    is_nano,
  },
  isEnabled = true
) => {
  const filters = useMemo(() => {
    return {
      limit,
      offset,
      order,
      is_nano,
      course_type: courseType,
      search_query,
      for_subscribed,
      publish_status,
    }
  }, [courseType, limit, offset, order, search_query, is_nano])

  return useQuery({
    queryKey: ["GET_COURSES_MIN_INFO", courseType, filters, userID],
    queryFn: ({ signal }) => {
      const client = getCourseGQLClient()
      return client.request(
        GET_COURSES_MIN_INFO,
        { filters },
        { signal, fetchPolicy: FETCH_POLICY }
      )
    },
    enabled: isEnabled,
    ...tanstackConfig,
  })
}

export const useFetchCourseRourseMinInfo = (filters, isEnabled = true) => {
  return useQuery({
    queryKey: ["GET_COURSE_RESOURCE_MIN_INFO", filters],
    queryFn: ({ signal }) => {
      const client = getCourseGQLClient()
      return client.request(
        GET_COURSE_CONTENT_MIN_INFO,
        { filters },
        { signal, fetchPolicy: FETCH_POLICY }
      )
    },
    enabled: isEnabled,
    ...tanstackConfig,
  })
}

export const useFetchBatchCourse = (filters, isEnabled = true) => {
  return useQuery({
    queryKey: ["GET_BATCH_COURSE", filters],
    queryFn: ({ signal }) => {
      const client = getCourseGQLClient()
      return client.request(GET_BATCH_COURSE, { filters }, { signal, fetchPolicy: FETCH_POLICY })
    },
    enabled: isEnabled,
    ...tanstackConfig,
  })
}

export const useGetCourseContent = (
  { userID, userRole, course_id = "SIGNATURE" },
  search_query = "", // Ensure it's a string
  page = 0,
  pageSize = 10
) => {
  const parsedCourseId = course_id ? parseInt(course_id, 10) : null
  // Structure the filters correctly
  const gqlFilters = {
    offset: page,
    limit: pageSize,
    search_query: search_query ?? "", // Ensure it's always a string
    course_id: parsedCourseId,
  }

  return useQuery({
    queryKey: ["GET_COURSE_CONTENT", { ...gqlFilters }, page, userID, userRole],
    queryFn: ({ signal }) => {
      const client = getCourseGQLClient()
      return client.request(
        GET_COURSE_CONTENTS,
        { filters: gqlFilters },
        { signal, fetchPolicy: FETCH_POLICY }
      )
    },
    ...tanstackConfig,
  })
}

export const useGetModuleResource = (courseId) => {
  const client = getCourseGQLClient()

  return useQuery({
    queryKey: ["GET_MODULE_RESOURCE", courseId],
    queryFn: async () => {
      return client.request(GET_MODULE_NAME, { filters: { course_id: courseId } }) // Wrap in an object
    },
    ...tanstackConfig,
  })
}

export const useFetchCourses = (page = 0, pageSize = 10, filters = {}) => {
  // Structure the filters correctly
  const gqlFilters = {
    ...filters,
    offset: page,
    limit: pageSize,
  }

  return useQuery({
    queryKey: ["FETCH_COURSE", { ...gqlFilters }, page],
    queryFn: () => {
      const client = getCourseGQLClient()
      return client.request(FETCH_COURSES, { filters: gqlFilters })
    },
    ...tanstackConfig,
  })
}
