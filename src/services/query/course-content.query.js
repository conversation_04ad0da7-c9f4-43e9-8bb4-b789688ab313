import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { tanstackConfig } from "./tanstack-config"
import {
  attachStudentandTrainerToCourse,
  attendanceForStudents,
  createCourseContent,
  createCourseModule,
  createCourseToic,
  deleteCourseContent,
  deleteCourseModule,
  deleteCourseTopic,
  fetchCourseContents,
  fetchCourseTopics,
  updateCourseContent,
  updateCourseModule,
  updateCourseModuleStatus,
  updateCourseTopic,
  updateCourseTopicStatus,
} from "../api/course-content.api"

export const useCreateCourseContentMutation = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "CREATE_COURSE_CONTENT",
    mutationFn: createCourseContent,
    onSuccess: () =>
      queryClient.invalidateQueries([{ queryKey: "GET_COURSE_CONTENT", exact: false }]),
  })
}

export const useFetchCourseContent = () => {
  return useQuery({
    queryKey: ["COURSE_CONTENT"],
    queryFn: fetchCourseContents,
    tanstackConfig,
  })
}

export const useDeleteCourseContentMutation = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "DELETE_COURSE_CONTENT",
    mutationFn: deleteCourseContent,
    onSuccess: () =>
      queryClient.invalidateQueries([{ queryKey: "GET_COURSE_CONTENT", exact: false }]),
  })
}

export const useUpdateCourseContentMutation = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "UPDATE_COURSE_CONTENT",
    mutationFn: updateCourseContent,
    onSuccess: () =>
      queryClient.invalidateQueries([{ queryKey: "GET_COURSE_CONTENT", exact: false }]),
  })
}

export const useCreateCourseModuleMutation = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "CREATE_COURSE_MODULE",
    mutationFn: ({ course_id, data }) => createCourseModule(course_id, data),
    onSuccess: () =>
      queryClient.invalidateQueries([{ queryKey: "GET_COURSE_CONTENT", exact: false }]),
  })
}

export const useDeleteCourseModuleMutation = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "DELETE_COURSE_MODULE",
    mutationFn: ({ course_id, module_id }) => deleteCourseModule(course_id, module_id),
    onSuccess: () =>
      queryClient.invalidateQueries([{ queryKey: "GET_COURSE_CONTENT", exact: false }]),
  })
}

export const useUpdateCourseModuleMutation = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "UPDATE_COURSE_UPDATE",
    mutationFn: ({ course_id, module_id, data }) => updateCourseModule(course_id, module_id, data),
    onSuccess: () =>
      queryClient.invalidateQueries([{ queryKey: "GET_COURSE_CONTENT", exact: false }]),
  })
}

export const useUpdateStatusMutation = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "UPDATE_MODULE_STATUS",
    mutationFn: ({ course_id, module_id, status }) =>
      updateCourseModuleStatus(course_id, module_id, status),
    onSuccess: () =>
      queryClient.invalidateQueries([{ queryKey: "GET_COURSE_CONTENT", exact: false }]),
  })
}

export const useUpdateTopicStatusMutation = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "UPDATE_TOPIC_STATUS",
    mutationFn: ({ course_id, module_id, resource_id, status, type }) =>
      updateCourseTopicStatus(course_id, module_id, resource_id, status, type),
    onSuccess: () =>
      queryClient.invalidateQueries([{ queryKey: "GET_COURSE_CONTENT", exact: false }]),
  })
}

export const useFetchCourseTopics = () => {
  return useQuery({
    queryKey: "COURSE_TOPICS",
    queryFn: fetchCourseTopics,
    tanstackConfig,
  })
}

export const useCreateCourseToicMutation = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "CREATE_COURSE_TOPIC",
    mutationFn: ({ course_id, module_id, formData }) =>
      createCourseToic(course_id, module_id, formData),
    onSuccess: () =>
      queryClient.invalidateQueries([{ queryKey: "GET_COURSE_CONTENT", exact: false }]),
  })
}

export const useDeleteCourseTopicMutation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: "DELETE_COURSE_TOPIC",
    mutationFn: ({ course_id, module_id, resource_id }) =>
      deleteCourseTopic(course_id, module_id, resource_id),
    onSuccess: () =>
      queryClient.invalidateQueries([{ queryKey: "GET_COURSE_CONTENT", exact: false }]),
  })
}

export const useUpdateCourseTopicMutation = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "UPDATE_COURSE_TOPIC",
    mutationFn: ({ course_id, module_id, resource_id, formData }) =>
      updateCourseTopic(course_id, module_id, resource_id, formData),
    onSuccess: () =>
      queryClient.invalidateQueries([{ queryKey: "GET_COURSE_CONTENT", exact: false }]),
  })
}

export const useAttachStudentandTrainer = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "ATTACH_TRAINERS_STUDENTS",
    mutationFn: ({ course_id, payload }) => attachStudentandTrainerToCourse(course_id, payload),
    onSuccess: () => queryClient.invalidateQueries([{ queryKey: "GET_BATCH_COURSE" }]),
  })
}

export const useAttendanceStudent = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "ATTENDANCE-STUDENTS",
    mutationFn: ({ course_id, resource_id, data, date }) =>
      attendanceForStudents(course_id, resource_id, data, date),
    onSettled: () => {
      queryClient.invalidateQueries([{ queryKey: "FETCH_COURSEACTIVITY_BY_ID", exact: false }])
    },
  })
}
