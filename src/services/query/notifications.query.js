import { useQuery } from "@tanstack/react-query"
import { getNotificationGQLClient } from "@/lib/graphql-client"
import { FETCH_NOTIFICATION_QUERY } from "../grphql-query/notification.graphql"

export const useFetchNotificationQuery = (filters, offset = 0, limit = 10) => {
  const sanitizedFilters = {}
  // Only add filters that have a value
  Object.entries(filters).forEach(([key, value]) => {
    if (value) sanitizedFilters[key] = value
  })

  return useQuery({
    queryKey: ["FETCH_NOTIFICATION_QUERY", offset, limit, sanitizedFilters],
    queryFn: async () => {
      const client = getNotificationGQLClient()
      return client.request(FETCH_NOTIFICATION_QUERY, {
        filters: sanitizedFilters,
        offset,
        limit,
      })
    },
    enabled: !!filters.user_id,
  })
}
