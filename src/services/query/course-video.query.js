import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { fetchResources, getStudentPerformance, postTrackVideo } from "../api/course-video.api"
import { tanstackConfig } from "./tanstack-config"

export const useFetchResources = (course_id, modules_id) => {
  const queryClient = useQueryClient()

  return useQuery({
    queryKey: ["resources", course_id, modules_id],
    queryFn: () => fetchResources(course_id, modules_id),
    enabled: !!course_id && !!modules_id,
    onSuccess: () => {
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ["resources", course_id, modules_id] })
      }, 500)
    },
  })
}

export const usePostTrackVideo = (resource_id) => {
  return useMutation({
    mutationFn: (data) => postTrackVideo(resource_id, data),
    onSuccess: () => {
      console.log(`Successfully tracked progress for resource ID: ${resource_id}`)
    },
    onError: (error) => {
      console.error(`Error tracking progress for resource ID: ${resource_id}:`, error)
    },
  })
}

export const useFetchStudentPerformance = (course_id) => {
  return useQuery({
    queryKey: ["student-performance", course_id],
    queryFn: () => getStudentPerformance(course_id),
    enabled: !!course_id,
    ...tanstackConfig,
  })
}
