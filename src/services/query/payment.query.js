import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import {
  createPaymentSession,
  createTrainerEarning,
  fetchPaymentRequests,
  fetchPayments,
  fetchTrainerEarnings,
  fetchTrainerPaymentStats,
  rejectPaymentRequest,
  sentPaymentRequest,
  storeTrainerPayment,
} from "../api/payment.api"
import { tanstackConfig } from "./tanstack-config"

/// //////////////////////////////// QUERIES //////////////////////////////////

export const useFetchPaymentRequests = (params) => {
  return useQuery({
    queryKey: ["FETCH_PAYMENT_REQUESTS", params],
    queryFn: () => fetchPaymentRequests(params),
    ...tanstackConfig,
  })
}

export const useFetchPayments = (params) => {
  return useQuery({
    queryKey: ["FETCH_PAYMENTS", params],
    queryFn: () => fetchPayments(params),
    ...tanstackConfig,
  })
}

export const useFetchTrainerEarnings = (params) => {
  return useQuery({
    queryKey: ["FETCH_TRAINER_EARNINGS", params],
    queryFn: () => fetchTrainerEarnings(params),
    // enabled: !!selectedTrainer,
    ...tanstackConfig,
  })
}

export const useFetchTrainerPaymentStats = (params, isEnabled) => {
  return useQuery({
    queryKey: ["FETCH_TRAINER_PAYMENT_STATS", params],
    queryFn: () => fetchTrainerPaymentStats(params),
    enabled: isEnabled,
    ...tanstackConfig,
  })
}

/// //////////////////////////////// MUTATIONS //////////////////////////////////

export const useCreatePaymentSession = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: "CREATE_PAYMENT_SESSION",
    mutationFn: ({ payload, provider }) => createPaymentSession(payload, provider),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["FETCH_PAYMENTS"], exact: false })
      queryClient.invalidateQueries({ queryKey: ["FETCH_PAYMENT_REQUESTS"] })
    },
  })
}

export const useSentPaymentRequest = () => {
  return useMutation({
    mutationKey: "SENT_PAYMENT_REQUEST",
    mutationFn: ({ payload, courseID }) => sentPaymentRequest(payload, courseID),
  })
}

export const useRejectPaymentRequest = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: "REJECT_PAYMENT_REQUEST",
    mutationFn: ({ id, request_status }) => rejectPaymentRequest(id, request_status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["FETCH_PAYMENT_REQUESTS"] })
    },
  })
}

export const useCreateTrainerEarning = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: "CREATE_TRAINER_EARNING",
    mutationFn: (data) => createTrainerEarning(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["FETCH_TRAINER_EARNINGS"] })
    },
  })
}

export const useStoreTrainerPayment = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: "STORE_TRAINER_PAYMENT",
    mutationFn: (data) => storeTrainerPayment(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["FETCH_PAYMENTS"], exact: false })
      queryClient.invalidateQueries({ queryKey: ["FETCH_TRAINER_EARNINGS"], exact: false })
    },
  })
}
