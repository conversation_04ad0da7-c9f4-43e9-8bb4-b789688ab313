import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import {
  CreateLeads,
  deleteLead,
  fetchLeads,
  updateLead,
  uploadLeadFile,
} from "../api/leads-management"
import { tanstackConfig } from "./tanstack-config"

export const useCreateLeads = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "CREATE-LEADS",
    mutationFn: (data) => CreateLeads(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["leads"] })
    },
  })
}

export const useFetchLeads = ({ filters }) => {
  return useQuery({
    queryKey: ["leads", filters],
    queryFn: () => fetchLeads(filters),
    ...tanstackConfig,
    enabled: !!filters,
  })
}

export const useUploadLeadFile = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: ["UPDATE-LEADS"],
    mutationFn: (file) => uploadLeadFile(file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["leads"] })
    },
  })
}
export const useDeleteLead = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "DELETE-LEADS",
    mutationFn: ({ leadId }) => deleteLead(leadId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["leads"] })
    },
  })
}

export const useUpdateLead = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: ["UPDATE-LEADS"],
    mutationFn: ({ leadId, updatedData }) => {
      return updateLead(leadId, updatedData)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["leads"] })
    },
  })
}
