import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import {
  getCandidates,
  getCandidateDetails,
  getAvailableJobs,
  parseCV,
  saveCandidate,
  archiveCandidate,
  getCandidateCV,
  applyToJob,
  getResume,
  getPublicUrls,
} from "../api/cv.api"

export const useCandidates = ({ page, limit, search, status }) => {
  return useQuery({
    queryKey: ["candidates", { page, limit, search, status }],
    queryFn: () => getCandidates({ page, limit, search, status }),
    select: (response) => ({
      candidates: response.data.items,
      pagination: {
        total: response.data.total,
        totalPages: Math.ceil(response.data.total / limit),
      },
    }),
    keepPreviousData: true,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

export const useCandidateDetails = (candidateId) => {
  return useQuery({
    queryKey: ["candidateDetails", candidateId],
    queryFn: () => getCandidateDetails(candidateId),
    select: (response) => response.data,
    enabled: !!candidateId,
    staleTime: 1000 * 60 * 60, // 1 hour
    keepPreviousData: true,
  })
}

export const useAvailableJobs = ({ page, limit, filter, candidateId }) => {
  return useQuery({
    queryKey: ["availableJobs", { page, limit, filter, candidateId }],
    queryFn: () => getAvailableJobs({ page, limit, filter, candidateId }),
    select: (response) => ({
      jobs: response.data.items,
      pagination: {
        total: response.data.total,
        totalPages: Math.ceil(response.data.total / limit),
      },
    }),
    keepPreviousData: true,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

export const useParseCV = () => {
  return useMutation({
    mutationFn: parseCV,
  })
}

export const useSaveCandidate = () => {
  return useMutation({
    mutationFn: (formData) => saveCandidate(formData),
  })
}

export const useArchiveCandidate = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: archiveCandidate,
    onSuccess: () => {
      queryClient.invalidateQueries(["candidates", "candidateDetails"])
    },
  })
}

export const useCandidateCV = (candidateId) => {
  return useQuery({
    queryKey: ["candidateCV", candidateId],
    queryFn: () => getCandidateCV(candidateId),
    select: (response) => response.data,
    enabled: !!candidateId,
    staleTime: 1000 * 60 * 45, // 45 minutes
    keepPreviousData: true,
  })
}

export const useApplyToJob = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: applyToJob,
    onSuccess: () => {
      queryClient.invalidateQueries(["availableJobs", "jd-candidates"])
    },
  })
}

export const useGetResumes = () => {
  return useQuery({
    queryKey: ["resumes"],
    queryFn: getResume,
    staleTime: Infinity,
    cacheTime: 5 * 60 * 1000,
  })
}

export const useGetPublicUrls = (cloudPath) => {
  return useQuery({
    queryKey: ["getPublicUrls", cloudPath],
    queryFn: () => getPublicUrls(cloudPath),
    enabled: !!cloudPath, // Prevent unnecessary calls
    staleTime: Infinity,
    cacheTime: 5 * 60 * 1000,
  })
}
