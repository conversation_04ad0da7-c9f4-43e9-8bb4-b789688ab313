import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { createCourse } from "../api/create-course-form.api"
import {
  createBatchCourse,
  deleteBatchCourse,
  fetchBatchCoursesByRole,
  updateBatchCourse,
  updateBatchStatus,
} from "../api/mange-batch.api"
import { tanstackConfig } from "./tanstack-config"

export const useCreateCourseBatch = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "CREATE_BATCH",
    mutationFn: ({ data }) => createCourse(data),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "GET_COURSE", exact: false }])
    },
    staleTime: 5 * 60 * 1000,
    keepPreviousData: true,
    cacheTime: 10 * 60 * 1000,
    refetchInterval: 10 * 60 * 1000,
    refetchOnWindowFocus: false,
    retry: 2,
  })
}

export const useUpdateCourseBatch = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ course_id, modules_id, assignment_id, publish_status, data }) => {
      updateBatchCourse(course_id, modules_id, assignment_id, publish_status, data)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["GET_COURSE"] })
    },
  })
}

export const useDeletCourseBatch = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ modules_id, course_id, assignment_id }) =>
      deleteBatchCourse(modules_id, course_id, assignment_id),
    onSuccess: () => {
      setTimeout(() => {
        queryClient.invalidateQueries([{ queryKey: "GET_COURSE", exact: false }])
      }, 500)
    },
  })
}

// student

export const useFetchBatchCoursesByRole = ({ params, type }) => {
  return useQuery({
    queryKey: ["FETCH_BY_ROLE", params, type],
    queryFn: () => fetchBatchCoursesByRole(params, type),
    ...tanstackConfig,
  })
}

export const useCreateStudentBatch = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "CREATE_BATCH",
    mutationFn: ({ data }) => createBatchCourse(data),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "GET_COURSE", exact: false }])
    },
  })
}

// export const useUpdateStudentBatch = () => {
//   const queryClient = useQueryClient()

//   return useMutation({
//     mutationFn: ({ course_id, modules_id, assignment_id, publish_status, data }) => {
//       updateBatchCourse(course_id, modules_id, assignment_id, publish_status, data)
//     },
//     onSuccess: () => {
//       queryClient.invalidateQueries({ queryKey: ["GET_COURSE"] })
//     },
//   })
// }

// export const useDeleteStudentBatch = () => {
//   const queryClient = useQueryClient()

//   return useMutation({
//     mutationFn: ({ modules_id, course_id, assignment_id }) =>
//       deleteBatchCourse(modules_id, course_id, assignment_id),
//     onSuccess: () => {
//       setTimeout(() => {
//         queryClient.invalidateQueries([{ queryKey: "GET_COURSE", exact: false }])
//       }, 500)
//     },
//   })
// }

// trainer

export const useCreateTrainerBatch = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "CREATE_BATCH",
    mutationFn: ({ course_id, modules_id, data }) => createBatchCourse(course_id, modules_id, data),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "GET_COURSE", exact: false }])
    },
  })
}

export const useUpdateStatus = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "UPDATE_BATCH",
    mutationFn: ({ course_id, data }) => updateBatchStatus(course_id, data),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "GET_COURSE", exact: false }])
    },
  })
}

// export const useUpdateTrainerBatch = () => {
//   const queryClient = useQueryClient()

//   return useMutation({
//     mutationFn: ({ course_id, modules_id, assignment_id, publish_status, data }) => {
//       updateBatchCourse(course_id, modules_id, assignment_id, publish_status, data)
//     },
//     onSuccess: () => {
//       queryClient.invalidateQueries({ queryKey: ["GET_COURSE"] })
//     },
//   })
// }

// export const useDeleteTrainerBatch = () => {
//   const queryClient = useQueryClient()

//   return useMutation({
//     mutationFn: ({ modules_id, course_id, assignment_id }) =>
//       deleteBatchCourse(modules_id, course_id, assignment_id),
//     onSuccess: () => {
//       setTimeout(() => {
//         queryClient.invalidateQueries([{ queryKey: "GET_COURSE", exact: false }])
//       }, 500)
//     },
//   })
// }
