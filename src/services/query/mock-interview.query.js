import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import { getMockInterviews, patchMockInterview, postMockInterview } from "../api/mock-interview.api"
import { tanstackConfig } from "./tanstack-config"



export const usePostMockInterview = () => {
  return useMutation({
    mutationFn: (data) => postMockInterview(data),
    mutationKey: "CREATE_MOCK_INTERVIEW",
    ...tanstackConfig,

    onSuccess: () => {
      console.log(`Successfully created mock interview`)
    },
    onError: (error) => {
      console.error(`Error creating mock interview:`, error)
    },
  })
}

export const useGetMockInterviews = () => {
  const queryClient = useQueryClient()
  return useQuery({
    queryKey: ["GET_MOCK_INTERVIEWS"],
    queryFn: () => getMockInterviews(),

    ...tanstackConfig,
    onSuccess: () => {
      // Invalidate the cache so that the next request will re-fetch the data
      queryClient.invalidateQueries({ queryKey: ["GET_MOCK_INTERVIEWS"] })
    },
    onError: (error) => {
      console.error("Error fetching mock interviews:", error)
    },
  })
}

export const usePatchMockInterview = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: patchMockInterview, // Simplified since we're just passing the same params
    mutationKey: "PATCH_MOCK_INTERVIEW",
    ...tanstackConfig,
    onSuccess: () => {
      queryClient.invalidateQueries("GET_MOCK_INTERVIEWS") // Add this to refresh data
      successToast("Interview updated successfully")
    },
    onError: (error) => {
      failureToast(`Error updating interview: ${error.message}`)
      console.error("Update error:", error)
    },
  })
}
