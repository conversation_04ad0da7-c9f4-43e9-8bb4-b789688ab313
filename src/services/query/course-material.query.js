import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { tanstackConfig } from "./tanstack-config"
import {
  createCourseMaterial,
  deleteCourseMaterial,
  fetchBatchCourse,
  fetchCourseMaterials,
  updateCourseMaterial,
  updateMaterialStatus,
} from "../api/course-material.api"

export const useCreateCourseMaterial = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "CREATE_COURSE_MATERIAL",
    mutationFn: ({ data, course_id }) => createCourseMaterial(data, course_id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["COURSE_MATERIAL"], exact: false })
    },
  })
}

export const useFetchCourseMaterial = ({ course_id, filters }) => {
  return useQuery({
    queryKey: ["COURSE_MATERIAL", course_id, filters],
    queryFn: () => fetchCourseMaterials(course_id, filters),
    tanstackConfig,
    enabled: !!course_id,
  })
}

export const useDeleteCourseMaterial = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "DELETE_COURSE_MATERIAL",
    mutationFn: ({ course_id, material_id }) => deleteCourseMaterial(course_id, material_id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["COURSE_MATERIAL"], exact: false })
    },
  })
}

export const useUpdateCourseMaterial = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "UPDATE_COURSE_MATERIAL",
    mutationFn: ({ updatedData, course_id, material_id }) =>
      updateCourseMaterial(updatedData, course_id, material_id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["COURSE_MATERIAL"], exact: false })
    },
  })
}

export const useUpdateMaterialStatus = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "UPDATE_MATERIAL_STATUS",
    mutationFn: ({ course_id, material_id, status }) =>
      updateMaterialStatus(course_id, material_id, status),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "COURSE_MATERIAL", exact: false }])
    },
  })
}

export const useFetchBatchCourseID = () => {
  return useQuery({
    queryKey: ["BATCH_COURSE"],
    queryFn: () => fetchBatchCourse(),
    ...tanstackConfig,
  })
}
