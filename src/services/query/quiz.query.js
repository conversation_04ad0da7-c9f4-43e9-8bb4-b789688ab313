import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { tanstackConfig } from "./tanstack-config"
import {
  addGradeOrFeedbackToQuiz,
  createQuiz,
  deleteQuiz,
  fetchQuizzes,
  getQuizSubmissionsView,
  submitTheQuiz,
  updateQuiz,
  updateQuizStatus,
  updateQuizSubmissionStatus,
} from "../api/quiz.api"

export const useCreateQuizMutation = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "CREATE-QUIZ",
    mutationFn: ({ data, course_id, module_id }) => createQuiz(data, course_id, module_id),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "FETCH_QUIZ_DETAILS", exact: false }])
    },
  })
}
export const useFetchQuizDetails = ({ course_id, module_id, filters }) => {
  return useQuery({
    queryKey: ["FETCH_QUIZ_DETAILS", course_id, module_id, filters],
    queryFn: () => fetchQuizzes(course_id, module_id, filters),
    ...tanstackConfig,
    enabled: !!course_id && !!module_id,
  })
}

export const useUpdateQuizMutation = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: ["UPDATE-QUIZ"], // Make mutationKey an array (best practice)
    mutationFn: ({ data, course_id, module_id, quiz_id }) =>
      updateQuiz(data, course_id, module_id, quiz_id),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "FETCH_QUIZ_DETAILS", exact: false }])
    },
  })
}

export const useDeleteQuizMutaion = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "DELETE-QUIZ",
    mutationFn: ({ course_id, module_id, quiz_id }) => deleteQuiz(course_id, module_id, quiz_id),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "FETCH_QUIZ_DETAILS", exact: false }])
    },
  })
}

export const useUpdateStatusMutation = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "UPDATE_QUIZ_STATUS",
    mutationFn: ({ course_id, module_id, quiz_id, status }) =>
      updateQuizStatus(course_id, module_id, quiz_id, status),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "FETCH_QUIZ_DETAILS", exact: false }])
    },
  })
}

export const useUpdateQuizSubmissionStatus = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "UPDATE_QUIZ_SUBMISSION_STATUS",
    mutationFn: updateQuizSubmissionStatus,
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "FETCH_QUIZ_DETAILS", exact: false }])
    },
  })
}

export const useSubmitQuizMutation = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "SUBMIT_QUIZ",
    mutationFn: ({ course_id, module_id, quiz_id, data }) =>
      submitTheQuiz(course_id, module_id, quiz_id, data),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "FETCH_QUIZ_DETAILS", exact: false }])
    },
  })
}

export const useAddGradeOrFeedbackToQuiz = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "FEEDBACK_OR_GRADE",
    mutationFn: ({ course_id, module_id, quiz_id, data }) =>
      addGradeOrFeedbackToQuiz(course_id, module_id, quiz_id, data),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "FETCH_QUIZ_DETAILS", exact: false }])
    },
  })
}

export const useGetQuizSubmissionsView = ({ course_id, module_id, quiz_id }) => {
  return useQuery({
    queryKey: ["FETCH_QUIZ_SUBMISSIONS", course_id, module_id, quiz_id],
    queryFn: () => getQuizSubmissionsView(course_id, module_id, quiz_id),
    ...tanstackConfig,
    enabled: !!course_id && !!module_id && !!quiz_id,
  })
}
