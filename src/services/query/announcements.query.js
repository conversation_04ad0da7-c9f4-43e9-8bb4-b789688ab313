import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import {
  createAnnouncement,
  deleteAnnouncement,
  fetchAllAnnouncements,
  updateAnnouncement,
  updateStatusAnnouncement,
} from "../api/announcement.api"
import { tanstackConfig } from "./tanstack-config"

export const useFetchAnnouncementsQuery = (data) => {
  return useQuery({
    queryKey: ["ALL_ANNOUNCEMENTS", data],
    queryFn: fetchAllAnnouncements,
    ...tanstackConfig,
  })
}

export const useCreateAnnouncementMutaion = (setOffset) => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "CREATE_ANNOUNCEMENT",
    mutationFn: createAnnouncement,
    onSuccess: () => {
      setOffset(0)
      queryClient.invalidateQueries([{ queryKey: "ALL_ANNOUNCEMENTS", exact: false }])
    },
  })
}

export const useUpdateAnnouncementMutation = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "UPDATE_ANNOUNCEMENT",
    mutationFn: ({ course_id, announcement_id, data }) =>
      updateAnnouncement(course_id, announcement_id, data),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "ALL_ANNOUNCEMENTS", exact: false }])
    },
  })
}

export const useDeleteAnnouncementMutation = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "DELETE_ANNOUNCEMENT",
    mutationFn: ({ course_id, announcement_id }) => deleteAnnouncement(course_id, announcement_id),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "ALL_ANNOUNCEMENTS", exact: false }])
    },
  })
}

export const useUpdateStatusAnnouncementMutation = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "UPDATE_STATUS_ANNOUNCEMENT",
    mutationFn: ({ course_id, announcement_id, status }) =>
      updateStatusAnnouncement(course_id, announcement_id, status),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "ALL_ANNOUNCEMENTS", exact: false }])
    },
  })
}
