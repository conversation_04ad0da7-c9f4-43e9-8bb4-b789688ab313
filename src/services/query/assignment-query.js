import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import {
  CreateAssignments,
  deleteAssignment,
  FetchAssignments,
  getAssignmentSubmissionView,
  submitAssignment,
  updateAssignment,
  updateAssignmentSubmissionStatus,
} from "../api/assignment-api"
import { tanstackConfig } from "./tanstack-config"

export const useCreateAssignment = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "CREATE-ASSIGNMENT",
    mutationFn: ({ course_id, modules_id, data }) => CreateAssignments(course_id, modules_id, data),
    onSuccess: () => {
      setTimeout(() => {
        queryClient.invalidateQueries([{ queryKey: "FetchAssignments", exact: false }])
      }, 500)
    },
  })
}
export const useFetchAssignments = ({ course_id, module_id, filters }) => {
  return useQuery({
    queryKey: ["FetchAssignments", course_id, module_id, filters],
    queryFn: () => FetchAssignments(course_id, module_id, filters),
  })
}

export const useUpdateAssignmentSubmissionStatus = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "UPDATE_ASSIGNMENT_SUBMISSION_STATUS",
    mutationFn: ({ course_id, module_id, assignment_id, data }) =>
      updateAssignmentSubmissionStatus(course_id, module_id, assignment_id, data),
    onSuccess: () => {
      queryClient.invalidateQueries(["FETCH_ASSIGNMENT_DETAILS"])
      queryClient.invalidateQueries(["FETCH_ASSIGNMENT_SUBMISSIONS"])
    },
  })
}

export const useUpdateAssignment = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ course_id, modules_id, assignment_id, publish_status, data }) => {
      updateAssignment(course_id, modules_id, assignment_id, publish_status, data)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["FetchAssignments"] })
    },
  })
}

export const useDeletAssignment = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ modules_id, course_id, assignment_id }) =>
      deleteAssignment(modules_id, course_id, assignment_id),
    onSuccess: () => {
      setTimeout(() => {
        queryClient.invalidateQueries([{ queryKey: "FetchAssignments", exact: false }])
      }, 500)
    },
  })
}

export const useSubmitAssignment = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "SUBMIT_ASSIGNMENT",
    mutationFn: ({ course_id, module_id, assignment_id, data }) =>
      submitAssignment(course_id, module_id, assignment_id, data),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "FETCH_ASSIGNMENT_DETAILS", exact: false }])
    },
  })
}

export const useGetAssignmentSubmissionView = ({ course_id, module_id, assignment_id }) => {
  return useQuery({
    queryKey: ["FETCH_ASSIGNMENT_SUBMISSION", course_id, module_id, assignment_id],
    queryFn: () => getAssignmentSubmissionView(course_id, module_id, assignment_id),
    ...tanstackConfig,
    enabled: !!course_id && !!module_id && !!assignment_id,
  })
}
