import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import {
  CreateProject,
  deleteProject,
  EditProject,
  editProjectStatus,
  fetchProject,
  GetProjectSubmission,
  SubmitProject,
  taskEvaluation,
} from "../api/project-api."
import { tanstackConfig } from "./tanstack-config"

export const useFetchProject = ({ course_id, filters, userID, userRole, signature }) => {
  return useQuery({
    queryKey: ["FetchProject", course_id, filters, userID, userRole, signature],
    queryFn: () => fetchProject(course_id, filters, userID, userRole, signature),
    ...tanstackConfig,
    enabled: !!course_id,
  })
}
export const useCreateProject = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "CREATE-PROJECT",
    mutationFn: ({ course_id, data, userID, userRole, signature }) =>
      CreateProject(course_id, data, userID, userRole, signature),
    onSettled: () => {
      setTimeout(() => {
        queryClient.invalidateQueries([{ queryKey: "FetchProject", exact: false }])
      }, 500)
    },
  })
}

export const useDeletProject = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ project_id, course_id }) => deleteProject(project_id, course_id),
    onSuccess: () => {
      setTimeout(() => {
        queryClient.invalidateQueries([{ queryKey: "FetchProject", exact: false }])
      }, 500)
    },
  })
}

export const useUpdateProject = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: ["UPDATE-TASK"],
    mutationFn: ({ data, project_id, course_id }) => EditProject(data, project_id, course_id),
    onSuccess: () => {
      setTimeout(() => {
        queryClient.invalidateQueries([{ queryKey: "FetchProject", exact: false }])
      }, 500)
    },
  })
}

export const useSubmitProject = () => {
  return useMutation({
    mutationKey: "SUBMIT-PROJECT",
    mutationFn: ({
      project_id,
      course_id,

      data,
      file = null,
      userID,
      userRole,
      signature,
    }) => SubmitProject(project_id, course_id, data, file, userID, userRole, signature),
  })
}

export const useProjectSubmission = ({ course_id, project_id, filters }) => {
  return useQuery({
    queryKey: ["GET-PROJECT-SUBMISSION", course_id, project_id, filters],
    queryFn: () => GetProjectSubmission(course_id, project_id, filters),
    ...tanstackConfig,
    enabled: !!project_id,
  })
}

export const useCreateEvaluation = () => {
  return useMutation({
    mutationKey: "POST_EVALUATION",
    mutationFn: ({
      course_id,
      project_id,
      tasks,
      project_tracking_id,
      acceptance_status,
      trainer_feedback,
    }) =>
      taskEvaluation(
        course_id,
        project_id,
        tasks,
        project_tracking_id,
        acceptance_status,
        trainer_feedback
      ),
  })
}

export const useEditPublishedStatus = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "EDIT-PUBLISHED-STATUS",
    mutationFn: ({ project_id, course_id, publishedStatus, userID, userRole, signature }) =>
      editProjectStatus(project_id, course_id, publishedStatus, userID, userRole, signature),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "FetchProject", exact: false }])
    },
  })
}
