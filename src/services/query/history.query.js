import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query"
import {
  deleteApplication,
  updateApplication,
  getApplications,
  createApplication,
} from "@api/history.api"

export const useDeleteApplication = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: deleteApplication,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["applications"] })
    },
  })
}

export const useUpdateApplication = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: updateApplication,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["applications"] })
    },
  })
}

export const useCreateApplication = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: createApplication,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["applications"] })
    },
  })
}

export const useApplications = ({ page, pageSize, selectCandidate, selectJD }) => {
  return useQuery({
    queryKey: ["applications", page, pageSize, selectCandidate, selectJD],
    queryFn: () =>
      getApplications({
        page,
        pageSize,
        selectCandidate,
        selectJD,
      }),
    select: (response) => ({
      applications: response.data.items,
      pagination: {
        total: response.data.total,
        totalPages: Math.ceil(response.data.total / pageSize),
      },
    }),
    keepPreviousData: true,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}
