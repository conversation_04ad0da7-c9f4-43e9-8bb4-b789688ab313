import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import {
  fetchCourseActivityById,
  fetchPerformanceById,
  fetchSubmissionById,
  getAttendance,
  getCourseOverview,
  updateAttendance,
} from "../api/course-overview.api"

import { tanstackConfig } from "./tanstack-config"

export const useFetchPerformanceById = ({ course_id, student_id }) => {
  return useQuery({
    queryKey: ["FETCH_PERFORMANCE_BY_ID", course_id, student_id],
    queryFn: () => fetchPerformanceById(course_id, student_id),
    enabled: !!course_id && !!student_id,
    ...tanstackConfig,
  })
}

export const useFetchSubmissionById = ({ course_id, student_id }) => {
  return useQuery({
    queryKey: ["FETCH_SUBMISSIONS_BY_ID", course_id, student_id],
    queryFn: () => fetchSubmissionById(course_id, student_id),
    enabled: !!course_id && !!student_id,
    ...tanstackConfig,
  })
}

export const useFetchCourseActivityById = ({ course_id, start_date, end_date }) => {
  return useQuery({
    queryKey: ["FETCH_COURSEACTIVITY_BY_ID", course_id, start_date, end_date],
    queryFn: () => fetchCourseActivityById(course_id, start_date, end_date),
    enabled: !!course_id && !!start_date,
    ...tanstackConfig,
  })
}

export const useFetchCourseOverview = ({ course_id, course_ids }) => {
  return useQuery({
    queryKey: ["FETCH_COURSE_OVERVIEW", course_id, course_ids],
    queryFn: () => getCourseOverview(course_id, course_ids),
    enabled: !!course_id,
    ...tanstackConfig,
  })
}


export const useFetchCourseAttendance = ({
  course_id,
  modules_id,
  resource_id,
  resourse_date,
  filters,
}) => {
  return useQuery({
    queryKey: [
      "FETCH_COURSE_ATTENDANCE",
      course_id,
      modules_id,
      resource_id,
      resourse_date,
      filters,
    ],
    queryFn: () => getAttendance(course_id, modules_id, resource_id, resourse_date, filters),
    enabled: !!modules_id && !!resource_id && !!resourse_date,
    ...tanstackConfig,
  })
}

export const useUpdateAttendance = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: ["UPDATE_ATTENDANCE"],
    mutationFn: ({ course_id, resource_id, session_date, data }) =>
      updateAttendance(course_id, resource_id, session_date, data),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "FETCH_COURSE_ATTENDANCE", exact: false }])
    },
  })
}
