import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import {
  archiveJD,
  createJD,
  deleteJobApplication,
  getAvalabileJDs,
  getJDDetails,
  getJdDetailsJDs,
  getJDList,
  getJobApplications,
  getRecommendedCandidates,
  parseJD,
  postJobApplication,
  toggleApplyStatus,
  updateJobApplicationStatus,
} from "../api/jd.api"

export const useJDList = (params) => {
  return useQuery({
    queryKey: [
      "jds",
      params.search,
      params.page,
      params.pageSize,
      params.status,
      params.provider,
      params,
    ],
    queryFn: () => getJDList(params),
    select: (response) => ({
      data: response.data.items,
      total: response.data.total,
    }),
    staleTime: 1000 * 60 * 1, // 5 minutes
    keepPreviousData: true,
  })
}

export const useJDDetails = (id) => {
  return useQuery({
    queryKey: ["jdDetails", id],
    queryFn: () => getJDDetails(id),
    enabled: !!id,
    staleTime: 1000 * 60 * 60, // 1 hour
    keepPreviousData: true,
  })
}

export const useRecommendedCandidates = (id, params, candidateFilter) => {
  return useQuery({
    queryKey: ["jd-candidates", id, params, candidateFilter],
    queryFn: () => getRecommendedCandidates(id, params, candidateFilter),
    enabled: !!id,
    select: (response) => ({
      candidates: response.data.items,
      pagination: {
        total: response.data.total,
        totalPages: Math.ceil(response.data.total / params.limit),
      },
    }),
    keepPreviousData: true,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

export const useArchiveJD = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id) => archiveJD(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["jds", "jdDetails"] })
    },
  })
}

export const useParseJD = () => {
  return useMutation({
    mutationFn: (text) => parseJD(text),
  })
}

export const useCreateJD = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data) => createJD(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["availableJDs"] })
    },
  })
}

export const usePostJobApplication = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data) => postJobApplication(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["jds"] })
    },
  })
}

export const useAvailableJDs = ({ jobType = "ALL", searchQuery, offset, limit }) => {
  return useQuery({
    queryKey: ["availableJDs", jobType, searchQuery, offset, limit],
    queryFn: () => getAvalabileJDs({ jobType, searchQuery, offset, limit }),
    select: (response) => response,
    staleTime: 1000 * 60 * 5,
    keepPreviousData: true,
  })
}

export const useJdDetailsJDs = (jd_id, job_type = "ALL") => {
  const queryClient = useQueryClient()

  return useQuery({
    queryKey: ["jdDetailsJDs", jd_id, job_type],
    queryFn: () => getJdDetailsJDs({ job_type }, jd_id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["jd-candidates"] })
    },
    enabled: !!jd_id,
    staleTime: 1000 * 60 * 60,
    keepPreviousData: true,
  })
}

export const useToggleApplyStatus = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data) => toggleApplyStatus(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["jd-candidates"] })
      queryClient.invalidateQueries({ queryKey: ["availableJobs"] })
    },
  })
}

export const useGetJobApplications = () => {
  return useQuery({
    queryKey: ["jobApplications"],
    queryFn: () => getJobApplications(),
    staleTime: 1000 * 60 * 5, // 5 minutes
    keepPreviousData: true,
  })
}

export const useUpdateJobApplicationStatus = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ application_id, data }) => updateJobApplicationStatus({ application_id, data }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["jobApplications"] })
    },
  })
}

export const useDeleteJobApplication = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationKey: "DELETE_JOB_APPLICATION",
    mutationFn: ({ application_id }) => deleteJobApplication({ application_id }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["jobApplications"] })
    },
  })
}
