import { USER_ROLES } from "@/utils/constants"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import {
  createVendor,
  fetchUsersByRole,
  getAllUsers,
  getAllUsersStudents,
  getAllVendors,
  getNotificationSettings,
  getUser,
  importUser,
  updateNotificationSettings,
  updateUser,
} from "../api/user.api"
import { tanstackConfig } from "./tanstack-config"

export const useGetNotificationSettings = (userID) => {
  return useQuery({
    queryKey: ["notificationSettings", userID],
    queryFn: () => getNotificationSettings(userID),
    enabled: !!userID,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

export const useGetAllUsers = (params, isEnabled = true) => {
  return useQuery({
    queryKey: ["users", params],
    queryFn: () => getAllUsers(params),
    enabled: isEnabled,
    ...tanstackConfig,
  })
}

export const useGetUser = (userID) => {
  return useQuery({
    queryKey: ["user", userID],
    queryFn: () => getUser(userID),
    enabled: !!userID,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

export const useGetAllVendors = () => {
  return useQuery({
    queryKey: ["vendors"],
    queryFn: () => getAllVendors(),
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

export const useFetchUsersByRole = ({ filters, filter_role = "TRAINER" }) => {
  return useQuery({
    queryKey: ["users", filter_role, JSON.stringify(filters)],
    queryFn: () => fetchUsersByRole(filters, filter_role),
    enabled: !!filter_role,
    ...tanstackConfig,
  })
}

export const useGetUsersStudents = () => {
  return useQuery({
    queryKey: ["users", "students"],
    queryFn: () => getAllUsersStudents({ filter_role: USER_ROLES.STUDENT }),
    enabled: true,
    ...tanstackConfig,
  })
}

/// ///////////////////////////////// MUTATIONS //////////////////////////////////////////////

export const useImportUser = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: ({ userID, data }) => importUser(userID, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] })
    },
  })
}

export const useCreateVendor = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: (data) => createVendor(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["vendors"] })
    },
  })
}

export const useUpdateUser = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: ({ userID, data }) => updateUser(userID, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["user"] })
    },
  })
}

export const useUpdateNotificationSettings = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: ({ userID, data }) => updateNotificationSettings(userID, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["notificationSettings"] })
    },
  })
}
