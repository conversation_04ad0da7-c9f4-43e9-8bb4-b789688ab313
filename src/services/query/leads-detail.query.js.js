import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import {
  createLedRelationShip,
  deleteLeadDetail,
  EditLedRelationShip,
  fetchJds,
  fetchLeadDetails,
  fetchUsers,
} from "../api/leads-details"

export const useFetchLeadDetails = ({ lead_id, filters }) => {
  return useQuery({
    queryKey: ["Detailleads", lead_id, filters],
    queryFn: () => fetchLeadDetails(lead_id, filters),
  })
}

export const useDeletLeads = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (relationShipId) => deleteLeadDetail(relationShipId),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "Detailleads", exact: false }])
    },
  })
}

export const useFeatchUsers = (filterRole, options = {}) => {
  const { search_query, interviewerSearchQuery } = options

  return useQuery({
    queryKey: ["FETCH_USERS", filterRole, search_query, interviewerSearchQuery],
    queryFn: () => fetchUsers(filterRole, search_query, interviewerSearchQuery),
  })
}

export const useAllJds = () => {
  return useQuery({
    queryKey: ["FEATCH_JDS"],
    queryFn: () => fetchJds(),
  })
}

export const useCreateLeadRelationShip = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ lead_id, data }) => createLedRelationShip(lead_id, data),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "Detailleads", exact: false }])
    },
  })
}
export const useUpdateLeadRelationShip = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ relation_id, data }) => EditLedRelationShip(relation_id, data),
    onSuccess: () => {
      queryClient.invalidateQueries([{ queryKey: "Detailleads", exact: false }])
    },
  })
}
