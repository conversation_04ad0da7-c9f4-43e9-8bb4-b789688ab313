import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import {
  createCourse,
  DeleteCategory,
  editCourse,
  getResources,
  updateCourseStatus,
} from "../api/create-course-form.api"
import { tanstackConfig } from "./tanstack-config"

export const RESOURCE_TYPES = {
  TAGS: "TAGS",
  SKILLS: "SKILLS",
  CATEGORIES: "CATEGORIES",
}

export const DATA_TYPES = {
  tags: "tags",
  skills: "skills",
  categories: "categories",
}

export const useCreateCourse = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createCourse,
    onSuccess: () => {
      queryClient.invalidateQueries(["courses"]) // Refresh courses list
    },
  })
}

export const useResources = (data_type, resource_type) => {
  return useQuery({
    queryKey: ["resources", data_type, resource_type],
    queryFn: () => getResources(data_type, resource_type),
    enabled: !!data_type && !!resource_type,
  })
}

export const UseDeleteCategory = (CategoryId) => {
  return useQuery({
    queryKey: ["DELETE-CATEGORY", CategoryId],
    queryFn: () => DeleteCategory(CategoryId),
    enabled: !!CategoryId,
  })
}

export const useUpdateCourseStatus = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateCourseStatus,
    onSuccess: (_, { course_id }) => {
      // Invalidate specific course query if you have one
      queryClient.invalidateQueries(["course", course_id])
      // Invalidate general courses list query
      queryClient.invalidateQueries(["courses"])
    },
    tanstackConfig,
  })
}
export const useUpdateCourse = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: editCourse,
    onSuccess: () => {
      queryClient.invalidateQueries(["courses"]) // Refresh courses list
    },
  })
}
