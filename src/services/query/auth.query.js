import { useMutation } from "@tanstack/react-query"
import { forgetPassword, logIn, logOut, resetPassword, signUp } from "../api/auth.api"

// export const useCandidates = ({ page, limit, search, status }) => {
//   return useQuery({
//     queryKey: ["candidates", { page, limit, search, status }],
//     queryFn: () => getCandidates({ page, limit, search, status }),
//     select: (response) => ({
//       candidates: response.data.items,
//       pagination: {
//         total: response.data.total,
//         totalPages: Math.ceil(response.data.total / limit),
//       },
//     }),
//     keepPreviousData: true,
//     staleTime: 1000 * 60 * 5, // 5 minutes
//   })
// }

// export const useCandidateCV = (candidateId) => {
//   return useQuery({
//     queryKey: ["candidateCV", candidateId],
//     queryFn: () => getCandidateCV(candidateId),
//     select: (response) => response.data,
//     enabled: !!candidateId,
//     staleTime: 1000 * 60 * 45, // 45 minutes
//     keepPreviousData: true,
//   })
// }

export const useSignUp = () => {
  // const queryClient = useQueryClient()
  return useMutation({
    mutationFn: (data) => signUp(data),
  })
}

export const useLogIn = () => {
  // const queryClient = useQueryClient()
  return useMutation({
    mutationFn: (data) => logIn(data),
  })
}

export const useForgetPassword = () => {
  // const queryClient = useQueryClient()
  return useMutation({
    mutationFn: (data) => forgetPassword(data),
  })
}

export const useResetPassword = () => {
  // const queryClient = useQueryClient()
  return useMutation({
    mutationFn: (data) => resetPassword(data),
  })
}

export const useMutateLogOut = () => {
  // const queryClient = useQueryClient()
  return useMutation({
    mutationFn: ({ userID, userRole, device_info }) => logOut(userID, userRole, device_info),
    retry: false,
  })
}
