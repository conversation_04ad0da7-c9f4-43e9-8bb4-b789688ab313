import { courseURL } from "@/lib/constants/url.constant"
import { store } from "@store/"
import axios from "axios"

const instance = axios.create({
  baseURL: "https://training10xapi.10xscale.ai",
})

const getAuthHeaders = () => {
  const { sessionData } = store.getState().user
  return sessionData?.access_token ? { Authorization: `Bearer ${sessionData.access_token}` } : {}
}

export const CreateAssignments = (course_id, modules_id, data) => {
  console.log("createDtaass", data)
  const response = instance.post(
    `${courseURL}/v1/courses/${course_id}/modules/${modules_id}/assignments`,
    data,
    { headers: getAuthHeaders() }
  )
  return response.data // Return the data from the response
}

export const updateAssignmentSubmissionStatus = (course_id, module_id, assignment_id, data) => {
  const config = {
    headers: {
      ...getAuthHeaders(),
      "Content-Type": "multipart/form-data",
    },
  }

  // data is already a FormData object, no need to recreate it
  return instance.patch(
    `course-tracking-service/v1/courses/${course_id}/modules/${module_id}/assignments/${assignment_id}`,
    data, // Pass the FormData directly
    config
  )
}
export const FetchAssignments = (course_id, module_id, filters) => {
  return instance.get(`${courseURL}/v1/courses/${course_id}/modules/${module_id}/assignments`, {
    headers: getAuthHeaders(),
    params: {
      ...filters,
    },
  })
}

export const deleteAssignment = (modules_id, course_id, assignment_id) => {
  const response = instance.delete(
    `${courseURL}/v1/courses/${course_id}/modules/${modules_id}/assignments/${assignment_id}`,
    { headers: getAuthHeaders() }
  )
  return response.data
}

export const updateAssignment = (course_id, modules_id, assignment_id, publish_status, data) => {
  console.log("updatezzzdData", course_id, modules_id, assignment_id, publish_status, data)

  const response = instance.put(
    `${courseURL}/v1/courses/${course_id}/modules/${modules_id}/assignments/${assignment_id}`,
    data,
    { headers: getAuthHeaders() }
  )
  return response.data
}

export const submitAssignment = (course_id, module_id, assignment_id, data) => {
  const config = {
    headers: {
      ...getAuthHeaders(),
      "Content-Type": "multipart/form-data",
    },
  }

  const response = instance.post(
    `course-tracking-service/v1/courses/${course_id}/modules/${module_id}/assignments/${assignment_id}/submission`,
    data,
    config
  )
  return response.data
}

export const getAssignmentSubmissionView = (course_id, module_id, assignment_id) => {
  return instance.get(
    `course-tracking-service/v1/courses/${course_id}/modules/${module_id}/assignments/${assignment_id}/submission`,

    { headers: getAuthHeaders() }
  )
}
