import { mockInterviewURL } from "@/lib/constants/url.constant"
import instance from "."

export const postMockInterview = async (data = {}) => {
  return instance.post(`${mockInterviewURL}/v1/interviews/mock`, data)
}

export const getMockInterviews = async () => {
  return instance.get(`${mockInterviewURL}/v1/interviews/mock`)
}

export const patchMockInterview = async (data) => {
  const { id, ...interviewData } = data // Changed from interview_id to id
  return instance.patch(`${mockInterviewURL}/v1/interviews/mock/${id}`, interviewData)
}
