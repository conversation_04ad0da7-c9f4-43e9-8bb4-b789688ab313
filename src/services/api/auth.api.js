import axios from "axios"

// API Created here seperately because these APIs are not authenticated(no need to share token)
const api = axios.create({
  baseURL: `${import.meta.env.VITE_BACKEND_SERVER}/user-auth-service/`,
})

/// //////////////////////////////// POST API //////////////////////////////////

export const signUp = async (data) => {
  const response = await api.post("v1/auth/signup", data)
  return response.data
}

export const logIn = async (data) => {
  const response = await api.post("v1/auth/login", data)
  return response.data
}

export const forgetPassword = async (email) => {
  const response = await api.post(`v1/auth/forget-password?email=${email}`)
  return response.data
}

/// //////////////////////////////// PATCH API //////////////////////////////////

export const resetPassword = async (data) => {
  const response = await api.patch("v1/auth/reset-password", data)
  return response.data
}

/// //////////////////////////////// DELETE API //////////////////////////////////

export const logOut = async (userID, userRole, deviceInfo) => {
  const response = await api.delete(`v1/auth/logout?user_id=${userID}&user_role=${userRole}`, {
    data: deviceInfo,
  })
  return response.data
}
