import { courseTrack, courseURL } from "@/lib/constants/url.constant"
import { getLocalTimezone } from "@/components/custom/custom-forms/local-date-zone"
import instance from "."

const localTimezone = getLocalTimezone()

export const fetchPerformanceById = (course_id, student_id) => {
  return instance.get(
    `${courseTrack}/v1/courses/${course_id}/students/${student_id}/quiz:performance`
  )
}

export const fetchSubmissionById = (course_id, student_id) => {
  return instance.get(
    `${courseTrack}/v1/courses/${course_id}/students/${student_id}/assignment_quiz:submissions`
  )
}

export const fetchCourseActivityById = (course_id, start_date, end_date) => {
  return instance.get(`${courseTrack}/v1/courses/${course_id}/students/course:activity`, {
    params: {
      start_date,
      end_date,
    },
  })
}

export const getCourseOverview = (course_id, course_ids) => {
  const params = {
    current_date: localTimezone.currentDate,
  }

  // Add course_ids to params only if it exists and has values
  if (course_ids && course_ids.length > 0) {
    params.course_ids = course_ids
  }

  return instance.get(`${courseURL}/v1/courses/${course_id}/course-overview`, {
    params,
    // Ensure array parameters are serialized correctly
    paramsSerializer: {
      indexes: null, // This creates course_ids=111&course_ids=110 format
    },
  })
}

export const getAttendance = (course_id, modules_id, resource_id, resourse_date, filters) => {
  return instance.get(
    `${courseTrack}/v1/courses/${course_id}/modules/${modules_id}/resources/${resource_id}/resource:tracking`,
    {
      params: {
        resourse_date,
        ...filters,
      },
    }
  )
}

export const updateAttendance = (course_id, resource_id, session_date, data) => {
  return instance.patch(
    `${courseTrack}/v1/courses/${course_id}/resources/${resource_id}/attendance`,
    data,
    {
      params: {
        session_date,
      },
    }
  )
}
