import apiConstant from "@constants/api.constant"
import { jd_cvURL } from "@/lib/constants/url.constant"
import axiosInstance from "./index"

export const getCandidates = async ({ page, limit, search, status }) => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    ...(search && { search }),
    status,
  })

  const response = await axiosInstance.get(`${apiConstant.candidate.list}?${params}`)
  return response.data
}

export const getCandidateDetails = async (candidateId) => {
  const response = await axiosInstance.get(`${apiConstant.candidate.details}/${candidateId}`)
  return response.data
}

export const getAvailableJobs = async ({ page, limit, filter, candidateId }) => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    job_type: filter,
    candidate_id: candidateId,
  })

  const response = await axiosInstance.get(`${apiConstant.candidate.availableJobs}?${params}`)
  return response.data
}

export const parseCV = async (file) => {
  const formData = new FormData()
  formData.append("file", file)

  const response = await axiosInstance.post(`${apiConstant.candidate.parse}`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  })
  return response.data
}

export const saveCandidate = async (formData) => {
  // Validate if meta is JSON string before sending
  // const metaString = formData.get("meta")
  // if (metaString) {
  //   try {
  //     JSON.parse(metaString) // validate JSON
  //   } catch (e) {
  //     throw new Error("Meta data must be valid JSON")
  //   }
  // }

  const response = await axiosInstance.post(apiConstant.candidate.save, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  })
  return response.data
}

export const archiveCandidate = async (candidateId) => {
  const response = await axiosInstance.patch(
    `${apiConstant.candidate.details}/${candidateId}/status?status=false`
  )
  return response.data
}

export const getCandidateCV = async (candidateId) => {
  const response = await axiosInstance.get(`${apiConstant.candidate.list}/${candidateId}/cv-url`, {
    headers: {
      Accept: "application/json",
    },
  })
  return response.data
}

export const applyToJob = async ({ candidateId, jobId, status }) => {
  if (status) {
    const response = await axiosInstance.post(
      `${apiConstant.candidate.list}/${candidateId}/applications/${jobId}`
    )
    return response.data
  }
  const response = await axiosInstance.delete(
    `${apiConstant.candidate.list}/${candidateId}/applications/${jobId}`
  )
  return response.data
}

export const getResume = async () => {
  const response = await axiosInstance.get(`${jd_cvURL}/v1/cvs`)
  return response.data
}

export const getPublicUrls = async (cloudPath) => {
  const response = await axiosInstance.get(`${jd_cvURL}/v1/generate/public-url`, {
    params: { cloud_path: cloudPath },
  })
  return response.data
}
