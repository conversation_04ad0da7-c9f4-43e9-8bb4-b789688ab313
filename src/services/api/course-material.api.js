import { courseURL } from "@/lib/constants/url.constant"
import instance from "."

export const createCourseMaterial = async (data, course_id = 2) => {
  console.log(course_id, "course_id")

  const formData = new FormData()

  const dataObject = {
    title: data?.title,
    description: data?.description,
    resource_type: data?.resource_type,
    resource_link: data?.resource_link,
    publish_status: data?.publish_status,
  }

  formData.append("data", JSON.stringify(dataObject))
  if (data?.docs) {
    formData.append("file", data.docs[0]) // Append file if it exists
  }
  const res = await instance.post(`/${courseURL}/v1/courses/${course_id}/materials`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  })
  return res.data
}

export const fetchCourseMaterials = (course_id, filters) => {
  return instance.get(`/${courseURL}/v1/courses/${course_id}/materials`, {
    params: {
      ...filters,
    },
  })
}

export const deleteCourseMaterial = (course_id, material_id) => {
  return instance.delete(`/${courseURL}/v1/courses/${course_id}/materials/${material_id}`)
}

export const updateCourseMaterial = async (data, course_id, material_id) => {
  const res = await instance.put(
    `/${courseURL}/v1/courses/${course_id}/materials/${material_id}`,
    data,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  )
  return res.data
}

export const updateMaterialStatus = (course_id, material_id, status) => {
  return instance.patch(
    `/${courseURL}/v1/courses/${course_id}/materials/${material_id}?publish_status=${status}`,
    {
      publish_status: status,
    }
  )
}

export const fetchBatchCourse = () => {
  return instance.get(`/${courseURL}/v1/courses`, {
    params: {
      for_batch_course: true,
    },
  })
}
