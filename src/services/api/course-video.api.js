import { courseTrack, courseURL } from "@/lib/constants/url.constant"
import instance from "."

export const fetchResources = async (course_id, modules_id) => {
  return instance.get(`${courseURL}/v1/courses/${course_id}/modules/${modules_id}/resources`)
}

export const postTrackVideo = async (resource_id, data = {}) => {
  console.log("postTrackVideo", resource_id, data)
  return instance.post(`${courseURL}/v1/videos/${resource_id}/progress`, {
    current_position: data.current_position || 0,
  })
}

export const getStudentPerformance = async (course_id) => {
  return instance.get(`${courseTrack}/v1/courses/${course_id}/student/performance`)
}
