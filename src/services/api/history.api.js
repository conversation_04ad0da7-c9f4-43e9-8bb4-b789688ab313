import apiConstant from "@/lib/constants/api.constant"
import axiosInstance from "./index"

export const deleteApplication = (id) => {
  return axiosInstance.delete(`${apiConstant.application.delete}/${id}`)
}

export const updateApplication = ({ id, data }) => {
  return axiosInstance.patch(`${apiConstant.application.delete}/${id}`, data)
}

export const getApplications = async ({ page = 1, pageSize = 10, selectCandidate, selectJD }) => {
  const params = {
    page,
    page_size: pageSize,
  }

  if (selectCandidate !== null) {
    params.candidate_id = selectCandidate
  }

  if (selectJD !== null) {
    params.job_post_id = selectJD
  }

  const response = await axiosInstance.get(apiConstant.application.list, {
    params,
  })

  console.log("getApplications", response)

  return response.data
}

export const createApplication = (data) => {
  return axiosInstance.post(apiConstant.application.list, {
    candidate_id: parseInt(data.candidate_id, 10),
    job_post_id: parseInt(data.job_post_id, 10),
    contacts: data.contacts,
    comments: data.comments,
  })
}
