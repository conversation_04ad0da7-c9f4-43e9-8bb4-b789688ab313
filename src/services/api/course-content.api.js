import { courseTrack, courseURL } from "@/lib/constants/url.constant"
import instance from "."

const COURSE_CONTENT_URL = "/course-content"

export const createCourseContent = (data) => {
  return instance.post(`/${courseURL}/v1/courses`, { data })
}

export const addCourseContent = (data) => {
  return instance.post(COURSE_CONTENT_URL, data)
}

export const addFeedback = (data) => {
  return instance.patch(COURSE_CONTENT_URL, data)
}

export const changeCourseContentStatus = (course_id, data) => {
  return instance.patch(`/${courseURL}/v1/courses/${course_id}`, data)
}

export const fetchCourseContents = () => {
  return instance.get(COURSE_CONTENT_URL)
}

export const deleteCourseContent = (id) => {
  return instance.delete(`${COURSE_CONTENT_URL}/${id}`)
}

export const updateCourseContent = (course_id, data) => {
  return instance.put(`/${courseURL}/v1/courses/${course_id}`, data)
}

export const createCourseModule = (course_id, data) => {
  return instance.post(`/${courseURL}/v1/courses/${course_id}/modules/`, data)
}

export const deleteCourseModule = (course_id, module_id) => {
  return instance.delete(`/${courseURL}/v1/courses/${course_id}/modules/${module_id}`)
}

export const updateCourseModule = (course_id, module_id, data) => {
  return instance.put(`/${courseURL}/v1/courses/${course_id}/modules/${module_id}`, data)
}

export const updateCourseModuleStatus = (course_id, module_id, status) => {
  return instance.patch(`/${courseURL}/v1/courses/${course_id}/modules/${module_id}`, null, {
    params: { publish_status: status },
  })
}

export const updateCourseTopicStatus = (course_id, module_id, resource_id, status, type) => {
  console.log("_status", status)

  let payload = {}

  if (type === "complete") {
    payload = { is_completed: status }
  } else {
    payload = { publish_status: status }
  }

  return instance.patch(
    `/${courseURL}/v1/courses/${course_id}/modules/${module_id}/resources/${resource_id}`,
    payload
  )
}

export const createCourseToic = (course_id, modules_id, formData) => {
  return instance.post(
    `/${courseURL}/v1/courses/${course_id}/modules/${modules_id}/resources`,
    formData
  )
}

export const deleteCourseTopic = (course_id, module_id, resource_id) => {
  return instance.delete(
    `/${courseURL}/v1/courses/${course_id}/modules/${module_id}/resources/${resource_id}`
  )
}

export const updateCourseTopic = (course_id, module_id, resource_id, formData) => {
  return instance.put(
    `/${courseURL}/v1/courses/${course_id}/modules/${module_id}/resources/${resource_id}`,
    formData
  )
}

export const fetchCourseTopics = (course_id, module_id, userID, userRole, signature) => {
  return instance.get(`/${courseURL}/v1/courses/${course_id}/modules/${module_id}/resources`, {
    headers: {
      "x-user-id": userID,
      "x-user-role": userRole,
      "x-signature": signature,
    },
  })
}

export const attachStudentandTrainerToCourse = (course_id, payload) => {
  return instance.post(`/${courseTrack}/v1/courses/${course_id}/enrollments`, payload)
}

export const attendanceForStudents = (course_id, resources_id, data, date) => {
  return instance.post(
    `/${courseTrack}/v1/courses/${course_id}/resources/${resources_id}/attendance`,
    data,
    {
      params: { date },
    }
  )
}
