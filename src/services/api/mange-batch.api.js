import { paymentURL } from "@/lib/constants/url.constant"
import instance from "."

export const createBatchCourse = (data) => {
  return instance.post(`/v1/courses/${data?.course_id ?? 2}/announcements`, data)
}

export const fetchAllAnnouncements = ({ queryKey }) => {
  const [, params] = queryKey // Extract parameters from queryKey
  return instance.get(`/v1/courses/${params?.course_id ?? 2}/announcements`, {
    params: {
      limit: params?.limit,
      offset: params?.offset,
      sort_by: params?.sort_by,
      sort_by_field: params?.sort_by_field,
      search_query: params?.search_query,
    },
  })
}

export const updateBatchCourse = (course_id, announcement_id, data) => {
  return instance.put(`v1/courses/${course_id}/announcements/${announcement_id}`, data)
}

export const deleteBatchCourse = (course_id, announcement_id) => {
  return instance.delete(`v1/courses/${course_id}/announcements/${announcement_id}`)
}

// student
export const fetchBatchCoursesByRole = (params, type) => {
  const baseURL = `${import.meta.env.VITE_BACKEND_SERVER}/${paymentURL}/v1/payments`
  return instance.get(baseURL, {
    params: {
      payment_user: type,
      ...params,
    },
  })
}

// `${paymentURL}/v1/payments?offset=0&limit=10&sort_by=DESC&sort_by_field=modified_at&payment_method=${payment_method}&payment_type=${payment_type}&payment_status=${payment_status}$course_id=${course_id}&payment_user=${type}`

export const createStudentBatchCourse = (data) => {
  return instance.post(`/v1/courses/${data?.course_id ?? 2}/announcements`, data)
}

export const updateStudentBatchCourse = (course_id, announcement_id, data) => {
  return instance.put(`v1/courses/${course_id}/announcements/${announcement_id}`, data)
}

export const deleteStudentBatchCourse = (course_id, announcement_id) => {
  return instance.delete(`v1/courses/${course_id}/announcements/${announcement_id}`)
}

// trainer

export const createTrainerBatchCourse = (data) => {
  return instance.post(`/v1/courses/${data?.course_id ?? 2}/announcements`, data)
}

export const updateTrainerBatchCourse = (course_id, announcement_id, data) => {
  return instance.put(`v1/courses/${course_id}/announcements/${announcement_id}`, data)
}

export const deleteTrainerBatchCourse = (course_id, announcement_id) => {
  return instance.delete(`v1/courses/${course_id}/announcements/${announcement_id}`)
}

export const updateBatchStatus = (course_id, data) => {
  return instance.patch(`course-service/v1/courses/${course_id}?publish_status=${data}`)
}
