import instance from "."

// Fetch assignments API function
export const fetchLeadDetails = async (lead_id, filters) => {
  const response = await instance.get(`/jd-cv-service/v1/leads/${lead_id}/relations`, {
    params: {
      ...filters,
    },
  }) // Use instance here
  return response.data
}

export const deleteLeadDetail = async (relationShipId) => {
  const response = await instance.delete(`/jd-cv-service/v1/leads/${relationShipId}/relations`)
  return response.data
}

export const fetchUsers = async (filterRole, search_query, interviewerSearchQuery) => {
  const response = await instance.get(`/user-service/v1/users`, {
    params: {
      filter_role: filterRole,
      search_query: interviewerSearchQuery || search_query,
    },
  })

  return response.data
}

export const fetchJds = async () => {
  const response = await instance.get(`/jd-cv-service/v1/jds`, {
    params: {
      job_type: "ALL",
    },
  })

  return response.data
}

export const createLedRelationShip = async (lead_id, data) => {
  const response = await instance.post(`/jd-cv-service/v1/leads/${lead_id}/relations`, data)
  return response.data
}

export const EditLedRelationShip = async (relation_id, data) => {
  const response = await instance.put(`/jd-cv-service/v1/leads/${relation_id}/relations`, data)
  return response.data
}
