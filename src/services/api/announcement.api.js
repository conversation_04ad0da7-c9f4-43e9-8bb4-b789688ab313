import { courseURL } from "@/lib/constants/url.constant"
import instance from "."

export const createAnnouncement = (data) => {
  return instance.post(`/${courseURL}/v1/courses/${data?.course_id ?? 2}/announcements`, data)
}

export const fetchAllAnnouncements = ({ queryKey }) => {
  const [, params] = queryKey // Extract parameters from queryKey
  return instance.get(`/${courseURL}/v1/courses/${params?.course_id ?? 2}/announcements`, {
    params: {
      limit: params?.limit,
      offset: params?.offset,
      sort_by: params?.sort_by,
      sort_by_field: params?.sort_by_field,
      search_query: params?.search_query,
    },
  })
}

export const updateAnnouncement = (course_id, announcement_id, data) => {
  return instance.put(
    `course-service/v1/courses/${course_id}/announcements/${announcement_id}`,
    data
  )
}

export const deleteAnnouncement = (course_id, announcement_id) => {
  return instance.delete(`course-service/v1/courses/${course_id}/announcements/${announcement_id}`)
}

export const updateStatusAnnouncement = (course_id, announcement_id, status) => {
  return instance.patch(
    `${courseURL}/v1/courses/${course_id}/announcements/${announcement_id}?publish_status=${status}`
  )
}
