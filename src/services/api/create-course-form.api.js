import { courseURL } from "@/lib/constants/url.constant"
import instance from "./index"

export const createCourse = async (formData) => {
  const { data } = await instance.post(`/${courseURL}/v1/courses`, formData)

  if (data?.error) {
    console.error("API Error:", data.error)
    throw data.error
  }

  return data
}

export const getResources = async (data_type, resource_type) => {
  const response = await instance.get(`/${courseURL}/v1/resources/${data_type}`, {
    params: { resource_type }, // Send as query param
  })
  return response.data
}

export const updateCourseStatus = async ({ course_id, publish_status }) => {
  const response = await instance.patch(`/${courseURL}/v1/courses/${course_id}`, null, {
    params: { publish_status },
  })
  return response.data
}

export const DeleteCategory = async (CategoryId) => {
  const response = await instance.delete(`/course-service/v1/categories/${CategoryId}`)
  return response.data
}

export const editCourse = async ({ formData, course_id }) => {
  const response = await instance.put(`/course-service/v1/courses/${course_id}`, formData)

  return response.data
}
