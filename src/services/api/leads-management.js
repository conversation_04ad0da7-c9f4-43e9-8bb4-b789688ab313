import instance from "."

export const fetchLeads = (filters) => {
  return instance.get(`/jd-cv-service/v1/leads`, {
    params: {
      ...filters,
    },
  })
}

export const CreateLeads = async ({ data }) => {
  const formData = new FormData()
  // Create the data object
  const dataObject = {
    name: data.name,
    email: data.email,
    phone: data.phone || "",
    linkedin: data.linkedin || "",
    company_name: data.company_name || "",
    designation: data.designation || "",
  }
  // Convert the data object to a string and append it to FormData
  formData.append("data", JSON.stringify(dataObject))
  const response = await instance.post("/jd-cv-service/v1/leads", formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  })
  return response.data
}

export const uploadLeadFile = (file) => {
  const formData = new FormData()
  formData.append("file", file) // Ensure the file is appended correctly

  const response = instance.post("/jd-cv-service/v1/leads", formData)

  return response.data // Assuming API returns file URL or ID
}

export const deleteLead = async (lead_id) => {
  const response = await instance.delete(`/jd-cv-service/v1/leads/${lead_id}`)
  return response.data
}

export const updateLead = async (leadId, updatedData) => {
  const response = await instance.put(`/jd-cv-service/v1/leads/${leadId}`, updatedData)
  return response.data
}
