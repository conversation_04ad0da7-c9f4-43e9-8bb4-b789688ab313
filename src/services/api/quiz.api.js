import { courseURL } from "@/lib/constants/url.constant"
import instance from "."

export const createQuiz = (data, course_id, module_id) => {
  return instance.post(
    `${courseURL}/v1/courses/${course_id ?? 2}/modules/${module_id ?? 2}/quizzes`,
    data
  )
}

export const fetchQuizzes = (course_id, module_id, filters) => {
  return instance.get(`${courseURL}/v1/courses/${course_id}/modules/${module_id}/quizzes`, {
    params: {
      ...filters,
    },
  })
}

export const updateQuiz = (data, course_id, module_id, quiz_id) => {
  console.log("_updateQuiz", data, quiz_id)
  return instance.put(
    `${courseURL}/v1/courses/${course_id ?? 2}/modules/${module_id ?? 2}/quizzes/${quiz_id ?? 2}`,
    data
  )
}

export const deleteQuiz = (course_id, module_id, quiz_id) => {
  return instance.delete(
    `${courseURL}/v1/courses/${course_id}/modules/${module_id}/quizzes/${quiz_id}`
  )
}

export const updateQuizStatus = (course_id, module_id, quiz_id, status) => {
  return instance.patch(
    `${courseURL}/v1/courses/${course_id}/modules/${module_id}/quizzes/${quiz_id}?publish_status=${status}`
  )
}

export const updateQuizSubmissionStatus = ({ course_id, module_id, quiz_tracking_id, data }) => {
  console.log("handleUpdateSubmissionStatus", course_id, module_id, quiz_tracking_id, data)
  return instance.patch(
    `course-tracking-service/v1/courses/${course_id}/modules/${module_id}/quizzes/${quiz_tracking_id}`,
    data
  )
}

export const submitTheQuiz = (course_id, module_id, quiz_id, data) => {
  return instance.post(
    `course-tracking-service/v1/courses/${course_id}/modules/${module_id}/quizzes/${quiz_id}/submission`,
    data
  )
}

export const addGradeOrFeedbackToQuiz = (course_id, module_id, quiz_id, data) => {
  return instance.patch(
    `http://192.168.29.46:8083/v1/modules/${course_id}/modules/${module_id}/quizzes/${quiz_id}`,
    data
  )
}

export const getQuizSubmissionsView = (course_id, module_id, quizId) => {
  return instance.get(
    `course-tracking-service/v1/courses/${course_id}/modules/${module_id}/quizzes:submissions`,
    {
      params: { quiz_id: quizId },
    }
  )
}
