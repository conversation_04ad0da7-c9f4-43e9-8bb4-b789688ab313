/* eslint-disable sonarjs/prefer-immediate-return */
/* eslint-disable no-underscore-dangle */
import { getDeviceInfo } from "@/pages/auth/login/utils/device-info"
import { store } from "@store/" // Import your Redux store directly
import axios from "axios"
import { checkTokenExpiry } from "@/utils/helper"
import { makeSessionInvalidAC, onSessionTokenChangeAC } from "../store/slices/user.slice"

const instance = axios.create({
  baseURL: `${import.meta.env.VITE_BACKEND_SERVER}`,
})

// Request Interceptor: Attach Token
instance.interceptors.request.use(
  (request) => {
    // Get token directly from store
    const { sessionData } = store.getState().user

    if (sessionData?.access_token)
      request.headers.Authorization = `Bearer ${sessionData.access_token}`

    return request
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Function to Refresh Token
export const refreshToken = async (refToken) => {
  try {
    const deviceInfo = getDeviceInfo()
    const api2 = axios.create({
      baseURL: `${import.meta.env.VITE_BACKEND_SERVER}/auth-service`,
    })
    const response = await api2.post("/v1/sessions/refresh", deviceInfo?.device_info, {
      headers: { Authorization: `Bearer ${refToken}` },
    })

    // Update Redux Store with tokens
    store.dispatch(onSessionTokenChangeAC(response.data))
    return response.data.access_token
  } catch (error) {
    console.error("Refresh Token Expired!", error)
    throw error // Throw error to trigger logout
  }
}

// Response Interceptor: Centralized Error Handling
instance.interceptors.response.use(
  (response) => response, // Pass successful responses
  async (error) => {
    if (error.code === "ERR_NETWORK" || error?.response?.status === 401) {
      const originalRequest = error.config
      originalRequest._retry = true // Prevent infinite loop

      try {
        const {
          sessionData: { access_token, refresh_token },
        } = store.getState().user

        // Logout if refresh token also expired
        if (checkTokenExpiry(refresh_token)) {
          store.dispatch(makeSessionInvalidAC())
          return Promise.reject(error)
        }

        if (checkTokenExpiry(access_token)) {
          const newAccessToken = await refreshToken(refresh_token)
          console.log("newAccessToken", newAccessToken)

          // Update request header with new access token
          originalRequest.headers.Authorization = `Bearer ${newAccessToken}`
          return instance(originalRequest) // Retry the original request
        }
      } catch (refreshError) {
        console.error("Refresh token also expired. Logging out user.")
        // Logout if refresh token fails
        store.dispatch(makeSessionInvalidAC())
        return Promise.reject(refreshError)
      }
    } else console.error(error)

    return Promise.reject(error)
  }
)

// const firebaseAuthPromise = new Promise((resolve) => {
//     const unsubscribe = firebaseAuth.onAuthStateChanged((user) => {
//         unsubscribe()
//         resolve(user)
//     })
// })

// const timeoutPromise = new Promise((resolve, reject) => {
//     setTimeout(() => {
//         reject(new Error('Firebase app initialization timed out'))
//     }, 5000) // Adjust the timeout duration as needed
// })

// instance.interceptors.request.use(async function (config) {
//     console.log('I am intercepting', config)

//     // const firebase = useContext(FirebaseContext)

//     try {
//         await Promise.race([firebaseAuthPromise, timeoutPromise]) // Wait for the Firebase app to initialize or time out
//         const idToken = (await firebaseAuth.currentUser?.getIdToken()) ?? ''
//         if (idToken.length !== 0) {
//             config.headers.Authorization = idToken
//         }
//     } catch (error) {
//         // toast.error("Session Expired, Please Login Again");
//         // window.location.href = "/login";
//         console.log('DEBUG: I am rejecting', error)
//         // throw error; // Propagate the error to the caller of the interceptor
//         return ''
//     }

//     console.log(config)

//     return config
// })

// // added interceptors to the response
// // easy to debug
// instance.interceptors.response.use(
//     (response) => {
//         console.log('api response,', response)
//         // Edit response config
//         return response
//     },
//     (error) => {
//         if (error.response === null) {
//             if (window.location.pathname !== '/misc/maintenance/') {
//                 window.location.href = '/misc/maintenance/'
//             }
//         } else if (Number(error.response?.status) === 400) {
//         } else if (Number(error.response?.status) === 401) {
//             if (window.location.pathname !== '/misc/not-authorized/') {
//                 window.location.href = '/misc/not-authorized/'
//             }
//             // window.location.href = "/misc/not-authorized/";
//             // handle 502
//         } else if (Number(error.response?.status) === 502) {
//             if (window.location.pathname !== '/misc/maintenance/') {
//                 window.location.href = '/misc/maintenance/'
//             }
//         }
//         return Promise.reject(error)
//     }
// )

export default instance
