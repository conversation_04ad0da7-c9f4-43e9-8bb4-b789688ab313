import instance from "./index"

/// //////////////////////////////// POST APIS //////////////////////////////////

export const createPaymentSession = async (data, provider) => {
  const response = await instance.post(`/payment-service/v1/create-session/${provider}`, data)
  return response.data
}

export const sentPaymentRequest = async (data, courseID) => {
  const response = await instance.post(
    `/payment-service/v1/courses/${courseID}/payment-request`,
    data
  )
  return response.data
}

export const createTrainerEarning = async (data) => {
  const response = await instance.post("/payment-service/v1/trainer/earnings", data)
  return response.data
}

export const storeTrainerPayment = async (data) => {
  const response = await instance.post("/payment-service/v1/trainer-payments", data)
  return response.data
}

/// //////////////////////////////// GET APIS //////////////////////////////////

export const fetchPaymentRequests = async (params) => {
  const response = await instance.get(`/payment-service/v1/payment-requests`, {
    params,
  })
  return response.data
}

export const fetchPayments = async (params) => {
  const response = await instance.get(`/payment-service/v1/payments`, {
    params,
  })
  return response.data
}

export const fetchTrainerEarnings = async (params) => {
  const response = await instance.get("/payment-service/v1/trainer/earnings", {
    params,
  })
  return response.data
}

export const fetchTrainerPaymentStats = async (params) => {
  const response = await instance.get("/payment-service/v1/trainer/payments:stats", {
    params,
  })
  return response.data
}

/// //////////////////////////////// PATCH APIS //////////////////////////////////

export const rejectPaymentRequest = async (id, request_status) => {
  const response = await instance.patch(
    `/payment-service/v1/payment-requests/${id}?request_status=${request_status}`
  )
  return response.data
}
