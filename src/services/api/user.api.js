import api from "./index"

/// //////////////////////////////// GET API //////////////////////////////////

export const getNotificationSettings = async (userID) => {
  const response = await api.get(`/user-service/v1/users/${userID}/settings`)
  return response.data
}

export const getUser = async (userID) => {
  const response = await api.get(`/user-service/v1/users/${userID}`)
  return response.data
}

export const getAllUsers = async (params) => {
  const response = await api.get(`/user-service/v1/users`, {
    params,
  })
  return response.data
}

export const getAllVendors = async () => {
  const response = await api.get(`/user-service/v1/vendors`)
  return response.data
}

/// //////////////////////////////// POST API //////////////////////////////////

export const importUser = async (userID, data) => {
  const response = await api.post(`/user-service/v1/users/${userID}/import:users`, data)
  return response.data
}

export const createVendor = async (data) => {
  const response = await api.post(`/user-service/v1/vendor`, data)
  return response.data
}

/// //////////////////////////////// PUT API //////////////////////////////////

export const updateUser = async (userID, data) => {
  const response = await api.put(`/user-service/v1/users/${userID}`, data)
  return response.data
}

/// //////////////////////////////// PATCH API //////////////////////////////////

export const updateNotificationSettings = async (userID, data) => {
  const response = await api.patch(`/user-service/v1/users/${userID}/settings`, data)
  return response.data
}

export const fetchUsersByRole = async (filters, filter_role = "TRAINER") => {
  console.log("fetchUsersByRole", filters)

  const params = {
    for_interservice: false,
    filter_role,
    ...filters,
  }

  // Transform user_ids array to multiple user_ids parameters
  if (params.user_ids && Array.isArray(params.user_ids)) {
    const userIds = params.user_ids
    delete params.user_ids

    // Create URLSearchParams to handle multiple values with same key
    const searchParams = new URLSearchParams()

    // Add all other params first
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined) {
        searchParams.append(key, params[key])
      }
    })

    // Add each user_id as a separate parameter
    userIds.forEach(id => {
      searchParams.append('user_ids', id)
    })

    console.log("Final params being sent:", searchParams.toString())

    const response = await api.get("/user-service/v1/users", {
      params: searchParams,
    })
    return response.data
  }

  console.log("Final params being sent:", params)

  const response = await api.get("/user-service/v1/users", {
    params,
  })
  return response.data
}

export const getAllUsersStudents = async (params) => {
  const response = await api.get("/user-service/v1/users", {
    params,
  })
  return response.data
}
