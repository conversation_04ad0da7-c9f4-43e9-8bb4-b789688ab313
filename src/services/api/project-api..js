import { failureToast, successToast } from "@/components/custom/toasts/tosters"
import instance from "@/services/api"

export const fetchProject = (course_id, filters, userID, userRole, signature) => {
  return instance.get(`/course-service/v1/courses/${course_id}/projects`, {
    params: {
      ...filters,
    },
    headers: {
      "x-user-id": userID,
      "x-user-role": userRole,
      "x-signature": signature,
    },
  })
}

// eslint-disable-next-line default-param-last
export const CreateProject = (course_id, data, userID, userRole, signature) => {
  const response = instance.post(`/course-service/v1/courses/${course_id}/projects`, data, {
    headers: {
      "x-user-id": userID,
      "x-user-role": userRole,
      "x-signature": signature,
    },
  })
  return response.data
}

export const deleteProject = async (project_id, course_id) => {
  try {
    const response = await instance.delete(
      `/course-service/v1/courses/${course_id}/projects/${project_id}`
    )
    return response.data
  } catch (error) {
    console.error("Error deleting project:", error)
    return error // Re-throwing error for handling in calling function
  }
}

export const EditProject = async (data, project_id, course_id, userID, userRole, signature) => {
  try {
    const response = await instance.put(
      `/course-service/v1/courses/${course_id}/projects/${project_id}`,
      data,
      {
        headers: {
          "x-user-id": userID,
          "x-user-role": userRole,
          "x-signature": signature,
        },
      }
    )
    return response.data
  } catch (error) {
    console.error("Error editing project:", error)
    throw error
  }
}

export const SubmitProject = async (
  project_id,
  course_id,
  data,
  file,
  userID,
  userRole,
  signature
) => {
  const formData = new FormData()

  // Create the data object
  const dataObject = {
    feedback_student: data.feedback_student,
    submission: data.submission,
    submission_type: data.submission_type || "",
  }
  if (file) {
    formData.append("file", file) // Append file if it exists
  }

  formData.append("submission", JSON.stringify(dataObject))

  const response = await instance.post(
    `/course-tracking-service/v1/courses/${course_id}/projects/${project_id}/submissions`,
    formData,
    {
      headers: {
        "x-user-id": userID,
        "x-user-role": userRole,
        "x-signature": signature,
      },
    }
  )
  return response.data
}
export const GetProjectSubmission = async (course_id, project_id, filters) => {
  const response = await instance.get(
    `/course-tracking-service/v1/courses/${course_id}/projects/submissions?project_ids=${project_id}`,
    {
      params: {
        ...filters,
      },
    }
  )
  return response.data
}

export const openPublicUrl = async (cloud_path) => {
  console.log(cloud_path, "cloud_path")

  try {
    const response = await instance.get("/jd-cv-service/v1/generate/public-url", {
      params: { cloud_path },
    })

    if (response.data?.data) {
      const downloadUrl = response.data.data

      // Check if the file is an image (you can expand this list as needed)
      const isImage = /\.(jpeg|jpg|gif|png|bmp|svg|webp)$/i.test(cloud_path)
      // Check if it's a PDF or other viewable file type
      const isViewableFile = /\.(pdf|txt|doc|docx|xls|xlsx|ppt|pptx)$/i.test(cloud_path)
      // Check if it's a video file
      const isVideo = /\.(mp4|webm|ogg|mov|avi|wmv|flv|mkv)$/i.test(cloud_path)

      if (isImage || isViewableFile || isVideo) {
        // Open in a new tab
        window.open(downloadUrl, "_blank")
      } else {
        // Create an invisible anchor element for download
        const link = document.createElement("a")
        link.href = downloadUrl
        link.setAttribute("download", "")
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }

      successToast("File downloaded successfully", "")
    } else {
      failureToast("No download link available", "Please try again")
    }
  } catch (error) {
    failureToast("Something went wrong", "Please try again")
  }
}
export const editProjectStatus = async (
  project_id,
  course_id,
  publishedStatus,
  userID,
  userRole,
  signature
) => {
  const response = await instance.patch(
    `/course-service/v1/courses/${course_id}/projects/${project_id}?publish_status=${publishedStatus}`,
    {
      headers: {
        "x-user-id": userID,
        "x-user-role": userRole,
        "x-signature": signature,
      },
    }
  )
  return response.data
}

export const fetchTasks = async (project_id, course_id, filters, userID, userRole, signature) => {
  // eslint-disable-next-line sonarjs/prefer-immediate-return
  const response = await instance.get(
    `/course-service/v1/courses/${course_id}/projects/${project_id}/tasks`,
    {
      params: {
        ...filters,
      },
      headers: {
        "x-user-id": userID,
        "x-user-role": userRole,
        "x-signature": signature,
      },
    }
  )
  return response
}

export const taskEvaluation = async (
  course_id,
  project_id,
  tasks,
  project_tracking_id,
  acceptance_status,
  trainer_feedback
) => {
  try {
    const payload = {
      tasks, // Sending the full tasks array
      project_tracking_id,
      acceptance_status,
      trainer_feedback,
    }

    const response = await instance.post(
      `/course-tracking-service/v1/courses/${course_id}/projects/${project_id}/tasks:evaluation`,
      payload
    )

    return response.data
  } catch (error) {
    console.error("Error submitting evaluation:", error)
    throw error // Re-throwing error for handling in calling function
  }
}
