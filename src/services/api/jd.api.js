import { jd_cvURL } from "@/lib/constants/url.constant"
import instance from "."

export const getJDList = async ({
  search = "",
  page = 1,
  pageSize = 10,
  status = "active",
  provider = "ALL",
}) => {
  const response = await instance.get(jd_cvURL.jd.list, {
    params: { search, page, page_size: pageSize, status, provider },
  })
  return response.data
}

export const getJDDetails = async (id) => {
  const response = await instance.get(`${jd_cvURL.jd.details}/${id}`)
  return response.data
}

export const getRecommendedCandidates = async (id, { offset = 0, limit = 10 }, candidateFilter) => {
  const response = await instance.get(jd_cvURL.jd.recommendedCandidates, {
    params: { offset, limit, jd_id: id, candidate_type: candidateFilter },
  })
  return response.data
}

export const archiveJD = async (id) => {
  const response = await instance.patch(`${jd_cvURL.jd.archive}/${id}/status?status=false`)
  return response.data
}

export const parseJD = async (text) => {
  const response = await instance.post(jd_cvURL.jd.parse, { text })
  return response.data
}

export const getAvalabileJDs = async ({ jobType = "ALL", searchQuery, offset, limit }) => {
  const response = await instance.get(`/${jd_cvURL}/v1/jds`, {
    params: { job_type: jobType, search_query: searchQuery, offset, limit },
  })
  return response.data
}

export const getJdDetailsJDs = async ({ job_type = "ALL" }, jd_id) => {
  const response = await instance.get(`${jd_cvURL}/v1/jds/${jd_id}`, {
    // Corrected the template literal
    params: { job_type },
  })
  return response.data
}

export const toggleApplyStatus = async (data) => {
  const response = await instance.post(`${jd_cvURL}/v1/apply:jds`, data)
  return response.data
}

export const createJD = async (data) => {
  const response = await instance.post(`${jd_cvURL}/v1/jds`, data)
  return response.data
}

export const getJobApplications = async () => {
  const response = await instance.get(`${jd_cvURL}/v1/jds:applications`)
  return response.data
}

export const updateJobApplicationStatus = async ({ application_id, data }) => {
  const response = await instance.patch(`${jd_cvURL}/v1/jds/applications`, data, {
    params: { application_id },
  })
  return response.data
}

export const deleteJobApplication = async ({ application_id }) => {
  const response = await instance.delete(`${jd_cvURL}/v1/jds/${application_id}/applications`)
  return response.data
}

export const postJobApplication = async (data) => {
  const response = await instance.post(`${jd_cvURL}/v1/jds/applications`, data)
  return response.data
}
