import gql from "graphql-tag"

export const GET_COURSE = gql`
  query GetCourse($filters: CourseFilter!) {
    get_course(filters: $filters) {
      total_records
      courses {
        id
        course_discount
        title
        category_id
        parent_course_id
        description
        intro
        nano_course_ids
        intro_image
        duration_days
        course_type
        course_tags
        course_skills
        total_hours
        course_level
        course_language
        pricing_type
        course_price
        base_currency
        prerequisites
        syllabus_url
        publish_status
        modified_at
        total_trainers
        total_students
        is_module_exists
      }
    }
  }
`

export const GET_COURSE_BY_BATCH = gql`
  query GetCourseById($filters: CourseFilter!) {
    get_course(filters: $filters) {
      total_records
      courses {
        id
        title
      }
    }
  }
`

export const GET_BATCH_COURSEID = gql`
  query GetBatchCourseId($filters: CourseFilter!) {
    get_course(filters: $filters) {
      total_records
      courses {
        id
        title
      }
    }
  }
`

export const GET_BATCH_COURSE = gql`
  query GetBatchCourse($filters: CourseFilter!) {
    get_course(filters: $filters) {
      total_records
      courses {
        id
        title
        category_id
        parent_course_id
        modified_at
        trainer_ids
        from_course_date
        to_course_date
        publish_status
        student_ids
        total_trainers
        total_students
        is_module_exists
      }
    }
  }
`

export const GET_COURSES_MIN_INFO = gql`
  query GetCourseMinInfo($filters: CourseFilter!) {
    get_course(filters: $filters) {
      total_records
      courses {
        id
        title
        parent_course_id
      }
    }
  }
`

export const GET_COURSE_CONTENTS = gql`
  query GetModuleResource($filters: ModuleFilter!) {
    get_module_resource(filters: $filters) {
      total_records
      modules {
        id
        module_name
        module_description
        duration_minutes
        sequence_number
        publish_status
        total_records
        modified_at
        resources {
          id
          resource_name
          resource_date
          resource_description
          duration_minutes
          notes
          resource_type
          resource_link
          publish_status
          modified_at
          is_completed
        }
      }
    }
  }
`

export const GET_JOB_APPLICATION = gql`
  query {
    get_module_resource(filters: { course_id: 2 }) {
      total_records
      modules {
        module_name
        module_description
        duration_minutes
        sequence_number
        publish_status
        total_records
        resources {
          resource_name
          resource_link
          resource_description
          modified_at
        }
      }
    }
  }
`

export const GET_MODULE_NAME = gql`
  query GetModuleResource($filters: ModuleFilter!) {
    get_module_resource(filters: $filters) {
      modules {
        id
        module_name
      }
    }
  }
`

export const GET_COURSE_CONTENT_MIN_INFO = gql`
  query GetMinInfoModuleResource($filters: ModuleFilter!) {
    get_module_resource(filters: $filters) {
      total_records
      modules {
        id
        module_name
        total_records
        resources {
          id
          resource_name
        }
      }
      trainers {
        id
        name
      }
    }
  }
`

export const FETCH_COURSES = gql`
  query GetCourse($filters: CourseFilter!) {
    get_course(filters: $filters) {
      total_records
      courses {
        course_discount
        title
        category_id
        parent_course_id
        id
      }
    }
  }
`
