import { combineReducers } from "redux"

import ct from "@constants/"

import user from "./slices/user.slice"
import theme from "./slices/theme.slice"
import courses from "./slices/courses.slice"
import batchSlice from "./slices/batch.slice"

const rootReducer = combineReducers({
  [ct.store.USER_STORE]: user,
  [ct.store.THEME_STORE]: theme,
  [ct.store.COURSES]: courses,
  [ct.store.BATCH_STORE]: batchSlice,
})

export default rootReducer
