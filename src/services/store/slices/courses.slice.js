import ct from "@constants/"
import { createSlice } from "@reduxjs/toolkit"

const coursesSlice = createSlice({
  name: ct.store.COURSES,
  initialState: {
    activeTab: "course-content",
    isActiveURL: true,
    activeTabDetails: {
      inerview_material: null,
      course_content: null,
    },
    myCourseDetails: null,
    activeCourseDetails: null,
    selectedBatchCourseDetails: null,
    activeCourse: "",
  },
  reducers: {
    setActiveTab: (state, action) => {
      state.activeTab = action.payload
    },
    setActiveURL: (state, action) => {
      state.isActiveURL = action.payload
    },
    setActiveTabDetails: (state, action) => {
      state.activeTabDetails = action.payload
    },
    setMyCourseDetails: (state, action) => {
      state.myCourseDetails = action.payload
    },
    setActiveCourseDetails: (state, action) => {
      console.log("setActiveCourseDetails", action.payload)
      state.activeCourseDetails = action.payload
    },
    setBatchCourseDetails: (state, action) => {
      state.selectedBatchCourseDetails = action.payload
    },
    setActiveCourse: (state, action) => {
      state.activeCourse = action.payload
    },
  },
})

export const {
  setActiveTab,
  setActiveURL,
  setActiveTabDetails,
  setMyCourseDetails: setMyCourseDetailsAC,
  setActiveCourseDetails,
  setBatchCourseDetails,
  setActiveCourse,
} = coursesSlice.actions
export default coursesSlice.reducer
