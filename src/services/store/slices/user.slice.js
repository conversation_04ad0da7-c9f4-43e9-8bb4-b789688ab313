import { createSlice } from "@reduxjs/toolkit"

import ct from "@constants/"

const initialState = {
  id: "",
  full_name: "",
  user_name: "",
  email: "",
  phone: "",
  userRole: "",
  date_of_birth: "",
  skills: "",
  location: "",
  designation: "",
  auth_provider: "",
  cv_link: "",
  linkedin_link: "",
  github_link: "",
  image: "",
  resume: "",
  summary: "",
  vendorData: {
    id: "",
    vendor_name: "",
    logo: "",
  },
  // Auth & Session
  isAuthenticated: true,
  sessionData: {
    access_token: "",
    refresh_token: "",
    type: "",
  },
  isSessionValid: true,
}

export const userSlice = createSlice({
  name: ct.store.USER_STORE,
  initialState,
  reducers: {
    login: (state, action) => {
      const { userData, vendorData, sessionData } = action.payload
      state.id = userData?.id
      state.full_name = userData?.full_name
      state.user_name = userData?.user_name
      state.email = userData?.email
      state.phone = userData?.phone
      state.userRole = userData?.role
      state.date_of_birth = userData?.date_of_birth
      state.skills = userData?.skills
      state.location = userData?.location
      state.designation = userData?.designation
      state.auth_provider = userData?.auth_provider
      state.cv_link = userData?.cv_link
      state.linkedin_link = userData?.linkedin_link
      state.github_link = userData?.github_link
      state.image = userData?.image
      state.resume = userData?.resume
      state.summary = userData?.summary
      // Vendor Data
      state.vendorData.id = vendorData?.id
      state.vendorData.vendor_name = vendorData?.vendor_name
      state.vendorData.logo = vendorData?.logo
      // Auth & Session
      state.isAuthenticated = true
      state.sessionData.access_token = sessionData?.access_token
      state.sessionData.refresh_token = sessionData?.refresh_token
      state.sessionData.type = sessionData?.type
      state.isSessionValid = true
    },

    logout: (state) => {
      state.id = initialState.id
      state.full_name = initialState.full_name
      state.user_name = initialState.user_name
      state.email = initialState.email
      state.phone = initialState.phone
      state.userRole = initialState.userRole
      state.date_of_birth = initialState.date_of_birth
      state.skills = initialState.skills
      state.location = initialState.location
      state.designation = initialState.designation
      state.auth_provider = initialState.auth_provider
      state.cv_link = initialState.cv_link
      state.linkedin_link = initialState.linkedin_link
      state.github_link = initialState.github_link
      state.image = initialState.image
      state.summary = initialState.summary
      // Vendor Data
      state.vendorData = { ...initialState.vendorData }
      // Auth & Session
      state.isSessionValid = true
      state.isAuthenticated = false
      state.sessionData = { ...initialState.sessionData }
    },

    onApplyChanges: (state, action) => {
      // Merge the new data with the existing state
      return {
        ...state,
        ...action.payload,
      }
    },

    onSessionTokenChange: (state, action) => {
      const { access_token, refresh_token, token_type } = action.payload
      state.sessionData.access_token = access_token
      state.sessionData.refresh_token = refresh_token
      state.sessionData.type = token_type
    },

    makeSessionInvalid: (state) => {
      state.isSessionValid = false
    },

    onUpdateSpecificFields: (state, action) => {
      const { field, value } = action.payload
      state[field] = value
    },
  },
})

export const {
  login: loginAC,
  logout: logoutAC,
  onApplyChanges: onApplyChangesAC,
  onSessionTokenChange: onSessionTokenChangeAC,
  makeSessionInvalid: makeSessionInvalidAC,
  onUpdateSpecificFields: onUpdateSpecificFieldsAC,
} = userSlice.actions

export default userSlice.reducer
