import { http, HttpResponse } from "msw"
import apiConstant from "@/lib/constants/api.constant"

// Generate 20 mock JDs
const mockJDs = Array.from({ length: 20 }, (_, i) => ({
  id: i + 1,
  designation: [
    `Senior Frontend Developer`,
    `Backend Developer`,
    `Full Stack Developer`,
    `DevOps Engineer`,
  ][i % 4],
  minExp: Math.floor(Math.random() * 3) + 1,
  maxExp: Math.floor(Math.random() * 5) + 4,
  skills: ["React", "TypeScript", "JavaScript", "Node.js", "Python"].slice(
    0,
    Math.floor(Math.random() * 3) + 2
  ),
  secondarySkills: ["Docker", "AWS", "GraphQL", "MongoDB"].slice(
    0,
    Math.floor(Math.random() * 2) + 2
  ),
  location: ["Remote", "New York", "San Francisco", "London"][i % 4],
  type: ["Full-Time", "Contract", "Part-Time"][i % 3],
  postedDate: new Date(
    Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000
  ).toISOString(),
  description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit...",
}))

const mockCandidates = Array.from({ length: 50 }, (_, i) => ({
  id: `c${i + 1}`,
  name: `Candidate ${i + 1}`,
  currentDesignation: ["Senior Developer", "Frontend Developer", "Backend Developer"][i % 3],
  skills: ["React", "Node.js", "Python", "Java"].slice(0, Math.floor(Math.random() * 2) + 2),
  experience: Math.floor(Math.random() * 8) + 2,
  location: ["New York", "San Francisco", "London", "Remote"][i % 4],
  cvUrl: `https://example.com/cv-${i + 1}.pdf`,
  applied: Math.random() > 0.5,
}))

const jdHandlers = [
  http.get(apiConstant.jd.list, ({ request }) => {
    const url = new URL(request.url)
    const query = url.searchParams.get("query") || ""
    const offset = parseInt(url.searchParams.get("offset") || "0", 10)
    const limit = parseInt(url.searchParams.get("limit") || "10", 10)

    let filteredJDs = mockJDs
    if (query) {
      filteredJDs = mockJDs.filter(
        (jd) =>
          jd.designation.toLowerCase().includes(query.toLowerCase()) ||
          jd.skills.some((skill) => skill.toLowerCase().includes(query.toLowerCase()))
      )
    }

    return HttpResponse.json({
      data: filteredJDs.slice(offset, offset + limit),
      total: filteredJDs.length,
    })
  }),

  http.get(`${apiConstant.jd.details}/:id`, ({ params }) => {
    const jd = mockJDs.find((j) => j.id === parseInt(params.id, 10))
    return HttpResponse.json({ data: jd })
  }),

  http.get(`${apiConstant.jd.recommendedCandidates}/:id`, ({ request }) => {
    const url = new URL(request.url)
    const offset = parseInt(url.searchParams.get("offset") || "0", 10)
    const limit = parseInt(url.searchParams.get("limit") || "10", 10)

    return HttpResponse.json({
      data: mockCandidates.slice(offset, offset + limit),
      total: mockCandidates.length,
    })
  }),
]

export default jdHandlers
