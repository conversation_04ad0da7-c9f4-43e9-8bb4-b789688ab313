import { http, HttpResponse } from "msw"
import apiConstant from "@constants/api.constant"

const mockCandidates = Array.from({ length: 50 }, (_, index) => ({
  id: `${index + 1}`,
  name: `Candidate ${index + 1}`,
  currentDesignation: index % 2 ? "Senior Developer" : "Frontend Developer",
  skills: ["JavaScript", "React", index % 2 ? "Node.js" : "Python"],
  experience: Math.floor(Math.random() * 10) + 1,
  location: index % 2 ? "New York" : "San Francisco",
  cvUrl: `https://example.com/cv-${index + 1}.pdf`,
  status: index % 5 === 0 ? "archived" : "active",
}))

const mockCandidateDetails = (id) => ({
  id,
  name: `Candidate ${id}`,
  currentDesignation: "Senior Developer",
  skills: ["JavaScript", "React", "Node.js"],
  experience: 5,
  location: "New York",
  cvUrl: `https://example.com/cv-${id}.pdf`,
  status: "active",
  appliedJobs: [
    // ...job objects...
  ],
})

const mockAvailableJobsData = Array.from({ length: 50 }, (_, index) => ({
  id: index + 1,
  designation: index % 2 ? "Senior Frontend Developer" : "Full Stack Developer",
  minExp: Math.floor(Math.random() * 5) + 1,
  maxExp: Math.floor(Math.random() * 5) + 6,
  skills: ["React", "JavaScript", index % 2 ? "Node.js" : "Python"],
  location: index % 2 ? "Remote" : "New York",
  type: "Full-time",
  postedDate: new Date(
    Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000
  ).toISOString(),
  isApplied: index % 3 === 0,
  isRecommended: index % 4 === 0,
}))

const mockAvailableJobs = (page, limit, filter) => {
  let filteredJobs = mockAvailableJobsData

  // Apply filters
  switch (filter) {
    case "applied":
      filteredJobs = mockAvailableJobsData.filter((job) => job.isApplied)
      break
    case "recommended":
      filteredJobs = mockAvailableJobsData.filter((job) => job.isRecommended)
      break
    default:
      break
  }

  const totalItems = filteredJobs.length
  const totalPages = Math.ceil(totalItems / limit)
  const startIndex = (page - 1) * limit
  const endIndex = startIndex + limit

  return {
    jobs: filteredJobs.slice(startIndex, endIndex),
    pagination: {
      total: totalItems,
      totalPages,
      currentPage: page,
      limit,
    },
  }
}

const cvHandlers = [
  http.get(apiConstant.candidate.list, ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get("page") || "1", 10)
    const limit = parseInt(url.searchParams.get("limit") || "6", 10)
    const search = url.searchParams.get("search") || ""
    const status = url.searchParams.get("status") || "active"

    const filteredCandidates = mockCandidates.filter((candidate) => {
      const matchesStatus = candidate.status === status
      const matchesSearch = search
        ? candidate.name.toLowerCase().includes(search.toLowerCase()) ||
          candidate.skills.some((skill) => skill.toLowerCase().includes(search.toLowerCase()))
        : true
      return matchesStatus && matchesSearch
    })

    const totalItems = filteredCandidates.length
    const totalPages = Math.ceil(totalItems / limit)
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit

    const paginatedCandidates = filteredCandidates.slice(startIndex, endIndex)

    return HttpResponse.json({
      data: {
        candidates: paginatedCandidates,
        pagination: {
          total: totalItems,
          totalPages,
          currentPage: page,
          limit,
        },
      },
    })
  }),

  // Add archive handler
  http.post(`${apiConstant.candidate.archive}/:id`, ({ params }) => {
    const { id } = params
    // Update candidate status in mockCandidates
    const candidateIndex = mockCandidates.findIndex((c) => c.id === id)
    if (candidateIndex !== -1) {
      mockCandidates[candidateIndex].status = "archived"
    }
    return HttpResponse.json({ success: true })
  }),

  http.get(`${apiConstant.candidate.details}/:id`, ({ params }) => {
    const { id } = params
    const candidate = mockCandidateDetails(id)
    return HttpResponse.json({ data: candidate })
  }),

  http.get(apiConstant.candidate.availableJobs, ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get("page") || "1", 10)
    const limit = parseInt(url.searchParams.get("limit") || "10", 10)
    const filter = url.searchParams.get("filter") || "all"

    const data = mockAvailableJobs(page, limit, filter)
    return HttpResponse.json({ data })
  }),
]

export default cvHandlers
