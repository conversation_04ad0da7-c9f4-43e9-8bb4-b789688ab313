/* eslint-disable import/no-extraneous-dependencies */
import { http, HttpResponse } from "msw"

const commentMock = http.get("https://jsonplaceholder.typicode.com/comments/", () => {
  // return HttpResponse.json(
  //   { message: "Something went wrong" },
  //   {
  //     status: 500,
  //   }
  // )

  // ...and respond to them using this JSON response.
  return HttpResponse.json([
    {
      id: 1,
      email: "<EMAIL>",
      body: "This is a comment body",
      name: "<PERSON>",
    },
    {
      id: 2,
      email: "<EMAIL>",
      body: "This is a comment body",
      name: "<PERSON>",
    },
  ])
})

const handlers = [commentMock]

export default handlers
