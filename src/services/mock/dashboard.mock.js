import { http, HttpResponse } from "msw"
import ct from "@constants/"

const dashboardHandlers = [
  http.get(`*/${ct.api.dashboard.dashboard}`, () => {
    return HttpResponse.json({
      totalJobs: 150,
      totalCandidates: 450,
      jobsPerWeek: [
        { date: "21-11-24", Linkedin: 186, Monster: 80, Indeed: 100 },
        { date: "21-11-25", Linkedin: 190, Monster: 85, Indeed: 110 },
        { date: "21-11-26", Linkedin: 200, Monster: 90, Indeed: 120 },
        { date: "21-11-27", Linkedin: 220, Monster: 95, Indeed: 130 },
        { date: "21-11-28", Linkedin: 240, Monster: 100, Indeed: 140 },
        { date: "21-11-29", Linkedin: 260, Monster: 105, Indeed: 150 },
        { date: "21-11-30", Linkedin: 280, Monster: 110, Indeed: 160 },
      ],
      appliedJobsPerWeek: [
        { date: "21-11-24", Linkedin: 186, Monster: 80, Indeed: 100 },
        { date: "21-11-25", Linkedin: 190, Monster: 85, Indeed: 110 },
        { date: "21-11-26", Linkedin: 200, Monster: 90, Indeed: 120 },
        { date: "21-11-27", <PERSON>edin: 220, Monster: 95, Indeed: 130 },
        { date: "21-11-28", Linkedin: 240, Monster: 100, Indeed: 140 },
        { date: "21-11-29", Linkedin: 260, Monster: 105, Indeed: 150 },
        { date: "21-11-30", Linkedin: 280, Monster: 110, Indeed: 160 },
      ],
    })
  }),
]

export default dashboardHandlers
