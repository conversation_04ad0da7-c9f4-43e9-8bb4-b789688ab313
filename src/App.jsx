import { RouterProvider } from "react-router-dom"
import { PersistQueryClientProvider } from "@tanstack/react-query-persist-client"
import { Suspense } from "react"
import { Provider } from "react-redux"
import { store, persistor } from "@store/"
import { PersistGate } from "redux-persist/integration/react"

import { asyncStoragePersister, queryClient } from "@lib/query-client"
import { ThemeProvider } from "@context/theme-provider"

import { GoogleOAuthProvider } from "@react-oauth/google"
import router from "./route/index"
import { Toaster } from "./components/ui/toaster"
import SessionAlert from "./components/session-alert"

function App() {
  return (
    <Suspense fallback={null}>
      <Provider store={store}>
        <PersistGate loading={null} persistor={persistor}>
          <GoogleOAuthProvider clientId="">
            <PersistQueryClientProvider
              client={queryClient}
              persistOptions={{ persister: asyncStoragePersister }}
            >
              <ThemeProvider>
                <RouterProvider router={router} />
                <Toaster />
              </ThemeProvider>
              <SessionAlert />
            </PersistQueryClientProvider>
          </GoogleOAuthProvider>
        </PersistGate>
      </Provider>
    </Suspense>
  )
}

export default App
