const routes = {
  ROOT: "/",
  AUTH: {
    LOGIN: "/login",
    SINGUP: "/signup",
    PASSWORD_RESET: "password-reset",
  },
  ADMIN_DASHBOARD: "/admin-dashboard",
  JD_PAGE: "/jd",
  JD_DETAILS: "jd-details",
  CA<PERSON><PERSON>AT<PERSON>: "candidate",
  CANDIDATE_DETAILS: "candidate-details",
  ADD_CANDIDATE: "candidate/add",
  HISTORY: "history",
  MANAGE_HISTORY: "history/add",
  LIVE_COURSES: "/live-courses",
  LIVE_COURSES_LIST: "live-courses",
  MOCK_INTERVIEW: "mock-interview",
  SHORT_COURSES_LIST: "short-courses-list",
  NANO_COURSES: "nano-courses-list",
  COURSE: "/courses-content",
  COURSE_CONTENT: "course-video",
  CV_PAGE: "cv-page",
  RESUME: "resume",
  PROFILE: "/profile",
  NOTIFICATION: "/notification",
  FEEDBACK: "feedback",
  COURSE_DETAILS: "/course-details/:id",
  LIVE_COURSES_DETAILS: "/live-courses-details/:id",
  MY_COURSES_DETAILS: "/my-courses-details/:id",
  MY_COURSES: "/my-courses",
  COURSE_OVERVIEW: "/course-overview",
  COURSE_CREATION: "/course-creation",
  EDIT_COURSE: "/edit-course",
  TRAINER_PAYMENT: "/trainer-payment",
  TRAINER_EARNING: "/trainer-earning",
  COURSE_ATTENDANCE: "/course-attendance",
  QUIZ_VIEW: "quiz-view",
  ASSIGNMENT_VIEW: "assignment-view",
  PROJECT_VIEW: "project-view",
  PUBLIC_URL: "/public-url",
  MODULE_CONTENT: "/module-content",
  MANAGE_BATCH: "manage-batch",
  PAYMENTS: "/payments",
  STUDENT_PAYMENT_HISTORY: "student-payment",
  BATCH_COURSE: "batch-course",
}

export default routes
