import { GraphQLClient } from "graphql-request"
import { store } from "@store/"

const configGraphQL = (servicePath) => {
  const endPoint = `${import.meta.env.VITE_BACKEND_SERVER}/${servicePath}`

  // Retrieve access token from Redux store
  const { sessionData } = store.getState().user
  const token = sessionData?.access_token

  // Construct headers
  const headers = { Authorization: `Bearer ${token}` }

  return new GraphQLClient(endPoint, { headers })
}

export const getCourseGQLClient = () => configGraphQL("course-service/graphql")
export const getNotificationGQLClient = () => configGraphQL("user-service/gql")
