import { useEffect, useRef, useState } from "react"

const DatePicker = ({ value, onChange, className = "" }) => {
  // Initialize with the provided value or current date
  const [date, setDate] = useState(value || new Date())
  const [showPicker, setShowPicker] = useState(false)
  const [inputValue, setInputValue] = useState(formatDate(value || new Date()))
  const datePickerRef = useRef(null)

  // Format date as MM/DD/YYYY
  function formatDate(date) {
    if (!date) return ""
    // Create a new date and set it to midnight to avoid timezone issues
    const normalizedDate = new Date(date)

    return normalizedDate.toLocaleDateString("en-US", {
      month: "2-digit",
      day: "2-digit",
      year: "numeric",
    })
  }

  // Update component if value prop changes
  useEffect(() => {
    if (value) {
      setDate(new Date(value))
      setInputValue(formatDate(value))
    }
  }, [value])

  // Handle click outside to close the picker
  useEffect(() => {
    function handleClickOutside(event) {
      if (datePickerRef.current && !datePickerRef.current.contains(event.target)) {
        setShowPicker(false)
      }
    }
    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  const handleDateChange = (newDate) => {
    // Create a proper Date object and normalize it to avoid timezone issues
    const selectedDate = new Date(newDate)

    // Set the time to noon (12:00) to avoid any date shifting due to timezone
    selectedDate.setHours(12, 0, 0, 0)

    // Update local state
    setDate(selectedDate)
    setInputValue(formatDate(selectedDate))

    // Call the onChange handler with the new date
    if (onChange) onChange(selectedDate)

    setShowPicker(false)
  }

  // Handle direct input changes (if input is not readonly)
  const handleInputChange = (e) => {
    const value = e.target.value
    setInputValue(value)

    // Parse input to date if valid
    const parsedDate = new Date(value)
    if (!isNaN(parsedDate.getTime())) {
      // Normalize the date to noon to avoid timezone issues
      parsedDate.setHours(12, 0, 0, 0)
      setDate(parsedDate)
      if (onChange) onChange(parsedDate)
    }
  }

  const navigateMonth = (direction) => {
    const newDate = new Date(date)
    newDate.setMonth(newDate.getMonth() + direction)
    setDate(newDate)
  }

  const renderDays = () => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1).getDay()
    const daysInMonth = new Date(year, month + 1, 0).getDate()
    const days = []

    // Previous month's days
    const daysInPrevMonth = new Date(year, month, 0).getDate()
    for (let i = 0; i < firstDay; i++) {
      days.push(
        <div key={`prev-${i}`} className="text-gray-400 p-1">
          {daysInPrevMonth - firstDay + i + 1}
        </div>
      )
    }

    // Current month's days
    const today = new Date()
    for (let i = 1; i <= daysInMonth; i++) {
      const isToday =
        i === today.getDate() && month === today.getMonth() && year === today.getFullYear()
      const isSelected =
        i === date.getDate() && month === date.getMonth() && year === date.getFullYear()

      days.push(
        <button
          key={`current-${i}`}
          className={`w-8 h-8 rounded-full flex items-center justify-center p-1
            ${isToday ? "border border-blue-500" : ""}
            ${isSelected ? "bg-blue-500 text-white" : "hover:bg-gray-100"}
          `}
          onClick={() => {
            // Create date at noon to avoid timezone issues
            const exactDate = new Date(year, month, i, 12, 0, 0, 0)
            handleDateChange(exactDate)
          }}
        >
          {i}
        </button>
      )
    }

    // Next month's days (to fill the grid)
    const totalCells = Math.ceil((firstDay + daysInMonth) / 7) * 7
    const remainingDays = totalCells - (firstDay + daysInMonth)
    for (let i = 1; i <= remainingDays; i++) {
      days.push(
        <div key={`next-${i}`} className="text-gray-400 p-1">
          {i}
        </div>
      )
    }

    return days
  }

  return (
    <div className={`relative ${className}`} ref={datePickerRef}>
      {/* Input field */}
      <div className="relative">
        <input
          type="text"
          className="w-full pl-4 pr-10 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Select date"
          value={inputValue}
          onChange={handleInputChange}
          onFocus={() => setShowPicker(true)}
          readOnly
        />
        {/* Calendar icon */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <svg
            className="w-5 h-5 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
        </div>
      </div>

      {/* Date picker dropdown */}
      {showPicker && (
        <div className="absolute z-10 mt-1 bg-white rounded-md shadow-lg p-4 w-64">
          {/* Header with month/year navigation */}
          <div className="flex justify-between items-center mb-4">
            <button
              type="button"
              className="p-1 rounded-full hover:bg-gray-100"
              onClick={() => navigateMonth(-1)}
            >
              <svg
                className="w-5 h-5 text-gray-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
            <div className="font-medium text-gray-700">
              {date.toLocaleDateString("en-US", { month: "long", year: "numeric" })}
            </div>
            <button
              type="button"
              className="p-1 rounded-full hover:bg-gray-100"
              onClick={() => navigateMonth(1)}
            >
              <svg
                className="w-5 h-5 text-gray-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>

          {/* Day names */}
          <div className="grid grid-cols-7 gap-1 mb-2">
            {["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"].map((day) => (
              <div key={day} className="text-center text-xs font-medium text-gray-500">
                {day}
              </div>
            ))}
          </div>

          {/* Calendar days */}
          <div className="grid grid-cols-7 gap-1">{renderDays()}</div>

          {/* Today button */}
          <div className="mt-4 flex justify-center">
            <button
              className="text-sm text-blue-500 hover:text-blue-700"
              onClick={() => {
                const today = new Date()
                today.setHours(12, 0, 0, 0) // Set to noon to avoid timezone issues
                handleDateChange(today)
              }}
            >
              Today
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default DatePicker
