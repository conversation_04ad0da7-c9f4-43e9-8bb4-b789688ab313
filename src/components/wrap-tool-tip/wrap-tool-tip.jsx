import { Tooltip, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import PropTypes from "prop-types"

const WrapToolTip = ({ children, delayDuration, toolTipContent, side = "bottom" }) => {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={delayDuration}>
        <TooltipTrigger asChild>
          <span>{children}</span>
        </TooltipTrigger>
        <TooltipContent side={side}>{toolTipContent}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

WrapToolTip.propTypes = {
  children: PropTypes.node,
  delayDuration: PropTypes.number,
  toolTipContent: PropTypes.string,
  side: PropTypes.string,
}

export default WrapToolTip
