import { CartesianGrid, <PERSON>, Line<PERSON>hart, XAxis } from "recharts"
import PropTypes from "prop-types"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const chartConfig = {
  linkedin: {
    label: "Linkedin",
    color: "hsl(var(--chart-1))",
  },
  monster: {
    label: "Monster",
    color: "hsl(var(--chart-2))",
  },
  indeed: {
    label: "Indeed",
    color: "hsl(var(--chart-3))",
  },
  dice: {
    label: "Dice",
    color: "hsl(var(--chart-4))",
  },
  website: {
    label: "Website",
    color: "hsl(var(--chart-5))",
  },
}

function ChartCard({ title, data }) {
  return (
    <Card className="h-[400px]">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>January - June 2024</CardDescription>
      </CardHeader>
      <CardContent className="h-[calc(100%-88px)]">
        <ChartContainer config={chartConfig} className="h-full w-full">
          <LineChart
            accessibilityLayer
            data={data}
            margin={{
              left: 12,
              right: 12,
              top: 12,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              // tickFormatter={(value) => value.slice(0, 3)}
            />
            <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
            <Line
              dataKey="Linkedin"
              type="monotone"
              stroke="var(--color-linkedin)"
              strokeWidth={2}
              dot={false}
            />
            <Line
              dataKey="Monster"
              type="monotone"
              stroke="var(--color-monster)"
              strokeWidth={2}
              dot={false}
            />
            <Line
              dataKey="Indeed"
              type="monotone"
              stroke="var(--color-indeed)"
              strokeWidth={2}
              dot={false}
            />
            <Line
              dataKey="Dice"
              type="monotone"
              stroke="var(--color-dice)"
              strokeWidth={2}
              dot={false}
            />
            <Line
              dataKey="Website"
              type="monotone"
              stroke="var(--color-website)"
              strokeWidth={2}
              dot={false}
            />
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}

ChartCard.propTypes = {
  title: PropTypes.string.isRequired,
  data: PropTypes.arrayOf(
    PropTypes.shape({
      month: PropTypes.string.isRequired,
      Linkedin: PropTypes.number,
      Monster: PropTypes.number,
      Indeed: PropTypes.number,
    })
  ).isRequired,
}

export default ChartCard
