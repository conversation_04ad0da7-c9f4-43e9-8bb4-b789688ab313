import { Input } from "@/components/ui/input"
import PropTypes from "prop-types"

function CustomeInput({ type = "text", placeholder = "Enter", className = "", label = "" }) {
  return (
    <div className="flex flex-col gap-2">
      <label htmlFor={label} className="font-inter text-sm font-semibold cursor-default">
        {label}
      </label>
      <Input type={type} placeholder={placeholder} className={className} />
    </div>
  )
}

CustomeInput.propTypes = {
  type: PropTypes.string,
  placeholder: PropTypes.string,
  className: PropTypes.string,
  label: PropTypes.string,
}

export default CustomeInput
