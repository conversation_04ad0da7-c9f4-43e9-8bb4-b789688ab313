import ModeToggle from "@/components/layout/header/theme-switch"
import { Separator } from "@/components/ui/separator"
import { SidebarTrigger } from "@/components/ui/sidebar"
import ct from "@constants/"
import { useEffect } from "react"
import { useSelector } from "react-redux"
import { useLocation, useNavigate } from "react-router-dom"
import NotificationPopup from "./notification-popup"
import UserNav from "./user-nav"

// import SheetMenu from "../sidebars/sheet-menu"
const Header = () => {
  useEffect(() => {
    const scrollY = sessionStorage.getItem("scrollPosition")
    if (scrollY) {
      setTimeout(() => {
        window.scrollTo({
          top: parseInt(scrollY, 10),
          behavior: "smooth",
        })
      }, 200)
    }
  }, [])
  const course = useSelector((st) => st[ct.store.COURSES]?.activeCourseDetails)
  const location = useLocation()
  const navigate = useNavigate()
  const { courseData, courseType } = location.state || {}
  const isLiveCourse = /^\/live-courses\/\d+(\/course-overview)?$/.test(location.pathname)
  const isBatchCourse = /^\/batch-course\/\d+(\/course-overview)?$/.test(location.pathname)
  const locationTrue = isLiveCourse || isBatchCourse
  console.log("locationTrue", courseData?.title, courseType);

  return (
    <div className="w-full">
      <header className="flex flex-wrap items-center justify-between bg-white p-4 dark:bg-[#020817] sticky top-0 z-50 w-full dark:shadow-secondary ">
        <div className="flex items-center gap-4">
          <SidebarTrigger className="w-5" />{" "}
          {locationTrue && (
            <>
              <Separator className="border h-4" orientation="vertical" />
              <div
                role="button"
                tabIndex={0}
                onClick={() => navigate(-1)}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") {
                    navigate(-1)
                  }
                }}
              >
                <span className="text-sm font-medium text-primary">
                  {courseType === "Nano courses"
                    ? courseData?.title
                    : isLiveCourse
                      ? course?.courseName
                      : course?.title}
                </span>
              </div>
            </>
          )}

        </div>

        {/* Use flex-wrap and responsive adjustments */}
        <div className="flex items-center justify-end gap-3 sm:gap-4 md:gap-5 flex-wrap">
          <NotificationPopup />
          <ModeToggle />
          <UserNav />
        </div>
      </header>
    </div>
  )
}

export default Header
