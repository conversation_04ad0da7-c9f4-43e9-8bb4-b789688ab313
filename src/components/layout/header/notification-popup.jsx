import { useCallback, useMemo, useState } from "react"
import { Bell, ChevronDown, Mail, MessageCircleHeart, User } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import ct from "@constants/"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useNavigate } from "react-router-dom"

const NotificationPopup = () => {
  const [menuOpen, setMenuOpen] = useState(false)
  const [notifications, setNotifications] = useState([])
  const navigate = useNavigate()
  // Dummy notifications data
  const dummyNotifications = [
    {
      id: 1,
      channel: "ws_add_to_jd",
      status: "in_progress",
      title: "New JD Added",
      message: "Sabari got new message ",
      date_time: new Date().toISOString(),
    },
    {
      id: 2,
      channel: "basic",
      title: "New Application",
      message: "You have a new application from <PERSON><PERSON>",
      date_time: new Date().toISOString(),
    },
    {
      id: 3,
      channel: "communication",
      title: "Message Received",
      message: "You have a new message from <PERSON><PERSON>",
      date_time: new Date().toISOString(),
    },
    {
      id: 4,
      channel: "basic",
      title: "New Message Notified",
      message: "You have a new notification from the system",
      date_time: new Date().toISOString(),
    },
  ]

  const getNotificationIcon = useCallback((type) => {
    const iconClass = "h-12 w-12 flex items-center justify-center"
    switch (type) {
      case "ws_add_to_jd":
        return (
          <div className={iconClass}>
            <Mail size={24} className="  text-amber-700 " />
          </div>
        )
      case "communication":
        return (
          <div className={iconClass}>
            <MessageCircleHeart className="  text-pink-500 " />
          </div>
        )
      default:
        return (
          <div className={iconClass}>
            <User className="  text-blue-700 " />
          </div>
        )
    }
  }, [])

  const renderNotificationContent = useCallback((notification) => {
    return (
      <div className="ml-3 flex-1">
        <p className="text-sm font-semibold dark:text-white">{notification?.title}</p>
        <p className="text-sm dark:text-gray-300 text-gray-600 mt-1">{notification?.message}</p>
        <p className="text-xs dark:text-gray-400 text-gray-500 mt-1">
          {new Date(notification?.date_time).toLocaleString()}
        </p>
      </div>
    )
  }, [])

  const renderNotifications = useCallback(() => {
    return notifications.map((notification) => (
      <li
        key={notification.id}
        className="px-4 py-3 border-b border-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
      >
        <div className="flex items-start">
          <div className="flex-shrink-0">{getNotificationIcon(notification.channel)}</div>
          {renderNotificationContent(notification)}
        </div>
      </li>
    ))
  }, [notifications, getNotificationIcon, renderNotificationContent])

  const handleMenuToggle = useCallback(
    (open) => {
      setMenuOpen(open)
      if (open && notifications.length === 0) {
        setNotifications(dummyNotifications)
      }
    },
    [notifications.length]
  )

  const handleClearAll = useCallback(() => {
    setNotifications([])
  }, [])

  const handleSeeMore = useMemo(
    () => () => {
      navigate(ct.route.NOTIFICATION)
      handleClearAll()
      setMenuOpen(false)
    },
    [navigate, handleClearAll]
  )
  return (
    <div className="relative">
      <DropdownMenu open={menuOpen} onOpenChange={handleMenuToggle}>
        <DropdownMenuTrigger asChild>
          <div className="relative">
            <Bell
              data-testid="notification-bell"
              className={`w-6 h-6 cursor-pointer transition-colors duration-300 ${
                menuOpen ? "text-blue-600" : "text-gray-300 hover:text-blue-600"
              }`}
              onClick={() => handleMenuToggle(!menuOpen)}
            />
            {notifications.length > 0 && (
              <div
                data-testid="notification-count"
                className="absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs font-bold animate-bounce"
              >
                {notifications.length}
              </div>
            )}
          </div>
        </DropdownMenuTrigger>

        {menuOpen && (
          <DropdownMenuContent
            data-testid="notification-content"
            className="w-[26rem] mt-4 py-2 px-6 me-12 bg-white shadow-lg rounded-lg notification-enter notification-enter-active"
          >
            <div className="flex justify-between items-center px-4 py-2">
              <span className="text-lg font-medium">Notifications</span>
              <Button
                variant="link"
                className="text-primary-600 text-primary"
                onClick={handleClearAll}
              >
                Clear All
              </Button>
            </div>
            <ScrollArea className="max-h-64" scrollHorizontally={false}>
              <ul>{renderNotifications()}</ul>
            </ScrollArea>
            <div className="text-end mb-7 pb-1">
              <Button variant="link" className="float-right text-primary" onClick={handleSeeMore}>
                See More <ChevronDown className="ml-1 w-4 h-4" />
              </Button>
            </div>
          </DropdownMenuContent>
        )}
      </DropdownMenu>
    </div>
  )
}

export default NotificationPopup
