import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON>,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { ChevronRight, Home, Info } from "lucide-react"
import React from "react"
import { useLocation, useNavigate } from "react-router-dom"

const Breadcrumbs = () => {
  const location = useLocation()
  const navigate = useNavigate()

  // Extract the path segments
  const pathSegments = location.pathname.split("/").filter((segment) => segment !== "")

  // Map routes to readable names
  const routeNameMap = {
    "live-courses": "Live Courses",
    "short-courses": "Short Courses",
    "my-courses": "My Courses",
    "course-overview": "Course Overview",
    "course-content": "Course Content",
    feedback: "Feedback",
    "quiz-view": "Quiz",
    jd: "Job Descriptions",
    profile: "Profile",
    resume: "Resume",
    "mock-interview": "Mock Interview",
    notification: "Notifications",
    "trainer-dashboard": "Trainer Dashboard",
    "student-dashboard": "Student Dashboard",
    "admin-dashboard": "Admin Dashboard",
    "vendor-dashboard": "Vendor Dashboard",
  }

  // Skip breadcrumbs on main routes
  if (
    pathSegments.length === 0 ||
    (pathSegments.length === 1 &&
      (pathSegments[0] === "dashboard" || pathSegments[0].includes("-dashboard")))
  ) {
    return null
  }

  // More intelligent condensing - always show home, first segment, and last two segments
  const shouldCondense = pathSegments.length > 3

  // Get segments for display
  const displaySegments = shouldCondense
    ? [pathSegments[0], ...pathSegments.slice(-2)]
    : pathSegments

  // Calculate the number of hidden segments
  const hiddenSegmentsCount = shouldCondense ? pathSegments.length - displaySegments.length : 0

  console.log(pathSegments, "breadcrumbs")
  return (
    <TooltipProvider>
      <nav aria-label="Breadcrumb Navigation" className="mb-4">
        <Breadcrumb className=" dark:bg-slate-800/90 mt-2 cursor-pointer mx-4 border-0  dark:border-gray-700 transition-all">
          <BreadcrumbList className="flex-wrap items-center">
            {/* Home Link */}
            <BreadcrumbItem>
              <BreadcrumbLink
                onClick={() => navigate("/admin-dashboard")}
                className="flex items-center text-gray-500 hover:text-primary transition-colors p-1 rounded-md hover:bg-gray-100 dark:hover:bg-slate-700"
                aria-label="Home"
              >
                <Home className="h-4 w-4" />
              </BreadcrumbLink>
            </BreadcrumbItem>

            {/* Segments */}
            {displaySegments.map((segment, index) => {
              // Build the path up to this segment
              const segmentIndex =
                shouldCondense && index > 0
                  ? pathSegments.length - (displaySegments.length - index) + 1
                  : index

              const path = `/${pathSegments.slice(0, segmentIndex + 1).join("/")}`

              // Determine if this is a course ID
              const isCourseId =
                segmentIndex > 0 &&
                (pathSegments[segmentIndex - 1] === "live-courses" ||
                  pathSegments[segmentIndex - 1] === "short-courses") &&
                !Number.isNaN(Number(segment))

              // Get course data from location state
              const courseData = location.state?.courseData

              // Determine display name
              const displayName =
                routeNameMap[segment] ||
                (isCourseId
                  ? courseData?.title || `Course ${segment}`
                  : segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, " "))
              console.log("pathSegments", pathSegments, segment)
              // If this is the first item after home and we're condensing, show the ellipsis
              if (shouldCondense && index === 0) {
                return (
                  <React.Fragment key={path}>
                    <BreadcrumbSeparator>
                      <ChevronRight className="h-4 w-4 text-muted-foreground" />
                    </BreadcrumbSeparator>
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        onClick={() => navigate(path)}
                        className="text-muted-foreground hover:text-primary transition-colors p-1 rounded-md hover:bg-gray-100 dark:hover:bg-slate-700"
                      >
                        {displayName}
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator>
                      <ChevronRight className="h-4 w-4 text-muted-foreground" />
                    </BreadcrumbSeparator>
                    <BreadcrumbItem>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="flex items-center text-muted-foreground px-1 cursor-help">
                            <Info className="h-4 w-4 mr-1" />
                            {hiddenSegmentsCount} more
                          </span>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>
                            {pathSegments
                              .slice(1, pathSegments.length - 2)
                              .map((s) => routeNameMap[s] || s.replace(/-/g, " "))
                              .join(" > ")}
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </BreadcrumbItem>
                  </React.Fragment>
                )
              }

              // If not the first condensed item, render normally
              return (
                <React.Fragment key={path}>
                  <BreadcrumbSeparator>
                    <ChevronRight className="h-4 w-4 text-muted-foreground" />
                  </BreadcrumbSeparator>
                  <BreadcrumbItem>
                    {index === displaySegments.length - 1 ? (
                      <span className="font-medium text-sm text-primary px-1">{displayName}</span>
                    ) : (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <BreadcrumbLink
                            onClick={() => navigate(path)}
                            className="text-muted-foreground font-medium text-sm  hover:text-primary transition-colors max-w-[150px] truncate block p-1 rounded-md hover:bg-gray-100 dark:hover:bg-slate-700"
                          >
                            {displayName}
                          </BreadcrumbLink>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{displayName}</p>
                        </TooltipContent>
                      </Tooltip>
                    )}
                  </BreadcrumbItem>
                </React.Fragment>
              )
            })}
          </BreadcrumbList>
        </Breadcrumb>
      </nav>
    </TooltipProvider>
  )
}

export default Breadcrumbs
