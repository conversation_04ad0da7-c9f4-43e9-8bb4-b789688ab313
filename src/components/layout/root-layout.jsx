// import { useSelector } from 'react-redux'
//
// import ct from '@constants/'
// import { ThemeProvider } from '@context/theme-provider'
// import {
//   getLayoutType,
//   handleLoginRedirect,
// } from '@/lib/layout/root-layout-helper'
//
// import { Toaster } from '../ui/toaster'
//
// import LayoutRenderer from './layout-type-render'
// import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
//
// export default function RootLayout() {
//   // const matches = useMatches()
//   // const navigate = useNavigate()
//   // const state = useRouterState()
//   const store = useSelector((st) => st[ct.store.USER_STORE])
//
//   console.log('matches', store)
//
//   // handle layout type
//   // const layoutType = getLayoutType(matches)
//   //
//   // useEffect(() => {
//   //   const { pathname } = state.location
//   //   handleLoginRedirect(matches, pathname, navigate)
//   // }, [matches, state, store, navigate])
//
//   return (
//     <>
//       <ThemeProvider>
//         <Toaster />
//         <LayoutRenderer layoutType={'default'} />
//       </ThemeProvider>
//       <ReactQueryDevtools />
//     </>
//   )
// }
