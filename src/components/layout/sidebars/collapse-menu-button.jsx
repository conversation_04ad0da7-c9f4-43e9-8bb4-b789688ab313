import { Link } from "react-router-dom"
import { useState } from "react"
import { ChevronDown, Dot } from "lucide-react"
import PropTypes from "prop-types"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DropdownMenuArrow } from "@radix-ui/react-dropdown-menu"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider } from "@/components/ui/tooltip"
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"

export default function CollapseMenuButton({ icon: Icon, label, active, submenus, isOpen }) {
  const isSubmenuActive = submenus.some((submenu) => submenu.active)
  const [isCollapsed, setIsCollapsed] = useState(isSubmenuActive)

  const toogleClassname = isOpen ? "translate-x-0 opacity-100" : "-translate-x-96 opacity-0"

  return isOpen ? (
    <Collapsible open={isCollapsed} onOpenChange={setIsCollapsed} className="w-full">
      <CollapsibleTrigger className="[&[data-state=open]>div>div>svg]:rotate-180 mb-1" asChild>
        <Button variant={active ? "secondary" : "ghost"} className="w-full justify-start h-10">
          <div className="w-full items-center flex justify-between">
            <div className="flex items-center">
              <span className="mr-4">
                <Icon size={18} />
              </span>
              <p className={cn("max-w-[150px] truncate", toogleClassname)}>{label}</p>
            </div>
            <div className={cn("whitespace-nowrap", toogleClassname)}>
              <ChevronDown size={18} className="transition-transform duration-200" />
            </div>
          </div>
        </Button>
      </CollapsibleTrigger>
      <CollapsibleContent className="overflow-hidden duration-200 transition-[width] ease-in-ou data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down">
        {submenus.map(({ href, slabel, sActive }) => (
          <Button
            key={label}
            variant={sActive ? "secondary" : "ghost"}
            className="w-full justify-start h-10 mb-1 text-muted-foreground"
            asChild
          >
            <Link to={href}>
              <span className="ml-5 mr-2">
                <Dot size={18} />
              </span>
              <p className={cn(" max-w-[170px] truncate", toogleClassname)}>{slabel}</p>
            </Link>
          </Button>
        ))}
      </CollapsibleContent>
    </Collapsible>
  ) : (
    <DropdownMenu>
      <TooltipProvider disableHoverableContent>
        <Tooltip delayDuration={100}>
          <TooltipTrigger asChild>
            <DropdownMenuTrigger asChild>
              <Button
                variant={active ? "secondary" : "ghost"}
                className="w-full justify-start h-10 mb-1"
              >
                <div className="w-full items-center flex justify-between">
                  <div className="flex items-center">
                    <span className={cn(isOpen === false ? "" : "mr-4")}>
                      <Icon size={18} />
                    </span>
                    <p
                      className={cn(
                        "max-w-[200px] truncate",
                        isOpen === false ? "opacity-0" : "opacity-100"
                      )}
                    >
                      {label}
                    </p>
                  </div>
                </div>
              </Button>
            </DropdownMenuTrigger>
          </TooltipTrigger>
          <TooltipContent side="right" align="start" alignOffset={2}>
            {label}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <DropdownMenuContent side="right" sideOffset={25} align="start">
        <DropdownMenuLabel className="max-w-[190px] truncate">{label}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {submenus.map(({ href, sLabel2 }) => (
          <DropdownMenuItem key="dmi" asChild>
            <Link className="cursor-pointer" to={href}>
              <p className="max-w-[180px] truncate">{sLabel2}</p>
            </Link>
          </DropdownMenuItem>
        ))}
        <DropdownMenuArrow className="fill-border" />
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

CollapseMenuButton.propTypes = {
  isOpen: PropTypes.bool,
  icon: PropTypes.elementType,
  label: PropTypes.string,
  active: PropTypes.bool,
  submenus: PropTypes.arrayOf(
    PropTypes.shape({
      href: PropTypes.string,
      label: PropTypes.string,
      active: PropTypes.bool,
    })
  ),
}
