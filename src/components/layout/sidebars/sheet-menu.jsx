import { ArrowLeftFromLine, MenuIcon, PanelsTopLeft } from "lucide-react"
import { Link } from "react-router-dom"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>et<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"

import ct from "@constants/"
import { useDispatch, useSelector } from "react-redux"
import { toggleSidebar } from "@/services/store/slices/theme.slice"
import { useEffect, useState } from "react"
import Menu from "./menu"

export default function SheetMenu() {
  const store = useSelector((st) => st[ct.store.THEME_STORE])
  const dispatch = useDispatch()
  const [isDialogOpen, setIsDialogOpen] = useState(true)
  const [isOpen, setsOpen] = useState(false)

  useEffect(() => {
    dispatch(toggleSidebar(""))
  }, [dispatch])

  function prevetEvents(event) {
    event.preventDefault()
    event.stopPropagation()
    setIsDialogOpen(true)
  }

  const handleHamburgerOpen = (event) => {
    prevetEvents(event)
    dispatch(toggleSidebar("open"))
    setsOpen(true)
  }

  const handleHamburgerClose = (event) => {
    prevetEvents(event)
    dispatch(toggleSidebar(""))
    setsOpen(false)
  }

  const handleInteractOutside = (event) => {
    event.preventDefault()
  }

  return (
    <Sheet open={isDialogOpen} data-state="open">
      <SheetTrigger asChild>
        <Button className="h-8" variant="outline" size="icon">
          {store?.isSidebarOpn ? (
            <ArrowLeftFromLine size={20} onClick={handleHamburgerClose} />
          ) : (
            <MenuIcon size={20} onClick={handleHamburgerOpen} />
          )}
        </Button>
      </SheetTrigger>
      <SheetContent
        onInteractOutside={handleInteractOutside}
        className={`${store?.isSidebarOpn ? "w-64" : "w-24"} transition-[width] duration-300 px-3 h-full flex flex-col`}
        side="left"
      >
        <SheetHeader>
          <Button className="flex justify-center items-center pb-2 pt-1" variant="link" asChild>
            <Link to="/dashboard" className="flex items-center gap-2">
              <PanelsTopLeft className="w-6 h-6 mr-1" />
              <h1 className="font-bold text-lg">Hire10x</h1>
            </Link>
          </Button>
        </SheetHeader>
        <Menu store={store} isOpen={isOpen} />
      </SheetContent>
    </Sheet>
  )
}
