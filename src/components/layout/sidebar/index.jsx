import { useEffect, useMemo } from "react"
import ct from "@constants/"
import {
  Sidebar,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarMenu,
  useSidebar,
  SidebarContent,
} from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import PropTypes from "prop-types"
import { useSelector, shallowEqual } from "react-redux"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useNavigate } from "react-router-dom"
import { setNavigate } from "@/utils/helper"
import { USER_ROLES } from "@/utils/constants"
import { SidebarUser } from "./sidebar-user"
// import SidebarStudent from "./sidebar-student"
// import SidebarTrainer from "./sidebar-trainer"
// import SidebarAdmin from "./sidebar-admin"
// import SidebarVendor from "./sidebar-vendor"
// import SidebarMarketing from "./sidebar-marketing"
import {
  AdminMenus,
  MarketingMenus,
  StudentMenus,
  TrainerMenus,
  VendorMenus,
} from "./sidebar-menu-by-role"

export default function AppSidebar(props) {
  const { open } = props
  const { state } = useSidebar()
  const userRole = useSelector((st) => st[ct.store.USER_STORE]?.userRole, shallowEqual)

  // Set the global navigation function
  const navigate = useNavigate()

  useEffect(() => {
    setNavigate(navigate)
  }, [navigate])

  const sidebarContent = useMemo(() => {
    switch (userRole) {
      case USER_ROLES.STUDENT:
        return <StudentMenus state={state} userRole={userRole} />
      case USER_ROLES.TRAINER:
        return <TrainerMenus state={state} userRole={userRole} />
      case USER_ROLES.ADMIN:
        return <AdminMenus state={state} userRole={userRole} />
      case USER_ROLES.VENDOR:
        return <VendorMenus state={state} userRole={userRole} />
      case USER_ROLES.MARKETER:
        return <MarketingMenus state={state} userRole={userRole} />
      default:
        return null
    }
  }, [userRole, open])

  return (
    <Sidebar
      collapsible="icon"
      {...props}
      className={`transition-all bg-white duration-300 mr-2 ${open ? "w-32" : "w-64"}`}
    >
      <SidebarHeader>
        <SidebarMenu className="flex-row items-center">
          <Avatar className={`h-10 w-10 rounded-sm `}>
            <AvatarImage src="" alt="" />
            <AvatarFallback className="rounded-sm h-8 w-8 font-medium text-sm">T</AvatarFallback>
          </Avatar>
          {state === "expanded" && <span className="text-sm font-medium">Trainings 10x</span>}
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>{sidebarContent}</SidebarContent>

      <SidebarFooter>
        <Separator className="my-2 border-t-2 border-slate-300 bg-slate-300" />
        <SidebarUser isExpanded={state} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}

AppSidebar.propTypes = {
  open: PropTypes.bool.isRequired,
}
