import {
  SidebarContent,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar"
import ct from "@constants/"
import {
  Home,
  LayoutDashboard,
  GraduationCap,
  BookText,
  Users,
  Video,
  Wallet,
  DollarSign,
  MessageSquare,
  HeartHandshake,
} from "lucide-react"

function SidebarTrainer() {
  return (
    <SidebarContent>
      <SidebarGroup>
        <SidebarGroupLabel>
          <Home className="h-4 w-4 mr-2" />
          Home
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem key="Dashboard">
              <SidebarMenuButton asChild>
                <a href="/trainer-dashboard">
                  <LayoutDashboard className="h-4 w-4 mr-2" />
                  <span>Dashboard</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <SidebarGroup>
        <SidebarGroupLabel>
          <GraduationCap className="h-4 w-4 mr-2" />
          Courses
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem key="ManageCourses">
              <SidebarMenuButton asChild>
                <a href="/course/trainer">
                  <BookText className="h-4 w-4 mr-2" />
                  <span>Manage Courses</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem key="ManageStudent">
              <SidebarMenuButton asChild>
                <a href="/student-tracking">
                  <Users className="h-4 w-4 mr-2" />
                  <span>Student Performance</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <SidebarGroup>
        <SidebarGroupLabel>
          <Video className="h-4 w-4 mr-2" />
          Interview
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem key="Interview">
              <SidebarMenuButton asChild>
                <a href={ct.route.MOCK_INTERVIEW}>
                  <Video className="h-4 w-4 mr-2" />
                  <span>Interview</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <SidebarGroup>
        <SidebarGroupLabel>
          <Wallet className="h-4 w-4 mr-2" />
          Payment
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem key="Payment">
              <SidebarMenuButton asChild>
                <a href={ct.route.TRAINER_PAYMENT}>
                  <Wallet className="h-4 w-4 mr-2" />
                  <span>Payment</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem key="Earning">
              <SidebarMenuButton asChild>
                <a href={ct.route.TRAINER_EARNING}>
                  <DollarSign className="h-4 w-4 mr-2" />
                  <span>Earning</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <SidebarGroup>
        <SidebarGroupLabel>
          <HeartHandshake className="h-4 w-4 mr-2" />
          Support
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem key="Community">
              <SidebarMenuButton asChild>
                <a href="https://www.google.com/" target="_blank" rel="noreferrer">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  <span>Community</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    </SidebarContent>
  )
}

export default SidebarTrainer
