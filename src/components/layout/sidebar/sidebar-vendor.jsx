import {
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import {
  BookMarked,
  HeartHandshake,
  Home,
  LayoutDashboard,
  LineChart,
  MessageSquare,
  School,
  // Bar<PERSON>hart,
  Users,
  Wallet,
} from "lucide-react"
import ct from "@constants/"

function SidebarVendor() {
  return (
    <SidebarContent>
      <SidebarGroup>
        <SidebarGroupLabel>
          <Home className="h-4 w-4 mr-2" />
          Home
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem key="Dashboard">
              <SidebarMenuButton asChild>
                <a href="/vendor-dashboard">
                  <LayoutDashboard className="h-4 w-4 mr-2" />
                  <span>Dashboard</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <SidebarGroup>
        <SidebarGroupLabel>
          <School className="h-4 w-4 mr-2" />
          Course
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem key="/live-courses">
              <SidebarMenuButton asChild>
                <a href="/live-courses">
                  <School className="h-4 w-4 mr-2" />
                  <span>Live Courses</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem key="ShortCourses">
              <SidebarMenuButton asChild>
                <a href="/ShortCourses">
                  <BookMarked className="h-4 w-4 mr-2" />
                  <span>Short Courses</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <SidebarGroup>
        <SidebarGroupLabel>
          <LineChart className="h-4 w-4 mr-2" />
          Performance Tracking
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem key="StudentTracking">
              <SidebarMenuButton asChild>
                <a href="/student-tracking">
                  <Users className="h-4 w-4 mr-2" />
                  <span>Student Tracking</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
            {/* <SidebarMenuItem key="MarketingTracking">
              <SidebarMenuButton asChild>
                <a href="/MarketingTracking">
                  <BarChart className="h-4 w-4 mr-2" />
                  <span>Marketing Tracking</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem> */}
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <SidebarGroup>
        <SidebarGroupLabel>
          <Users className="h-4 w-4 mr-2" />
          Management
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem key="Students">
              <SidebarMenuButton asChild>
                <a href="/user-management">
                  <Users className="h-4 w-4 mr-2" />
                  <span>Students</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem key="Payment">
              <SidebarMenuButton asChild>
                <a href={ct.route.PAYMENTS}>
                  <Wallet className="h-4 w-4 mr-2" />
                  <span>Payment</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <SidebarGroup>
        <SidebarGroupLabel>
          <HeartHandshake className="h-4 w-4 mr-2" />
          Support
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem key="Community">
              <SidebarMenuButton asChild>
                <a href="/Community">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  <span>Community</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    </SidebarContent>
  )
}

export default SidebarVendor
