import {
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import {
  Briefcase,
  FileText,
  HandshakeIcon,
  HeartHandshake,
  Home,
  LayoutDashboard,
  MessageSquare,
  Users,
} from "lucide-react"

function SidebarMarketing() {
  return (
    <SidebarContent>
      <SidebarGroup>
        <SidebarGroupLabel>
          <Home className="h-4 w-4 mr-2" />
          Home
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <a href="/marketing-dashboard">
                  <LayoutDashboard className="h-4 w-4 mr-2" />
                  <span>Dashboard</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <SidebarGroup>
        <SidebarGroupLabel>
          <Users className="h-4 w-4 mr-2" />
          Performance Tracking
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <a href="/student-tracking">
                  <Users className="h-4 w-4 mr-2" />
                  <span>Student Tracking</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <SidebarGroup>
        <SidebarGroupLabel>
          <Briefcase className="h-4 w-4 mr-2" />
          Marketing
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <a href="https://ai-jobs-9b4c6.web.app/login">
                  <Briefcase className="h-4 w-4 mr-2" />
                  <span>Available JD</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <a href="/resume">
                  <FileText className="h-4 w-4 mr-2" />
                  <span>Resume</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <a href="/applications">
                  <FileText className="h-4 w-4 mr-2" />
                  <span>Job Application</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <a href="/relationship">
                  <HandshakeIcon className="h-4 w-4 mr-2" />
                  <span>Leads</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <SidebarGroup>
        <SidebarGroupLabel>
          <HeartHandshake className="h-4 w-4 mr-2" />
          Community
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <a href="/community">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  <span>Community</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    </SidebarContent>
  )
}

export default SidebarMarketing
