import Navigation from "@/components/custom/nav-link"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import {
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/ui/sidebar"
import ct from "@constants/"
import {
  BookMarked,
  Briefcase,
  ChartLine,
  ChevronRight,
  Code2,
  DollarSign,
  HeartHandshake,
  Home,
  LayoutDashboard,
  School,
  UserCircle,
  UserCog,
  UserRound,
  UsersRound,
  Wallet,
} from "lucide-react"

function SidebarAdmin() {
  const { state } = useSidebar()
  return (
    <SidebarContent>
      <SidebarGroup>
        <SidebarGroupLabel className="text-[hsl(var(--primary))] px-1 text-sm">
          <Home className="h-4 w-4 mr-2" />
          Home
        </SidebarGroupLabel>

        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem key="Dashboard">
              <Navigation
                to="/admin-dashboard"
                content="Dashboard"
                icon={<LayoutDashboard className="h-4 w-4" />}
                isExpanded={state}
              />
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <SidebarGroup>
        <SidebarGroupLabel className="text-[hsl(var(--primary))] px-1 text-sm">
          <School className="h-4 w-4" />
          Course
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <Collapsible defaultOpen className="group/collapsible">
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton>
                    <UsersRound className="h-4 w-4" />
                    <span className="text-sm font-medium">Manage Courses</span>
                    <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    <SidebarMenuItem size="sm" key="LiveCourse">
                      <Navigation
                        to="/live-courses-list"
                        content="Live Course"
                        icon={<School className="h-4 w-4" />}
                        isExpanded={state}
                      />
                    </SidebarMenuItem>
                    <SidebarMenuItem key="ShortCourse">
                      <SidebarMenuButton asChild>
                        <Navigation
                          to="/short-courses-list"
                          content="Short Course"
                          icon={<BookMarked className="h-4 w-4" />}
                          isExpanded={state}
                        />
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>

            <Collapsible defaultOpen className="group/collapsible">
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton>
                    <UsersRound className="h-4 w-4" />

                    <span className="text-sm font-medium">Manage Batch</span>
                    <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton asChild>
                        <Navigation
                          to={ct.route.MANAGE_BATCH}
                          content="Courses"
                          icon={<UsersRound className="h-4 w-4" />}
                          isExpanded={state}
                        />
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton asChild>
                        <Navigation
                          to="/mock-interview"
                          content="Mock Interview"
                          icon={<UserCog className="h-4 w-4" />}
                          isExpanded={state}
                        />
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>

            <SidebarMenuItem key="ShortCourse">
              <Navigation
                to="/student-tracking"
                content="Student Performance"
                icon={<ChartLine size={18} className="" />}
                isExpanded={state}
              />
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <SidebarGroup>
        <SidebarGroupLabel className="text-[hsl(var(--primary))] px-1 text-sm">
          <UserCircle className="h-4 w-4" />
          Management
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <a href="https://ai-jobs-9b4c6.web.app/login">
                  <Briefcase className="h-4 w-4 mr-2" />
                  <span>Available JD</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem key="Dashboard">
              <SidebarMenuButton asChild>
                <Navigation
                  to="/user-management"
                  content="User Management"
                  icon={<UserRound className="h-4 w-4" />}
                  isExpanded={state}
                />
              </SidebarMenuButton>
            </SidebarMenuItem>
            {/* <Collapsible defaultOpen className="group/collapsible">
              <SidebarMenuItem>
                <SidebarMenuButton>
                  <a href="/manage/user">
                    <UsersRound className="h-4 w-4" />
                      isExpanded={state}
                    <span>User Management</span>
                  </a>

                  <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                </SidebarMenuButton>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton>
                    <UsersRound className="h-4 w-4" />
                      isExpanded={state}
                    <span>User Management</span>
                    <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton asChild>
                        <a href="/admin/users/student">
                          <UsersRound className="h-4 w-4" />
                            isExpanded={state}
                          <span>Student</span>
                        </a>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton asChild>
                        <a href="/admin/users/trainer">
                          <UserCog className="h-4 w-4" />
                            isExpanded={state}
                          <span>Trainer</span>
                        </a>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton asChild>
                        <a href="/admin/users/vendor">
                          <Building2 className="h-4 w-4" />
                            isExpanded={state}
                          <span>Vendor</span>
                        </a>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton asChild>
                        <a href="/admin/users/marketing">
                          <Megaphone className="h-4 w-4" />
                            isExpanded={state}
                          <span>Marketing</span>
                        </a>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton asChild>
                        <a href="/admin/users/admin">
                          <UserCircle className="h-4 w-4" />
                            isExpanded={state}
                          <span>Admin</span>
                        </a>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible> */}
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
      <SidebarGroup>
        <SidebarGroupLabel className="text-[hsl(var(--primary))] px-1 text-sm">
          <DollarSign className="h-4 w-4" />
          Payment
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem key="StudentPayment">
              <SidebarMenuButton asChild>
                <Navigation
                  to={ct.route.PAYMENTS}
                  content="Student Payment"
                  icon={<DollarSign className="h-4 w-4" />}
                  isExpanded={state}
                />
              </SidebarMenuButton>
            </SidebarMenuItem>
            <Collapsible defaultOpen className="group/collapsible">
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton>
                    <Wallet className="h-4 w-4" />

                    <span className="text-sm font-medium">Trainer Payment</span>
                    <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    <SidebarMenuItem key="LiveCourse">
                      <Navigation
                        to={ct.route.TRAINER_PAYMENT}
                        content="Trainer Payment"
                        icon={<DollarSign className="h-4 w-4" />}
                        isExpanded={state}
                      />
                    </SidebarMenuItem>
                    <SidebarMenuItem key="ShortCourse">
                      <Navigation
                        to={ct.route.TRAINER_EARNING}
                        content="Trainer Earning"
                        icon={<Wallet className="h-4 w-4" />}
                        isExpanded={state}
                      />
                    </SidebarMenuItem>
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <SidebarGroup>
        <SidebarGroupLabel className="text-[hsl(var(--primary))] px-1 text-sm">
          <HeartHandshake className="h-4 w-4" />
          Miscellaneous
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem key="LiveCoding">
              <SidebarMenuButton asChild>
                {/* <a href="https://coding.10xscale.ai" target="_blank" rel="noopener noreferrer">
                  <Code2 className="h-4 w-4" />
                    isExpanded={state}
                  <span>Live Coding</span>
                </a> */}

                <Navigation
                  to="https://coding.10xscale.ai"
                  content="Live Coding"
                  icon={<Code2 className="h-4 w-4" />}
                  isExpanded={state}
                />
              </SidebarMenuButton>
            </SidebarMenuItem>
            {/* <SidebarMenuItem key="Community">
              <Navigation
                to="/admin/community"
                content="Community"
                icon={<MessageSquare className="h-4 w-4" />}
                isExpanded={state}
              />
            </SidebarMenuItem> */}
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    </SidebarContent>
  )
}

export default SidebarAdmin
