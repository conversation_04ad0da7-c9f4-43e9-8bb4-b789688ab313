import { SidebarContent, useSidebar } from "@/components/ui/sidebar"
import PropTypes from "prop-types"
import {
  Courses,
  Dashboard,
  Interview,
  ManageMents,
  Marketing,
  Miscellaneous,
  Payments,
  Practice,
  StudentPerformance,
  TrainerCourses,
} from "./sidebar-menus"

const userRoleProps = {
  userRole: PropTypes.string,
}

export const AdminMenus = ({ userRole }) => {
  const { state } = useSidebar()
  return (
    <SidebarContent>
      <Dashboard isExpanded={state} userRole={userRole} />
      <Courses isExpanded={state} userRole={userRole} />
      <Interview isExpanded={state} userRole={userRole} />
      <ManageMents isExpanded={state} userRole={userRole} />
      <Payments isExpanded={state} userRole={userRole} />
      <Miscellaneous isExpanded={state} userRole={userRole} />
    </SidebarContent>
  )
}

AdminMenus.propTypes = userRoleProps

export const VendorMenus = ({ userRole }) => {
  const { state } = useSidebar()
  return (
    <SidebarContent>
      <Dashboard isExpanded={state} userRole={userRole} />
      <Courses isExpanded={state} userRole={userRole} />
      <ManageMents isExpanded={state} userRole={userRole} />
      <StudentPerformance isExpanded={state} userRole={userRole} />
      <Payments isExpanded={state} userRole={userRole} />
      <Miscellaneous isExpanded={state} userRole={userRole} />
    </SidebarContent>
  )
}

VendorMenus.propTypes = userRoleProps

export const StudentMenus = ({ userRole }) => {
  const { state } = useSidebar()
  return (
    <SidebarContent>
      <Dashboard isExpanded={state} userRole={userRole} />
      <Courses isExpanded={state} userRole={userRole} />
      <Marketing isExpanded={state} userRole={userRole} />
      <Interview isExpanded={state} userRole={userRole} />
      <Practice isExpanded={state} userRole={userRole} />
      <Payments isExpanded={state} userRole={userRole} />
      <Miscellaneous isExpanded={state} userRole={userRole} />
    </SidebarContent>
  )
}

StudentMenus.propTypes = userRoleProps

export const TrainerMenus = ({ userRole }) => {
  const { state } = useSidebar()
  return (
    <SidebarContent>
      <Dashboard isExpanded={state} userRole={userRole} />
      <TrainerCourses isExpanded={state} userRole={userRole} />
      <Interview isExpanded={state} userRole={userRole} />
      <StudentPerformance isExpanded={state} userRole={userRole} />
      <Payments isExpanded={state} userRole={userRole} />
      <Miscellaneous isExpanded={state} userRole={userRole} />
    </SidebarContent>
  )
}

TrainerMenus.propTypes = userRoleProps

export const MarketingMenus = ({ userRole }) => {
  const { state } = useSidebar()
  return (
    <SidebarContent userRole={userRole}>
      <Dashboard isExpanded={state} userRole={userRole} />
      <Marketing isExpanded={state} userRole={userRole} />
      <StudentPerformance isExpanded={state} userRole={userRole} />
      <Miscellaneous isExpanded={state} userRole={userRole} />
    </SidebarContent>
  )
}

MarketingMenus.propTypes = userRoleProps
