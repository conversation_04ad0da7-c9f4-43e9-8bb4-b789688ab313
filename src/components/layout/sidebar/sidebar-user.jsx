import { SidebarMenu, SidebarMenuButton, SidebarMenuItem } from "@/components/ui/sidebar"
import PropTypes from "prop-types"
import { memo } from "react"
import { UserAvatarCell } from "@/components/custom/cutsom-table/table-cells"
import { useSelector } from "react-redux"
import ct from "@constants/"

export const SidebarUser = memo(function SidebarUser({ isExpanded }) {
  const { email, name, image } = useSelector((st) => st[ct.store.USER_STORE])
  return (
    <SidebarMenu>
      <SidebarMenuItem>
        {/* <DropdownMenu className="rounded-[1rem]">
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <UserAvatarCell value={userRole} isExpanded={isExpanded === "expanded"} />
        
              <ChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-[.4rem]"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >

            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => handleRoleSwitch("student")}>
              <PiStudentBold size={18} className="mr-2 cursor-pointer" />
              Switch to Student
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleRoleSwitch("trainer")}>
              <FaChalkboardTeacher size={18} className="mr-2 cursor-pointer" />
              Switch to trainer
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleRoleSwitch("admin")}>
              <RiAdminLine size={18} className="mr-2 cursor-pointer" />
              Switch to admin
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleRoleSwitch("vendor")}>
              <FiUsers size={18} className="mr-2 cursor-pointer" />
              Switch to vendor
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleRoleSwitch("marketing")}>
              <GrUserSettings size={18} className="mr-2 cursor-pointer" />
              Switch to marketing
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleLogout}>
              <LogOut size={18} className="mr-2 cursor-pointer" />
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu> */}

        <SidebarMenuButton
          size="lg"
          className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
        >
          <UserAvatarCell
            name={name}
            email={email}
            imageUrl={image}
            isExpanded={isExpanded === "expanded"}
          />
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>
  )
})

SidebarUser.propTypes = {
  isExpanded: PropTypes.bool.isRequired,
  user: PropTypes.shape({
    email: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
  }).isRequired, // Mark user as required
}
