import {
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import ct from "@constants/"
import {
  BarChart,
  BookMarked,
  BookOpen,
  BookText,
  Briefcase,
  Code,
  Code2,
  DollarSign,
  FileText,
  GraduationCap,
  HeartHandshake,
  Home,
  LayoutDashboard,
  MessageSquare,
  School,
  Users,
  Video,
} from "lucide-react"
import { NavLink } from "react-router-dom"

function SidebarStudent() {
  return (
    <SidebarContent>
      <SidebarGroup>
        <SidebarGroupLabel>
          <Home className="h-4 w-4 mr-2" />
          Home
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem key="Dashboard">
              <SidebarMenuButton asChild>
                <NavLink
                  to="/student-dashboard"
                  className={({ isActive }) =>
                    `flex items-center p-1 navLink rounded ${isActive ? "navLink" : "text-gray-700"}`
                  }
                >
                  <LayoutDashboard className="h-4 w-4 mr-2" />
                  <span className="text-xs lg:text-sm  font-medium">Dashboard</span>
                </NavLink>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
      <SidebarGroup>
        <SidebarGroupLabel>
          <GraduationCap className="h-4 w-4 mr-2" />
          Courses
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem key="MyCourses">
              <SidebarMenuButton asChild>
                <NavLink
                  to={ct.route.MY_COURSES}
                  className={({ isActive }) =>
                    `flex items-center p-1 rounded ${isActive ? "navLink" : "text-gray-700"}`
                  }
                >
                  <BookText className="h-4 w-4 mr-2" />
                  <span className="text-xs lg:text-sm  font-medium">My Courses</span>
                </NavLink>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem key="LiveCourses">
              <SidebarMenuButton asChild>
                <a href={ct.route.LIVE_COURSES_LIST}>
                  <School className="h-4 w-4 mr-2" />
                  <span>Live Courses</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem key="ShortCourses">
              <SidebarMenuButton asChild>
                <a href={ct.route.SHORT_COURSES_LIST}>
                  <BookMarked className="h-4 w-4 mr-2" />
                  <span>Short Courses</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
      <SidebarGroup>
        <SidebarGroupLabel>
          <Code className="h-4 w-4 mr-2" />
          Practice
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem key="LiveCoding">
              <SidebarMenuButton asChild>
                <a href="https:///coding.hire10x.ai" target="_blank" rel="noreferrer">
                  <Code2 className="h-4 w-4 mr-2" />
                  <span>Live Coding</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem key="Notebook">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <SidebarMenuButton disabled>
                      <BookOpen className="h-4 w-4 mr-2" />
                      <span>Notebook</span>
                    </SidebarMenuButton>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Coming soon!</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
      <SidebarGroup>
        <SidebarGroupLabel>
          <Briefcase className="h-4 w-4 mr-2" />
          Career
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem key="Resume">
              <SidebarMenuButton asChild>
                <a href="https://resume-builder-475ff.web.app/" target="_blank" rel="noreferrer">
                  <FileText className="h-4 w-4 mr-2" />
                  <span>Resume</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem key="AvailableJD">
              <SidebarMenuButton asChild>
                <a href="https://ai-jobs-9b4c6.web.app/login" target="_blank" rel="noreferrer">
                  <Briefcase className="h-4 w-4 mr-2" />
                  <span>Available JD</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem key="MarketingDashboard">
              <SidebarMenuButton asChild>
                <a href="/marketing-dashboard">
                  <BarChart className="h-4 w-4 mr-2" />
                  <span>Marketing Dashboard</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
      <SidebarGroup>
        <SidebarGroupLabel>
          <Video className="h-4 w-4 mr-2" />
          Interview
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem key="AIInterview">
              <SidebarMenuButton asChild>
                <a href={ct.route.LIVE_COURSES}>
                  <Video className="h-4 w-4 mr-2" />
                  <span>AI Interview</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem key="MockInterview">
              <SidebarMenuButton asChild>
                <a href={ct.route.MOCK_INTERVIEW}>
                  <Users className="h-4 w-4 mr-2" />
                  <span>Mock Interview</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
      {/* <SidebarGroup>
        <SidebarGroupLabel>
          <Wallet className="h-4 w-4 mr-2" />
          Payment
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem key="AIInterview">
              <SidebarMenuButton asChild>
                <a href="/payment/user">
                  <DollarSign className="h-4 w-4 mr-2" />
                  <span>Payment</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup> */}
      <SidebarGroup>
        <SidebarGroupLabel>
          <HeartHandshake className="h-4 w-4 mr-2" />
          Miscellaneous
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem key="AIInterview">
              <SidebarMenuButton asChild>
                <a href={ct.route.PAYMENTS}>
                  <DollarSign className="h-4 w-4 mr-2" />
                  <span>Payment</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem key="Community">
              <SidebarMenuButton asChild>
                <a href={ct.route.ROOT}>
                  <MessageSquare className="h-4 w-4 mr-2" />
                  <span>Community</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    </SidebarContent>
  )
}

export default SidebarStudent
