import Navigation from "@/components/custom/nav-link"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { USER_ROLES } from "@/utils/constants"
import { sidebarGroupLablesPropsTypes, sidebarMenusPropsTypes } from "@/utils/props-types"
import ct from "@constants/"
import {
  AppWindow,
  BarChart,
  BookMarked,
  BookOpen,
  BookText,
  Briefcase,
  ChartLine,
  ChevronRight,
  Code,
  Code2,
  ContactRound,
  DollarSign,
  FileText,
  FileUser,
  FolderOpenDot,
  HeartHandshake,
  Home,
  Layers,
  Layers2,
  LayoutDashboard,
  MessageSquare,
  Newspaper,
  School,
  UserCircle,
  UserRound,
  UserRoundCog,
  UsersRound,
  Video,
  Wallet,
} from "lucide-react"

export const GroupLabel = ({ content, icon }) => {
  return (
    <SidebarGroupLabel className="text-[hsl(var(--primary))] px-1 text-sm">
      {icon}
      {content}
    </SidebarGroupLabel>
  )
}
GroupLabel.propTypes = sidebarGroupLablesPropsTypes

export const Dashboard = ({ isExpanded }) => {
  return (
    <SidebarGroup>
      <GroupLabel content="Home" icon={<Home className="h-4 w-4 mr-2" />} />
      <SidebarGroupContent>
        <SidebarMenu>
          <SidebarMenuItem key="Dashboard">
            <Navigation
              to="/admin-dashboard"
              content="Dashboard"
              icon={<LayoutDashboard className="h-4 w-4" />}
              isExpanded={isExpanded}
            />
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}

Dashboard.propTypes = sidebarMenusPropsTypes.isExpanded

export const MockInterView = ({ isExpanded }) => {
  return (
    <SidebarMenuSubItem>
      <SidebarMenuSubButton asChild>
        <Navigation
          to="/mock-interview"
          content="Mock Interview"
          icon={<ContactRound className="h-4 w-4" />}
          isExpanded={isExpanded}
        />
      </SidebarMenuSubButton>
    </SidebarMenuSubItem>
  )
}

MockInterView.propTypes = sidebarMenusPropsTypes.isExpanded

export const AIInterview = ({ isExpanded }) => {
  return (
    <SidebarMenuSubItem>
      <SidebarMenuSubButton asChild>
        <Navigation
          to="https://ai-interview-f748d.web.app/"
          content="AI Interview"
          icon={<Video className="h-4 w-4" />}
          isExpanded={isExpanded}
        />
      </SidebarMenuSubButton>
    </SidebarMenuSubItem>
  )
}

AIInterview.propTypes = sidebarMenusPropsTypes.isExpanded

export const Practice = ({ isExpanded }) => {
  return (
    <SidebarGroup>
      <GroupLabel content="Practice" icon={<Code className="h-4 w-4 mr-2" />} />
      <SidebarGroupContent>
        <SidebarMenu>
          <LiveCoding isExpanded={isExpanded} />
          <Notebook isExpanded={isExpanded} />
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}

Practice.propTypes = sidebarMenusPropsTypes.isExpanded

export const MyCourses = ({ isExpanded }) => {
  return (
    <SidebarMenuItem key="MyCourses">
      <SidebarMenuButton asChild>
        <Navigation
          to={ct.route.MY_COURSES}
          content="My Courses"
          icon={<BookText className="h-4 w-4" />}
          isExpanded={isExpanded}
        />
      </SidebarMenuButton>
    </SidebarMenuItem>
  )
}

MyCourses.propTypes = sidebarMenusPropsTypes

export const Notebook = () => {
  return (
    <SidebarMenuItem key="Notebook">
      <SidebarMenuButton asChild>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <SidebarMenuButton disabled>
                <BookOpen className="h-4 w-4" />
                <span>Notebook</span>
              </SidebarMenuButton>
            </TooltipTrigger>
            <TooltipContent>
              <p>Coming soon!</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </SidebarMenuButton>
    </SidebarMenuItem>
  )
}

export const Courses = ({ isExpanded, userRole }) => {
  return (
    <SidebarGroup>
      <GroupLabel content="Courses" icon={<Layers2 className="h-4 w-4 mr-2" />} />
      <SidebarGroupContent>
        <SidebarMenu>
          <Collapsible className="group/collapsible">
            <SidebarMenuItem>
              <CollapsibleTrigger asChild>
                <SidebarMenuButton>
                  <UsersRound className="h-4 w-4" />
                  <span className="text-sm font-medium">Manage Courses</span>
                  <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                </SidebarMenuButton>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <SidebarMenuSub>
                  <SidebarMenuItem size="sm" key="LiveCourse">
                    <Navigation
                      to={ct.route.LIVE_COURSES}
                      content="Live Course"
                      icon={<School className="h-4 w-4" />}
                      isExpanded={isExpanded}
                    />
                  </SidebarMenuItem>
                  <SidebarMenuItem key="ShortCourse">
                    <SidebarMenuButton asChild>
                      <Navigation
                        to="/short-courses-list"
                        content="Short Course"
                        icon={<BookMarked className="h-4 w-4" />}
                        isExpanded={isExpanded}
                      />
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                  <SidebarMenuItem key="NanoCourse">
                    <Navigation
                      to="/nano-courses-list"
                      content="Nano Course"
                      icon={<BookOpen className="h-4 w-4" />}
                      isExpanded={isExpanded}
                    />
                  </SidebarMenuItem>
                  {userRole === USER_ROLES.STUDENT && <MyCourses isExpanded={isExpanded} />}
                </SidebarMenuSub>
              </CollapsibleContent>
            </SidebarMenuItem>
          </Collapsible>
          {userRole === USER_ROLES.ADMIN && (
            <Collapsible className="group/collapsible">
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton>
                    <UsersRound className="h-4 w-4" />

                    <span className="text-sm font-medium">Manage Batch</span>
                    <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton asChild>
                        <Navigation
                          to={ct.route.MANAGE_BATCH}
                          content="Courses"
                          icon={<UsersRound className="h-4 w-4" />}
                          isExpanded={isExpanded}
                        />
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          )}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}

Courses.propTypes = sidebarMenusPropsTypes

export const StudentPerformance = ({ isExpanded }) => {
  return (
    <SidebarGroup>
      <GroupLabel content="Performance" icon={<Layers className="h-4 w-4 mr-2" />} />
      <SidebarGroupContent>
        <SidebarMenu>
          <SidebarMenuItem key="ShortCourse">
            <Navigation
              to="/student-tracking"
              content="Student Performance"
              icon={<ChartLine size={18} className="" />}
              isExpanded={isExpanded}
            />
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}

StudentPerformance.propTypes = sidebarMenusPropsTypes

export const StudentPayments = ({ isExpanded, userRole }) => {
  return (
    <SidebarMenuItem key="StudentPayment">
      <SidebarMenuButton asChild>
        <Navigation
          to={ct.route.PAYMENTS}
          content={userRole === USER_ROLES.STUDENT ? "Payment" : "Student Payment"}
          icon={<DollarSign className="h-4 w-4" />}
          isExpanded={isExpanded}
        />
      </SidebarMenuButton>
    </SidebarMenuItem>
  )
}

StudentPayments.propTypes = sidebarMenusPropsTypes

export const TrainerPayments = ({ isExpanded, userRole }) => {
  return (
    <Collapsible className="group/collapsible">
      <SidebarMenuItem>
        <CollapsibleTrigger asChild>
          <SidebarMenuButton>
            <Wallet className="h-4 w-4" />
            <span className="text-sm font-medium">
              {userRole === USER_ROLES.ADMIN && USER_ROLES.TRAINER}Payment
            </span>
            <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
          </SidebarMenuButton>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <SidebarMenuSub>
            <SidebarMenuItem key="LiveCourse">
              <Navigation
                to={ct.route.TRAINER_PAYMENT}
                content={`${userRole === USER_ROLES.ADMIN ? USER_ROLES.TRAINER : ""} Payment`}
                icon={<DollarSign className="h-4 w-4" />}
                isExpanded={isExpanded}
              />
            </SidebarMenuItem>
            <SidebarMenuItem key="ShortCourse">
              <Navigation
                to={ct.route.TRAINER_EARNING}
                content={`${userRole === USER_ROLES.ADMIN ? USER_ROLES.TRAINER : ""} Earning`}
                icon={<Wallet className="h-4 w-4" />}
                isExpanded={isExpanded}
              />
            </SidebarMenuItem>
          </SidebarMenuSub>
        </CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible>
  )
}

TrainerPayments.propTypes = sidebarMenusPropsTypes

export const TrainerCourses = ({ isExpanded }) => {
  return (
    <SidebarGroup>
      <GroupLabel content="Courses" icon={<Layers className="h-4 w-4 mr-2" />} />
      <SidebarGroupContent>
        <SidebarMenu>
          <SidebarMenuItem key="ManageCourses">
            <SidebarMenuButton asChild>
              <Navigation
                // to={ct.route.TRAINER_EARNING}
                to="/course/trainer"
                content="Manage Courses"
                icon={<Wallet className="h-4 w-4" />}
                isExpanded={isExpanded}
              />
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}

TrainerCourses.propTypes = sidebarMenusPropsTypes.isExpanded

export const Payments = ({ isExpanded, userRole }) => {
  return (
    <SidebarGroup>
      <GroupLabel content="Payments" icon={<FolderOpenDot className="h-4 w-4 mr-2" />} />
      <SidebarGroupContent>
        <SidebarMenu>
          {[USER_ROLES.ADMIN, USER_ROLES.STUDENT, USER_ROLES.VENDOR]?.includes(userRole) && (
            <StudentPayments isExpanded={isExpanded} userRole={userRole} />
          )}
          {[USER_ROLES.ADMIN, USER_ROLES.TRAINER]?.includes(userRole) && (
            <TrainerPayments isExpanded={isExpanded} userRole={userRole} />
          )}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}
Payments.propTypes = sidebarMenusPropsTypes

export const StudentPayment = ({ isExpanded }) => {
  return (
    <SidebarMenuItem key="ShortCourse">
      <Navigation
        to="/student-tracking"
        content="Student Performance"
        icon={<ChartLine size={18} className="" />}
        isExpanded={isExpanded}
      />
    </SidebarMenuItem>
  )
}
StudentPayment.propTypes = sidebarMenusPropsTypes.isExpanded

export const TrainerPayment = ({ isExpanded }) => {
  return (
    <Collapsible defaultOpen className="group/collapsible">
      <SidebarMenuItem>
        <CollapsibleTrigger asChild>
          <SidebarMenuButton>
            <Wallet className="h-4 w-4" />

            <span className="text-sm font-medium">Trainer Payment</span>
            <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
          </SidebarMenuButton>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <SidebarMenuSub>
            <SidebarMenuItem key="LiveCourse">
              <Navigation
                to={ct.route.TRAINER_PAYMENT}
                content="Trainer Payment"
                icon={<DollarSign className="h-4 w-4" />}
                isExpanded={isExpanded}
              />
            </SidebarMenuItem>
            <SidebarMenuItem key="ShortCourse">
              <Navigation
                to={ct.route.TRAINER_EARNING}
                content="Trainer Earning"
                icon={<Wallet className="h-4 w-4" />}
                isExpanded={isExpanded}
              />
            </SidebarMenuItem>
          </SidebarMenuSub>
        </CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible>
  )
}

TrainerPayment.propTypes = sidebarMenusPropsTypes.isExpanded

export const Community = ({ isExpanded }) => {
  return (
    <SidebarMenuItem key="Community">
      <Navigation
        to="/admin/community"
        content="Community"
        icon={<MessageSquare className="h-4 w-4" />}
        isExpanded={isExpanded}
      />
    </SidebarMenuItem>
  )
}

Community.propTypes = sidebarMenusPropsTypes.isExpanded

export const LiveCoding = ({ isExpanded }) => {
  return (
    <SidebarMenuItem key="LiveCoding">
      <SidebarMenuButton asChild>
        {/* <a href="https://coding.10xscale.ai" target="_blank" rel="noopener noreferrer">
                          <Code2 className="h-4 w-4" />
                            isExpanded={isExpanded}
                          <span>Live Coding</span>
                        </a> */}

        <Navigation
          to="https://coding.10xscale.ai"
          content="Live Coding"
          icon={<Code2 className="h-4 w-4" />}
          isExpanded={isExpanded}
        />
      </SidebarMenuButton>
    </SidebarMenuItem>
  )
}

LiveCoding.propTypes = sidebarMenusPropsTypes.isExpanded

export const Interview = ({ isExpanded, userRole }) => {
  return (
    <SidebarGroup>
      <GroupLabel content="Interview" icon={<UserRoundCog className="h-4 w-4 mr-2" />} />
      <SidebarGroupContent>
        <SidebarMenu>
          <MockInterView isExpanded={isExpanded} />
        </SidebarMenu>
        {userRole === USER_ROLES.TRAINER && (
          <SidebarMenu>
            <Resume isExpanded={isExpanded} />
          </SidebarMenu>
        )}
        {userRole === USER_ROLES.STUDENT && (
          <SidebarMenu>
            <AIInterview isExpanded={isExpanded} />
          </SidebarMenu>
        )}
      </SidebarGroupContent>
    </SidebarGroup>
  )
}

Interview.propTypes = sidebarMenusPropsTypes

export const Miscellaneous = ({ isExpanded, userRole }) => {
  return (
    <SidebarGroup>
      <GroupLabel content="Miscellaneous" icon={<HeartHandshake className="h-4 w-4 mr-2" />} />
      <SidebarGroupContent>
        <SidebarMenu>
          {userRole === USER_ROLES.ADMIN && (
            <SidebarMenuItem key="LiveCoding">
              <SidebarMenuButton asChild>
                {/* <a href="https://coding.10xscale.ai" target="_blank" rel="noopener noreferrer">
                      <Code2 className="h-4 w-4" />
                        isExpanded={isExpanded}
                      <span>Live Coding</span>
                    </a> */}

                <Navigation
                  to="https://coding.10xscale.ai"
                  content="Live Coding"
                  icon={<Code2 className="h-4 w-4" />}
                  isExpanded={isExpanded}
                />
              </SidebarMenuButton>
            </SidebarMenuItem>
          )}
          <SidebarMenuItem key="Community">
            <Tooltip>
              <TooltipTrigger asChild>
                <SidebarMenuButton>
                  <div className="flex items-center">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    <span>Community</span>
                  </div>
                </SidebarMenuButton>
              </TooltipTrigger>
              <TooltipContent>
                <p>Comming soon...</p>
              </TooltipContent>
            </Tooltip>
          </SidebarMenuItem>
          {/* <SidebarMenuItem key="Community">
            <Navigation
              to="/admin/community"
              content="Community"
              icon={<MessageSquare className="h-4 w-4" />}
              isExpanded={isExpanded}
            />
          </SidebarMenuItem> */}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}

Miscellaneous.propTypes = sidebarMenusPropsTypes

export const ManageMents = ({ isExpanded }) => {
  return (
    <SidebarGroup>
      <GroupLabel content="Management" icon={<UserCircle className="h-4 w-4 mr-2" />} />
      <SidebarGroupContent>
        <SidebarMenu>
          <SidebarMenuItem key="Dashboard">
            <Navigation
              to="/user-management"
              content="User Management"
              icon={<UserRound className="h-4 w-4" />}
              isExpanded={isExpanded}
            />
          </SidebarMenuItem>
          <SidebarMenuItem>
            <Navigation
              to="https://ai-jobs-9b4c6.web.app/login"
              content="Available JD"
              icon={<Newspaper className="h-4 w-4 " />}
              isExpanded={isExpanded}
            />
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}

ManageMents.propTypes = sidebarMenusPropsTypes.isExpanded

export const MarketingDashboard = ({ isExpanded }) => {
  return (
    <SidebarMenuItem key="MarketingDashboard">
      <Navigation
        to="/marketing-dashboard"
        content="Marketing Dashboard"
        icon={<BarChart className="h-4 w-4 " />}
        isExpanded={isExpanded}
      />
    </SidebarMenuItem>
  )
}

MarketingDashboard.propTypes = sidebarMenusPropsTypes.isExpanded

export const Marketing = ({ isExpanded, userRole }) => {
  return (
    <SidebarGroup>
      <GroupLabel
        content={userRole === USER_ROLES.STUDENT ? "Career" : "Marketing"}
        icon={<Briefcase className="h-4 w-4 mr-2" />}
      />
      <SidebarGroupContent>
        <SidebarMenu>
          <AvailableJD isExpanded={isExpanded} />
        </SidebarMenu>
        <SidebarMenu>
          <Resume isExpanded={isExpanded} />
        </SidebarMenu>

        {[USER_ROLES.MARKETER, USER_ROLES.ADMIN].includes(userRole) && (
          <SidebarMenu>
            <JobApplications isExpanded={isExpanded} />
          </SidebarMenu>
        )}

        {userRole === USER_ROLES.MARKETER && (
          <SidebarMenu>
            <Leads isExpanded={isExpanded} />
          </SidebarMenu>
        )}

        {userRole === USER_ROLES.STUDENT && (
          <SidebarMenu>
            <MarketingDashboard isExpanded={isExpanded} />
          </SidebarMenu>
        )}
      </SidebarGroupContent>
    </SidebarGroup>
  )
}

Marketing.propTypes = sidebarMenusPropsTypes

export const AvailableJD = ({ isExpanded }) => {
  return (
    <SidebarMenuItem>
      <Navigation
        to="https://ai-jobs-9b4c6.web.app/login"
        content="Available JD"
        icon={<Newspaper className="h-4 w-4 " />}
        isExpanded={isExpanded}
      />
    </SidebarMenuItem>
  )
}
AvailableJD.propTypes = sidebarMenusPropsTypes.isExpanded
export const Resume = ({ isExpanded }) => {
  return (
    <SidebarMenuItem>
      <Navigation
        to="https://resume-builder-475ff.web.app/"
        content="Resume"
        icon={<FileText className="h-4 w-4" />}
        isExpanded={isExpanded}
      />
    </SidebarMenuItem>
  )
}
Resume.propTypes = sidebarMenusPropsTypes.isExpanded
export const JobApplications = ({ isExpanded }) => {
  return (
    <SidebarMenuItem>
      <Navigation
        to="/applications"
        content="Job Application"
        icon={<AppWindow className="h-4 w-4" />}
        isExpanded={isExpanded}
      />
    </SidebarMenuItem>
  )
}
JobApplications.propTypes = sidebarMenusPropsTypes.isExpanded
export const Leads = ({ isExpanded }) => {
  return (
    <SidebarMenuItem>
      <Navigation
        to="/leads-management"
        content="Leads"
        icon={<FileUser className="h-4 w-4 " />}
        isExpanded={isExpanded}
      />
    </SidebarMenuItem>
  )
}

Leads.propTypes = sidebarMenusPropsTypes.isExpanded
