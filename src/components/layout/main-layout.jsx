import { SidebarProvider } from "@/components/ui/sidebar"
import { Toaster } from "@/components/ui/toaster"
import { ReactQueryDevtools } from "@/lib/devtools"
import ct from "@constants/"
import { useEffect } from "react"
import { useSelector } from "react-redux"
import { Outlet, useLocation, useNavigate } from "react-router-dom"
import Header from "./header"
import Breadcrumbs from "./header/bread-crumb"
import AppSidebar from "./sidebar"

export default function MainLayout() {
  const store = useSelector((st) => st[ct.store.USER_STORE])
  const navigate = useNavigate()
  const location = useLocation()
  useEffect(() => {
    if (store.isAuthenticated) {
      return
    }
    // route to login page
    navigate(`/${ct.route.AUTH.LOGIN}`, { replace: true })
  }, [store, navigate])
  const courseOverview = location.pathname?.includes("course-overview")
  return (
    <SidebarProvider>
      <AppSidebar />
      <main className="min-h-screen w-full">
        <Header />
        <div className="">
          {!courseOverview && <Breadcrumbs />}

          <Toaster />
          <div className="mx-[1rem] my-[2rem]">
            <Outlet />
          </div>
        </div>
        <ReactQueryDevtools />
      </main>
    </SidebarProvider>
  )
}
