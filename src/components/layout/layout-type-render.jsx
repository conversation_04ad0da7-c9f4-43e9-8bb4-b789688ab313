// import PropTypes from 'prop-types'
//
// import BlankLayout from '@/components/layout/blank-layout'
// import MainLayout from '@/components/layout/main-layout'
// import ct from '@constants/'
//
// /**
//  * Get the layout based on type.
//  * If the match is not found, return the default layout type.
//  * @param {string} layoutType - current layout type.
//  * @return {JSX.Element} The layout type based on the match or the default layout type.
//  */
// export default function LayoutRenderer({ layoutType }) {
//   switch (layoutType) {
//     case ct.layout.LAYOUT_TYPE_DEFAULT:
//       return <MainLayout />
//     case ct.layout.LAYOUT_TYPE_BLANK:
//       return <BlankLayout />
//     default:
//       return <MainLayout />
//   }
// }
//
// LayoutRenderer.propTypes = {
//   layoutType: PropTypes.oneOf([
//     ct.layout.LAYOUT_TYPE_DEFAULT,
//     ct.layout.LAYOUT_TYPE_BLANK,
//   ]).isRequired,
// }
