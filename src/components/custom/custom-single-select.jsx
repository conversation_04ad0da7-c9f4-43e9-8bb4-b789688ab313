/* eslint-disable no-nested-ternary */
import { DeleteCategory } from "@/services/api/create-course-form.api"
import { UseDeleteCategory } from "@/services/query/create-course-form.query"
import { Check, ChevronsUpDown, PlusCircle, Search, X } from "lucide-react"
import PropTypes from "prop-types" // Import PropTypes
import { useEffect, useRef, useState } from "react"
import { Button } from "../ui/button"
import { Input } from "../ui/input"



const CustomSingleSelect = ({
  iteratedList,
  categoryEditId,
  onChangeValue,
  setValue,
  watch,
  name,
  placeholder = "Select an item...",
}) => {
  const [open, setOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const dropdownRef = useRef(null)
  const [isAddingNew, setIsAddingNew] = useState(false)

  // Get selected value from the form state
  const selectedValue = watch(`course_data.${name}`) || null

  // eslint-disable-next-line eqeqeq
  const editValue = iteratedList?.find((item) => item.value == categoryEditId)
  console.log(iteratedList, "iteratedList")

  useEffect(() => {
    if (categoryEditId) {
      setSearchTerm(editValue?.label || "") // Ensure setSearchTerm gets a string
      setTimeout(() => {
        setValue("course_data.category_id", String(categoryEditId))
      }, [500])
    }
  }, [categoryEditId, editValue, setValue]) // Added dependencies

  const { mutate: deleteCategory } = UseDeleteCategory() // Keep this if you're using it

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setOpen(false)
        setIsAddingNew(false)
        // Reset searchTerm if nothing was selected/entered
        if (!selectedValue && searchTerm.trim() !== "") {
          setSearchTerm("");
        }
      }
    }
    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [selectedValue, searchTerm]) // Added dependencies

  const handleSelect = (itemValue) => {
    console.log(itemValue, "selected itemValue in handleSelect")

    // Determine if the selected itemValue is from the existing list
    const isExistingCategory = iteratedList?.some(
      (item) => String(item.value) === String(itemValue) // Ensure strict comparison for values
    )

    // Set the main form field (e.g., course_data.category_id)
    setValue(`course_data.${name}`, itemValue, { shouldValidate: true })

    // Special handling for category_id and new_category
    if (name === "category_id") {
      if (isExistingCategory) {
        // If an existing category is selected, clear the new_category field
        setValue("course_data.new_category", "", { shouldValidate: true })
        console.log("Existing category selected. new_category set to ''")
      } else if (typeof itemValue === "string" && itemValue.trim() !== "") {
        // If a new category string is being added (and it's for category_id)
        setValue("course_data.new_category", itemValue.trim(), { shouldValidate: true })
        console.log("New category entered. new_category set to:", itemValue.trim())
      } else {
        // Fallback for cases where no new category is truly entered (e.g., empty string)
        setValue("course_data.new_category", "", { shouldValidate: true })
        console.log("new_category set to '' (fallback)")
      }
    }

    // Call onChangeValue if provided
    if (onChangeValue) {
      onChangeValue(itemValue)
    }

    setSearchTerm("")
    setOpen(false)
    setIsAddingNew(false)
  }

  const handleKeyDown = (e) => {
    // Prevent typing numbers for category names if adding new, assuming category names are text
    // This is specific if 'name' is 'category_id' AND we are in 'add new' mode
    if (name === "category_id" && isAddingNew && (e.key >= '0' && e.key <= '9')) {
      e.preventDefault();
      return;
    }

    if (e.key === "Enter") {
      e.preventDefault()
      if (isAddingNew && searchTerm.trim() !== "") {
        handleSelect(searchTerm.trim())
      } else if (!isAddingNew && filteredItems?.length === 1 && searchTerm.trim() !== "") {
        // If searching and only one item matches, select it on Enter
        handleSelect(filteredItems[0].value);
      }
    }
  }

  const handleButtonClick = (e) => {
    e.preventDefault()
    setOpen(!open)
    // If opening and no value selected, set search term to current selected label
    if (!open && selectedValue) {
      setSearchTerm(getDisplayLabel(selectedValue));
    } else if (!open && !isAddingNew) {
      // If opening and no value, and not adding new, clear search term
      setSearchTerm("");
    }
  }

  const toggleAddNew = (e) => {
    e.preventDefault()
    e.stopPropagation()
    setIsAddingNew(!isAddingNew)
    setSearchTerm("") // Clear search term when toggling add new
    if (isAddingNew) { // If toggling OFF 'add new' mode
      // Reset new_category if the user toggled off without entering anything
      setValue("course_data.new_category", "", { shouldValidate: true });
    }
    setOpen(true); // Keep dropdown open if toggling to add new, or close if toggling off
  }

  const removeValue = (e) => {
    e.preventDefault()
    e.stopPropagation()
    setValue(`course_data.${name}`, null, { shouldValidate: true })

    // Also clear new_category if clearing category_id
    if (name === "category_id") {
      setValue("course_data.new_category", "", { shouldValidate: true })
      console.log("Value removed. new_category set to ''")
    }
    setSearchTerm(""); // Clear search term on remove
  }

  const handleDelete = (CategoryId) => {
    // Uncomment these when you want to re-enable actual deletion and toasts
    // deleteCategory(
    //   {
    //     CategoryId,
    //   },
    //   {
    //     onSuccess: () => {
    //       successToast(
    //         "Category deleted successfully",
    //         "The Category has been removed from the course."
    //       )
    //     },
    //     onError: () => {
    //       failureToast("Deletion Error", "Unable to delete the Category. Please try again later.")
    //     },
    //   }
    // )

    // For now, keeping the direct call to DeleteCategory which might not show toasts
    console.log(`Attempting to delete category with ID: ${CategoryId}`)
    DeleteCategory(CategoryId) // Ensure this function actually performs the delete operation if not using react-query mutations
  }

  const getDisplayLabel = (value) => {
    // Find the corresponding item in the iteratedList
    const item = iteratedList?.find((item) => String(item.value) === String(value))
    // If found, return its label. If not found, it might be a new category name (string)
    // or an invalid value, so return the value itself.
    return item ? item.label : value;
  }

  const filteredItems = iteratedList?.filter((item) =>
    item.label.toLowerCase().includes(searchTerm?.toLowerCase())
  ) || []; // Ensure filteredItems is always an array

  return (
    <div className="relative" ref={dropdownRef}>
      <Button
        type="button"
        className="w-full flex items-center px-3 py-1 text-sm border rounded-md bg-white hover:bg-gray-50 cursor-text h-12" // Added h-12 for consistent height
        onClick={handleButtonClick}
      >
        <div className="flex flex-wrap gap-1 flex-1">
          {selectedValue ? (
            <div className="flex items-center gap-1 rounded-md bg-gray-100 px-2 text-xs">
              <div className="flex items-center gap-2 px-2 py-2">
                <span className="text-sm font-medium">{getDisplayLabel(selectedValue)}</span>
                <Button
                  type="button"
                  onClick={removeValue}
                  className="hover:bg-gray-200 rounded-full h-auto p-1" // Adjusted padding and height for smaller button
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </div>
          ) : isAddingNew ? (
            <Input
              type="text"
              className="border-0 bg-transparent text-sm outline-none placeholder:text-gray-400 flex-1 min-w-[50px] h-full"
              placeholder="Enter new category"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={handleKeyDown}
              size="sm"
              autoFocus
            />
          ) : (
            <Input
              type="text"
              className="border-0 bg-transparent text-sm outline-none placeholder:text-gray-400 flex-1 min-w-[50px] h-full"
              placeholder={placeholder}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={handleKeyDown}
              size="sm"
            />
          )}
        </div>
        <div className="flex items-center">
          {!selectedValue && ( // Only show add new button if nothing is selected
            <Button
              type="button"
              onClick={toggleAddNew}
              className="hover:bg-gray-200 rounded-full mr-2 h-auto p-1"
              title={isAddingNew ? "Select from list" : "Add new"}
            >
              <PlusCircle className="h-4 w-4" />
            </Button>
          )}
          <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
        </div>
      </Button>

      {open && (isAddingNew || filteredItems.length > 0) && ( // Show content if open AND (adding new OR has filtered items)
        <div className="absolute mt-1 w-full rounded-md border bg-white shadow-lg z-50">
          {!isAddingNew && ( // Only show search input if not in "add new" mode
            <div className="flex items-center border-b px-3 py-2">
              <Search className="h-4 w-4 text-gray-400" />
              <Input
                type="text"
                className="w-full border-0 bg-transparent text-sm outline-none placeholder:text-gray-400"
                placeholder={`Search ${name}...`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={handleKeyDown}
                autoFocus={!selectedValue} // Auto-focus search when no value is selected
              />
            </div>
          )}
          <div className="max-h-60 overflow-auto">
            {isAddingNew && searchTerm.trim() === "" && ( // Show helper text when adding new and no input
              <div className="px-3 py-2 text-gray-500 italic">Type a new category name.</div>
            )}
            {filteredItems.length === 0 && !isAddingNew && searchTerm.trim() !== "" && (
              <div className="px-3 py-2 text-gray-500">No results found.</div>
            )}
            {filteredItems?.map((item) => (
              <Button
                type="button"
                key={item.value}
                className="flex items-center w-full justify-between px-3 py-2 text-sm hover:bg-gray-100 cursor-pointer"
                onClick={(e) => {
                  e.preventDefault()
                  handleSelect(item.value)
                }}
              >
                <div className="flex items-center">
                  <div className="w-4 h-4 mr-2">
                    {String(selectedValue) === String(item.value) && ( // Ensure strict comparison
                      <div className="h-4 w-4 rounded-sm bg-blue-500 text-white flex items-center justify-center">
                        <Check className="h-3 w-3" />
                      </div>
                    )}
                  </div>
                  {item.label}
                </div>
                <Button
                  type="button"
                  className="hover:bg-gray-200 rounded-full h-auto p-1 ml-2" // Added spacing and reduced size
                  onClick={(e) => {
                    e.stopPropagation(); // prevent triggering handleSelect
                    handleDelete(item.value);
                  }}
                >
                  <X className="h-3 w-3 text-gray-500 hover:text-red-500" />
                </Button>
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

CustomSingleSelect.propTypes = {
  iteratedList: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
      label: PropTypes.string.isRequired,
    })
  ),
  categoryEditId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChangeValue: PropTypes.func,
  setValue: PropTypes.func.isRequired,
  watch: PropTypes.func.isRequired,
  name: PropTypes.string.isRequired,
  placeholder: PropTypes.string,
}

export default CustomSingleSelect