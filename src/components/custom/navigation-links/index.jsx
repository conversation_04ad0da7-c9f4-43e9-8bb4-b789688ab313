/* eslint-disable react/jsx-no-target-blank */
import { Download, ExternalLink, Minus } from "lucide-react"
import PropTypes from "prop-types"

const NavigationLinks = ({ url }) => {
  // Helper function to determine if URL is downloadable

  console.log("_url", url)
  const isDownloadableUrl = () => {
    if (!url) return false
    const fileExtensions = [".pdf", ".doc", ".docx", ".xls", ".xlsx", ".zip", ".rar", ".txt"]
    return fileExtensions.some((ext) => url.toLowerCase().endsWith(ext))
  }

  // Helper function to render the appropriate icon
  const renderIcon = () => {
    if (!url) return <Minus className="h-4 w-4" />
    return isDownloadableUrl(url) ? (
      <Download className="h-4 w-4" />
    ) : (
      <ExternalLink className="h-4 w-4" />
    )
  }

  return (
    <div className="flex items-center justify-center 0 py-2 last:border-0">
      {/* <span className="text-sm font-medium">{item}</span> */}
      {url ? (
        <a
          href={url}
          target={isDownloadableUrl(url) ? "_self" : "_blank"}
          rel={url && !isDownloadableUrl(url) ? "noreferrer" : undefined}
          download={isDownloadableUrl(url)}
          className="flex gap-x-2 items-center font-medium text-sm "
        >
          {renderIcon()}
          {isDownloadableUrl(url) ? "Download" : "Visit"}
        </a>
      ) : (
        <span className="text-gray-400">-</span>
      )}
    </div>
  )
}

NavigationLinks.propTypes = {
  url: PropTypes.string,
}

export default NavigationLinks
