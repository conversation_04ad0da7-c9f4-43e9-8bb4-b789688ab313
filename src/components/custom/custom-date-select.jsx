import { format } from "date-fns"
import { Calendar as CalendarIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import PropTypes from "prop-types"

const CustomDateSelect = ({ date, setDate, width = "w-250px" }) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            `${width} justify-start items-center text-left font-normal",
            !date && "text-muted-foreground`
          )}
        >
          <CalendarIcon size={15} />
          {date ? format(date, "PPP") : <span className="text-sm font-medium">Pick a date</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="flex w-auto flex-col space-y-2 p-2">
        {/* <Select onValueChange={(value) => setDate(addDays(new Date(), parseInt(value, 10)))}>
          <SelectTrigger>
            <SelectValue placeholder="Select" />
          </SelectTrigger>
          <SelectContent position="popper">
            <SelectItem value="0">Today</SelectItem>
            <SelectItem value="1">Tomorrow</SelectItem>
            <SelectItem value="3">In 3 days</SelectItem>
            <SelectItem value="7">In a week</SelectItem>
          </SelectContent>
        </Select> */}
        <div className="rounded-md border">
          <Calendar
            mode="single"
            selected={date}
            onSelect={setDate}
            disabled={(day) => day < new Date().setHours(0, 0, 0, 0)}
          />
        </div>
      </PopoverContent>
    </Popover>
  )
}

CustomDateSelect.propTypes = {
  date: PropTypes.instanceOf(Date),
  setDate: PropTypes.func,
  width: PropTypes.string,
}

export default CustomDateSelect
