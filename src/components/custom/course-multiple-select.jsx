// CourseMultipleSelect.jsx
import { Check, ChevronsUpDown, Search, X } from "lucide-react"
import { useEffect, useRef, useState } from "react"
import PropTypes from "prop-types"
import { But<PERSON> } from "../ui/button"
import { Input } from "../ui/input"

const CourseMultipleSelect = ({
    courseList,
    selectedValues = [],
    onChangeValue,
    name = "courses",
    placeholder = "Select courses...",
}) => {
    const [open, setOpen] = useState(false)
    const [searchTerm, setSearchTerm] = useState("")
    const dropdownRef = useRef(null)

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setOpen(false)
            }
        }
        document.addEventListener("mousedown", handleClickOutside)
        return () => document.removeEventListener("mousedown", handleClickOutside)
    }, [])

    const handleSelect = (courseId) => {
        const updatedValues = selectedValues.includes(courseId)
            ? selectedValues.filter((value) => value !== courseId)
            : [...selectedValues, courseId]
        if (onChangeValue) onChangeValue(updatedValues)
        setSearchTerm("")
    }

    const handleButtonClick = (e) => {
        e.preventDefault()
        setOpen(!open)
    }

    const removeValue = (valueToRemove, e) => {
        e.preventDefault()
        e.stopPropagation()
        handleSelect(valueToRemove)
    }

    const getDisplayLabel = (courseId) => {
        const found = courseList?.find((c) => c.id === courseId)
        return found ? found.title : courseId
    }

    const filteredCourses = courseList?.filter(
        (c) =>
            c.title?.toLowerCase()?.includes(searchTerm?.toLowerCase()) ||
            c.instructor?.toLowerCase()?.includes(searchTerm?.toLowerCase())
    )

    return (
        <div className="relative" ref={dropdownRef}>
            <Button
                type="button"
                className="w-full flex items-center px-3 py-2 text-sm border rounded-md bg-white hover:bg-gray-50 cursor-pointer h-12 focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
                onClick={handleButtonClick}
            >
                <div className="flex flex-wrap gap-1 flex-1 text-left">
                    {selectedValues.length === 0 ? (
                        <span className="text-gray-400">{placeholder}</span>
                    ) : (
                        selectedValues.map((courseId) => (
                            <div
                                key={courseId}
                                className="flex items-center gap-1 rounded-md bg-gray-100 px-2 py-1 text-xs"
                            >
                                <span className="text-sm font-medium  ">{getDisplayLabel(courseId)}</span>
                                <Button
                                    type="button"
                                    onClick={(e) => removeValue(courseId, e)}
                                    className="hover:bg-red-100 rounded-full p-0 h-4 w-4 bg-transparent"
                                >
                                    <X className="h-3 w-3 text-red-600" />
                                </Button>
                            </div>
                        ))
                    )}
                </div>
                <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50 ml-2" />
            </Button>

            {open && (
                <div className="absolute mt-1 w-full rounded-md border bg-white shadow-lg z-50">
                    <div className="flex items-center border-b px-3 py-2">
                        <Search className="h-4 w-4 text-gray-400 mr-2" />
                        <Input
                            type="text"
                            className="w-full border-0 bg-transparent text-sm outline-none placeholder:text-gray-400"
                            placeholder="Search courses or instructors..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                    </div>
                    <div className="max-h-60 overflow-auto">
                        {filteredCourses?.map((course) => (
                            <Button
                                type="button"
                                key={course.id}
                                className="flex items-center w-full justify-start px-3 py-2 text-sm hover:bg-gray-100 cursor-pointer border-0 rounded-none"
                                onClick={(e) => {
                                    e.preventDefault()
                                    handleSelect(course.id)
                                }}
                            >
                                <div className="w-4 h-4 mr-3">
                                    {selectedValues.includes(course.id) && (
                                        <div className="h-4 w-4 rounded-sm bg-blue-500 text-white flex items-center justify-center">
                                            <Check className="h-3 w-3" />
                                        </div>
                                    )}
                                </div>
                                <div className="flex-1 text-left">
                                    <div className="font-medium text-gray-900">{course.title}</div>
                                    <div className="text-xs text-gray-500">{course.instructor}</div>
                                </div>
                            </Button>
                        ))}
                        {filteredCourses && filteredCourses.length === 0 && (
                            <div className="px-3 py-2 text-sm text-gray-500">
                                No courses found matching &quot;{searchTerm}&quot;
                            </div>
                        )}
                        {(!courseList || courseList.length === 0) && (
                            <div className="px-3 py-2 text-sm text-gray-500">No courses available</div>
                        )}
                    </div>
                </div>
            )}
        </div>
    )
}

CourseMultipleSelect.propTypes = {
    courseList: PropTypes.arrayOf(
        PropTypes.shape({
            id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
            title: PropTypes.string.isRequired,
            instructor: PropTypes.string.isRequired,
        })
    ).isRequired,
    selectedValues: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])),
    onChangeValue: PropTypes.func,
    name: PropTypes.string,
    placeholder: PropTypes.string,
}

export default CourseMultipleSelect
