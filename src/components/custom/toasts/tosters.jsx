import { toast } from "@/components/ui/use-toast"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, CircleCheckBig, CircleX, Loader2, Trash2 } from "lucide-react"

export const successToast = (title = "Write titles", description = "Description of toasters") => {
  toast({
    title: (
      <div className="flex items-center">
        <div className="flex items-center justify-center">
          <div className="absolute ml-8 h-16 w-16 bg-green-50 rounded-full flex justify-center items-center animate-pulse text-white">
            <div className="h-10 w-10 bg-green-500 rounded-full flex justify-center items-center text-white">
              <CircleCheckBig className="" size={25} />
            </div>
          </div>
        </div>

        <div className="flex flex-col ms-14 justify-between w-full flex-shrink-0">
          <span className="text-green-500 font-semibold">{title}</span>
          <p className="text-gray-500 font-medium text-sm">{description}</p>
        </div>
      </div>
    ),
  })
}

export const failureToast = (title = "Write title", description = "Description of toaster") => {
  toast({
    title: (
      <div className="flex items-center">
        <div className="flex items-center justify-center">
          <div className="absolute ml-8 h-14 w-14 bg-red-50 rounded-full flex justify-center items-center animate-pulse text-white">
            <div className="h-10 w-10 bg-red-500 rounded-full flex justify-center items-center text-white">
              <CircleX className="" size={25} />
            </div>
          </div>
        </div>

        <div className="flex flex-col ms-14 justify-between w-full flex-shrink-0">
          <span className="text-red-500 font-semibold">{title}</span>
          <p className="text-gray-500 font-medium text-sm">{description}</p>
        </div>
      </div>
    ),
  })
}

export const deleteToast = (title = "Write title", description = "Description of toaster") => {
  toast({
    title: (
      <div className="flex items-center">
        <div className="flex items-center justify-center">
          <div className="absolute ml-8 h-16 w-16 bg-red-50 rounded-full flex justify-center items-center animate-pulse text-white">
            <div className="h-10 w-10 bg-red-500 rounded-full flex justify-center items-center text-white">
              <Trash2 className="" size={25} />
            </div>
          </div>
        </div>

        <div className="flex flex-col ms-14 justify-between w-full flex-shrink-0">
          <span className="text-red-500 font-semibold">{title}</span>
          <p className="text-gray-500 font-medium text-sm">{description}</p>
        </div>
      </div>
    ),
  })
}

// Store active loading toast reference
let activeLoadingToast = null

export const loadingToast = (title = "Processing", description = "Please wait...") => {
  // Dismiss any existing loading toast
  if (activeLoadingToast) {
    activeLoadingToast.dismiss()
  }

  // Create new loading toast
  activeLoadingToast = toast({
    title: (
      <div className="flex items-center">
        <div className="flex items-center justify-center">
          <div className="absolute ml-8 h-16 w-16 bg-blue-50 rounded-full flex justify-center items-center animate-pulse">
            <div className="h-10 w-10 bg-blue-500 rounded-full flex justify-center items-center text-white">
              <Loader2 className="animate-spin" size={25} />
            </div>
          </div>
        </div>

        <div className="flex flex-col ms-14 justify-between w-full flex-shrink-0">
          <span className="text-blue-500 font-semibold">{title}</span>
          <p className="text-gray-500 font-medium text-sm">{description}</p>
        </div>
      </div>
    ),
    duration: Infinity, // This prevents auto-dismissal
  })

  return activeLoadingToast
}

// Function to manually dismiss loading toast
export const dismissLoadingToast = () => {
  if (activeLoadingToast) {
    activeLoadingToast.dismiss()
    activeLoadingToast = null
  }
}

export const warningToast = (title = "Warning", description = "") => {
  return toast({
    title: (
      <div className="flex items-center">
        <div className="flex items-center justify-center">
          <div className="absolute ml-8 h-16 w-16 bg-yellow-50 rounded-full flex justify-center items-center">
            <div className="h-10 w-10 bg-yellow-500 rounded-full flex justify-center items-center text-white">
              <AlertTriangle className="animate-pulse" size={25} />
            </div>
          </div>
        </div>

        <div className="flex flex-col ms-14 justify-between w-full flex-shrink-0">
          <span className="text-yellow-600 font-semibold">{title}</span>
          <p className="text-gray-500 font-medium text-sm">{description}</p>
        </div>
      </div>
    ),
    duration: 5000, // 5 seconds
  })
}