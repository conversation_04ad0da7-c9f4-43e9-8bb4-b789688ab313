import { toast } from "@/components/ui/use-toast"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

const UploadToast = ({ file, description = "Please wait while we upload your file." }) => {
  toast({
    title: (
      <div className="borde">
        <div className="flex items-center space-x-3">
          {/* Upload icon */}
          <div className="flex-shrink-0">
            <div className="w-6 h-6 text-blue-600">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v4h3l-4 4-4-4h3V7z" />
              </svg>
            </div>
          </div>

          {/* Content */}
          <div className="flex-grow">
            <h4 className="text-sm font-medium text-gray-900">Uploading 'toasts-FINAL06.fig'</h4>
            <p className="text-xs text-gray-500">{description}</p>

            {/* Progress bar */}
            <div className="mt-2 h-1 w-full rounded-full overflow-hidden">
              <div
                className="h-full bg-blue-600 rounded-full transition-all duration-300"
                style={{ width: "60%" }}
              />
            </div>
          </div>

          {/* Close button */}
          <Button
            variant="ghost"
            size="sm"
            className="flex-shrink-0 -mr-1 h-6 w-6 p-0 hover:bg-gray-100 rounded-full"
          >
            <span className="text-gray-400 text-xs">×</span>
          </Button>
        </div>

        {/* Action buttons
        <div className="flex space-x-2 mt-2">
          <Button variant="ghost" size="sm" className="text-gray-600 text-xs hover:bg-gray-100">
            Cancel
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-600 text-xs hover:bg-gray-100">
            Upload another
          </Button>
        </div> */}
      </div>
    ),
  })
}

export default UploadToast
