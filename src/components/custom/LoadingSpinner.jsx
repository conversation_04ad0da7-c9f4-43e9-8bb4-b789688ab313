import PropTypes from "prop-types"

function LoadingSpinner({
  className = "flex items-center justify-center my-8",
  iconWidth = 48,
  iconHeight = 48,
  iconClassName = "animate-spin",
  variant = "default",
  color = "text-blue-600", // Tailwind text color class for flexibility
}) {
  const renderSpinner = () => {
    switch (variant) {
      case "dots":
        return (
          <div className="flex space-x-2">
            <div className={`w-3 h-3 ${color} rounded-full animate-bounce`} />
            <div
              className={`w-3 h-3 ${color} rounded-full animate-bounce [animation-delay:-.3s]`}
            />
            <div
              className={`w-3 h-3 ${color} rounded-full animate-bounce [animation-delay:-.5s]`}
            />
          </div>
        )

      case "pulse":
        return (
          <div className={`rounded-full border-4 ${color} border-t-transparent animate-spin`} />
        )

      default:
        return (
          <svg
            className={`${iconClassName}`}
            width={iconWidth}
            height={iconHeight}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 4.75V6.25"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M17.1266 6.87347L16.0659 7.93413"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M19.25 12L17.75 12"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M17.1266 17.1265L16.0659 16.0659"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M12 17.75V19.25"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M7.9342 16.0659L6.87354 17.1265"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M6.25 12L4.75 12"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M7.9342 7.93413L6.87354 6.87347"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        )
    }
  }

  return (
    <div className={className} data-testid="loading_spinner">
      {renderSpinner()}
    </div>
  )
}

LoadingSpinner.propTypes = {
  className: PropTypes.string,
  iconWidth: PropTypes.number,
  iconHeight: PropTypes.number,
  iconClassName: PropTypes.string,
  variant: PropTypes.oneOf(["default", "dots", "pulse"]),
  color: PropTypes.string,
}

export default LoadingSpinner
