/* eslint-disable react/no-array-index-key */
/* eslint-disable sonarjs/no-duplicate-string */
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import usePagination from "@/hooks/usePagination.hook"
import PropTypes from "prop-types"

const CustomPagination = ({
  totalItems,
  pageSize = 10,
  currentPage = 1,
  onPageChange,
  paginationPosition = "end",
}) => {
  const totalPages = Math.ceil(totalItems / pageSize)
  const { getPageNumbers } = usePagination(totalPages, currentPage, onPageChange)
  const pages = getPageNumbers()
  console.log(totalItems, "yyyy")
  if (totalPages <= 1) return null
  return (
    <Pagination className={`text-end  justify-${paginationPosition}`}>
      <PaginationContent>
        <PaginationItem>
          <PaginationPrevious
            className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
            onClick={() => currentPage > 1 && onPageChange(currentPage - 1)}
          />
        </PaginationItem>

        {pages.map((page, index) => (
          <PaginationItem key={index}>
            {page === "..." ? (
              <PaginationEllipsis />
            ) : (
              <PaginationLink
                className={
                  page === currentPage ? "bg-primary text-primary-foreground" : "cursor-pointer"
                }
                onClick={() => onPageChange(page)}
              >
                {page}
              </PaginationLink>
            )}
          </PaginationItem>
        ))}

        <PaginationItem>
          <PaginationNext
            className={
              currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"
            }
            onClick={() => currentPage < totalPages && onPageChange(currentPage + 1)}
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  )
}

CustomPagination.propTypes = {
  paginationPosition: PropTypes.string,
  total: PropTypes.number,
  pageSize: PropTypes.number,
  currentPage: PropTypes.number,
  onPageChange: PropTypes.func,
}

export default CustomPagination
