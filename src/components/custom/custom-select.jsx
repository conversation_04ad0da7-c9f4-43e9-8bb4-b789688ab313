import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { applicationStatus } from "@/utils/constants"
import PropTypes from "prop-types"

const CustomSelect = ({
  publishedStatus,
  setPublishedStatus,
  placeholder = "Select publish status",
  iteratedData,
}) => {
  return (
    <Select
      data-testid="publish-status"
      onValueChange={setPublishedStatus}
      defaultValue={publishedStatus}
    >
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          {iteratedData?.map((item) => (
            <SelectItem className="font-medium text-sm" key={item} value={item?.toUpperCase()}>
              <span className="font-medium text-sm">{item.replace(/_/g, " ")}</span>
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  )
}

CustomSelect.propTypes = {
  publishedStatus: PropTypes.string,
  setPublishedStatus: PropTypes.func,
  placeholder: PropTypes.string,
  iteratedData: PropTypes.arrayOf([PropTypes.string]),
}

const JDSelect = ({
  publishedStatus,
  setPublishedStatus,
  placeholder = "Select publish status",
  notes,
  setNotes,
}) => {
  const handleNotesChange = (e) => {
    if (setNotes && e && e.target) {
      setNotes(e.target.value)
    }
  }
  return (
    <div className="flex flex-col gap-2">
      <Select
        data-testid="publish-status"
        onValueChange={setPublishedStatus}
        defaultValue={publishedStatus}
        className="w-full"
      >
        <SelectTrigger>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            {applicationStatus?.map((item) => (
              <SelectItem className="font-medium text-sm" key={item} value={item?.toUpperCase()}>
                <span className="font-medium text-sm">{item}</span>
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
      <div className="flex flex-col mt-2 mb-1 ">
        <h6 className="  text-black ">Notes</h6>
        <Textarea
          placeholder="Enter notes"
          className="w-full p-2 border border-gray-200 rounded-md resize-none h-40"
          value={notes || ""}
          onChange={handleNotesChange}
        />
      </div>
    </div>
  )
}

JDSelect.propTypes = {
  publishedStatus: PropTypes.string,
  setPublishedStatus: PropTypes.func,
  placeholder: PropTypes.string,
  notes: PropTypes.string,
  setNotes: PropTypes.func,
}

export { CustomSelect, JDSelect }
