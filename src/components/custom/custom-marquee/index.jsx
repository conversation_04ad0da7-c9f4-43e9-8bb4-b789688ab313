import { Marquee } from "@/components/magicui/marquee"
import { cn } from "@/lib/utils"
import PropTypes from "prop-types"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

const ReviewCard = ({ img, username, body, trainer }) => {
  console.log("__review", trainer)
  return (
    <figure
      className={cn(
        "relative h-full w-64 cursor-pointer overflow-hidden rounded-xl border p-4",
        // light styles
        "border-gray-950/[.1] bg-white hover:bg-gray-950/[.05]",
        // dark styles
        "dark:border-gray-50/[.1] dark:bg-gray-50/[.10] dark:hover:bg-gray-50/[.15]"
      )}
    >
      <div className="flex flex-row items-center gap-2">
        <Avatar>
          <AvatarImage src={img} alt="trainer-img" />
          <AvatarFallback>{trainer?.slice(0, 2)}</AvatarFallback>
        </Avatar>
        <div className="flex flex-col">
          <figcaption className="text-sm font-medium dark:text-white">{trainer}</figcaption>
          <p className="text-xs font-medium dark:text-white/40">{username}</p>
        </div>
      </div>
      <blockquote className="mt-2 text-sm">{body}</blockquote>
    </figure>
  )
}

ReviewCard.propTypes = {
  img: PropTypes.string,
  trainer: PropTypes.string,
  username: PropTypes.string,
  body: PropTypes.node,
}

const MarqueeCard = ({ data }) => {
  console.log("___data", data)
  return (
    <div className="relative w-full max-w-screen overflow-hidden flex flex-col items-center justify-center">
      <Marquee pauseOnHover className="w-full max-w-screen [--duration:20s]">
        {data?.map((review) => (
          <ReviewCard key={review} trainer={review} {...review} />
        ))}
      </Marquee>
      <div className="pointer-events-none absolute inset-y-0 left-0 w-1/4 bg-gradient-to-r from-background" />
      <div className="pointer-events-none absolute inset-y-0 right-0 w-1/4 bg-gradient-to-l from-background" />
    </div>
  )
}

MarqueeCard.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      trainer: PropTypes.string.isRequired,
    })
  ),
}

export default MarqueeCard
