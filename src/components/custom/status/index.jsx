import { useState } from "react"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import PropTypes from "prop-types"

const StatusSelect = ({ value = "Pending", onChange, disabled = false, selectedStatus }) => {
  const [showDialog, setShowDialog] = useState(false)
  const [feedback, setFeedback] = useState("")
  const [tempStatus, setTempStatus] = useState(selectedStatus ?? value?.toUpperCase())

  const statuses = [
    {
      label: "Pending",
      color: "bg-amber-50 hover:bg-amber-100 text-amber-700 border-amber-200",
      dotColor: "bg-amber-500",
    },
    {
      label: "Completed",
      color: "bg-green-50 hover:bg-green-100 text-green-700 border-green-200",
      dotColor: "bg-green-500",
    },
    {
      label: "Approved",
      color: "bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200",
      dotColor: "bg-blue-500",
    },
    {
      label: "Reject",
      color: "bg-red-50 hover:bg-red-100 text-red-700 border-red-200",
      dotColor: "bg-red-500",
    },
  ]

  const handleStatusChange = (newStatus) => {
    if (newStatus === "Completed") {
      setTempStatus(newStatus)
      setShowDialog(true)
    } else {
      onChange?.(newStatus)
    }
  }

  const handleSubmitFeedback = () => {
    onChange?.(tempStatus)
    setShowDialog(false)
    setFeedback("")
  }

  const currentStatus = statuses.find((s) => s.label === value) || statuses[0]

  return (
    <>
      <div className="relative w-32">
        <Select value={value} onValueChange={handleStatusChange} disabled={disabled}>
          <SelectTrigger
            className={`
              relative border rounded-md transition-all duration-200
              ${currentStatus.color}
              ${disabled ? "opacity-60 cursor-not-allowed" : "cursor-pointer"}
            `}
          >
            <div className="flex items-center w-full">
              <div
                className={`w-2 h-2 rounded-full ${currentStatus.dotColor} ${
                  value === "Pending" ? "animate-ping" : ""
                } mr-2`}
              />
              <SelectValue placeholder="Pending" />
            </div>
          </SelectTrigger>
          <SelectContent>
            {statuses.map((status) => (
              <SelectItem key={status.label} value={status.label}>
                {status.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Provide Feedback</DialogTitle>
            <DialogDescription>
              Please provide feedback before marking as completed
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder="Enter your feedback here..."
              className="min-h-[100px]"
            />
          </div>
          <DialogFooter>
            <Button variant="secondary" onClick={() => setShowDialog(false)}>
              Cancel
            </Button>
            <Button variant="primary" onClick={handleSubmitFeedback} disabled={!feedback.trim()}>
              Submit
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

StatusSelect.propTypes = {
  value: PropTypes.string,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
}

export default StatusSelect
