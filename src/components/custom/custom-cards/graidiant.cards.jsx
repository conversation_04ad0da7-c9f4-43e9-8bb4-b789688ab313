import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { USER_ROLES } from "@/utils/constants"
import { motion } from "framer-motion"
import {
  CalendarDays,
  ChevronRight,
  Tag,
  User
} from "lucide-react"
import moment from "moment"
import PropTypes from "prop-types"
import { useState } from "react"
import { ActionCell } from "../cutsom-table/table-cells"

const GradientCard = ({
  onCardClick,
  title,
  modified_at,
  postedBy,
  message,
  maxLength = 80,
  onDelete,
  publish_status,
  id,
  index,
  userRole,
  onEditStatus,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  
  const truncate = (str, length) => {
    if (!str) return ""
    return str.length > length ? `${str.substring(0, length)}...` : str
  }
  
  const badgeConfig = {
    PUBLISHED: {
      className: "bg-green-50 text-green-600 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800",
      icon: <Tag className="w-3 h-3 mr-1" />
    },
    ARCHIVED: {
      className: "bg-gray-50 text-gray-600 border-gray-200 dark:bg-gray-900/40 dark:text-gray-400 dark:border-gray-700",
      icon: <Tag className="w-3 h-3 mr-1" />
    },
    UNLISTED: {
      className: "bg-yellow-50 text-yellow-600 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800",
      icon: <Tag className="w-3 h-3 mr-1" />
    },
    DRAFT: {
      className: "bg-blue-50 text-blue-600 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800",
      icon: <Tag className="w-3 h-3 mr-1" />
    }
  };

  const formattedDate = moment(modified_at).format("MMM D, YYYY");
  const timeAgo = moment(modified_at).fromNow();
  
  const hasLongMessage = message && message.length > maxLength;
  
  return (
    <TooltipProvider>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: Math.min(index * 0.1, 0.5), duration: 0.3 }}
        whileHover={{ y: -4 }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        className="will-change-transform"
      >
        <Card
          role="button"
          tabIndex={0}
          onClick={() => onCardClick({ title, id, publish_status, message })}
          onKeyPress={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              onCardClick({ title, id, publish_status, message });
            }
          }}
          className={`
            group relative overflow-hidden
            py-2 rounded-xl bg-white dark:bg-gray-800 cursor-pointer 
            border-l-4 ${isHovered ? 'border-l-primary shadow-lg' : 'border-l-violet-100 dark:border-l-violet-900'}
            transition-all duration-300 hover:shadow-lg
          `}
        >
          {/* Decorative elements */}
          <div className="absolute top-0 right-0 w-full h-1 bg-gradient-to-r from-transparent via-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
          <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-bl from-violet-50/30 dark:from-violet-900/10 to-transparent rounded-full -translate-y-20 translate-x-20 opacity-0 group-hover:opacity-100 transition-all duration-500" />

          <div className="p-4 space-y-4 relative z-10">
            {/* Header Section */}
            <div className="flex justify-between items-start gap-4">
              <div>
                <h3 className="font-semibold text-lg text-gray-800 dark:text-gray-100 group-hover:text-primary transition-colors">
                  {title}
                </h3>
                <Badge 
                  variant="outline" 
                  className={`mt-1.5 inline-flex items-center ${badgeConfig[publish_status]?.className || "bg-gray-50 text-gray-600 border-gray-200"}`}
                >
                  {badgeConfig[publish_status]?.icon}
                  {publish_status}
                </Badge>
              </div>

              {userRole !== USER_ROLES.STUDENT && (
                <div className="z-50">
                  <ActionCell
                    isEdit
                    isDelete
                    label2="Change Status"
                    onEdit={() => onEditStatus(id, publish_status)}
                    onDelete={() => onDelete(id)}
                  />
                </div>
              )}
            </div>

            {/* Message Content */}
            <div className="min-h-14">
              <p className="text-gray-600 dark:text-gray-300 font-medium text-sm leading-relaxed">
                {truncate(message, maxLength)}
                {hasLongMessage && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="text-primary cursor-help">...</span>
                    </TooltipTrigger>
                    <TooltipContent side="bottom" className="max-w-sm">
                      {message}
                    </TooltipContent>
                  </Tooltip>
                )}
              </p>
            </div>

            <Separator className="bg-violet-100/50 dark:bg-violet-900/20" />

            {/* Footer Section */}
            <div className="flex flex-wrap justify-between items-center pt-1 gap-2">
              <div className="flex gap-4">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-1 text-gray-500 dark:text-gray-400 text-xs">
                      <User className="w-3.5 h-3.5 text-primary" />
                      <span className="text-violet-600 dark:text-violet-400 font-medium">{postedBy}</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">Posted by</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-1 text-gray-500 dark:text-gray-400 text-xs">
                      <CalendarDays className="w-3.5 h-3.5 text-primary" />
                      <time className="text-xs text-primary dark:text-primary/80 font-medium">
                        {formattedDate}
                      </time>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">{timeAgo}</TooltipContent>
                </Tooltip>
              </div>

              <motion.div
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: isHovered ? 1 : 0, x: isHovered ? 0 : -10 }}
                className="flex items-center gap-1 text-primary dark:text-primary/90 text-xs font-medium transition-all"
              >
                View Details
                <ChevronRight className="w-4 h-4" />
              </motion.div>
            </div>
          </div>

          {/* Hover Effect Overlay */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-l from-violet-100/20 dark:from-violet-900/20 to-transparent opacity-0 group-hover:opacity-100"
            initial={{ opacity: 0 }}
            animate={{ opacity: isHovered ? 1 : 0 }}
            transition={{ duration: 0.3 }}
          />
        </Card>
      </motion.div>
    </TooltipProvider>
  )
}

GradientCard.propTypes = {
  onCardClick: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  modified_at: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]).isRequired,
  postedBy: PropTypes.string.isRequired,
  message: PropTypes.string.isRequired,
  maxLength: PropTypes.number,
  onDelete: PropTypes.func,
  publish_status: PropTypes.string,
  id: PropTypes.number,
  index: PropTypes.number,
  userRole: PropTypes.string,
  onEditStatus: PropTypes.func,
}

export default GradientCard