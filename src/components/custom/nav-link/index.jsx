import { NavLink } from "react-router-dom"
import PropTypes from "prop-types"

const Navigation = ({ to, content, icon, isExpanded }) => {
  return (
    <NavLink
      to={to}
      end
      className={({ isActive }) =>
        `flex items-center  p-[.4rem]  rounded ${isActive ? "bg-[hsl(var(--background))] text-[hsl(var(--primary))]" : "text-gray-700"}`
      }
    >
      {icon}
      {isExpanded === "expanded" ? (
        <span className="text-xs ms-2 lg:text-sm  font-medium">{content}</span>
      ) : (
        ""
      )}
    </NavLink>
  )
}

Navigation.defaultProps = {
  to: "/",
}

Navigation.propTypes = {
  to: PropTypes.string,
  content: PropTypes.string.isRequired,
  icon: PropTypes.element.isRequired,
}

export default Navigation
