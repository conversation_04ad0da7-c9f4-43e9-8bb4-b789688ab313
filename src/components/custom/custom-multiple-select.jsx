import { Check, ChevronsUpDown, Plus, Search, X } from "lucide-react"
import { useEffect, useRef, useState } from "react"
import { Button } from "../ui/button"
import { Input } from "../ui/input"

const CustomMultipleSelect = ({
  iteratedList,
  editValue,
  reRender,
  setRerender,
  onChangeValue,
  setValue,
  watch,
  name,
  placeholder = "Select items...",
}) => {
  const [open, setOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const dropdownRef = useRef(null)

  // Get selected values from the form state
  const selectedValues = watch(`course_data.${name}`) || []

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setOpen(false)
      }
    }
    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const handleSelect = (item) => {
    const currentValues = watch(`course_data.${name}`) || []

    // Add or remove based on selection
    const updatedValues = currentValues.includes(item)
      ? currentValues.filter((v) => v !== item)
      : [...currentValues, item]

    // Normalize:
    // - Map number items (existing) to string labels to check against strings
    // - Remove duplicates with same name (e.g., 'Google' string vs its ID)

    const selectedLabels = new Set()
    const deduplicated = []

    for (const val of updatedValues) {
      const label =
        typeof val === "number"
          ? iteratedList?.find((i) => i.value === val)?.label?.toLowerCase()
          : val?.toLowerCase()

      if (!label) continue
      if (!selectedLabels.has(label)) {
        selectedLabels.add(label)
        deduplicated.push(val)
      }
    }

    // Separate into IDs and new strings
    const ids = [...new Set(deduplicated.filter((v) => typeof v === "number"))]
    const newStrings = [
      ...new Set(deduplicated.filter((v) => typeof v === "string").map((s) => s.trim())),
    ]

    setValue(`course_data.${name}_ids`, ids)
    setValue(`course_data.new_${name}`, newStrings)
    setValue(`course_data.${name}`, deduplicated)

    onChangeValue?.(deduplicated)
    setSearchTerm("")
  }

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault()
      const trimmedSearch = searchTerm.trim()
      if (trimmedSearch !== "" && !selectedValues.includes(trimmedSearch)) {
        handleSelect(trimmedSearch)
      }
    }
  }

  const handleButtonClick = (e) => {
    e.preventDefault()
    setOpen(true)
  }

  const removeValue = (valueToRemove, e) => {
    e.preventDefault()
    e.stopPropagation()
    handleSelect(valueToRemove)
  }

  const getDisplayLabel = (value) => {
    if (typeof value === "number") {
      const item = iteratedList?.find((item) => item.value === value)
      return item ? item.label : value
    }
    return value
  }

  const filteredItems =
    iteratedList?.filter((item) => item.label.toLowerCase().includes(searchTerm.toLowerCase())) ||
    []

  const showAddNew =
    searchTerm.trim() !== "" &&
    !filteredItems.some((item) => item.label.toLowerCase() === searchTerm.toLowerCase()) &&
    !selectedValues.includes(searchTerm.trim())

  useEffect(() => {
    if (editValue && iteratedList && reRender) {
      const editLabelSet = new Set(editValue?.map((label) => label?.toLowerCase()))
      const matchingItems = iteratedList?.filter((item) =>
        editLabelSet.has(item.label?.toLowerCase())
      )

      const editIds = matchingItems.map((item) => item.value)

      const currentValues = watch(`course_data.${name}`) || []
      const allValues = [...currentValues, ...editIds]

      // Deduplicate by normalized label
      const labelSet = new Set()
      const deduplicated = []

      for (const val of allValues) {
        const label =
          typeof val === "number"
            ? iteratedList.find((i) => i.value === val)?.label?.toLowerCase()
            : val.toLowerCase()

        if (label && !labelSet.has(label)) {
          labelSet.add(label)
          deduplicated.push(val)
        }
      }

      const ids = [...new Set(deduplicated.filter((v) => typeof v === "number"))]
      const newStrings = [
        ...new Set(deduplicated.filter((v) => typeof v === "string").map((s) => s.trim())),
      ]

      setValue(`course_data.${name}_ids`, ids)
      setValue(`course_data.new_${name}`, newStrings)
      setValue(`course_data.${name}`, deduplicated)
      onChangeValue?.(deduplicated)
      setRerender(false)
    }
  }, [reRender, editValue, iteratedList, watch, name, setValue, onChangeValue, setRerender])

  // Generate unique keys for rendering
  const getUniqueKey = (value, index) => {
    if (typeof value === "number") {
      return `id-${value}`
    }
    return `string-${value}-${index}`
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <Button
        type="button"
        className="w-full flex items-center px-3 py-1 text-sm border rounded-md bg-white hover:bg-gray-50 cursor-text"
        onClick={handleButtonClick}
      >
        <div className="flex flex-wrap gap-1 flex-1">
          {selectedValues.map((value, index) => (
            <div
              key={getUniqueKey(value, index)}
              className="flex items-center gap-1 rounded-md bg-gray-100 px-2 text-xs"
            >
              <span className="text-sm font-medium">{getDisplayLabel(value)}</span>
              <Button
                type="button"
                onClick={(e) => removeValue(value, e)}
                className="hover:bg-gray-200 rounded-full p-0 h-4 w-4 flex items-center justify-center"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ))}
          <Input
            type="text"
            className="border-0 bg-transparent text-sm outline-none placeholder:text-gray-400 flex-1 min-w-[50px]"
            placeholder={selectedValues.length ? "" : placeholder}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={handleKeyDown}
            size="sm"
          />
        </div>
        <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50 ml-2" />
      </Button>

      {open && (
        <div className="absolute mt-1 w-full rounded-md border bg-white shadow-lg z-50">
          <div className="flex items-center border-b px-3 py-2">
            <Search className="h-4 w-4 text-gray-400" />
            <Input
              type="text"
              className="w-full border-0 bg-transparent text-sm outline-none placeholder:text-gray-400"
              placeholder={`Search or add new ${name}...`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={handleKeyDown}
            />
          </div>
          <div className="max-h-60 overflow-auto">
            {filteredItems.map((item) => (
              <Button
                type="button"
                key={`option-${item.value}`}
                className="flex items-center w-full justify-start px-3 py-2 text-sm hover:bg-gray-100 cursor-pointer"
                onClick={(e) => {
                  e.preventDefault()
                  handleSelect(item.value)
                }}
              >
                <div className="w-4 h-4 mr-2">
                  {selectedValues.includes(item.value) && (
                    <div className="h-4 w-4 rounded-sm bg-blue-500 text-white flex items-center justify-center">
                      <Check className="h-3 w-3" />
                    </div>
                  )}
                </div>
                {item.label}
              </Button>
            ))}
            {showAddNew && (
              <Button
                type="button"
                key={`add-new-${searchTerm}`}
                className="flex items-center w-full justify-start px-3 py-2 text-sm hover:bg-gray-100 cursor-pointer text-blue-600"
                onClick={(e) => {
                  e.preventDefault()
                  handleSelect(searchTerm.trim())
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add "{searchTerm}" as new {name}
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default CustomMultipleSelect
