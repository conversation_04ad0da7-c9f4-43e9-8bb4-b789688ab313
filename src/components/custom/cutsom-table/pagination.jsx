import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeftIcon, ChevronRightIcon, Loader2 } from "lucide-react"
import PropTypes from "prop-types"


function DataTablePagination({
  table,
  found,
  pageCount,
  pageSizeOptions = [10, 20, 30, 40, 50],
  pageName = "Records",
  isLoading,
}) {
  return (
    <div className="flex w-full flex-col-reverse items-center justify-between text-muted-foreground overflow-auto px-1 sm:flex-row sm:gap-6">
      {/* Page size selection (can be enabled if needed) */}
      <div className="whitespace-nowrap text-sm text-muted-foreground">
        {/* <div className="flex items-center gap-4">
          <p className="whitespace-nowrap text-sm font-medium">Rows per page</p>
          <Select
            value={`${table?.getState().pagination?.pageSize}`}
            onValueChange={(value) => {
              table.setPageSize(Number(value))
            }}
            data-testid="rows_per_page_select"
          >
            <SelectTrigger className="h-8 w-[4.5rem]" data-testid="rows_per_page_trigger">
              <SelectValue
                data-testid="rows_per_page_value"
                placeholder={table?.getState().pagination?.pageSize}
              />
            </SelectTrigger>
            <SelectContent side="top">
              {pageSizeOptions.map((pageSize) => (
                <SelectItem data-testid="rows_per_page_item" key={pageSize} value={`${pageSize}`}>
                  {pageSize}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div> */}
      </div>

      <div className="flex items-center text-muted-foreground sm:flex-row gap-1 lg:gap-2">
        {isLoading ? (
          <div className="flex items-center gap-2">
            <Loader2 className="animate-spin h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Loading {pageName}...</span>
          </div>
        ) : (
          <>
            <p data-testid="page_found" className="text-sm font-medium space-x-1">
              Found {found} {pageName}
            </p>
            {found > 0 && (
              <>
                <p data-testid="page_count" className="text-sm font-medium">
                  | &nbsp;Page {table.getState().pagination.pageIndex + 1} of {pageCount}
                </p>

                <div className="flex items-center space-x-2">
                  <Button
                    aria-label="Go to previous page"
                    variant="ghost"
                    className="p-0 m-0 h-6"
                    onClick={() => table?.previousPage()}
                    disabled={!table?.getCanPreviousPage()}
                    data-testid="previous_page_btn"
                  >
                    <ChevronLeftIcon size={17} />
                  </Button>
                  <Button
                    aria-label="Go to next page"
                    variant="ghost"
                    className="p-0 m-0 h-6"
                    onClick={() => table?.nextPage()}
                    disabled={!table?.getCanNextPage()}
                    data-testid="next_page_btn"
                  >
                    <ChevronRightIcon size={17} />
                  </Button>
                </div>
              </>
            )}
          </>
        )}
      </div>
    </div>
  )
}

DataTablePagination.propTypes = {
  table: PropTypes.shape({
    getFilteredSelectedRowModel: PropTypes.func.isRequired,
    getFilteredRowModel: PropTypes.func.isRequired,
    getState: PropTypes.func.isRequired,
    setPageSize: PropTypes.func.isRequired,
    getPageCount: PropTypes.func.isRequired,
    setPageIndex: PropTypes.func.isRequired,
    previousPage: PropTypes.func.isRequired,
    nextPage: PropTypes.func.isRequired,
    getCanPreviousPage: PropTypes.func.isRequired,
    getCanNextPage: PropTypes.func.isRequired,
  }).isRequired,
  found: PropTypes.number.isRequired,
  pageCount: PropTypes.number.isRequired,
  pageSizeOptions: PropTypes.arrayOf(PropTypes.number),
  pageName: PropTypes.string,
  isLoading: PropTypes.bool.isRequired,
}

export default DataTablePagination
