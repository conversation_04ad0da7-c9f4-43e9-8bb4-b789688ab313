import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { flexRender } from "@tanstack/react-table"
import { ScrollText } from "lucide-react"
import PropTypes from "prop-types"
import Loading<PERSON>pinner from "../../LoadingSpinner"
import DataTablePagination from "../pagination"

// Give our default column cell renderer editing superpowers!
// const defaultColumn = {
//   cell: ({ getValue, row: { index }, column: { id }, table }) => {
//     const initialValue = getValue()
//     // We need to keep and update the state of the cell normally
//     const [value, setValue] = React.useState(initialValue)

//     // When the input is blurred, we'll call our table meta's updateData function
//     const onBlur = () => {
//       table.options.meta?.updateData(index, id, value)
//     }

//     // If the initialValue is changed external, sync it up with our state
//     React.useEffect(() => {
//       setValue(initialValue)
//     }, [initialValue])

//     return <input value={value} onChange={(e) => setValue(e.target.value)} onBlur={onBlur} />
//   },
// }

// function useSkipper() {
//   const shouldSkipRef = React.useRef(true)
//   const shouldSkip = shouldSkipRef.current

//   // Wrap a function with this to skip a pagination reset temporarily
//   const skip = React.useCallback(() => {
//     shouldSkipRef.current = false
//   }, [])

//   React.useEffect(() => {
//     shouldSkipRef.current = true
//   })

//   return [shouldSkip, skip]
// }

const CellEditableTable = ({
  renderCellContent,
  table,
  found,
  pageCount,
  pagination,
  pageName,
  isLoading,
  notFoundPlaceholder = "No data available",
  columns,
}) => {
  // Function to track changes without immediately applying them

  const lastRow = (rowIndex) => {
    const rowCount = table.getRowModel().rows?.length ?? 0 // Default to 0 if undefined
    return rowIndex === rowCount - 1
  }

  return (
    <div className="p-2">
      <div className="flex items-center justify-between space-x-2 py-3">
        <DataTablePagination
          found={found}
          table={table}
          pagination={pagination}
          pageCount={pageCount}
          listName={pageName}
        />
      </div>
      <div className="rounded-[0.6rem] border  border-[#D9DBDE] overflow-hidden ">
        <ScrollArea className=" whitespace-nowrap rounded-md border">
          <Table table={table} className="w-full bg-white dark:bg-[#1E1E1E]">
            <TableHeader className="text-sm overflow-hidden h-12  dark:bg-[#141414] bg-[hsl(var(--background))] ">
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead className="border-b border-[#D9DBDE]" key={header.id}>
                        {header.isPlaceholder ? null : (
                          <div className="lg:min-w-[100px] flex items-center text-center font-medium dark:text-white text-black text-sm text-nowrap">
                            {flexRender(header.column.columnDef.header, header.getContext())}
                          </div>
                        )}
                      </TableHead>
                    )
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table?.getRowModel().rows?.length ? (
                table?.getRowModel().rows.map((row, rowIndex) => (
                  <TableRow
                    className={`
        h-[4rem]  
        ${rowIndex % 2 !== 0 ? "bg-[hsl(var(--background))] dark:bg-gray-800" : ""}
      `}
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell
                        key={cell.id}
                        className={`
  ${lastRow(rowIndex) ? "border-b border-[#D9DBDE]" : ""} 
  dark:text-[#CCCDD8]
`}
                      >
                        {renderCellContent(cell, row)}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow className="text-center ">
                  <TableCell colSpan={(columns?.length ?? 0) + 1} className="text-center ">
                    {isLoading ? (
                      <LoadingSpinner />
                    ) : (
                      <div className="flex  flex-col  items-center text-secondary justify-center w-full">
                        <ScrollText className="block w-16 h-16" />
                        <p className="text-base font-medium mt-5"> {notFoundPlaceholder}</p>
                      </div>
                    )}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </ScrollArea>
      </div>
    </div>
  )
}

CellEditableTable.propTypes = {
  isLoading: PropTypes.bool,
  pageName: PropTypes.string,
  renderCellContent: PropTypes.func.isRequired,
  found: PropTypes.number,
  pageCount: PropTypes.number,
  pagination: PropTypes.objectOf,
  table: PropTypes.objectOf,
  notFoundPlaceholder: PropTypes.string,
  columns: PropTypes.arrayOf,
  pendingChanges: PropTypes.shape({}),
  setPendingChanges: PropTypes.shape({}),
}

export default CellEditableTable

// function Filter({ column, table }) {
//   const firstValue = table.getPreFilteredRowModel().flatRows[0]?.getValue(column.id)

//   const columnFilterValue = column.getFilterValue()

//   return typeof firstValue === "number" ? (
//     <div className="flex space-x-2">
//       <input
//         type="number"
//         value={columnFilterValue?.[0] ?? ""}
//         onChange={(e) => column.setFilterValue((old) => [e.target.value, old?.[1]])}
//         placeholder="Min"
//         className="w-24 border shadow rounded"
//       />
//       <input
//         type="number"
//         value={columnFilterValue?.[1] ?? ""}
//         onChange={(e) => column.setFilterValue((old) => [old?.[0], e.target.value])}
//         placeholder="Max"
//         className="w-24 border shadow rounded"
//       />
//     </div>
//   ) : (
//     <input
//       type="text"
//       value={columnFilterValue ?? ""}
//       onChange={(e) => column.setFilterValue(e.target.value)}
//       placeholder="Search..."
//       className="w-36 border shadow rounded"
//     />
//   )
// }
