import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import PropTypes from "prop-types"
import { Check, X, Calendar, TentTree } from "lucide-react"
import { useEffect, useState } from "react"

const AttendanceCell = ({
  rowIndex,
  columnId,
  initialValue = "Present", // Default value set to "Present"
  pendingChanges,
  trackChange,
  isEditMode,
  id,
  student_id,
  attendanceValues = ["Present", "Absent", "Late"],
}) => {
  const getStatusIcon = (status) => {
    switch (status) {
      case "Present":
        return <Check className="mr-2 h-4 w-4 text-green-500" />
      case "Absent":
        return <X className="mr-2 h-4 w-4 text-red-500" />
      case "Late":
        return <Calendar className="mr-2 h-4 w-4 text-yellow-500" />
      case "Holiday":
        return <TentTree className="mr-2 h-4 w-4 text-blue-500" />
      default:
        return null
    }
  }

  const changeKey = `${rowIndex}|${columnId}`

  console.log("__initialValue", initialValue)

  // Ensure initial selection is set properly
  const [inputValue, setInputValue] = useState(pendingChanges[changeKey] ?? initialValue)

  // Effect to sync local state with external changes
  useEffect(() => {
    const valueToUse = pendingChanges[changeKey] ?? initialValue
    setInputValue(valueToUse)
  }, [initialValue, pendingChanges[changeKey]])

  // Handle change and update pending changes immediately
  const handleChange = (value, rowID, st_id) => {
    setInputValue(value)
    trackChange(rowIndex, columnId, value, rowID, st_id)
  }

  if (!isEditMode) {
    return <span>{inputValue}</span> // Ensure correct display of default
  }

  return (
    <Select
      data-testid="publish-status"
      // onValueChange={(e) => handleChange(e, id,student_id)}
      onValueChange={(e) => handleChange(e, id, student_id)}
      value={String(inputValue)}
    >
      <SelectTrigger className="w-full">
        <SelectValue placeholder="Select status" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          {attendanceValues.map((item) => (
            <SelectItem className="font-medium text-sm w-full" key={item} value={item}>
              <span className="font-medium text-sm flex items-center gap-x-2">
                {getStatusIcon(item)} {item}
              </span>
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  )
}

AttendanceCell.propTypes = {
  rowIndex: PropTypes.number,
  columnId: PropTypes.string,
  initialValue: PropTypes.string,
  trackChange: PropTypes.func,
  isEditMode: PropTypes.bool,
  id: PropTypes.number,
  student_id: PropTypes.number,
  pendingChanges: PropTypes.func,
  attendanceValues: PropTypes.arrayOf(PropTypes.objectOf),
}

export default AttendanceCell
