/* eslint-disable jsx-a11y/anchor-is-valid */
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { USER_ROLES } from "@/utils/constants"
import { badgeColor } from "@/utils/helper"
import { isPaidUserPropTypes } from "@/utils/props-types"
import {
  Clock2,
  EllipsisVertical,
  ExternalLink,
  File,
  MessageCircleMore,
  PencilLine,
  RotateCcw,
  SquareChartGantt,
  Trash2,
  Video,
} from "lucide-react"
import moment from "moment"
import PropTypes from "prop-types"
import { FaCheck } from "react-icons/fa"
import { RiFeedbackLine } from "react-icons/ri"
import { Link } from "react-router-dom"

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, TooltipTrigger } from "@/components/ui/tooltip"
import { warningToast } from "../toasts/tosters"

export const CvLinkCell = ({ type, onClick }) => {
  const isResume = type === "resume"

  return (
    <Button
      onClick={onClick}
      className="flex items-center justify-center   text-primary hover:text-primary active:text-primary gap-2 font-semibold text-xs"
      aria-label={isResume ? "View CV" : "View Questions"}
    >
      <ExternalLink className="h-5 w-5" size={14} />
      {isResume ? "View CV" : "View Questions"}
    </Button>
  )
}

CvLinkCell.propTypes = {
  type: PropTypes.oneOf(["resume", "questions"]).isRequired,
  onClick: PropTypes.func.isRequired,
}

export function LinkCell({ value, type, date, onClick, isPaidUser }) {
  if (!value) return null

  // const linkButton = ({ label }) => {
  //   return (
  //     <Button
  //       asChild // Makes the button behave like an `<a>` (if using ShadCN)
  //       className={`text-blue-500 flex gap-x-2 items-start text-sm font-medium
  //   ${isPaidUser?.paidUser ? "cursor-not-allowed text-gray-400" : "hover:text-blue-700 active:text-blue-900"}`}
  //       onClick={(e) => {
  //         if (isPaidUser?.paidUser) {
  //           e.preventDefault() // Prevent navigation for paid users
  //           return
  //         }
  //         e.stopPropagation()
  //         onClick()
  //       }}
  //       disabled={isPaidUser?.paidUser} // Only disables button styling, not navigation
  //     >
  //       <a href={isPaidUser?.paidUser ? "#" : value} target="_blank" rel="noopener noreferrer">
  //         Visit <ExternalLink size={15} />
  //       </a>
  //     </Button>
  //   )
  // }

  let content

  if (type?.toLowerCase().includes("live")) {
    content = (
      <Link
        to={value}
        className="flex items-center justify-start  text-sky-600 hover:text-600 gap-2 font-semibold text-xs"
        onClick={(e) => {
          e.stopPropagation()
          onClick()
        }}
        disabled={isPaidUser?.paidUser}
      >
        <div className="h-6 w-6 bg-yellow-200 rounded-sm flex items-center justify-start text-yellow-800">
          <Video className="animate-pulse" size={14} />
        </div>
        Live
      </Link>
    )
  } else if (type?.toLowerCase().includes("upcoming")) {
    // const time = value.match(/\d{1,2}:\d{2}\s?(AM|PM)?/i)?.[0] || "TBA"
    content = (
      <Link
        to={value}
        className="flex items-center justify-start text-sky-600 hover:text-indigo-600 gap-2 font-semibold text-xs"
        onClick={(e) => {
          e.stopPropagation()
          onClick()
        }}
        disabled={isPaidUser?.paidUser}
      >
        <div className="h-6 w-6 bg-slate-200 rounded-sm flex items-center justify-start text-slate-800">
          <Clock2 size={14} />
        </div>
        <div className="flex flex-col justify-start items-start">
          <p className="text-sm font-medium"> Scheduled at </p>
          <p className="text-sm font-medium text-slate-500">{date}</p>
        </div>
      </Link>
    )
  } else if (type?.toLowerCase().includes("completed")) {
    content = (
      <Link
        to={value}
        className="flex items-center justify-start  text-sky-600 hover:text-indigo-600 gap-2 font-semibold text-xs"
        onClick={(e) => {
          e.stopPropagation()
          onClick()
        }}
        disabled={isPaidUser?.paidUser}
      >
        <div className="h-6 w-6 bg-indigo-200 rounded-sm flex items-center justify-start text-indigo-800">
          <Video size={14} />
        </div>
        <div className="flex flex-col justify-start items-start">
          <p className="text-sm font-medium">View Recording</p>
          <p className="text-sm font-medium text-slate-500">{date}</p>
        </div>
      </Link>
    )
  } else if (type?.toLowerCase().includes("view-resume")) {
    content = (
      <Link
        to={value}
        className="flex items-center justify-start text-sky-600 hover:text-blue-700 active:text-blue-900 gap-2 font-semibold text-xs"
        onClick={(e) => {
          e.stopPropagation()
          onClick()
        }}
        disabled={isPaidUser?.paidUser}
      >
        <File className="h-6 w-6" size={14} />
        View Resume
      </Link>
    )
  } else if (type?.toLowerCase().includes("view-question")) {
    content = (
      <Button
        asChild // Makes the button behave like an `<a>` (if using ShadCN)
        className={`text-blue-500 flex gap-x-2 items-center justify-start text-sm font-medium 
    ${isPaidUser?.paidUser ? "cursor-not-allowed text-gray-400" : "hover:text-blue-700 active:text-blue-900"}`}
        onClick={(e) => {
          if (isPaidUser?.paidUser) {
            e.preventDefault() // Prevent navigation for paid users
            return
          }
          e.stopPropagation()
          onClick()
        }}
        disabled={isPaidUser?.paidUser} // Only disables button styling, not navigation
      >
        <a href={isPaidUser?.paidUser ? "#" : value} target="_blank" rel="noopener noreferrer">
          View Question <MessageCircleMore className="h-6 w-6" size={14} />
        </a>
      </Button>
    )
  } else {
    content = (
      <Button
        asChild // Makes the button behave like an `<a>` (if using ShadCN)
        className={`text-blue-500 flex gap-x-2 items-center justify-start text-sm font-medium 
    ${isPaidUser?.paidUser ? "cursor-not-allowed text-gray-400" : "hover:text-blue-700 active:text-blue-900"}`}
        onClick={(e) => {
          if (isPaidUser?.paidUser) {
            e.preventDefault() // Prevent navigation for paid users
            return
          }
          e.stopPropagation()
          onClick()
        }}
        disabled={isPaidUser?.paidUser} // Only disables button styling, not navigation
      >
        <a href={isPaidUser?.paidUser ? "#" : value} target="_blank" rel="noopener noreferrer">
          Visit <ExternalLink size={15} />
        </a>
      </Button>
    )
  }

  return <div>{content}</div>
}

LinkCell.propTypes = {
  value: PropTypes.string.isRequired,
  type: PropTypes.string,
  date: PropTypes.string,
  onClick: PropTypes.func,
  isPaidUser: isPaidUserPropTypes,
}

export function DateCell({ value }) {
  return <div className="whitespace-nowrap text-sm">{moment(value).format("MMMM, Do YYYY")}</div>
}

DateCell.propTypes = {
  value: PropTypes.string.isRequired,
}

export function UserAvatarCell({ name, email, imageUrl, isExpanded, isCircle }) {
  return (
    <div className="flex gap-x-3  font-medium items-center justify-start text-sm">
      <div
        className={`${isExpanded && "h-12 w-12"}  ${isCircle ? "rounded-full" : "rounded "} flex items-center justify-center bg-slate-300`}
      >
        <Avatar className={`h-10 w-10 ${isCircle ? "rounded-full" : "rounded"}`}>
          <AvatarImage src={imageUrl} alt="@shadcn" />
          <AvatarFallback className="bg-transparent">{name?.substring(0, 2)}</AvatarFallback>
        </Avatar>
      </div>
      <div className="text-start">
        <p className="font-medium text-sm mt-1">{name}</p>
        <p className="font-medium text-sm text-muted">{email}</p>
      </div>
    </div>
  )
}

UserAvatarCell.propTypes = {
  isExpanded: PropTypes.bool,
  isCircle: PropTypes.bool,
  email: PropTypes.string,
  name: PropTypes.string,
  imageUrl: PropTypes.string,
}

export const RenderTableData = ({ content, char = 30 }) => {
  const shouldTruncate = content?.length > char
  const truncatedText = shouldTruncate ? `${content.substring(0, char)}...` : content

  return shouldTruncate ? (
    <Tooltip>
      <TooltipTrigger asChild>
        <span className="inline-block font-medium text-sm cursor-default">{truncatedText}</span>
      </TooltipTrigger>
      <TooltipContent className="max-w-[300px] break-words">
        <p className="whitespace-pre-wrap">{content}</p>
      </TooltipContent>
    </Tooltip>
  ) : (
    <p className="font-medium text-sm">{content}</p>
  )
}

RenderTableData.propTypes = {
  content: PropTypes.string.isRequired,
  char: PropTypes.number.isRequired,
}

export function ActionCell({
  row,
  isView,
  isEdit,
  isDelete,
  isAddFeedback,
  feedBack,
  onView,
  onEdit,
  onFeedback,
  onDelete,
  label1,
  label2,
  label3,
  submit,
  label4 = "Delete",
  label5 = "status",
  isEvaluate_trainer,
  Evaluate_Trainer_label,
  onEvaluateTrainer,
  isDisabled,
}) {
  console.log("__isDisabled", isDisabled)
  return (
    <DropdownMenu className="rounded-full">
      <DropdownMenuTrigger onClick={(e) => e.stopPropagation()}>
        <EllipsisVertical size={18} />
      </DropdownMenuTrigger>
      <DropdownMenuContent forceMount className="rounded-[.8rem] ">
        {isView && (
          <DropdownMenuItem
            className="font-medium fon-sm"
            onClick={(e) => {
              e.stopPropagation()
              onView(row, "view")
            }}
          >
            <SquareChartGantt size={18} className="mr-2" /> {label1}
          </DropdownMenuItem>
        )}

        {isEdit && (
          <DropdownMenuItem
            className="font-medium fon-sm"
            onClick={(e) => {
              e.stopPropagation()
              onEdit(row, "edit")
              console.log("row", row)
            }}
          >
            <PencilLine size={18} className="mr-2" /> {label2}
          </DropdownMenuItem>
        )}
        {feedBack && (
          <DropdownMenuItem
            className="font-medium fon-sm"
            onClick={(e) => {
              e.stopPropagation()
              onView(row, "view")
            }}
          >
            <PencilLine size={18} className="mr-2" /> {label3}
          </DropdownMenuItem>
        )}

        {submit && (
          <DropdownMenuItem
            className="font-medium fon-sm"
            onClick={(e) => {
              e.stopPropagation()
              submit(row)
            }}
          >
            <FaCheck size={18} className="mr-2" /> {submit}
          </DropdownMenuItem>
        )}

        {isAddFeedback && (
          <DropdownMenuItem
            className="font-medium fon-sm"
            onClick={(e) => {
              e.stopPropagation()
              onFeedback(row)
            }}
          // disabled={isDisabled}
          >
            <MessageCircleMore size={18} className="mr-2" /> {label5}
          </DropdownMenuItem>
        )}
        {isDelete && (
          <div>
            <DropdownMenuItem
              className="text-red-500 font-medium fon-sm"
              onClick={(e) => {
                e.stopPropagation()
                onDelete(row)
              }}
            >
              <Trash2 size={18} className="mr-2" />
              {label4}
            </DropdownMenuItem>
          </div>
        )}

        {isEvaluate_trainer && (
          <div>
            <DropdownMenuItem
              className="font-medium fon-sm"
              onClick={(e) => {
                e.stopPropagation()
                onEvaluateTrainer(row)
              }}
            >
              <RiFeedbackLine size={18} className="mr-2" />
              {Evaluate_Trainer_label}
            </DropdownMenuItem>
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

ActionCell.propTypes = {
  isView: PropTypes.bool,
  isEdit: PropTypes.bool,
  isDelete: PropTypes.bool,
  isAddFeedback: PropTypes.bool,
  onView: PropTypes.func,
  onEdit: PropTypes.func,
  onDelete: PropTypes.func,
  onFeedback: PropTypes.func,
  row: PropTypes.objectOf.isRequired,
  label1: PropTypes.string,
  label2: PropTypes.string,
  label3: PropTypes.string,
  label4: PropTypes.string,
  label5: PropTypes.string,
  submit: PropTypes.string,
  feedBack: PropTypes.string,
  isEvaluate_trainer: PropTypes.bool,
  Evaluate_Trainer_label: PropTypes.string,
  onEvaluateTrainer: PropTypes.func,
  isDisabled: PropTypes.bool,
}

export const SubmissionButton = ({
  onSubmit,
  isSubmitted,
  data,
  userRole,

  submitedStatus,
  isDisabled,
  isPaidUser,
}) => {
  const handleSubmit = () => {
    if (submitedStatus === "SUBMITTED") {
      warningToast("The quiz has already been submitted", "")
      return
    }

    onSubmit(data)
  }
  return (
    <div>
      {userRole === USER_ROLES.TRAINER || userRole === USER_ROLES.ADMIN ? (
        <Button variant="secondary" size="xs" onClick={() => onSubmit(data)}>
          View{" "}
          <span className="h-4 w-4 text-xs font-semibold bg-[hsl(var(--primary)/.1)] rounded-full ">
            {" "}
            {data ?? 0}
          </span>
        </Button>
      ) : (
        <Button
          className={`${isDisabled && "pointer-events-none cursor-default opacity-50"}`}
          variant="primary"
          size="xs"
          onClick={handleSubmit}
          disabled={isDisabled || isPaidUser?.paidUser}
        >
          {isSubmitted ? (
            <div className="flex gap-x-1 items-center font-medium text-sm">
              <RotateCcw size={14} /> Re-Submit{" "}
            </div>
          ) : (
            <div className="flex gap-x-1 items-center font-medium text-sm">
              <File size={14} /> Submit
            </div>
          )}
        </Button>
      )}
    </div>
  )
}

SubmissionButton.propTypes = {
  onSubmit: PropTypes.func.isRequired,
  isSubmitted: PropTypes.bool,
  userRole: PropTypes.string,
  submitedStatus: PropTypes.string,
  data: PropTypes.number,
  isDisabled: PropTypes.bool,
  isPaidUser: isPaidUserPropTypes,
}

export const StatusUpdationCell = ({ value, key }) => {
  return (
    <div key={key} className="flex items-center gap-x-2">
      <Badge className={`${badgeColor(value)} rounded-sm ml-2`}>
        {value
          ? value.charAt(0).toUpperCase() + value.replace("_", " ").slice(1).toLowerCase()
          : ""}
      </Badge>
    </div>
  )
}

StatusUpdationCell.propTypes = {
  value: PropTypes.string,
  key: PropTypes.number,
}
