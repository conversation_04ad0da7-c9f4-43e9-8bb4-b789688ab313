import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

import { flexRender } from "@tanstack/react-table"
import { ScrollText } from "lucide-react"
import PropTypes from "prop-types"
import LoadingSpinner from "../LoadingSpinner"
import DataTablePagination from "./pagination"

function DataTable({
  table,
  found,
  columns,
  height,
  pageName,
  pageCount,
  isLoading,
  onRowClick,
  renderCellContent,
  notFoundPlaceholder,
  pageSizeOptions = [10, 20, 30, 40, 50],
  isCheckbox,
}) {
  console.log("_table_cell", table?.getRowModel().rows)

  const notFoundUI = () => {
    return (
      <TableRow className="text-center ">
        <TableCell colSpan={(columns?.length ?? 0) + 1} className="text-center ">
          {isLoading ? (
            <LoadingSpinner />
          ) : (
            <div className="flex  flex-col  items-center text-secondary justify-center w-full">
              <ScrollText className="block w-16 h-16" />
              <p className="text-base font-medium mt-5"> {notFoundPlaceholder}</p>
            </div>
          )}
        </TableCell>
      </TableRow>
    )
  }
  return (
    <div>
      <div className="flex items-center justify-between space-x-2 py-3">
        <DataTablePagination
          table={table}
          found={found}
          pageCount={pageCount}
          pageName={pageName}
          pageSizeOptions={pageSizeOptions}
          isLoading={isLoading}
        />
      </div>

      <div className="rounded-[0.6rem] border  border-[#D9DBDE] overflow-hidden ">
        {/* <div className={`${height} overflow-y-auto`}> */}
        <ScrollArea className={`${height} whitespace-nowrap rounded-md border`}>
          <Table className="w-full bg-white dark:bg-[#1E1E1E]">
            <TableHeader className="text-sm overflow-hidden h-12  dark:bg-[#141414] bg-[hsl(var(--background))]">
              {table &&
                table?.getHeaderGroups()?.map((headerGroup) => (
                  <TableRow key={headerGroup.id} className="">
                    {headerGroup.headers.map((header) => (
                      <TableHead className="border-b border-[#D9DBDE]" key={header.id}>
                        {header.isPlaceholder ? null : (
                          <div className="lg:min-w-[100px] flex items-center text-center font-medium dark:text-white text-black text-sm text-nowrap">
                            {flexRender(header.column.columnDef.header, header.getContext())}
                          </div>
                        )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
            </TableHeader>
            {isCheckbox ? (
              <TableBody>
                {table?.getRowModel().rows?.length
                  ? table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      onClick={() => onRowClick && onRowClick(row)}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>{renderCellContent(cell, row)}</TableCell>
                      ))}
                    </TableRow>
                  ))
                  : notFoundUI()}
              </TableBody>
            ) : (
              <TableBody>
                {table?.getRowModel().rows?.length
                  ? table?.getRowModel().rows.map((row, rowIndex) => (
                    <TableRow
                      className={`
        h-[4rem]  
        ${rowIndex % 2 !== 0 ? "bg-[hsl(var(--background))] dark:bg-gray-800" : ""}
      `}
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      onClick={() => onRowClick && onRowClick(row)}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell
                          key={cell.id}
                          className={`
            ${rowIndex === table.getRowModel().rows.length - 1 ? "" : "border-b border-[#D9DBDE]"} 
            dark:text-[#CCCDD8]
          `}
                        >
                          {renderCellContent(cell, row)}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                  : notFoundUI()}
              </TableBody>
            )}
          </Table>
        </ScrollArea>
        {/* </div> */}
      </div>
    </div>
  )
}
DataTable.propTypes = {
  isLoading: PropTypes.bool,
  pageName: PropTypes.string,
  renderCellContent: PropTypes.func.isRequired,
  found: PropTypes.number,
  pageCount: PropTypes.number,
  table: PropTypes.objectOf,
  onRowClick: PropTypes.func,
  notFoundPlaceholder: PropTypes.func,
  columns: PropTypes.arrayOf,
  pageSizeOptions: PropTypes.arrayOf(PropTypes.number),
  isCheckbox: PropTypes.bool,
}

export default DataTable
