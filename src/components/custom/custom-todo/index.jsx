import "./custom-todo.css"
import { ScrollArea } from "@/components/ui/scroll-area"
import PropTypes from "prop-types"
import TodoItem from "./todo-item.ui"

function CustomTodo({ classes, onNavigate, items, name }) {
  return (
    <ScrollArea className={`todo-list pr-3 ${classes}`}>
      {items.map((task) => (
        <TodoItem
          key={task.id}
          task={{
            id: task.id,
            text: task.title,
            course: task.courseName,
            completed: false,

          }}
          name={name}
          toggleCompleted=""
          onNavigate={onNavigate}
        />
      ))}
    </ScrollArea>
  )
}

CustomTodo.propTypes = {
  classes: PropTypes.string,
  onNavigate: PropTypes.func.isRequired,
  items: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      title: PropTypes.string.isRequired,
    })
  ).isRequired,
}

export default CustomTodo
