import PropTypes from "prop-types"
import moment from "moment"
import { Card } from "@/components/ui/card"

function TodoItem({ task, onNavigate, name }) {
  console.log("NanoCoursesOverview_aggregatedData", task, name)

  const formattedDate = moment(new Date()).format("ll")

  return (
    <Card
      className="p-4 mb-4 cursor-pointer transition hover:shadow-md"
      onClick={() => onNavigate(task)}
      role="button"
    >
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
        {/* Task Text & Date (Left Side) */}
        <div className="flex flex-col gap-1">
          <p className="text-sm font-medium text-slate-800">{task.text}</p>

          {name === "Nano courses" && (
            <span className="text-xs text-muted-foreground">
              Date: {formattedDate}
            </span>
          )}
        </div>

        {/* Right Side Info */}
        {name === "Nano courses" ? (
          <div className="text-xs text-muted-foreground sm:text-right">
            Course:{" "}
            <span className="  text-primary text-xs">
              {task?.course}
            </span>
          </div>
        ) : (
          <div className="text-xs text-muted-foreground sm:text-right">
            Date: {formattedDate}
          </div>
        )}
      </div>
    </Card>
  )
}

TodoItem.propTypes = {
  task: PropTypes.shape({
    text: PropTypes.string.isRequired,
    completed: PropTypes.bool,
    course: PropTypes.string,
  }).isRequired,
  onNavigate: PropTypes.func.isRequired,
  name: PropTypes.string,
}

export default TodoItem
