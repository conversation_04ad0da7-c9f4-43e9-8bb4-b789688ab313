import { useEffect, useRef, useState } from "react"

const VideoRendering = () => {
  const videoRef = useRef(null)
  const trackIntervalRef = useRef(null)
  const [isPlaying, setIsPlaying] = useState(false)

  const getLastWatchedPosition = async () => {
    try {
      const response = await fetch(
        "https://training10xapi.10xscale.ai/course-service/v1/courses/2/modules/2/resources?resource_id=1&limit=10&offset=0&sort_field=modified_at&order_type=desc"
      )
      const result = await response.json()
      console.log(result.data?.[0]?.last_position) // Log the full response to debug
      return result.data?.[0]?.last_position || 0
    } catch (error) {
      console.error("Error fetching last watched position:", error)
      return 0
    }
  }

  const trackProgress = () => {
    if (videoRef.current) {
      fetch("https://training10xapi.10xscale.ai/course-service/v1/track-video/1", {
        method: "POST",
        body: JSON.stringify({ watched_seconds: videoRef.current.currentTime }),
        headers: { "Content-Type": "application/json" },
      }).catch((error) => console.error("Error tracking progress:", error))
    }
  }

  const handlePlay = () => {
    setIsPlaying(true)
    trackIntervalRef.current = setInterval(trackProgress, 10000) // 10 seconds
  }

  const handlePause = () => {
    setIsPlaying(false)
    clearInterval(trackIntervalRef.current)
  }

  const handleEnded = () => {
    setIsPlaying(false)
    clearInterval(trackIntervalRef.current)
  }

  useEffect(() => {
    const initializeVideo = async () => {
      try {
        const lastWatchedPosition = await getLastWatchedPosition()

        if (videoRef.current) {
          // Set video source
          videoRef.current.src =
            "https://training10xapi.10xscale.ai/v1/video-stream?cloud_path=12321"

          // Set the last watched position once metadata is loaded
          videoRef.current.addEventListener(
            "loadedmetadata",
            () => {
              videoRef.current.currentTime = lastWatchedPosition
            },
            { once: true }
          )
        }
      } catch (error) {
        console.error("Error initializing video:", error)
      }
    }

    initializeVideo()

    return () => {
      // Clean up interval on component unmount
      if (trackIntervalRef.current) {
        clearInterval(trackIntervalRef.current)
      }
    }
  }, [])

  return (
    <>
      <h2>Streaming Video</h2>
      <video
        id="video-player"
        ref={videoRef}
        width="720"
        height="480"
        controls
        onPlay={handlePlay}
        onPause={handlePause}
        onEnded={handleEnded}
      >
        <track
          kind="captions"
          src="http://localhost:8081/v1/captions?cloud_path=12321"
          srcLang="en"
          label="English"
          default
        />
        Your browser does not support the video tag.
      </video>
    </>
  )
}

export default VideoRendering
