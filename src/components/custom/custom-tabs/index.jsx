import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import PropTypes from "prop-types"
import { useDispatch } from "react-redux"

function CustomTabs({
  tabs,
  defaultTab = "",
  size = "w-[400px]",
  setActiveTab,
  activeTab,
  tabsListClassName = "",
}) {
  const dispatch = useDispatch()

  return (
    <Tabs
      defaultValue={defaultTab}
      value={activeTab}
      className={`${size}`}
      onValueChange={(val) => dispatch(setActiveTab(val))}
    >
      <TabsList
        className={`py-2 flex gap-x-6 w-full gap-y-4 justify-between h-auto bg-white rounded-[.6rem] mb-6 bg:border-[hsl(var(--border))]  text-xl content-center flex-wrap ${tabsListClassName}`}
        style={{ gridTemplateColumns: `repeat(${tabs.length}, 1fr)` }}
      >
        {tabs?.map((tab) => (
          <TabsTrigger
            key={tab?.id}
            value={tab?.id}
            className={` h-10 items-center ${activeTab === tab.id ? "dark:data-[state=active]:text-[hsl(var(--primary))] dar:data-[state=active]:bg-[hsl(var(--background))] data-[state=active]:bg-[hsl(var(--background))]  data-[state=active]:text-[hsl(var(--primary))]  button" : ""}`}
          >
            {tab?.icon} {tab?.label}
          </TabsTrigger>
        ))}
      </TabsList>

      {tabs?.map((tab) => (
        <TabsContent key={tab?.id} value={tab?.id}>
          <div className="rounded-2xl ">{tab?.content}</div>
        </TabsContent>
      ))}
    </Tabs>
  )
}
CustomTabs.propTypes = {
  tabs: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      content: PropTypes.oneOfType([PropTypes.string, PropTypes.element]).isRequired,
      id: PropTypes.string,
    })
  ).isRequired,
  defaultTab: PropTypes.string,
  size: PropTypes.string,
  setActiveTab: PropTypes.func.isRequired,
  activeTab: PropTypes.string.isRequired,
  tabsListClassName: PropTypes.string,
}

export default CustomTabs
