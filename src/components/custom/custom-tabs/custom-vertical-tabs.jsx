import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import PropTypes from "prop-types"

const CustomVerticalTabs = ({ tabs, onTabChange, activeTab, setActiveTab }) => {
  const handleTabChange = (tabId) => {
    setActiveTab(tabId)
    if (onTabChange) {
      onTabChange(tabId)
    }
  }

  return (
    <div className="flex w-full ">
      {/* Vertical Tab Navigation */}
      <div className="w-64  space-y-2 relative">
        {tabs.map((tab) => (
          <Button
            key={tab.id}
            variant={activeTab === tab.id ? "primary" : "outline"}
            className={cn(
              "w-full ",
              activeTab === tab.id
                ? "bg-primary text-primary-foreground hover:bg-primary/90"
                : "text-muted-foreground "
            )}
            onClick={() => handleTabChange(tab.id)}
          >
            {tab.label}
          </Button>
        ))}
      </div>

      {/* Tab Content Area */}
      <div className="flex-1 pl-6">
        {tabs.find((tab) => tab.id === activeTab)?.content || (
          <div className="text-muted-foreground">No content available</div>
        )}
      </div>
    </div>
  )
}

CustomVerticalTabs.propTypes = {
  tabs: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
      content: PropTypes.node,
    })
  ).isRequired,
  activeTab: PropTypes.string,
  onTabChange: PropTypes.func,
  setActiveTab: PropTypes.func,
}
export default CustomVerticalTabs
