import { Dialog, DialogContent } from "@/components/ui/dialog"
import PropTypes from "prop-types"
import { CircleAlert } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export function DeleteDialog({
  onOpen,
  setOnOpen,
  title = "Delete article",
  subContent = "This action cannot be undone",
  content = "Are you sure want to delete this article?",
  onDelete,
  onCancel,
}) {
  return (
    <Dialog open={onOpen} onOpenChange={setOnOpen} className="rounded-[.5rem]">
      <DialogContent className="max-w-[450px] rounded-[.5rem]" onOpenChange={setOnOpen}>
        <div className=" rounded-[.5rem]">
          <div className="flex justify-center mb-4 ">
            <div className="w-14 h-14 rounded-full  bg-red-50 flex items-center justify-center ">
              <div className="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center ">
                <CircleAlert className="w-6 h-6 animate-pulse text-red-500 " />
              </div>
            </div>
          </div>

          <div className="text-center space-y-2">
            <h5 className="font-semibold text-2xl">{title}</h5>

            <div className="text-xs   font-me mb-0 text-gray-500">
              <p className="font-medium">{content}</p>
              <p className="space-y-0 mt-0 font-medium">{subContent}</p>
            </div>
          </div>

          <div className="flex justify-center gap-3 mt-6">
            <Button variant="ghost" className="" onClick={onCancel}>
              Cancel
            </Button>
            <Button variant="destructive" className="" onClick={onDelete}>
              Delete
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

DeleteDialog.propTypes = {
  onOpen: PropTypes.bool.isRequired,
  setOnOpen: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  content: PropTypes.string.isRequired,
  onDelete: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  subContent: PropTypes.string,
}
