import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog"
import PropTypes from "prop-types"

export function GeneralDialog({
  onOpen,
  setOnOpen,
  title = "Delete article",
  onClickCTA,
  ctaLabel,
  onCancel,
  content,
  ctaPosition = "justify-center",
}) {
  return (
    <Dialog open={onOpen} onOpenChange={setOnOpen} className="rounded-[.5rem]">
      <DialogContent className="max-w-[450px] rounded-[.5rem]" onOpenChange={setOnOpen}>
        <DialogTitle>{title}</DialogTitle>
        <div className="">
          {content}

          <div className={`flex  gap-3 mt-6 ${ctaPosition}`}>
            <Button variant="ghost" className="" onClick={onCancel}>
              Cancel
            </Button>
            <Button variant="primary" className="" onClick={onClickCTA}>
              {ctaLabel}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

GeneralDialog.propTypes = {
  onOpen: PropTypes.bool.isRequired,
  setOnOpen: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  content: PropTypes.string.isRequired,
  onClickCTA: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  subContent: PropTypes.string,
}
