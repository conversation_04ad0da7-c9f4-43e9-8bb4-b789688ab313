import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { formsProps } from "./utils/props-type"

const FormInput = ({
  dataTestID = "test-id",
  dataTestIDError = "error",
  fieldControlName,
  control,
  label,
  placeholder,
  isRequired,
  type = "text",
  onChange,
  isTypeNumer,
  formDes = "",
  disabled = false,
}) => {
  return (
    <FormField
      control={control}
      name={fieldControlName}
      render={({ field }) => (
        <FormItem>
          <FormLabel htmlFor={fieldControlName} className="block">
            {label} {isRequired && <span className="text-red-600">*</span>}
          </FormLabel>

          <FormControl>
            <Input
              data-testid={dataTestID}
              placeholder={placeholder}
              {...field}
              type={type}
              onChange={(e) => {
                let { value } = e.target
                if (isTypeNumer) {
                  value = value.replace(/\D/g, "") // Remove non-numeric characters
                }
                field.onChange(value) // Update form state
                if (onChange) {
                  onChange(value)
                }
              }}
              disabled={disabled}
            />
          </FormControl>

          <FormMessage data-testid={dataTestIDError} className="text-xs font-medium mt-1" />
          {formDes?.length > 0 && <FormDescription>{formDes}</FormDescription>}
        </FormItem>
      )}
    />
  )
}

FormInput.propTypes = formsProps

export default FormInput
