/* eslint-disable no-nested-ternary */
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogClose,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"
import {
  addMonths,
  eachDayOfInterval,
  endOfMonth,
  format,
  isSameDay,
  isSameMonth,
  isToday,
  startOfMonth,
  subMonths,
} from "date-fns"
import { CalendarIcon, ChevronLeft, ChevronRight, Clock, Globe } from "lucide-react"
import { useEffect, useState } from "react"
import { formsProps } from "./utils/props-type"

const AppointmentScheduler = ({
  dataTestID = "test-id",
  dataTestIDError = "error",
  fieldControlName,
  control,
  label,
  placeholder = "Schedule",
  isRequired,
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [currentMonth, setCurrentMonth] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [selectedTime, setSelectedTime] = useState("00:00")

  // Get the local timezone name and offset
  const getLocalTimezone = () => {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
    const offset = new Date().getTimezoneOffset()
    const offsetHours = Math.abs(Math.floor(offset / 60))
    const offsetMinutes = Math.abs(offset % 60)
    const offsetSign = offset <= 0 ? "+" : "-"
    const formattedOffset = `${offsetSign}${offsetHours.toString().padStart(2, "0")}:${offsetMinutes.toString().padStart(2, "0")}`
    return { timezone, formattedOffset }
  }

  const localTimezone = getLocalTimezone()

  // Initialize with existing value if available
  const initializeFromValue = (value) => {
    if (value) {
      try {
        // Parse the date string directly without timezone conversion
        // Remove any trailing Z or timezone indicator
        const cleanValue = value.replace(/Z|(\+|-)\d{2}:\d{2}$/, "")
        const [datePart, timePart] = cleanValue.split("T")

        if (datePart && timePart) {
          const [year, month, day] = datePart.split("-").map(Number)
          const [hours, minutes] = timePart.split(":").map(Number)

          // Create date with local components (no timezone conversion)
          const localDate = new Date(year, month - 1, day, hours, minutes, 0, 0)

          setSelectedDate(new Date(year, month - 1, day))
          setSelectedTime(format(localDate, "HH:mm"))
          setCurrentMonth(localDate)
        }
      } catch (error) {
        console.error("Error parsing existing date value:", error)
      }
    }
  }

  const handlePreviousMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1))
  }

  const handleNextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1))
  }

  const handleDateSelect = (date) => {
    setSelectedDate(date)
  }

  const handleTimeChange = (e) => {
    setSelectedTime(e.target.value)
  }

  const handleSave = (onChange) => {
    try {
      // Parse the time string (format: HH:MM)
      const [hours, minutes] = selectedTime.split(":").map(Number)

      // Create date components (local time)
      const year = selectedDate.getFullYear()
      const month = selectedDate.getMonth()
      const day = selectedDate.getDate()

      // Create a temporary date to get the timezone offset
      const tempDate = new Date(year, month, day, hours, minutes)
      const offsetMinutes = tempDate.getTimezoneOffset()

      // Calculate timezone offset (invert the sign)
      const offsetHours = Math.floor(Math.abs(offsetMinutes) / 60)
      const offsetRemainingMinutes = Math.abs(offsetMinutes) % 60
      const offsetSign = offsetMinutes > 0 ? "-" : "+" // Inverted sign

      // Format the timezone offset (e.g., "+05:30" or "-04:00")
      const timezoneOffset = `${offsetSign}${offsetHours.toString().padStart(2, "0")}:${offsetRemainingMinutes.toString().padStart(2, "0")}`

      // Create final ISO string with correct time and timezone
      const dateTimeString = `${year}-${(month + 1).toString().padStart(2, "0")}-${day.toString().padStart(2, "0")}T${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}${timezoneOffset}`

      // Pass the final datetime to the form field
      onChange(dateTimeString)
      setIsOpen(false)
    } catch (error) {
      console.error("Error creating datetime:", error)
    }
  }
  // Generate calendar data
  const monthStart = startOfMonth(currentMonth)
  const monthEnd = endOfMonth(currentMonth)
  const daysInMonth = eachDayOfInterval({ start: monthStart, end: monthEnd })

  const weekdays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]

  const startDay = monthStart.getDay()
  const endDay = 6 - monthEnd.getDay()

  const prevMonthDays =
    startDay > 0
      ? eachDayOfInterval({
        start: new Date(currentMonth.getFullYear(), currentMonth.getMonth(), -startDay + 1),
        end: new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 0),
      })
      : []

  const nextMonthDays =
    endDay > 0
      ? eachDayOfInterval({
        start: new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1),
        end: new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, endDay),
      })
      : []

  const allDays = [...prevMonthDays, ...daysInMonth, ...nextMonthDays]

  const calendarRows = []
  for (let i = 0; i < allDays.length; i += 7) {
    calendarRows.push(allDays.slice(i, i + 7))
  }

  // Format the scheduled datetime for display in local time
  const formatScheduledDateTime = (dateTimeString) => {
    console.log(dateTimeString, "dateTimeString")

    if (!dateTimeString) return placeholder

    try {
      // Parse ISO date string with timezone information
      const date = new Date(dateTimeString)

      if (isNaN(date.getTime())) return placeholder

      // Format in local time
      const formattedDate = format(date, "EEE, MMM d, yyyy")
      const formattedTime = format(date, "h:mm a") // 12-hour format with AM/PM

      return `${formattedDate} at ${formattedTime}`
      // return `${formattedDate} at ${formattedTime} (${localTimezone.timezone})`
    } catch (error) {
      console.error("Error formatting date for display:", error)
      return placeholder
    }
  }

  return (
    <FormField
      control={control}
      name={fieldControlName}
      render={({ field }) => {
        useEffect(() => {
          if (field.value) {
            initializeFromValue(field.value)
          }
        }, [field.value])

        return (
          <FormItem className="flex flex-col">
            {label && (
              <FormLabel>
                {label} {isRequired && <span className="text-red-600">*</span>}
              </FormLabel>
            )}
            <FormControl>
              <div className="font-sans">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsOpen(true)}
                  className={cn(
                    "relative flex items-center justify-start gap-2 shadow-sm transition-all duration-200",
                    "px-8 py-1.5 rounded-lg text-sm w-full",
                    !field.value && "bg-white border border-gray-200 text-gray-700 hover:bg-gray-50"
                  )}
                  data-testid={dataTestID}
                >
                  <CalendarIcon className="h-4 w-4" />
                  <span className="font-medium">{formatScheduledDateTime(field.value)}</span>
                </Button>

                <Dialog open={isOpen} onOpenChange={setIsOpen}>
                  <DialogContent className="border-0 p-0 gap-0 overflow-hidden">
                    <DialogHeader className="bg-gradient-to-r border-b px-6 py-4">
                      <DialogTitle className="text-2xl text-left font-semibold">
                        Schedule a Session
                      </DialogTitle>
                      <DialogClose className="absolute hover:opacity-100 opacity-70 right-4 top-4" />
                    </DialogHeader>

                    <div className="bg-white p-6">
                      {/* Calendar Navigation */}
                      <div className="flex justify-between items-center mb-6">
                        <button
                          type="button"
                          onClick={handlePreviousMonth}
                          className="flex justify-center p-2 rounded-full text-gray-600 hover:bg-gray-100 items-center"
                          aria-label="Previous month"
                        >
                          <ChevronLeft className="h-5 w-5" />
                        </button>
                        <h2 className="text-gray-800 text-lg font-medium">
                          {format(currentMonth, "MMMM yyyy")}
                        </h2>
                        <button
                          type="button"
                          onClick={handleNextMonth}
                          className="flex justify-center p-2 rounded-full text-gray-600 hover:bg-gray-100 items-center"
                          aria-label="Next month"
                        >
                          <ChevronRight className="h-5 w-5" />
                        </button>
                      </div>

                      {/* Calendar */}
                      <div className="mb-8">
                        <div className="grid grid-cols-7 text-center mb-2">
                          {weekdays.map((day) => (
                            <div key={day} className="text-gray-500 text-xs font-semibold mb-2">
                              {day}
                            </div>
                          ))}
                        </div>

                        <div className="grid grid-cols-7 gap-1">
                          {calendarRows.flat().map((date, i) => {
                            const isCurrentMonth = isSameMonth(date, currentMonth)
                            const isSelected = selectedDate && isSameDay(date, selectedDate)
                            const isDayToday = isToday(date)

                            return (
                              <button
                                type="button"
                                key={i}
                                onClick={() => handleDateSelect(date)}
                                className={cn(
                                  "relative h-10 w-10 rounded-full flex items-center justify-center text-sm",
                                  "transition-all duration-200 font-medium",

                                  !isCurrentMonth
                                    ? "text-gray-300"
                                    : isDayToday
                                      ? "text-blue-700"
                                      : "text-gray-700",
                                  isSelected
                                    ? "bg-blue-600 text-white shadow-md"
                                    : isCurrentMonth
                                      ? "hover:bg-blue-50"
                                      : "hover:bg-gray-50",
                                  isDayToday && !isSelected && "ring-1 ring-blue-200"
                                )}
                                aria-pressed={isSelected}
                                disabled={!isCurrentMonth}
                              >
                                {date.getDate()}
                                {isSelected && (
                                  <span className="bg-white h-1 rounded-full w-1 -bottom-1 -translate-x-1/2 absolute left-1/2 transform" />
                                )}
                              </button>
                            )
                          })}
                        </div>
                      </div>

                      {/* Selected Date Display */}
                      <div className="flex bg-blue-50 rounded-lg text-blue-700 text-sm font-medium items-center mb-6 px-4 py-3">
                        <CalendarIcon className="h-4 w-4 mr-2" />
                        {selectedDate && format(selectedDate, "EEEE, MMMM d, yyyy")}
                      </div>

                      {/* Time Selection */}
                      <div>
                        <h3 className="flex text-gray-700 text-sm font-medium gap-2 items-center mb-3">
                          Pick your time
                        </h3>

                        <div className="flex items-center">
                          <div className="w-full max-w-xs relative">
                            <Clock className="h-4 text-gray-500 w-4 -translate-y-1/2 absolute left-3 top-1/2" />
                            <Input
                              type="time"
                              value={selectedTime}
                              onChange={handleTimeChange}
                              className={cn("pl-10 font-medium")}
                              required
                            />
                          </div>
                        </div>
                      </div>

                      {/* Timezone Display */}
                      <div className="flex items-center mt-5 text-gray-600 text-sm">
                        <Globe className="h-4 w-4 mr-2" />
                        <span>
                          Your local timezone:{" "}
                          <span className="font-medium">
                            {localTimezone.timezone} ({localTimezone.formattedOffset})
                          </span>
                        </span>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex bg-gray-50 justify-end gap-5 border-t p-4">
                      <Button
                        onClick={() => handleSave(field.onChange)}
                        variant="primary"
                        className="px-7"
                      >
                        Save
                      </Button>
                      <Button
                        onClick={() => setIsOpen(false)}
                        variant="outline"
                        className="bg-white hover:bg-gray-50 transition-all"
                      >
                        Discard
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </FormControl>
            <FormMessage data-testid={dataTestIDError} />
          </FormItem>
        )
      }}
    />
  )
}

AppointmentScheduler.propTypes = formsProps

export default AppointmentScheduler
