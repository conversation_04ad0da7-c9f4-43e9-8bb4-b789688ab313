import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { format, parseISO } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { formsProps } from "./utils/props-type"

const UtcFromDate = ({
  dataTestID = "test-id",
  dataTestIDError = "error",
  fieldControlName,
  control,
  label,
  placeholder,
  isRequired,
  pastDateDisabled = new Date(),
  futureDateDisabled = null,
}) => {
  return (
    <FormField
      control={control}
      name={fieldControlName}
      render={({ field }) => (
        <FormItem className="flex flex-col">
          <FormLabel>
            {label} {isRequired && <span className="text-red-600">*</span>}{" "}
          </FormLabel>
          <Popover>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant="outline"
                  className={cn(
                    " pl-3 text-left font-normal",
                    !field.value && "text-muted-foreground"
                  )}
                  data-testId={dataTestID}
                >
                  {field.value ? (
                    format(parseISO(field.value), "PPP") // Display in readable format
                  ) : (
                    <span className="text-sm font-medium">{placeholder}</span>
                  )}
                  <CalendarIcon className="h-4 w-4 ml-auto opacity-50" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="p-0 w-auto" align="start">
              <Calendar
                mode="single"
                selected={field.value ? new Date(field.value) : null}
                onSelect={(date) => {
                  if (date) {
                    const utcDate = new Date(
                      Date.UTC(date.getFullYear(), date.getMonth(), date.getDate())
                    )
                    field.onChange(utcDate.toISOString()) // Store in UTC format
                  }
                }}
                disabled={(date) => {
                  const today = new Date()
                  today.setHours(0, 0, 0, 0) // Set to beginning of today
                  return date < today // Disable all dates before today
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>

          <FormMessage data-testId={dataTestIDError} />
        </FormItem>
      )}
    />
  )
}

UtcFromDate.propTypes = formsProps

export default UtcFromDate
