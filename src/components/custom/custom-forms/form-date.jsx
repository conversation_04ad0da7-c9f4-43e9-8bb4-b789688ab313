import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { formsProps } from "./utils/props-type"

const FormDate = ({
  dataTestID = "test-id",
  dataTestIDError = "error",
  fieldControlName,
  control,
  label,
  placeholder,
  isRequired,
  pastDateDisabled = "1900-01-01",
  futureDateDisabled = new Date(),
}) => {
  return (
    <FormField
      control={control}
      name={fieldControlName}
      render={({ field }) => (
        <FormItem className="flex flex-col">
          <FormLabel>
            {label} {isRequired && <span className="text-red-600">*</span>}{" "}
          </FormLabel>
          <Popover>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant="outline"
                  className={cn(
                    " pl-3 text-left font-normal",
                    !field.value && "text-muted-foreground"
                  )}
                  data-testId={dataTestID}
                >
                  {field.value ? (
                    format(field.value, "PPP")
                  ) : (
                    <span className="text-sm font-medium">{placeholder}</span>
                  )}
                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={field.value}
                onSelect={field.onChange}
                disabled={(date) =>
                  (futureDateDisabled && date > futureDateDisabled) ||
                  date < new Date(pastDateDisabled)
                }
                initialFocus
              />
            </PopoverContent>
          </Popover>

          <FormMessage data-testId={dataTestIDError} />
        </FormItem>
      )}
    />
  )
}

FormDate.propTypes = formsProps

export default FormDate
