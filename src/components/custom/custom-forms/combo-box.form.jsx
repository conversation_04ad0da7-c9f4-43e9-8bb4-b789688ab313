import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import PropTypes from "prop-types"
import { Check, ChevronsUpDown } from "lucide-react"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"

const FormComboBox = ({
  dataTestIDError = "combo-error",
  fieldControlName,
  control,
  label,
  iterateData,
  placeholder,
  isRequired,
  width,
}) => {
  return (
    <FormField
      control={control}
      name={fieldControlName}
      render={({ field }) => {
        const selectedModule = iterateData?.find((item) => item.value === field.value)

        return (
          <FormItem>
            <FormLabel htmlFor={fieldControlName} className="block">
              {label} {isRequired && <span className="text-red-600">*</span>}
            </FormLabel>

            <FormControl>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    className={cn("w-[20rem] justify-between pl-2", !field.value && "text-muted")}
                  >
                    {selectedModule ? selectedModule.label : placeholder}
                    <ChevronsUpDown className="opacity-50" size={19} />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className={`min-w-[${width}] p-0`}>
                  <Command>
                    {/* Ensure CommandInput updates correctly */}
                    <CommandInput placeholder={placeholder} className="h-9" />

                    <CommandList>
                      {/* Ensure list updates properly on search */}
                      <CommandEmpty>No modules found.</CommandEmpty>
                      <CommandGroup>
                        {iterateData?.map((item) => (
                          <CommandItem
                            key={item.value}
                            value={item.label?.toLowerCase()} // Ensure filtering works
                            onSelect={() => {
                              field.onChange(item.value)
                            }}
                          >
                            {item.label}
                            <Check
                              className={cn(
                                "ml-auto",
                                item.value === field.value ? "opacity-100" : "opacity-0"
                              )}
                            />
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            </FormControl>
            <FormMessage data-testid={dataTestIDError} className="text-xs font-medium mt-1" />
          </FormItem>
        )
      }}
    />
  )
}

FormComboBox.propTypes = {
  dataTestIDError: PropTypes.string,
  fieldControlName: PropTypes.string.isRequired,
  control: PropTypes.objectOf,
  label: PropTypes.string.isRequired,
  iterateData: PropTypes.arrayOf,
  placeholder: PropTypes.string,
  isRequired: PropTypes.bool,
  width: PropTypes.string,
}

export default FormComboBox
