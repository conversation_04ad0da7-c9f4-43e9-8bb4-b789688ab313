import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { formsProps } from "./utils/props-type"

const FormSelect = ({
  dataTestID = "test-id",
  dataTestIDError = "error",
  fieldControlName,
  control,
  label,
  iterateData = [], // Ensure it has a default empty array
  placeholder,
  isRequired,
  defaultValue = "",
  disabled = false,
}) => {
  // Automatically set the first item in the dropdown if no defaultValue is provided
  const initialValue = defaultValue || iterateData?.[0]?.value?.toUpperCase() || ""

  console.log("__iterateData", iterateData)

  return (
    <FormField
      control={control}
      name={fieldControlName}
      render={({ field }) => (
        <FormItem>
          <FormLabel htmlFor={label} className="block">
            {label} {isRequired && <span className="text-red-600">*</span>}
          </FormLabel>

          <FormControl>
            <Select
              data-testid={dataTestID}
              onValueChange={field.onChange}
              defaultValue={field.value || initialValue} // Use initialValue
              disabled={disabled}
            >
              <SelectTrigger>
                <SelectValue placeholder={placeholder} />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  {iterateData?.map((item) => (
                    <SelectItem
                      className="font-medium text-sm"
                      key={item.value}
                      value={item.value?.toUpperCase()} // Ensure only using item.value
                    >
                      <span className="font-medium text-sm">{item?.label?.replace(/_/g, " ")}</span>
                    </SelectItem>
                  ))}
                </SelectGroup>
              </SelectContent>
            </Select>
          </FormControl>
          <FormMessage data-testid={dataTestIDError} className="text-xs font-medium mt-1" />
        </FormItem>
      )}
    />
  )
}

FormSelect.propTypes = formsProps

export default FormSelect
