import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import { formsProps } from "./utils/props-type"

const FormTextArea = ({
  dataTestID = "test-id",
  dataTestIDError = "error",
  fieldControlName,
  control,
  label,
  placeholder,
  isRequired,
  disabled,
}) => {
  return (
    <FormField
      control={control}
      name={fieldControlName}
      render={({ field }) => (
        <FormItem className="">
          <FormLabel htmlFor="topic" className="block">
            {label} {isRequired && <span className="text-red-600">*</span>}
          </FormLabel>

          <FormControl>
            <Textarea
              disabled={disabled}
              data-testid={dataTestID}
              placeholder={placeholder}
              {...field}
            />
          </FormControl>
          <FormMessage dataTestid={dataTestIDError} className="text-xs font-medium mt-1" />
        </FormItem>
      )}
    />
  )
}
FormTextArea.propTypes = formsProps

export default FormTextArea
