/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/anchor-is-valid */
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { USER_ROLES } from "@/utils/constants"
import { roleRestrictions } from "@/utils/helper"
import { functionsPropTypes, isPaidUserPropTypes } from "@/utils/props-types"
import ct from "@constants/"
import { ChevronDown, Clock, File, Video, ChevronUp } from "lucide-react"
import moment from "moment"
import PropTypes from "prop-types"
import { useState } from "react"

import { Link, useNavigate, useParams } from "react-router-dom"
import { ActionCell, RenderTableData, StatusUpdationCell } from "../cutsom-table/table-cells"
import LoadingSpinner from "../LoadingSpinner"

const getIcons = (type, resource_link, onClick) => {
  const isDownloadable =
    type === "DOCUMENT" && (resource_link?.endsWith(".pdf") || resource_link?.endsWith(".docs"))

  const icons = {
    VIDEO: (
      <Video className="h-5 w-5 text-primary flex-shrink-0 cursor-pointer" onClick={onClick} />
    ),
    LINK: <Link className="h-5 w-5 text-primary flex-shrink-0 cursor-pointer" onClick={onClick} />,
    DOCUMENT: isDownloadable ? (
      <File className="h-5 w-5 text-primary flex-shrink-0 cursor-pointer" onClick={onClick} />
    ) : (
      <File className="h-5 w-5 text-primary flex-shrink-0" />
    ),
  }

  return icons[type] || <File className="h-5 w-5 text-primary flex-shrink-0" />
}

const CircularProgress = ({ completed = 3, total = 5, size = 50 }) => {
  console.log("__total", total)
  const percentage = (completed / total) * 100
  const strokeWidth = 4
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const dash = (percentage * circumference) / 100

  return (
    <div className={`relative w-[${size}px] h-[${size}px] flex items-center justify-center`}>
      <svg
        width={size}
        height={size}
        viewBox={`0 0 ${size} ${size}`}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill="none"
          stroke="#E5E7EB"
          strokeWidth={strokeWidth}
        />
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill="none"
          stroke={completed > 0 ? "#10B981" : "#E5E7EB"} // Dynamically change stroke color
          strokeWidth={strokeWidth}
          strokeDasharray={`${dash} ${circumference - dash}`}
          strokeLinecap="round"
        />
      </svg>
      <span className="absolute text-xs font-bold text-gray-700">
        {total ? `${completed}/${total}` : "-"}
      </span>
    </div>
  )
}

CircularProgress.propTypes = {
  completed: PropTypes.number,
  total: PropTypes.number,
  size: PropTypes.number,
}

const AccordionSection = ({
  children,
  isOpen,
  onToggle,
  completedCount,
  totalCount,
  moduleName,
  onDeleteModule,
  onEditModule,
  onCreateTopic,
  isExpanded,
  setIsExpanded,
  id,
  data,
  publish_status,
  description,
  onUpdateStatus,
  userRole,
  totalTopics,
}) => (
  <div className="border-b border-gray-100" key={id}>
    <Button
      onClick={onToggle}
      className="w-full py-4 px-2 flex items-center justify-between hover:bg-gray-50 transition-colors"
    >
      <div className="flex items-center w-full justify-between gap-4">
        <div className="flex items-start gap-x-6">
          <CircularProgress completed={completedCount} total={totalCount} />
          <div className="flex flex-col justify-center text-start max-w-full">
            <h2 className="text-sm sm:text-base lg:text-lg font-semibold text-gray-900">
              {moduleName}
            </h2>

            <p
              className={`text-xs sm:text-sm text-gray-700 whitespace-normal break-words mt-1 transition-all duration-200 ${isExpanded ? "" : "line-clamp-2"
                }`}
            >
              {description}
            </p>

            {description.length > 100 && (
              <Button
                onClick={() => setIsExpanded(!isExpanded)}
                className="mt-1 flex items-center gap-1 text-blue-500 text-xs font-medium focus:outline-none"
              >
                {isExpanded ? "Show less" : "Show more"}
                {isExpanded ? (
                  <ChevronUp className="w-4 h-4" />
                ) : (
                  <ChevronDown className="w-4 h-4" />
                )}
              </Button>
            )}
          </div>
        </div>

        <div className="flex gap-x-4 mr-3">
          {userRole === USER_ROLES.STUDENT && (
            <p className="text-sm font-medium text-muted">{totalTopics} Topics</p>
          )}
          {userRole !== USER_ROLES.STUDENT && (
            <StatusUpdationCell value={publish_status} key={id} />
          )}
          {roleRestrictions(userRole, "admin_trainer") && (
            <ActionCell
              label1="Create Topic"
              label2="Edit Module"
              label4="Delete Module"
              label5="Update Status"
              isEdit
              isDelete
              isView
              isAddFeedback
              onDelete={() => onDeleteModule(id)}
              onEdit={() => onEditModule(data)}
              onView={() => onCreateTopic(id)}
              onFeedback={() => onUpdateStatus(data, "", "module")}
              isDisabled={!totalTopics}
            />
          )}
        </div>
      </div>
      <ChevronDown
        className={`h-5 w-5 text-gray-500 transition-transform duration-200 ${isOpen ? "transform rotate-180" : ""
          }`}
      />
    </Button>
    <div
      className={`overflow-hidden transition-all duration-200 ${isOpen ? "max-h-[500px] opacity-100" : "max-h-0 opacity-0"
        }`}
    >
      {children}
    </div>
  </div>
)

const CourseSection = ({
  moduleName,
  resource_name,
  resourceDate,
  resourceID,
  type,
  modified_at,
  resource_description,
  resource_link,
  onDeleteTopic,
  onEditTopic,
  onCompleteStatus,
  id,
  moduleID,
  data,
  userRole,
  onTopicStatus,
  publish_status,
  is_completed,
  resource_type,
  isPaidUser,
}) => {
  // Function to handle document downloads
  const handleDocumentClick = (e, link) => {
    if (resource_type === "DOCUMENT" && (link?.endsWith(".pdf") || link?.endsWith(".docs"))) {
      e.preventDefault()
      // Create an anchor element and trigger download
      const downloadLink = document.createElement("a")
      downloadLink.href = link
      downloadLink.download = link.split("/").pop() // Extract filename from path
      document.body.appendChild(downloadLink)
      downloadLink.click()
      document.body.removeChild(downloadLink)
    }
  }
  const params = useParams()
  const navigate = useNavigate()
  console.log("CourseSection", resource_name)

  const handleLinkClick = (e) => {
    e.preventDefault()
    if (resource_link) {
      window.open(resource_link, "_blank")
    }
  }

  // Function to handle video navigation
  const handleVideoClick = (e) => {
    e.preventDefault()
    // Navigate to the COURSE route with the video ID
    navigate(`${ct.route.COURSE}/${params?.id}`, {
      state: { resourceId: id, moduleId: moduleID, courseId: params?.id, moduleName },
    })
  }

  // Get the appropriate click handler based on resource type
  const getClickHandler = () => {
    switch (resource_type) {
      case "VIDEO":
        return handleVideoClick
      case "LINK":
        return handleLinkClick
      case "DOCUMENT":
        return (e) => handleDocumentClick(e, resource_link)
      default:
        return null
    }
  }

  const getResourceStatus = (resourceDate) => {
    const now = moment()
    const resourceMoment = moment.parseZone(resourceDate).local()

    if (resourceMoment.isBefore(now)) {
      return (
        <Badge variant="secondary" className="text-gray-800">
          Completed
        </Badge>
      )
    }

    if (resourceMoment.isSame(now, "day")) {
      return (
        <Badge variant="success" className="text-green-800">
          Live
        </Badge>
      )
    }

    return (
      <Badge variant="warning" className="text-yellow-800">
        Scheduled
      </Badge>
    )
  }
  console.log("Parsed (raw):", moment(resourceDate).format()) // check what it's reading
  console.log("Parsed (raw)2:", moment(resourceDate).local().format())

  return (
    <div className="flex items-start gap-4 py-3 px-1">
      {/* Show clock if not completed, otherwise show resource type icon */}
      {!is_completed ? (
        <div className="mt-1">
          <Clock className="h-5 w-5 text-gray-400 flex-shrink-0" />
        </div>
      ) : (
        <div className="mt-1">{getIcons(resource_type, resource_link, getClickHandler())}</div>
      )}
      {console.log("_paidUser", isPaidUser)}
      <a
        href={isPaidUser?.paidUser ? "#" : resource_link}
        target="_blank"
        className={`flex-1 block ${isPaidUser?.paidUser
            ? "cursor-not-allowed text-gray-400"
            : "text-blue-600 hover:underline"
          }`}
        rel="noreferrer"
        onClick={(e) => {
          if (isPaidUser?.paidUser) {
            e.preventDefault()
            return
          }

          const clickHandler = getClickHandler()
          if (clickHandler) {
            clickHandler(e)
          }
        }}
      >
        <div className="flex justify-between mb-2">
          <div className="flex justify-between items-center w-full">
            <h2 className="text-xs sm:text-sm lg:text-base text-start font-semibold text-primary mb-0">
              {resource_name}
            </h2>
          </div>

          <div className="flex">
            {userRole !== USER_ROLES.STUDENT && (
              <StatusUpdationCell value={publish_status} key={id} />
            )}
            {/* Status indicator based on date comparison */}
            <p className="text-sm px-2 py-1 rounded">{getResourceStatus(resourceDate)}</p>
            {roleRestrictions(userRole, "admin_trainer") && (
              <ActionCell
                label1="Complete Topic"
                label2="Edit"
                label3="Delete"
                label5="Update Status"
                isEdit
                isDelete
                isView
                isAddFeedback
                onView={() => onCompleteStatus(id, moduleID)}
                onDelete={() => onDeleteTopic(id, moduleID)}
                onEdit={() => onEditTopic(moduleID, data)}
                onFeedback={() => onTopicStatus(data, moduleID)}
              />
            )}
          </div>
        </div>

        <div className="flex gap-2 text-sm text-gray-600 items-center justify-between">
          <div className="flex">
            <span className="capitalize ">{type}</span>

            <span className="text-xs md:text-xs xl:text-sm font-medium text-muted block truncate md:max-w-[300px] lg:max-w-[500px]">
              <RenderTableData content={resource_description} char={70} />
            </span>
          </div>
          <div className="flex gap-2">
            <p className="text-sm font-medium text-primary">
              {moment.parseZone(resourceDate).local().format("MMM D, YYYY • hh:mm A")}
            </p>
            <p className="text-sm font-medium">{moment(modified_at).fromNow()}</p>
          </div>
        </div>
      </a>
    </div>
  )
}

const CustomAccordionTimeline = ({
  modulesList,
  onDeleteTopic,
  onEditTopic,
  onEditModule,
  onDeleteModule,
  onCreateTopic,
  onCompleteStatus,
  onUpdateStatus,
  onTopicStatus,
  userRole,
  isLoading,
  isPaidUser,
}) => {
  const [openModuleIndex, setOpenModuleIndex] = useState(-1)

  const [expandedDescriptions, setExpandedDescriptions] = useState({})

  const toggleAccordion = (moduleIndex) => {
    setOpenModuleIndex(openModuleIndex === moduleIndex ? -1 : moduleIndex)
  }

  const toggleDescriptionExpansion = (moduleId) => {
    setExpandedDescriptions((prev) => ({
      ...prev,
      [moduleId]: !prev[moduleId],
    }))
  }

  return (
    <div>
      <ScrollArea className="h-[64vh] w-full rounded-md">
        <Card className="">
          <CardContent className="p-4">
            <div className="divide-y divide-gray-100">
              {isLoading ? (
                <LoadingSpinner />
              ) : (
                <div>
                  {modulesList?.length > 0 &&
                    modulesList.map((module, moduleIndex) => {
                      const completedCount =
                        module?.resources?.length > 0 &&
                        module?.resources?.filter((section) => section?.is_completed)?.length
                      const totalCount = module?.total_records
                      const isResource = module?.resources?.length > 0
                      const isModuleOpen = openModuleIndex === moduleIndex
                      const isDescriptionExpanded = expandedDescriptions[module?.id] || false

                      return (
                        <div key={module?.id}>
                          <AccordionSection
                            key={module?.id}
                            title={module?.title}
                            isOpen={isModuleOpen}
                            onToggle={() => toggleAccordion(moduleIndex)}
                            isExpanded={isDescriptionExpanded}
                            setIsExpanded={() => toggleDescriptionExpansion(module?.id)}
                            completedCount={completedCount}
                            totalCount={totalCount}
                            moduleName={module?.module_name}
                            description={module?.module_description}
                            onDeleteModule={onDeleteModule}
                            onEditModule={onEditModule}
                            data={module}
                            id={module?.id}
                            onCreateTopic={onCreateTopic}
                            publish_status={module?.publish_status}
                            onUpdateStatus={onUpdateStatus}
                            userRole={userRole}
                            totalTopics={module?.total_records}
                            isResource={isResource}
                          >
                            <ScrollArea
                              className={`w-full rounded-md ${module?.resources?.length > 3 ? "h-64" : "h-auto"
                                }`}
                            >
                              <div className="divide-y ml-10 divide-gray-100">
                                {module?.resources?.map((section) => (
                                  <CourseSection
                                    moduleName={module?.module_name}
                                    key={section?.id}
                                    resourceDate={section.resource_date}
                                    {...section}
                                    onDeleteTopic={onDeleteTopic}
                                    onCompleteStatus={onCompleteStatus}
                                    onEditTopic={onEditTopic}
                                    moduleID={module?.id}
                                    data={section}
                                    userRole={userRole}
                                    onTopicStatus={onTopicStatus}
                                    is_completed={section?.is_completed}
                                    resource_type={section?.resource_type}
                                    isResource={isResource}
                                    isPaidUser={isPaidUser}
                                  />
                                ))}
                              </div>
                            </ScrollArea>
                          </AccordionSection>
                        </div>
                      )
                    })}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </ScrollArea>
    </div>
  )
}

CustomAccordionTimeline.propTypes = {
  modulesList: PropTypes.objectOf({
    modules: {
      title: PropTypes.string,
    },
  }),
  userRole: PropTypes.string,
  totalRecords: PropTypes.string,
  ...functionsPropTypes,
}

AccordionSection.propTypes = {
  children: PropTypes.node,
  isOpen: PropTypes.bool,
  completedCount: PropTypes.number,
  totalCount: PropTypes.number,
  moduleName: PropTypes.string,
  id: PropTypes.number,
  data: PropTypes.objectOf(),
  publish_status: PropTypes.string,
  description: PropTypes.string,
  ...functionsPropTypes,
}

export default CustomAccordionTimeline
