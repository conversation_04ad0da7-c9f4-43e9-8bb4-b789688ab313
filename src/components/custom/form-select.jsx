import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import PropTypes from "prop-types"
import { useState } from "react"

const FormsSelect = ({ setValue, value }) => {
  const [selectedValue, setSelectedValue] = useState("")

  // useEffect(() => {
  //   if (value?.length > 0) {
  //     setSelectedValue(value[0]?.toUpperCase()) // Set the first item as default
  //     setValue(value[0]?.toUpperCase())
  //   }
  // }, [value, setValue])

  const handleChange = (newValue) => {
    setSelectedValue(newValue)
    setValue(newValue)
  }

  return (
    <div>
      <Label>Resource Type</Label>
      <Select data-testid="publish-status" value={selectedValue} onValueChange={handleChange}>
        <SelectTrigger>
          <SelectValue>{selectedValue}</SelectValue>
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            {value?.map((item) => (
              <SelectItem className="font-medium text-sm" key={item} value={item.toUpperCase()}>
                <span className="font-medium text-sm">{item}</span>
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  )
}

FormsSelect.propTypes = {
  setValue: PropTypes.func.isRequired,
  value: PropTypes.arrayOf(PropTypes.string).isRequired,
}

export default FormsSelect
