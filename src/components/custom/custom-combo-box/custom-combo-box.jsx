import { Check, ChevronsUpDown } from "lucide-react"
import PropTypes from "prop-types"

import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { useEffect } from "react"

const CustomComboBox = ({
  setSearchQuery,
  iterateData,
  selectedValue,
  setSelectedValue,
  comboIsOpen,
  setComboIsOpen,
  notFoundMessage = "",
  placeholder = "Search module...",
  width = "w-full",
  disabled = false,
}) => {
  useEffect(() => {
    if (iterateData?.length > 0 && !selectedValue) {
      setSelectedValue(iterateData[0]) // Set default only if not already selected
    }
  }, [iterateData, selectedValue])

  return (
    <Popover open={comboIsOpen} onOpenChange={!disabled ? setComboIsOpen : undefined}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={comboIsOpen}
          className={`justify-between text-gray-600  items-center text-left font-medium ${width}`}
          disabled={disabled}
        >
          {selectedValue || placeholder}
          <ChevronsUpDown size={15} className="opacity-50" />
        </Button>
      </PopoverTrigger>
      {!disabled && (
        <PopoverContent className={`min-w-[${width}] p-0`}>
          <Command>
            <CommandInput
              className="text-sm font-medium h-9"
              placeholder={placeholder}
              onValueChange={setSearchQuery}
            />
            <CommandList>
              <CommandEmpty>No {notFoundMessage} found.</CommandEmpty>
              <CommandGroup>
                {iterateData?.length > 0 &&
                  iterateData.map((module) => (
                    <CommandItem
                      key={module.id}
                      value={module.name}
                      onSelect={() => {
                        setSelectedValue(module) // Store full module object
                        setComboIsOpen(false)
                      }}
                    >
                      {module.name}
                      <Check
                        className={cn(
                          "ml-auto",
                          selectedValue === module.name ? "opacity-100" : "opacity-0"
                        )}
                      />
                    </CommandItem>
                  ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      )}
    </Popover>
  )
}

CustomComboBox.propTypes = {
  iterateData: PropTypes.arrayOf.isRequired,
  selectedValue: PropTypes.string.isRequired,
  setSelectedValue: PropTypes.func.isRequired,
  comboIsOpen: PropTypes.bool.isRequired,
  setComboIsOpen: PropTypes.func.isRequired,
  notFoundMessage: PropTypes.string,
  placeholder: PropTypes.string,
  width: PropTypes.string,
  disabled: PropTypes.bool,
}

export default CustomComboBox
