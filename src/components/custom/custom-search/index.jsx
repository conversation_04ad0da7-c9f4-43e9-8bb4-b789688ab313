import { Input } from "@/components/ui/input"
import { Search } from "lucide-react"
import PropTypes from "prop-types"

const CustomSearchbar = ({
  isRightIcon = false,
  inputSize = "max-w-2xl",
  placeholder = "Search by designation or skills...",
  searchedValue = "",
  setSearchedValue,
}) => {
  return (
    <div className="relative">
      <Search size={18} className={`absolute top-3 ${isRightIcon ? "right-2" : "left-2"}`} />
      <Input
        value={searchedValue}
        className={`${inputSize} indent-5 `}
        placeholder={placeholder}
        onChange={setSearchedValue}
      />
    </div>
  )
}
CustomSearchbar.propTypes = {
  isRightIcon: PropTypes.bool,
  inputSize: PropTypes.string,
  placeholder: PropTypes.string,
  searchedValue: PropTypes.string,
  setSearchedValue: PropTypes.func.isRequired,
}

export default CustomSearchbar
