import { Card } from "@/components/ui/card"
import { addWeeks, startOfMonth } from "date-fns"
import PropTypes from "prop-types"
import {
  CartesianGrid,
  Line,
  LineChart,
  ReferenceDot,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

const generateWeeklyData = (data) => {
  if (!data || data.length === 0) return []

  const startDate = startOfMonth(new Date())

  return data.map((entry, i) => {
    const weekStart = addWeeks(startDate, i)
    console.log("weekStart", weekStart)

    return {
      weeks: entry?.weeks || `Week ${i + 1}`,
      total_marks: entry?.total_marks ?? 0,
    }
  })
}

const LineGraph = ({ data }) => {
  const filterData = generateWeeklyData(data)
  console.log("InterviewDetails", data)
  // Determine Y-Axis range dynamically
  const minMarks = Math.min(...filterData.map((d) => d.total_marks), 0)
  const maxMarks = Math.max(...filterData.map((d) => d.total_marks), 100)

  return (
    <Card className="shadow-lg border-0 rounded-2xl">
      {/* Title */}
      <div className="px-2 pt-2">
        <h3 className="text-gray-800 text-lg font-bold">Quiz Performance</h3>
      </div>

      {/* Chart */}
      <ResponsiveContainer width="100%" height={250}>
        <LineChart data={filterData} margin={{ top: 10, right: 30, left: 10, bottom: 5 }}>
          {/* Grid Lines */}
          <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />

          {/* Y-Axis */}
          <YAxis
            tick={{ fontSize: 12, fill: "#6B7280" }}
            tickLine={false}
            axisLine={false}
            domain={[minMarks, maxMarks]}
          />

          {/* X-Axis */}
          <XAxis
            dataKey="weeks"
            tick={{ fontSize: 12, fill: "#6B7280" }}
            tickLine={false}
            axisLine={{ stroke: "#D1D5DB" }}
          />

          {/* Tooltip for Hover Details */}
          <Tooltip
            contentStyle={{ backgroundColor: "#fff", borderRadius: "8px", borderColor: "#ddd" }}
          />

          {/* Highlight First Data Point */}
          {filterData.length > 0 && (
            <ReferenceDot
              x={filterData[0].weeks}
              y={filterData[0].total_marks}
              r={8}
              fill="#DC2626"
              stroke="white"
              strokeWidth={2}
            />
          )}

          {/* Line */}
          <Line
            type="monotone"
            dataKey="total_marks"
            stroke="#2563EB"
            strokeWidth={3}
            strokeOpacity={1}
            dot={{ r: 6, fill: "#2563EB", strokeWidth: 2, stroke: "white" }}
            activeDot={{ r: 8, fill: "#2563EB", stroke: "white" }}
          />
        </LineChart>
      </ResponsiveContainer>
    </Card>
  )
}

LineGraph.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      weeks: PropTypes.string.isRequired,
      total_marks: PropTypes.number.isRequired,
    })
  ).isRequired,
}

export default LineGraph
