/* eslint-disable no-nested-ternary */
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { File, Upload, X } from "lucide-react"
import PropTypes from "prop-types"
import { useEffect, useRef, useState } from "react"

const FileUploadCard = ({ setValue, onChange, value, descriptions, disables, fileType }) => {
  const [dragActive, setDragActive] = useState(false)
  const [files, setFiles] = useState([])
  const inputRef = useRef(null)

  const handleDrag = (e) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    let droppedFiles = Array.from(e.dataTransfer.files)

    // Filter files based on fileType
    if (fileType === "zip") {
      droppedFiles = droppedFiles.filter((file) => file.name.endsWith(".zip"))
    } else if (fileType === "document") {
      // Add handling for 'document' type
      droppedFiles = droppedFiles.filter(
        (file) =>
          [
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "text/plain",
            "text/csv",
          ].includes(file.type) ||
          [".pdf", ".doc", ".docx", ".txt", ".csv"].some((ext) =>
            file.name.toLowerCase().endsWith(ext)
          )
      )
    }

    setFiles(droppedFiles)
    setValue("docs", droppedFiles, { shouldValidate: true })
    onChange(droppedFiles)
  }

  const handleChange = (e) => {
    e.preventDefault()
    let selectedFiles = Array.from(e.target.files)

    // Filter files based on fileType
    if (fileType === "zip") {
      selectedFiles = selectedFiles.filter((file) => file.name.endsWith(".zip"))
    } else if (fileType === "document") {
      // Add handling for 'document' type
      selectedFiles = selectedFiles.filter(
        (file) =>
          [
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "text/plain",
            "text/csv",
          ].includes(file.type) ||
          [".pdf", ".doc", ".docx", ".txt", ".csv"].some((ext) =>
            file.name.toLowerCase().endsWith(ext)
          )
      )
    }

    setFiles(selectedFiles)
    // Check if setValue is available before calling it (it's optional based on PropTypes)
    if (setValue) {
      setValue("docs", selectedFiles, { shouldValidate: true })
    }
    onChange(selectedFiles)
  }

  const removeFile = (e, index) => {
    e?.stopPropagation()
    const removedFiles = files.filter((_, i) => i !== index)
    setFiles(removedFiles)
    // Check if setValue is available before calling it
    if (setValue) {
      setValue("docs", removedFiles, { shouldValidate: true })
    }
    // Corrected: Pass the updated array, not null
    onChange(removedFiles.length > 0 ? removedFiles : null)
  }

  const openFileDialog = (e) => {
    e.stopPropagation()
    inputRef.current.click()
  }

  useEffect(() => {
    // Ensure value is an array before setting files
    if (Array.isArray(value)) {
      setFiles(value)
    } else if (value === null) {
      setFiles([]) // Clear files if value becomes null
    }
  }, [value])

  return (
    <Card
      onClick={disables ? null : openFileDialog} // Disable click if `disables` is true
      className={`w-full p-3 mt-2 border-slate-300 text-center ${dragActive ? "border-primary border-2" : "bg-background"} ${disables ? "opacity-50 cursor-not-allowed" : ""}`}
    >
      <div
        onDragEnter={disables ? null : handleDrag}
        onDragLeave={disables ? null : handleDrag}
        onDragOver={disables ? null : handleDrag}
        onDrop={disables ? null : handleDrop}
        className="space-y-6"
      >
        <div className="w-14 h-14 mx-auto bg-primary/10 rounded-md flex justify-center items-center">
          <Upload className="w-4 h-4 text-primary" />
        </div>

        <div className="space-y-2">
          <h3 className="text-base font-semibold">
            {descriptions ?? (
              <div>
                Drag & drop{" "}
                <span className="text-primary">
                  {fileType === "zip"
                    ? "ZIP files"
                    : fileType === "document"
                      ? "documents" // Updated text
                      : "here"}
                </span>
              </div>
            )}
          </h3>

          <p className="text-sm text-muted-foreground">
            or{" "}
            <Button
              type="button"
              onClick={openFileDialog}
              className="text-primary hover:underline focus:outline-none"
              disabled={disables} // Disable button if `disables` is true
            >
              browse files
            </Button>{" "}
            on your computer
          </p>
        </div>

        <input
          ref={inputRef}
          type="file"
          multiple
          onChange={handleChange}
          accept={
            fileType === "zip"
              ? ".zip"
              : fileType === "csv"
                ? ".csv"
                : fileType === "excel"
                  ? ".xls,.xlsx"
                  : fileType === "csv-excel"
                    ? ".csv,.xls,.xlsx"
                    : fileType === "all-docs"
                      ? ".pdf,.doc,.docx,.txt,.csv"
                      : fileType === "document"
                        ? ".pdf,.doc,.docx,.txt,.csv"
                        : fileType === "video"
                          ? ".mp4,.mov,.avi,.mkv"
                          : undefined
          }
          className="hidden"
          disabled={disables}
        />

        {files.length > 0 && (
          <div className="space-y-4">
            {files.map((file, index) => (
              <div
                key={file.name}
                className="flex items-center justify-between p-3 bg-primary/10 text-primary rounded-md"
              >
                <div className="flex items-center space-x-3">
                  <File className="w-5 h-5" />
                  <span className="text-xs font-semibold truncate">
                    {file.name.length > 35 ? `${file.name.slice(0, 35)}...` : file.name}
                  </span>
                </div>
                {!disables && ( // Only show remove button if not disabled
                  <Button
                    type="button"
                    onClick={(e) => removeFile(e, index)}
                    className="text-primary bg-background w-5 h-5 rounded-full hover:text-destructive"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </Card>
  )
}

FileUploadCard.propTypes = {
  setValue: PropTypes.func,
  onChange: PropTypes.func,
  value: PropTypes.arrayOf(PropTypes.instanceOf(File)),
  disables: PropTypes.bool,
  descriptions: PropTypes.string,
  fileType: PropTypes.string,
}

export default FileUploadCard
