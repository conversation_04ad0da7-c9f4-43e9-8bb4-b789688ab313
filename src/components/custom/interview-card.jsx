import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"
import {
  Award,
  Calendar,
  Clock,
  ExternalLink,
  MessageSquare,
  User2,
  UserCircle,
} from "lucide-react"
import moment from "moment"
import PropTypes from "prop-types"

const StatusConfig = {
  SCHEDULED: {
    color: "bg-blue-100 text-blue-800",
    icon: Clock,
  },
  COMPLETED: {
    color: "bg-green-100 text-green-800",
    icon: Award,
  },
  CANCELLED: {
    color: "bg-red-100 text-red-800",
    icon: MessageSquare,
  },
  REQUESTED: {
    color: "bg-yellow-100 text-yellow-800",
    icon: UserCircle,
  },
}

const InterviewCard = ({ interview, onClick }) => {
  console.log("InterviewCard", interview)

  const StatusIcon = StatusConfig[interview.interview_status]?.icon

  const formatDuration = (duration) => {
    if (!duration) return "-" // or return "" or any fallback text you prefer

    return typeof duration === "string" && duration.includes("min")
      ? duration
      : `${duration} minutes`
  }

  return (
    <Card
      className="group cursor-pointer hover:shadow-lg transition-all duration-200 border-l-4 hover:border-l-primary"
      onClick={() => onClick(interview)}
      tabIndex={0}
      role="button"
      aria-label={`Interview details for ${interview.student_name || "Student"}`}
      onKeyPress={(e) => e.key === "Enter" && onClick(interview)}
    >
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start gap-4">
          <CardTitle className="text-lg font-semibold group-hover:text-primary transition-colors">
            {interview.title}
          </CardTitle>
          <Badge
            className={cn(
              "flex items-center gap-1 px-3 py-1",
              StatusConfig[interview.interview_status]?.color || "bg-gray-100"
            )}
          >
            {StatusIcon && <StatusIcon className="h-3 w-3" />}
            {interview.interview_status.charAt(0).toUpperCase() +
              interview.interview_status.slice(1).toLowerCase()}
          </Badge>
        </div>
        <CardDescription className="line-clamp-2 mt-1">{interview.description}</CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-3">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-2 text-muted-foreground hover:text-primary transition-colors">
                    <UserCircle className="h-4 w-4 flex-shrink-0" />
                    <span className="text-sm truncate">
                      {interview.interviewer_name || "Interviewer"}
                    </span>
                  </div>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p className="font-medium">Interviewer</p>
                  <p className="text-sm text-muted-foreground">
                    {interview.interviewer_name || "Interviewer"}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-2 text-muted-foreground hover:text-primary transition-colors">
                    <User2 className="h-4 w-4 flex-shrink-0" />
                    <span className="text-sm truncate">{interview.student_name || "Student"}</span>
                  </div>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p className="font-medium">Student</p>
                  <p className="text-sm text-muted-foreground">
                    {interview.student_name || "Student"}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          <div className="space-y-3">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Calendar className="h-4 w-4 flex-shrink-0" />
              <time dateTime={interview.interview_date} className="text-sm">
                {moment(interview.interview_date).isValid()
                  ? moment
                      .parseZone(interview.interview_date)
                      .local()
                      .format("MMM D, YYYY • hh:mm A")
                  : "-"}
              </time>
            </div>
            <div className="flex items-center gap-2 text-muted-foreground">
              <Clock className="h-4 w-4 flex-shrink-0" />
              <span className="text-sm">{formatDuration(interview.interview_duration)}</span>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3 justify-center bg-gradient-to-r from-violet-50 via-violet-100 to-violet-50 py-2 rounded-md">
          <Award className="h-5 w-5 text-primary" />
          <span className="text-sm font-medium text-primary">
            Grade: {interview.interview_grade || "Pending"}
          </span>
        </div>

        <div className="grid grid-cols-2 gap-4 pt-3 border-t">
          {interview.interview_link && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-2 text-primary hover:text-primary transition-colors cursor-pointer">
                    <ExternalLink className="h-4 w-4" />
                    <a
                      href={interview.interview_link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs font-medium"
                      onClick={(e) => e.stopPropagation()}
                    >
                      Join Meeting
                    </a>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Click to join the interview</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          {interview.candidate_feedback ||
            ("no feedback" && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-2 text-emerald-600 hover:text-emerald-700 transition-colors cursor-pointer justify-end">
                      <MessageSquare className="h-4 w-4" />
                      <span className="text-xs font-medium">View Feedback</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Click to view feedback</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ))}
        </div>
      </CardContent>
    </Card>
  )
}
InterviewCard.propTypes = {
  interview: PropTypes.shape({
    status: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    description: PropTypes.string,
    interview: PropTypes.string,
    student: PropTypes.string.isRequired,
    date: PropTypes.string.isRequired,
    duration: PropTypes.string.isRequired,
    grade: PropTypes.string,
    interviewLink: PropTypes.string,
    feedback: PropTypes.string,
  }).isRequired,
  onClick: PropTypes.func.isRequired,
}

export default InterviewCard
