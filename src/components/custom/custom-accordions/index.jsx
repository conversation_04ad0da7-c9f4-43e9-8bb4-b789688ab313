import {
  Accordion,
  AccordionI<PERSON>,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Copy } from "lucide-react"
import PropTypes from "prop-types"
import { useState } from "react"

const CustomAccordions = ({ itratedData }) => {
  const [copied, setCopied] = useState(false)

  const handleCopy = ({ link }) => {
    navigator.clipboard.writeText(link)
    setCopied(true)

    setTimeout(() => {
      setCopied(false)
    }, 2000) // Reset "Copied" state after 2 seconds
  }
  return (
    <Accordion type="single" className="mb-0" collapsible>
      {itratedData?.modules?.length > 0 &&
        itratedData?.modules?.map((item, index) => (
          <AccordionItem
            key={item}
            value={`item-${index}`}
            className="border mb-2 shadow-md rounded-lg"
          >
            <AccordionTrigger className="p-4 text-lg font-semibold h-8 mt-3 transition flex justify-between items-center">
              <span>{item?.module_name}</span>
            </AccordionTrigger>
            {item?.resources?.length > 0 &&
              item?.resources?.map((topic) =>
                topic ? ( // ✅ Ensure topic is not undefined
                  <AccordionContent key={index} className="p-4 bg-white border-t">
                    <p className="text-primary font-medium text-sm">{topic?.resource_name}</p>
                    <div className="flex my-4 items-center space-x-2 border border-gray-300 rounded-lg p-2 w-full max-w-md">
                      <Input
                        type="text"
                        value={topic?.resource_link || ""}
                        readOnly
                        className="flex-1 text-sm h-6 focus:outline-none border-none"
                        size="sm"
                      />
                      <Button onClick={handleCopy} variant="outline" className="px-2 py-0">
                        {copied ? "✔" : <Copy className="w-3 h-3" />}
                      </Button>
                    </div>
                    <div className="">
                      <span className="text-primary">Descriptions</span>
                      <p>{topic.resource_descriptions || "No description available"}</p>
                    </div>
                  </AccordionContent>
                ) : null
              )}
          </AccordionItem>
        ))}
    </Accordion>
  )
}
CustomAccordions.propTypes = {
  itratedData: PropTypes.arrayOf(
    PropTypes.shape({
      module: PropTypes.string,
      topics: PropTypes.arrayOf(PropTypes.string),
    })
  ).isRequired,
}
export default CustomAccordions
