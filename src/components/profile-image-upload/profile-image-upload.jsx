/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable import/no-unresolved */
import propTypes from "prop-types"
import { Camera } from "lucide-react"
import { useState } from "react"
import { Card } from "../ui/card"
import { Input } from "../ui/input"
// eslint-disable-next-line import/no-absolute-path
import dummyAvatar from "/dummy-avatar.png"

function ProfileImageUpload({ image, handleImageChange, size = "md" }) {
  const [isHovered, setIsHovered] = useState(false)

  const sizeClasses = {
    sm: "w-24 h-24",
    md: "w-34 h-34",
    lg: "w-44 h-44",
  }

  const handleImageUpload = (e) => {
    const file = e.target.files[0]
    if (file) {
      const render = new FileReader()
      render.onloadend = () => {
        handleImageChange({
          file,
          preview: render.result,
        })
      }
      // This converts the file to a base64 URL string
      // When complete, it triggers onloadend above
      render.readAsDataURL(file)
    }
  }

  return (
    <Card
      className={`${sizeClasses[size]} relative cursor-pointer overflow-hidden rounded-2xl
      ${isHovered ? "border-6 border-white" : "border-4 border-white"}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <img src={image || dummyAvatar} alt="profile" className="w-full h-full object-cover" />
      <label
        htmlFor="profile-upload"
        className={`absolute inset-0 flex flex-col justify-center items-center bg-gray-400 cursor-pointer 
          ${isHovered ? "opacity-80" : "opacity-0"} border-2 border-black rounded-2xl transition-opacity duration-300`}
      >
        <Camera className="w-14 h-14 fill-white p-1" />
        <span className="text-md font-bold mt-1">Edit</span>
      </label>
      <Input
        id="profile-upload"
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
        className="hidden"
      />
    </Card>
  )
}

ProfileImageUpload.propTypes = {
  image: propTypes.string,
  handleImageChange: propTypes.func,
  size: propTypes.string,
}

export default ProfileImageUpload
