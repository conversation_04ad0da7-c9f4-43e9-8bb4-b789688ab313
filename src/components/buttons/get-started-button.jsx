import { <PERSON><PERSON><PERSON> } from "lucide-react"
import { cn } from "@/lib/utils"
import PropTypes from "prop-types"
import { But<PERSON> } from "../ui/button"

export default function GetStartedButton({
  text = "Get started",
  className,
  onClick,
  icon: Icon = ArrowRight,
}) {
  return (
    <div className="min-h-12 w-40">
      <Button
        className={cn(
          "group flex h-[40px] w-40 items-center justify-center gap-3 rounded-lg bg-primary p-2 font-bold transition-colors duration-100 ease-in-out hover:bg-white hover:border hover:border-primary",
          className
        )}
        onClick={onClick}
      >
        <span
          className={cn(
            "text-white transition-colors duration-100 ease-in-out group-hover:text-primary"
          )}
        >
          {" "}
          {text}{" "}
        </span>
        <div
          className={cn(
            "relative flex h-7 w-7 items-center justify-center overflow-hidden rounded-full transition-transform duration-100",
            "bg-white group-hover:bg-primary"
          )}
        >
          <div className="absolute left-0 flex h-7 w-14 -translate-x-1/2 items-center justify-center transition-all duration-200 ease-in-out group-hover:translate-x-0">
            <Icon
              size={16}
              className={cn("size-7 transform p-1 text-white opacity-0 group-hover:opacity-100")}
            />
            <Icon
              size={16}
              className={cn(
                "size-7 transform p-1 text-primary opacity-100 transition-transform duration-300 ease-in-out group-hover:opacity-0"
              )}
            />
          </div>
        </div>
      </Button>
    </div>
  )
}

GetStartedButton.propTypes = {
  text: PropTypes.string,
  className: PropTypes.string,
  onClick: PropTypes.func,
  icon: PropTypes.element,
}
