import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Archive, CheckCircle, EllipsisVertical, EyeOff, FileText } from "lucide-react"
import PropTypes from "prop-types"
import { Button } from "./ui/button"
import WrapToolTip from "./wrap-tool-tip/wrap-tool-tip"

const StatusDropDown = ({ isLoading, activeStatus, setActiveStatus, isReadyToPublish }) => {
  const PUBLISH_STATUSES = {
    PUBLISHED: "PUBLISHED",
    DRAFT: "DRAFT",
    ARCHIVED: "ARCHIVED",
    UNLISTED: "UNLISTED",
  }

  const STATUS_ICONS = {
    PUBLISHED: { icon: <CheckCircle size={15} className="text-green-500" />, label: "Published" },
    DRAFT: { icon: <FileText size={15} className="text-gray-500" />, label: "Draft" },
    ARCHIVED: { icon: <Archive size={15} className="text-red-500" />, label: "Archived" },
    UNLISTED: { icon: <EyeOff size={15} className="text-yellow-500" />, label: "Unlisted" },
  }

  const getDropDownItem = (status) => {
    const isDisabled = status === PUBLISH_STATUSES.PUBLISHED && !isReadyToPublish
    const item = (
      <DropdownMenuItem
        key={status}
        onClick={(e) => setActiveStatus(status, e)}
        disabled={isLoading || isDisabled}
        className={`flex items-center gap-2 ${activeStatus === status ? "bg-muted" : ""}`}
      >
        {STATUS_ICONS[status].icon}
        <span className="font-semibold text-gray-600 text-xs">{STATUS_ICONS[status].label}</span>
      </DropdownMenuItem>
    )

    // if isReadyToPublish is false, then disable item and Wrap with tooltip
    if (isDisabled)
      return (
        <WrapToolTip
          toolTipContent="To publish, course modules need to be present."
          delayDuration={50}
          side="top"
        >
          {item}
        </WrapToolTip>
      )

    return item
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8 p-0">
          <EllipsisVertical size={18} />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-40">
        {Object.values(PUBLISH_STATUSES).map((status) => getDropDownItem(status))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

StatusDropDown.propTypes = {
  isLoading: PropTypes.bool,
  activeStatus: PropTypes.string,
  setActiveStatus: PropTypes.func,
  isReadyToPublish: PropTypes.bool,
}

export default StatusDropDown
