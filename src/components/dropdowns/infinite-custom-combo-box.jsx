/* eslint-disable react/forbid-prop-types */
import LoadingSpinner from "@/components/custom/LoadingSpinner"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { Check, ChevronsUpDown } from "lucide-react"
import PropTypes from "prop-types"
import { useState } from "react"

export default function InfiniteCustomComboBox({
  selected,
  options,
  onSelectChange,
  handleSearchChange,
  keyName,
  hasMore = false,
  isLoading = false,
  loadMoreOptions,
}) {
  const [open, setOpen] = useState(false)

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
        >
          {selected?.label ? (
            selected.label
          ) : (
            <span className="text-muted text-sm">Select {keyName}</span>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-1">
        <Command>
          {handleSearchChange ? (
            <CommandInput
              placeholder="Search module..."
              onValueChange={(value) => handleSearchChange(value, keyName)}
            />
          ) : (
            <CommandInput placeholder="Search module..." />
          )}
          {!isLoading && <CommandEmpty>No options found</CommandEmpty>}
          <CommandGroup>
            {options.map((option) => (
              <CommandItem
                key={option.value}
                // Given User Searchable Value in value props
                value={option.label}
                onSelect={() => {
                  onSelectChange(option, keyName)
                  setOpen(false)
                }}
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    selected?.value === option.value ? "opacity-100" : "opacity-0"
                  )}
                />
                {option.label}
              </CommandItem>
            ))}
          </CommandGroup>
          {hasMore && (
            <Button variant="outline" className="w-full text-center my-1" onClick={loadMoreOptions}>
              {isLoading ? <LoadingSpinner /> : "Click to load more"}
            </Button>
          )}
        </Command>
      </PopoverContent>
    </Popover>
  )
}

InfiniteCustomComboBox.propTypes = {
  selected: PropTypes.shape({
    label: PropTypes.any,
    value: PropTypes.any,
  }),
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.any.isRequired,
      value: PropTypes.any.isRequired,
    })
  ).isRequired,
  onSelectChange: PropTypes.func.isRequired,
  handleSearchChange: PropTypes.func,
  keyName: PropTypes.string,
  hasMore: PropTypes.bool,
  isLoading: PropTypes.bool,
  loadMoreOptions: PropTypes.func,
}
