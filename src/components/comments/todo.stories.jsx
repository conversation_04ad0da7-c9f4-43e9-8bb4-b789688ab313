import CommentCard from "./comments-card"

export default {
  title: "Component/Comments/CommentsCard",
  component: CommentCard,
  tags: ["autodocs"],
}

function Template(args) {
  return <CommentCard name={args?.name} email={args?.email} body={args?.body} />
}

export const Default = Template.bind({})
// email, body, name
Default.args = {
  email: "<EMAIL>",
  body: "Hello this is data",
  name: "SDT",
}

export const ErrorComponent = Template.bind({})
// email, body, name
ErrorComponent.args = {
  email: null,
  body: "Hello this is data",
  name: "SDT",
}
