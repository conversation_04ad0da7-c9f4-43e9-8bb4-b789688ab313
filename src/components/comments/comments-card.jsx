import { Card, CardDescription, CardTitle } from "@/components/ui/card"
import PropTypes from "prop-types"
import { useTranslation } from "react-i18next"
import ct from "@constants/"

/**
 * TodoCard is a component that displays a comments card.
 * @param {string} email - User Email.
 * @param {string} body - User Comment.
 * @param {string} name - Username
 * @return {JSX.Element} React Component.
 */

export default function CommentCard({ email, body, name }) {
  const { t } = useTranslation()

  return (
    <Card className="m-5 p-5 w-[250px] h-[200px]">
      <CardTitle className="text-base text-amber-500">
        {t(ct.translate.todo.user)}:{name}
      </CardTitle>
      <CardDescription className="text-base">
        <p className="text-sm text-gray-500">
          {t(ct.translate.todo.postedBy)}: {email}
        </p>
        <span className="text-sm py-2 text-opasty-1 text-wrap font-bold">
          {t(ct.translate.todo.comments)}:
        </span>
        {body?.slice(0, 50)}
      </CardDescription>
    </Card>
  )
}

CommentCard.propTypes = {
  body: PropTypes.string,
  name: PropTypes.string,
  email: PropTypes.string,
}
