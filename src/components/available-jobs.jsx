import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import PropTypes from "prop-types"

function AvailableJobs({
  jobs,
  handleFilterChange,
  jobFilter,
  currentPage,
  totalPages,
  onPageChange,
  isLoading,
}) {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Select value={jobFilter} onValueChange={handleFilterChange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter jobs" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Jobs</SelectItem>
            <SelectItem value="applied">Applied Jobs</SelectItem>
            <SelectItem value="recommended">Recommended Jobs</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {isLoading ? (
        <div>Loading...</div>
      ) : (
        <div className="space-y-4">
          {jobs.map((job) => (
            <div key={job.id} className="border p-4 rounded-lg">
              <h3 className="text-lg font-semibold">{job.designation}</h3>
              <div className="mt-2 text-sm text-gray-600">
                <p>
                  Experience: {job.minExp} - {job.maxExp} years
                </p>
                <p>Location: {job.location}</p>
                <div className="flex gap-2 mt-2">
                  {job.skills.map((skill) => (
                    <span key={skill} className="bg-gray-100 px-2 py-1 rounded">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}

          <div className="flex justify-center gap-4 mt-4">
            <Button
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 1 || isLoading}
              variant="outline"
            >
              Previous
            </Button>
            <span className="flex items-center">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage === totalPages || isLoading}
              variant="outline"
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

AvailableJobs.propTypes = {
  jobs: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      designation: PropTypes.string.isRequired,
      minExp: PropTypes.number.isRequired,
      maxExp: PropTypes.number.isRequired,
      location: PropTypes.string.isRequired,
      skills: PropTypes.arrayOf(PropTypes.string).isRequired,
    })
  ).isRequired,
  handleFilterChange: PropTypes.func.isRequired,
  jobFilter: PropTypes.string.isRequired,
  currentPage: PropTypes.number.isRequired,
  totalPages: PropTypes.number.isRequired,
  onPageChange: PropTypes.func.isRequired,
  isLoading: PropTypes.bool.isRequired,
}

export default AvailableJobs
