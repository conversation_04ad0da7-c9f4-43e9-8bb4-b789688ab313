import { useState, useEffect } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  CheckCircle,
  CheckSquare,
  MoreVertical,
  Briefcase,
  MapPin,
  Mail,
  Clock,
  Layers,
} from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { formatDistanceToNow } from "date-fns"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useNavigate } from "react-router-dom"
import { motion } from "framer-motion"
import PropTypes from "prop-types"
import { useToggleApplyStatus } from "@/services/query/jd.query"
import { failureToast, successToast } from "../custom/toasts/tosters"

function CandidateCard({
  candidate,
  showApplied = false,
  onArchiveClicked,
  handleCVAppliedClicked,
  candidateFilter,
  jobId,
  refetch,
}) {
  const [currentApplied, setCurrentApplied] = useState(candidate.applied_at !== null)

  useEffect(() => {
    if (candidateFilter === "recommended") {
      setCurrentApplied(false)
    } else if (candidateFilter === "applied") {
      setCurrentApplied(true)
    }
  }, [candidateFilter])

  const { mutateAsync: toggleApply } = useToggleApplyStatus()

  const onToggleApply = async (candidateName, resumeID) => {
    console.log("onToggleApply", jobId, resumeID)

    try {
      await toggleApply({
        job_id: jobId,
        resume_id: resumeID,
        student_feedback: "",
        notes: "",
      })
      refetch()
      setCurrentApplied((prev) => !prev)
      handleCVAppliedClicked?.(jobId, !currentApplied)

      successToast(
        `${candidateName} ${!currentApplied ? "Applied" : "Unapplied"}`,
        `${candidateName} ${!currentApplied ? "Applied" : "Unapplied"} successfully`
      )
    } catch (err) {
      if (err?.response.data.error.code === "INTEGRITY_ERROR") {
        failureToast("Failed to  Duplicate apply status", "You have already applied for this job")
      }
    }
  }

  const navigate = useNavigate()
  const initials = candidate.name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase()

  const handleCandidateClick = () => {
    navigate(`/candidates/details/${candidate.id}`, { state: { candidate } })
  }

  const formatDate = (dateString) => {
    if (!dateString) return ""
    return formatDistanceToNow(new Date(dateString), { addSuffix: true })
  }

  // Calculate match color based on the distance
  const getMatchColor = (distance) => {
    const matchPercentage = (1 - distance) * 100
    if (matchPercentage >= 85) return "bg-emerald-50 text-emerald-700 border-emerald-200"
    if (matchPercentage >= 70) return "bg-green-50 text-green-700 border-green-200"
    if (matchPercentage >= 55) return "bg-yellow-50 text-yellow-700 border-yellow-200"
    return "bg-gray-100 text-gray-700 border-gray-200"
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card
        className={`
        group relative overflow-hidden
        py-2 rounded-xl
        bg-white
        cursor-pointer 
        border-l-4 ${currentApplied ? "border-l-green-500" : "border-l-transparent hover:border-l-primary"}
        transition-all duration-300
        hover:shadow-lg hover:shadow-violet-100
        hover:scale-[1.02]
        focus:outline-none focus:ring-2 focus:ring-violet-200
      `}
      >
        <CardContent className="flex flex-col gap-4 p-6">
          <div className="flex items-start gap-4">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Avatar
                onClick={handleCandidateClick}
                className="cursor-pointer shrink-0 ring-2 ring-transparent group-hover:ring-primary/20 transition-all h-16 w-16"
              >
                {candidate.avatar_url ? (
                  <AvatarImage src={candidate.avatar_url} alt={candidate.name} />
                ) : (
                  <AvatarFallback className="bg-primary/10 text-primary text-lg">
                    {initials}
                  </AvatarFallback>
                )}
              </Avatar>
            </motion.div>
            <div className="flex-grow min-w-0">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Button
                    className="font-bold group-hover:text-primary text-lg cursor-pointer hover:text-primary transition-colors"
                    onClick={handleCandidateClick}
                  >
                    {candidate.name}
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mt-3">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Briefcase className="h-4 w-4 text-primary/70" />
                    <span className="text-sm font-medium">{candidate.designation}</span>
                  </div>
                  <div className="text-sm text-gray-600 pl-6">at {candidate.company}</div>
                </div>

                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-primary/70" />
                    <span className="text-sm font-medium">
                      {candidate.total_years_of_exp} years
                    </span>
                  </div>
                </div>

                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-primary/70" />
                    <span className="text-sm font-medium">{candidate.location || "-"}</span>
                  </div>
                </div>

                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-primary/70" />
                    <span className="text-sm text-gray-600 truncate">{candidate.email}</span>
                  </div>
                </div>
              </div>

              <div className="flex flex-wrap gap-x-4 gap-y-2 mt-4">
                {candidate.modified_at && (
                  <span className="text-xs text-gray-500 flex items-center gap-1">
                    <Clock className="h-3 w-3 text-gray-400" />
                    Last modified: {formatDate(candidate.modified_at)}
                  </span>
                )}
              </div>

              <div className="mt-4 space-y-3">
                <div className="flex items-start gap-2">
                  <Layers className="h-5 w-5 text-primary/70 mt-1 flex-shrink-0" />
                  <div className="w-full">
                    <strong className="text-sm block mb-1">Primary Skills:</strong>
                    <div className="flex flex-wrap gap-2">
                      {candidate.primary_skills.split(",").map((skill) => (
                        <Badge
                          key={skill.id}
                          variant="outline"
                          className="bg-blue-50 text-primary border border-violet-200 dark:bg-gray-800 dark:text-violet-400 px-2 py-0.5"
                        >
                          {skill.trim()}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <Layers className="h-5 w-5 text-primary/70 mt-1 flex-shrink-0" />
                  <div className="w-full">
                    <strong className="text-sm block mb-1">Secondary Skills:</strong>
                    <div className="flex flex-wrap gap-2">
                      {candidate.secondary_skills.split(",").map((skill) => (
                        <Badge
                          key={skill.id}
                          variant="outline"
                          className="bg-blue-50 text-gray-700 border border-violet-200 dark:bg-gray-800 dark:text-violet-400 px-2 py-0.5"
                        >
                          {skill.trim()}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex mt-2 text-nowrap">
              {candidate.distance && candidateFilter !== "applied" && (
                <Badge className={`text-xs ${getMatchColor(candidate.distance)}`}>
                  {((1 - candidate.distance) * 100).toFixed(0)}% Match
                </Badge>
              )}
            </div>
            {showApplied && (
              <div className="flex gap-2">
                <Button
                  className=" mt-1.5"
                  onClick={() => onToggleApply(candidate.name, candidate.resume_id)}
                >
                  {currentApplied ? (
                    <CheckCircle className="h-5 w-5 -mt-1 text-green-500" />
                  ) : (
                    <>
                      <CheckSquare className="h-5 w-5 text-gray-400" />
                      <span className="text-gray-500 ms-2 font-medium">Mark Applied</span>
                    </>
                  )}
                </Button>
                {currentApplied && (
                  <div className="text-sm text-nowrap text-green-600 flex items-center gap-2">
                    <span>Applied {formatDate(candidate.applied_at)}</span>
                  </div>
                )}
              </div>
            )}
            <div className="flex gap-2 shrink-0">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="link"
                    size="icon"
                    className="hover:bg-primary/10 text-gray-500 hover:text-primary rounded-full"
                  >
                    <MoreVertical className="h-5 w-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem
                    onClick={() => onArchiveClicked(candidate.id)}
                    className="cursor-pointer hover:bg-primary/10"
                  >
                    Update Status
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => onArchiveClicked(candidate.id)}
                    className="cursor-pointer hover:bg-primary/10"
                  >
                    Add Notes
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

CandidateCard.propTypes = {
  candidate: PropTypes.shape({
    id: PropTypes.number.isRequired,
    name: PropTypes.string.isRequired,
    designation: PropTypes.string.isRequired,
    company: PropTypes.string.isRequired,
    location: PropTypes.string.isRequired,
    total_years_of_exp: PropTypes.number.isRequired,
    primary_skills: PropTypes.string.isRequired,
    secondary_skills: PropTypes.string.isRequired,
    cv_link: PropTypes.string.isRequired,
    applied_at: PropTypes.string,
    distance: PropTypes.number,
    email: PropTypes.string.isRequired,
    modified_at: PropTypes.string,
    avatar_url: PropTypes.string,
    resume_id: PropTypes.number.isRequired,
  }).isRequired,
  handleCVAppliedClicked: PropTypes.func.isRequired,
  showApplied: PropTypes.bool,
  onArchiveClicked: PropTypes.func.isRequired,
  candidateFilter: PropTypes.string,
  jobId: PropTypes.number.isRequired,
  refetch: PropTypes.func.isRequired,
}

export default CandidateCard
