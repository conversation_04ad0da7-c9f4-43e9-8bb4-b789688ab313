import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva } from "class-variance-authority"
import { cn } from "@/lib/utils"

const inputVariants = cva(
  `flex h-10 w-full  rounded-md border-[.12rem]  bg-white px-3 py-2
   text-sm ring-offset-white file:border-0 mt-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-slate-500 
   focus-visible:outline-none 
   dark:border-slate-800 dark:bg-slate-950 
   dark:ring-offset-slate-950 dark:placeholder:text-slate-400 dark:focus-visible:ring-slate-300 font-medium text-sm placeholder:font-medium disabled:cursor-not-allowed disabled:opacity-50`,
  {
    variants: {
      variant: {
        default: "",
        error:
          "border-red-500 text-red-900 placeholder-red-700 text-sm rounded-lg focus:ring-red-500 dark:bg-gray-700 focus:border-red-500 block w-full p-2.5 dark:text-red-500 dark:placeholder-red-500 dark:border-red-500",
        success:
          "border-green-500 text-green-900 dark:text-green-400 placeholder-green-700 dark:placeholder-green-500 text-sm rounded-lg focus:ring-green-500 focus:border-green-500 block w-full p-2.5 dark:bg-gray-700 dark:border-green-500",
        disabled:
          "peer border-blue-gray-200  bg-transparent px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0  focus:border-2 focus:border-gray-900 disabled:border-0 disabled:bg-blue-gray-50",
      },
      size: {
        xs: "h-8",
        sm: "h-9",
        default: "h-10 ",
        lg: "h-11  ",
        xl: "h-12",
        "2xl": "h-14 text-base py-1",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

const Input = React.forwardRef(({ className, variant, size, asChild = false, ...props }, ref) => {
  const Comp = asChild ? Slot : "input"
  return <Comp className={cn(inputVariants({ variant, size, className }))} ref={ref} {...props} />
})
Input.displayName = "Input"

export { Input, inputVariants }
