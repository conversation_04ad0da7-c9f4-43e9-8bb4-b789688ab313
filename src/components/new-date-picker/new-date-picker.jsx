import { But<PERSON> } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    <PERSON>alog<PERSON>lose,
    <PERSON>alog<PERSON>ontent,
    <PERSON>alogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { formsProps } from "@/utils/props-types"
import {
    addMonths,
    eachDayOfInterval,
    endOfMonth,
    format,
    isSameDay,
    isSameMonth,
    isToday,
    parseISO,
    startOfMonth,
    subMonths,
} from "date-fns"
import { CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react"
import { useEffect, useState } from "react"


const AppointmentScheduler = ({
  dataTestID = "test-id",
  dataTestIDError = "error",
  fieldControlName,
  control,
  label,
  placeholder = "Schedule",
  isRequired,
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [currentMonth, setCurrentMonth] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState(new Date())

  // Initialize with existing value if available
  const initializeFromValue = (value) => {
    if (value) {
      try {
        const date = parseISO(value)
        setSelectedDate(date)
        // Set current month to the selected date's month for better UX
        setCurrentMonth(date)
      } catch (error) {
        console.error("Error parsing existing date value:", error)
      }
    }
  }

  const handlePreviousMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1))
  }

  const handleNextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1))
  }

  const handleDateSelect = (date) => {
    setSelectedDate(date)
  }

  const handleSave = (onChange) => {
    try {
      // Create a new date at midnight in local timezone
      const dateAtMidnight = new Date(
        selectedDate.getFullYear(),
        selectedDate.getMonth(),
        selectedDate.getDate()
      )

      // Convert to ISO string
      const finalDate = dateAtMidnight.toISOString()

      // Pass the final date to the form field
      onChange(finalDate)
      setIsOpen(false)
    } catch (error) {
      console.error("Error creating date:", error)
    }
  }

  // Generate calendar data
  const monthStart = startOfMonth(currentMonth)
  const monthEnd = endOfMonth(currentMonth)
  const daysInMonth = eachDayOfInterval({ start: monthStart, end: monthEnd })

  const weekdays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]

  const startDay = monthStart.getDay()
  const endDay = 6 - monthEnd.getDay()

  const prevMonthDays =
    startDay > 0
      ? eachDayOfInterval({
          start: new Date(currentMonth.getFullYear(), currentMonth.getMonth(), -startDay + 1),
          end: new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 0),
        })
      : []

  const nextMonthDays =
    endDay > 0
      ? eachDayOfInterval({
          start: new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1),
          end: new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, endDay),
        })
      : []

  const allDays = [...prevMonthDays, ...daysInMonth, ...nextMonthDays]

  const calendarRows = []
  for (let i = 0; i < allDays.length; i += 7) {
    calendarRows.push(allDays.slice(i, i + 7))
  }

  // Format the scheduled date for display in local time
  const formatScheduledDate = (isoString) => {
    if (!isoString) return null

    try {
      const date = parseISO(isoString)
      return format(date, "EEE, MMM d, yyyy") // e.g. "Mon, Jan 1, 2023"
    } catch (error) {
      console.error("Error formatting date for display:", error)
      return "Invalid date"
    }
  }

  return (
    <FormField
      control={control}
      name={fieldControlName}
      render={({ field }) => {
        // Use useEffect instead of useState for initialization
        useEffect(() => {
          if (field.value) {
            initializeFromValue(field.value)
          }
        }, [field.value])

        return (
          <FormItem className="flex flex-col">
            {label && (
              <FormLabel>
                {label} {isRequired && <span className="text-red-600">*</span>}{" "}
              </FormLabel>
            )}
            <FormControl>
              <div className="font-sans">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsOpen(true)}
                  className={`
                    relative flex items-center justify-start gap-2 shadow-sm transition-all duration-200
                    px-8 py-1.5 rounded-lg text-sm w-full
                    ${
                      field.value
                        ? " "
                        : "bg-white border border-gray-200 text-gray-700 hover:bg-gray-50"
                    }`}
                  data-testId={dataTestID}
                >
                  <CalendarIcon className="h-4 w-4" />
                  {field.value ? (
                    <span className="font-medium">{formatScheduledDate(field.value)}</span>
                  ) : (
                    <span>{placeholder}</span>
                  )}
                </Button>

                <Dialog open={isOpen} className="rounded-xl shadow-xl" onOpenChange={setIsOpen}>
                  <DialogContent className="border-0 p-0 gap-0 overflow-hidden">
                    <DialogHeader className="bg-gradient-to-r border-b px-6 py-4">
                      <DialogTitle className="text-2xl text-left font-semibold">
                        Select a Date
                      </DialogTitle>
                      <DialogClose className="absolute hover:opacity-100 opacity-70 right-4 top-4" />
                    </DialogHeader>

                    <div className="bg-white p-6">
                      {/* Calendar Navigation */}
                      <div className="flex justify-between items-center mb-6">
                        <button
                          type="button"
                          onClick={handlePreviousMonth}
                          className="flex justify-center p-2 rounded-full text-gray-600 hover:bg-gray-100 items-center"
                          aria-label="Previous month"
                        >
                          <ChevronLeft className="h-5 w-5" />
                        </button>
                        <h2 className="text-gray-800 text-lg font-medium">
                          {format(currentMonth, "MMMM yyyy")}
                        </h2>
                        <button
                          type="button"
                          onClick={handleNextMonth}
                          className="flex justify-center p-2 rounded-full text-gray-600 hover:bg-gray-100 items-center"
                          aria-label="Next month"
                        >
                          <ChevronRight className="h-5 w-5" />
                        </button>
                      </div>

                      {/* Calendar */}
                      <div className="mb-8">
                        <div className="grid grid-cols-7 text-center mb-2">
                          {weekdays.map((day) => (
                            <div key={day} className="text-gray-500 text-xs font-semibold mb-2">
                              {day}
                            </div>
                          ))}
                        </div>

                        <div className="grid grid-cols-7 gap-1">
                          {calendarRows.flat().map((date, i) => {
                            const isCurrentMonth = isSameMonth(date, currentMonth)
                            const isSelected = isSameDay(date, selectedDate)
                            const isDayToday = isToday(date)

                            return (
                              <button
                                type="button"
                                key={i}
                                onClick={() => handleDateSelect(date)}
                                className={`
                                  relative h-10 w-10 rounded-full flex items-center justify-center text-sm
                                  transition-all duration-200 font-medium
                                  ${!isCurrentMonth ? "text-gray-300" : isDayToday ? "text-blue-700" : "text-gray-700"}
                                  ${
                                    isSelected
                                      ? "bg-blue-600 text-white shadow-md"
                                      : isCurrentMonth
                                        ? "hover:bg-blue-50"
                                        : "hover:bg-gray-50"
                                  }
                                  ${isDayToday && !isSelected ? "ring-1 ring-blue-200" : ""}
                                `}
                                aria-pressed={isSelected}
                                disabled={!isCurrentMonth}
                              >
                                {date.getDate()}
                                {isSelected && (
                                  <span className="bg-white h-1 rounded-full w-1 -bottom-1 -translate-x-1/2 absolute left-1/2 transform" />
                                )}
                              </button>
                            )
                          })}
                        </div>
                      </div>

                      {/* Selected Date Display */}
                      <div className="flex bg-blue-50 rounded-lg text-blue-700 text-sm font-medium items-center mb-6 px-4 py-3">
                        <CalendarIcon className="h-4 w-4 mr-2" />
                        {format(selectedDate, "EEEE, MMMM d, yyyy")}
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex bg-gray-50 justify-end gap-5 border-t p-4">
                      <Button
                        onClick={() => setIsOpen(false)}
                        variant="outline"
                        className="bg-white hover:bg-gray-50 transition-all"
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={() => handleSave(field.onChange)}
                        variant="primary"
                        className="px-7"
                      >
                        Select
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </FormControl>
            <FormMessage data-testId={dataTestIDError} />
          </FormItem>
        )
      }}
    />
  )
}

AppointmentScheduler.propTypes = formsProps

export default AppointmentScheduler