import { Textarea } from "@/components/ui/textarea"
import PropTypes from "prop-types"

export default function CustomeTextArea({ labelText, placeHolder, className, testid }) {
  return (
    <div className="flex flex-col gap-2">
      <label htmlFor="textarea" className="font-inter text-sm font-semibold mb">
        {labelText}
      </label>
      <Textarea
        id="textarea"
        placeholder={placeHolder}
        data-testid={testid}
        className={className}
      />
    </div>
  )
}

CustomeTextArea.propTypes = {
  labelText: PropTypes.string.isRequired,
  placeHolder: PropTypes.string,

  testid: PropTypes.string,
  className: PropTypes.string,
}
