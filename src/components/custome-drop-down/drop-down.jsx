import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip"
import { ChevronDown } from "lucide-react"
import PropTypes from "prop-types"

export const ReusableDropdown = ({
  label = "",
  options = [],
  selected = null,
  onSelect,
  placeholder = "Select option",
  disabled = false,
}) => {
  const displayText = selected?.label || placeholder
  const shouldTruncate = displayText.length > 18
  const truncatedText = shouldTruncate ? `${displayText.substring(0, 15)}...` : displayText

  return (
    <div className="flex flex-col">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="w-auto justify-between" disabled={disabled}>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="truncate">{truncatedText}</span>
              </TooltipTrigger>
              {shouldTruncate && (
                <TooltipContent className="w-[250px] max-w-[250px]">
                  <p className="break-words whitespace-normal">{displayText}</p>
                </TooltipContent>
              )}
            </Tooltip>
            <ChevronDown className="w-4 h-4 ml-2" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-full">
          {options.map((option) => (
            <DropdownMenuItem
              key={option.value}
              onSelect={() => onSelect(option)}
              className="cursor-pointer"
            >
              {option.label}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

ReusableDropdown.propTypes = {
  label: PropTypes.string.isRequired,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.string.isRequired,
    })
  ),
  selected: PropTypes.shape({
    label: PropTypes.string,
    value: PropTypes.string,
  }),
  onSelect: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  disabled: PropTypes.bool,
}
