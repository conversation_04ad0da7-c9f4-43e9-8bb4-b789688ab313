import { Button } from "@/components/ui/button" // Assuming shadcn button component
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu" // Assuming shadcn dropdown components
import PropTypes from "prop-types"

const StatusDropdown = ({ status, setStatus }) => {
  const options = ["PUBLISHED", "DRAFT", "ARCHIVED", "UNLISTED"]

  const handleChange = (selectedStatus) => {
    setStatus(selectedStatus)
  }

  // Define status colors
  const statusColors = {
    PUBLISHED: "bg-teal-100 text-teal-800 border-teal-200", // Teal for PUBLISHED
    DRAFT: "bg-indigo-100 text-indigo-800 border-indigo-200", // Indigo for DRAFT
    ARCHIVED: "bg-red-100 text-red-800 border-red-200", // Red for ARCHIVED
    UNLISTED: "bg-pink-100 text-pink-800 border-pink-200", // Pink for UNLISTED
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className={`w-full justify-start ${statusColors[status]}`} // Apply dynamic color based on status
        >
          {status}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-48">
        {options.map((option) => (
          <DropdownMenuItem
            key={option}
            onClick={(event) => {
              event.stopPropagation() // Stop event propagation
              handleChange(option)
            }}
          >
            {option}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

StatusDropdown.propTypes = {
  status: PropTypes.string.isRequired,
  setStatus: PropTypes.func.isRequired,
}

export default StatusDropdown
