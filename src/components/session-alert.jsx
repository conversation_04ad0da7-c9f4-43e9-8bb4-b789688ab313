import { useState, useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import ct from "@constants/"
import { TriangleAlert } from "lucide-react"
import { logoutAC } from "@/services/store/slices/user.slice"
import { useMutateLogOut } from "@/services/query/auth.query"
import { getDeviceInfo } from "@/pages/auth/login/utils/device-info"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "./ui/dialog"
import { Button } from "./ui/button"
import { toast } from "./ui/use-toast"
import LoadingSpinner from "./custom/LoadingSpinner"

function SessionAlert() {
  const { id: userID, userRole, isSessionValid } = useSelector((st) => st[ct.store.USER_STORE])
  const { device_info } = getDeviceInfo()
  const dispatch = useDispatch()

  const [open, setOpen] = useState(true)

  const { mutate: mutateLogout, isPending } = useMutateLogOut()

  const handleLogout = () => {
    if (!userID || !userRole) return
    mutateLogout(
      { userID, userRole, device_info },
      {
        onSuccess: () => {
          dispatch(logoutAC())
          setOpen(false) // Close dialog
        },
        onError: (error) => {
          toast({
            title: error?.response?.data?.error?.message ?? "Logout Failed. Please try again later",
            variant: "destructive",
          })
        },
      }
    )
  }

  useEffect(() => {
    if (!isSessionValid) setOpen(true)
  }, [isSessionValid])

  return (
    <Dialog open={!isSessionValid} onOpenChange={setOpen} modal>
      <DialogContent className="sm:max-w-md bg-amber-50">
        <DialogHeader className="space-y-5">
          <div className="flex items-center gap-x-2">
            <TriangleAlert className="w-6 h-6 text-amber-500" />
            <DialogTitle className="m-0">Session Expired</DialogTitle>
          </div>
          <DialogDescription className="font-semibold">
            Your session has expired or is no longer valid. Please log in again to continue using
            the application.
          </DialogDescription>
        </DialogHeader>

        <DialogFooter>
          <Button onClick={handleLogout} variant="primary">
            {isPending ? (
              <LoadingSpinner className="flex items-center justify-center my-8 mx-3" />
            ) : (
              "Back to Login"
            )}
          </Button>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default SessionAlert
