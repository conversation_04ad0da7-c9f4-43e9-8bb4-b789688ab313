import { Switch } from "@/components/ui/switch"
import PropTypes from "prop-types"
import { useEffect, useRef, useState } from "react"

const PriceRangeFilter = ({setSelectedCurrency, setMin, setMax }) => {
  const [minPrice, setMinPrice] = useState(0)
  const [maxPrice, setMaxPrice] = useState(1000)
  const [minValue, setMinValue] = useState(200)
  const [maxValue, setMaxValue] = useState(800)
  const [isDraggingMin, setIsDraggingMin] = useState(false)
  const [isDraggingMax, setIsDraggingMax] = useState(false)
  const [useINR, setUseINR] = useState(false) // Currency switch

  const sliderRef = useRef(null)
  const currencySymbol = useINR ? "₹" : "$"

  const minPos = ((minValue - minPrice) / (maxPrice - minPrice)) * 100
  const maxPos = ((maxValue - minPrice) / (maxPrice - minPrice)) * 100

  const applyPrice = () => {
    setMin(minValue)
    setMax(maxValue)
    setSelectedCurrency(useINR ? "INR" : "USD")
  }

  const handleMinChange = (e) => {
    const value = Math.min(Number(e.target.value), maxValue - 1)
    setMinValue(value)
  }

  const handleMaxChange = (e) => {
    const value = Math.max(Number(e.target.value), minValue + 1)
    setMaxValue(value)
  }

  const handleMinInputChange = (e) => {
    let value = Number(e.target.value)
    value = Math.max(minPrice, Math.min(value, maxValue - 1))
    setMinValue(value)
  }

  const handleMaxInputChange = (e) => {
    let value = Number(e.target.value)
    value = Math.max(minValue + 1, Math.min(value, maxPrice))
    setMaxValue(value)
  }

  const handleMouseDown = (type) => {
    if (type === "min") setIsDraggingMin(true)
    else setIsDraggingMax(true)
  }

  useEffect(() => {
    const handleMouseUp = () => {
      setIsDraggingMin(false)
      setIsDraggingMax(false)
    }

    const handleMouseMove = (e) => {
      if (!isDraggingMin && !isDraggingMax) return
      if (!sliderRef.current) return

      const rect = sliderRef.current.getBoundingClientRect()
      const offsetX = e.clientX - rect.left
      const sliderWidth = rect.width
      const newValue = Math.round((offsetX / sliderWidth) * (maxPrice - minPrice) + minPrice)

      if (isDraggingMin) {
        const value = Math.max(minPrice, Math.min(newValue, maxValue - 1))
        setMinValue(value)
      } else if (isDraggingMax) {
        const value = Math.max(minValue + 1, Math.min(newValue, maxPrice))
        setMaxValue(value)
      }
    }

    document.addEventListener("mousemove", handleMouseMove)
    document.addEventListener("mouseup", handleMouseUp)
    return () => {
      document.removeEventListener("mousemove", handleMouseMove)
      document.removeEventListener("mouseup", handleMouseUp)
    }
  }, [isDraggingMin, isDraggingMax, minPrice, maxPrice, minValue, maxValue])

  return (
    <div className="w-full max-w-md mx-auto p-6 bg-white rounded-xl shadow-md">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600">USD</span>
          <Switch checked={useINR} onCheckedChange={setUseINR} />
          <span className="text-sm text-gray-600">INR</span>
        </div>
      </div>

      <div className="relative mb-6 mt-10 h-10" ref={sliderRef}>
        <div className="absolute top-4 h-2 w-full bg-gray-200 rounded-full"></div>
        <div
          className="absolute top-4 h-2 bg-blue-500 rounded-full"
          style={{ left: `${minPos}%`, width: `${maxPos - minPos}%` }}
        ></div>
        <div
          className="absolute top-1 w-6 h-6 bg-white border-2 border-blue-500 rounded-full shadow cursor-pointer"
          style={{ left: `calc(${minPos}% - 12px)` }}
          onMouseDown={() => handleMouseDown("min")}
        ></div>
        <div
          className="absolute top-1 w-6 h-6 bg-white border-2 border-blue-500 rounded-full shadow cursor-pointer"
          style={{ left: `calc(${maxPos}% - 12px)` }}
          onMouseDown={() => handleMouseDown("max")}
        ></div>
      </div>

      <div className="flex justify-between items-center mb-6">
        <div className="flex flex-col items-center">
          <span className="text-sm text-gray-500">Min</span>
          <div className="flex items-center mt-1 border border-gray-300 rounded-md p-2">
            <span className="text-gray-600 mr-1">{currencySymbol}</span>
            <input
              type="number"
              value={minValue}
              onChange={handleMinInputChange}
              className="w-16 focus:outline-none"
            />
          </div>
        </div>
        <div className="w-6 h-px bg-gray-300"></div>
        <div className="flex flex-col items-center">
          <span className="text-sm text-gray-500">Max</span>
          <div className="flex items-center mt-1 border border-gray-300 rounded-md p-2">
            <span className="text-gray-600 mr-1">{currencySymbol}</span>
            <input
              type="number"
              value={maxValue}
              onChange={handleMaxInputChange}
              className="w-16 focus:outline-none"
            />
          </div>
        </div>
      </div>

      <button
        onClick={applyPrice}
        className="w-full py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
      >
        Apply Filter
      </button>
    </div>
  )
}

PriceRangeFilter.propTypes = {
  setMin: PropTypes.func.isRequired,
  setMax: PropTypes.func.isRequired,
}

export default PriceRangeFilter
