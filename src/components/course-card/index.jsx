import { Button } from "@/components/ui/button"
import { useUpdateCourseStatus } from "@/services/query/create-course-form.query"
import {
  setActiveCourse,
  setActiveCourseDetails,
  setActiveTab,
} from "@/services/store/slices/courses.slice"
import { USER_ROLES } from "@/utils/constants"
import { getStatusColor } from "@/utils/helper"
import { motion } from "framer-motion"
import { ArrowRight, CircleAlert, Clock, Users } from "lucide-react"
import { useEffect, useRef, useState } from "react"
import { useLocation, useNavigate } from "react-router-dom"

import PropTypes from "prop-types"
import { CiEdit } from "react-icons/ci"
import { useDispatch } from "react-redux"

import ct from "@constants/"
import { failureToast, successToast } from "../custom/toasts/tosters"
import StatusDropDown from "../status-dropdown"
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar"
import { Badge } from "../ui/badge"
import { Skeleton } from "../ui/skeleton"
import WrapToolTip from "../wrap-tool-tip/wrap-tool-tip"

export default function CourseCard({ course, onViewCourseDetails, courseType, userRole }) {
  console.log(courseType, "course")

  const navigate = useNavigate()
  const dispatch = useDispatch()
  const [currentStatus, setCurrentStatus] = useState(course.publish_status || "DRAFT")
  const [isHighlighted, setIsHighlighted] = useState(false)
  const [isImageLoaded, setIsImageLoaded] = useState(false)
  const location = useLocation()

  const updateStatusMutation = useUpdateCourseStatus()
  const handleStatusUpdate = async (newStatus, event) => {
    event.stopPropagation()
    updateStatusMutation.mutate(
      { course_id: course?.id, publish_status: newStatus },
      {
        onSuccess: () => {
          setCurrentStatus(newStatus)
          successToast("Status Updated", "Course status was successfully updated!")
        },
        onError: (error) => {
          failureToast("Update Failed", error?.response.data.error.message)
        },
      }
    )
  }
  console.log(course, "coursedata")
  const courseCardRef = useRef(null)
  useEffect(() => {
    const storedCourseId = sessionStorage.getItem("highlightedCourseId")
    if (storedCourseId && storedCourseId === String(course.id)) {
      setIsHighlighted(true)
      setTimeout(() => {
        courseCardRef.current?.scrollIntoView({ behavior: "smooth", block: "center" })
      }, 300)
      const timer = setTimeout(() => setIsHighlighted(false), 2000)
      return () => clearTimeout(timer)
    }
  }, [course.id])

  const setCourseInState = () => {
    dispatch(setActiveCourse(courseType))
    dispatch(setActiveCourseDetails(course))
  }

  const handleCardClick = (e) => {
    // Don't navigate if clicking on interactive elements
    if (
      e.target.closest("button") ||
      e.target.closest("a") ||
      e.target.closest('[role="button"]') ||
      e.target.closest("[data-prevent-navigation]")
    ) {
      return
    }

    setCourseInState()

    if (courseType === "Live courses") {
      sessionStorage.setItem("highlightedCourseId", course.id)
      sessionStorage.setItem("scrollPosition", window.scrollY)
      // eslint-disable-next-line sonarjs/no-duplicate-string
      dispatch(setActiveTab("course-overview"))
      navigate(`/live-courses/${course.id}`, {
        state: { courseData: course.courseDetails },
      })
    } else if (courseType === "My Courses") {
      dispatch(setActiveTab("course-overview"))
      navigate(`${ct.route.LIVE_COURSES}/${course?.id}${ct.route.COURSE_OVERVIEW}`)
    } else if (courseType === "Nano courses") {
      dispatch(setActiveTab("course-overview"))
      navigate(`/nano-courses-list/${course.id}`, {
        state: { courseData: course.courseDetails },
      })
    } else {
      onViewCourseDetails()
    }
  }

  const getDurationBadge = () => {
    if (!course.duration) return null
    return (
      <Badge variant="secondary" className="flex items-center gap-1 text-xs font-normal">
        <Clock className="w-3 h-3" />
        {course.duration}
      </Badge>
    )
  }

  const getEnrollmentBadge = () => {
    if (!course.enrollmentCount) return null
    return (
      <Badge variant="secondary" className="flex items-center gap-1 text-xs font-normal">
        <Users className="w-3 h-3" />
        {course.enrollmentCount} enrolled
      </Badge>
    )
  }

  return (
    <motion.div
      ref={courseCardRef}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`w-full max-w-full mx-auto p-2 sm:p-4 transition-all duration-500 ease-in-out rounded-xl ${isHighlighted
        ? "shadow-[0_0_15px_rgba(131,197,190,0.8)] border-2 border-blue-300 scale-[1.03]"
        : ""
        }`}
    >
      <div
        onClick={handleCardClick}
        className="bg-white dark:bg-slate-900 rounded-xl shadow-sm overflow-hidden transition-all duration-300 hover:shadow-md flex flex-col sm:flex-row border border-gray-100 dark:border-slate-800 cursor-pointer"
      >
        {/* Image Container */}
        <div className="w-full sm:w-[280px] md:w-[320px] h-[200px] sm:h-[240px] relative group p-2 sm:p-3">
          {!isImageLoaded && (
            <Skeleton className="w-full h-full rounded-lg bg-gray-200 dark:bg-slate-700" />
          )}
          <motion.img
            src={course?.imageUrl}
            alt={course?.courseName}
            className={`w-full h-full object-cover rounded-lg transition-all duration-500 ${isImageLoaded ? "block" : "hidden"
              }`}
            onLoad={() => setIsImageLoaded(true)}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          />

          <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg flex items-end p-4">
            <motion.p
              className="text-white font-medium text-sm translate-y-2 group-hover:translate-y-0 transition-transform duration-300"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              View Details
            </motion.p>
          </div>
        </div>

        {/* Content Container */}
        <div className="p-4 sm:p-5 flex-1 flex flex-col">
          {/* Header with title and status */}
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3 mb-2">
            <div>
              <h3 className="text-lg sm:text-xl font-bold dark:text-gray-100 line-clamp-2">
                {course?.courseName?.charAt(0).toUpperCase() + course?.courseName?.slice(1)}
              </h3>
              <div className="flex gap-2 mt-2">
                {getDurationBadge()}
                {getEnrollmentBadge()}
              </div>
            </div>

            <div className="flex items-center gap-2 sm:gap-3" data-prevent-navigation>
              {userRole !== USER_ROLES.STUDENT && (
                <Badge className={`${getStatusColor(currentStatus)} text-xs font-medium`}>
                  {currentStatus
                    ? currentStatus.charAt(0).toUpperCase() + currentStatus.slice(1).toLowerCase()
                    : "Unknown"}
                </Badge>
              )}

              {userRole === USER_ROLES.ADMIN && (
                <div className="flex items-center gap-2" data-prevent-navigation>
                  {/* Module Creation Warning */}
                  {location?.pathname !== "/short-courses-list" && !course?.isModuleExists && (
                    <WrapToolTip
                      toolTipContent={
                        <div className="flex items-center gap-2">
                          <p className="text-sm">Add course modules</p>
                          <Button
                            onClick={(e) => {
                              e.stopPropagation()
                              dispatch(setActiveTab("course-overview"))
                              navigate(
                                `${ct.route.LIVE_COURSES}/${course?.id}${ct.route.COURSE_OVERVIEW}`
                              )
                            }}
                            variant="ghost"
                            size="icon"
                            className="w-6 h-6"
                          >
                            <ArrowRight size={14} />
                          </Button>
                        </div>
                      }
                      delayDuration={100}
                      side="top"
                    >
                      <CircleAlert className="w-5 h-5 text-amber-500 animate-pulse" />
                    </WrapToolTip>
                  )}

                  {/* Status Dropdown */}
                  <StatusDropDown
                    isLoading={updateStatusMutation.isLoading}
                    setActiveStatus={handleStatusUpdate}
                    activeStatus={currentStatus}
                    isReadyToPublish={
                      course?.courseDetails?.course_type === "SHORT" || course?.isModuleExists
                    }
                  />

                  {/* Edit Button */}
                  <WrapToolTip toolTipContent="Edit Course" delayDuration={100} side="top">
                    <Button
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation()
                        navigate(`${ct.route.EDIT_COURSE}`, {
                          state: { courseDetails: course?.courseDetails },
                        })
                      }}
                      className="text-gray-500 hover:text-primary"
                    >
                      <CiEdit className="w-5 h-5" />
                    </Button>
                  </WrapToolTip>
                </div>
              )}
            </div>
          </div>

          {/* Course description */}
          <div className="my-3 flex-1">
            <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-3">
              {course.courseDesc?.replace(/[#*-]/g, "") || "No description available"}
            </p>
          </div>

          {/* Footer with trainer and action button */}
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mt-4 pt-3 border-t border-gray-100 dark:border-slate-800">
            <div className="flex items-center gap-3">
              <Avatar className="w-8 h-8">
                <AvatarImage src={course.trainer?.avatarUrl} />
                <AvatarFallback>{course.trainer?.name?.charAt(0).toUpperCase()}</AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm font-medium dark:text-gray-200">{course.trainer?.name}</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">{course.trainer?.type}</p>
              </div>

              {/* <div>
                <p className="text-sm font-medium dark:text-gray-200">
                  {course?.pricing?.currencySymbol}
                  {course?.pricing?.currentPrice}
                </p>
              </div> */}
            </div>

            {/* <div className="flex justify-end" data-prevent-navigation>
              <Button
                className="w-full sm:w-auto group text-primary font-medium hover:bg-primary/10 transition-colors"
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation()
                  handleCardClick(e)
                }}
              >
                {courseType === "Live courses"
                  ? "Go to Live Course"
                  : courseType === "My Courses"
                    ? "Go to My Course"
                    : "View Details"}
                <ArrowRight
                  size={16}
                  className="ml-2 group-hover:text-primary transition-transform group-hover:translate-x-1"
                />
              </Button>
            </div> */}
          </div>
        </div>
      </div>
    </motion.div>
  )
}

CourseCard.propTypes = {
  course: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    courseName: PropTypes.string.isRequired,
    imageUrl: PropTypes.string.isRequired,
    courseDesc: PropTypes.string,
    publish_status: PropTypes.string,
    duration: PropTypes.string,
    enrollmentCount: PropTypes.number,
    pricing: PropTypes.shape({
      currencySymbol: PropTypes.string.isRequired,
      currentPrice: PropTypes.string.isRequired,
    }),
    trainer: PropTypes.shape({
      avatarUrl: PropTypes.string,
      name: PropTypes.string.isRequired,
      type: PropTypes.string.isRequired,
    }).isRequired,
    courseDetails: PropTypes.objectOf.isRequired,
    isModuleExists: PropTypes.bool.isRequired,
  }).isRequired,
  onViewCourseDetails: PropTypes.func.isRequired,
  courseType: PropTypes.string,
  userRole: PropTypes.string,
}

CourseCard.defaultProps = {
  courseType: "",
  userRole: "",
}
