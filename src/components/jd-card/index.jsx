import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useUpdateJobApplicationStatus } from "@/services/query/jd.query"
import { USER_ROLES } from "@/utils/constants"
import { format, formatDistanceToNow } from "date-fns"
import {
  AlertCircle,
  Award,
  Briefcase,
  Building2,
  Calendar,
  CheckCircle,
  CheckSquare,
  Clock,
  ExternalLink,
  IndianRupee,
  MapPin,
  MoreVertical,
} from "lucide-react"
import PropTypes from "prop-types"
import { useEffect, useState } from "react"
import { useNavigate } from "react-router-dom"
import { failureToast } from "../custom/toasts/tosters"
import { FeedbackDialog } from "./student-feedback"

const PROVIDER_MAP = {
  1: "LinkedIn",
  2: "Monster",
  3: "Indeed",
  4: "Dice",
  5: "Website",
  6: "Talgy",
}

const PROVIDER_COLORS = {
  1: "bg-blue-50 text-blue-700 border-blue-200",
  2: "bg-purple-50 text-purple-700 border-purple-200",
  3: "bg-sky-50 text-sky-700 border-sky-200",
  4: "bg-slate-50 text-slate-700 border-slate-200",
  5: "bg-emerald-50 text-emerald-700 border-emerald-200",
  6: "bg-orange-50 text-orange-700 border-orange-200",
}

export default function JDCard({
  jobData,
  handleJDAppliedClicked,
  showApplied = false,
  isApplied = false,
  showMatch = false,
  allowFeedback = false,
  userRole,
  selectedFilter,
  error,
}) {
  const navigate = useNavigate()
  const [currentApplied, setCurrentApplied] = useState(jobData?.applied_at)
  const [showError, setShowError] = useState(false)
  const [cardBorderColor, setCardBorderColor] = useState(
    currentApplied ? "border-l-green-500" : "border-l-transparent"
  )
  console.log("selectedFilter", selectedFilter)

  const { mutate: updateJdStatusMutate } = useUpdateJobApplicationStatus()
  const [isFeedbackDialogOpen, setIsFeedbackDialogOpen] = useState(false)
  const submitFeedback = async (jobId, feedbackText) => {
    console.log("Submitting feedback for job ID:", jobId, "Feedback:", feedbackText)
    const updateData = {
      jd_id: jobId,
      student_feedback: feedbackText,
      notes: "",
      application_status: "APPLIED" ?? null,
    }
    setIsFeedbackDialogOpen(false)
    updateJdStatusMutate({
      application_id: null,
      data: updateData,
    })
  }
  useEffect(() => {
    if (selectedFilter === "recommended") {
      setCurrentApplied(false)
    } else if (selectedFilter === "applied") {
      setCurrentApplied(true)
    }
  }, [selectedFilter])
  const handleFeedback = () => {
    setIsFeedbackDialogOpen(true)
  }
  useEffect(() => {
    setCardBorderColor(currentApplied ? "border-l-green-500" : "border-l-transparent")
  }, [currentApplied])

  const onToggleApply = (jobId, resumeId) => {
    if (error === "INTEGRITY_ERROR") {
      setShowError(true)
      setCardBorderColor("border-l-red-500")
      failureToast("Failed to Duplicate apply", "You have already applied for this job")

      setTimeout(() => {
        setShowError(false)
        setCardBorderColor(currentApplied ? "border-l-green-500" : "border-l-transparent")
      }, 5000)

      return
    }

    if (!currentApplied) {
      setCurrentApplied(true)
      if (handleJDAppliedClicked) {
        handleJDAppliedClicked(jobId, resumeId, true)
      }
    }
  }

  const handleViewDetails = () => {
    navigate("/jd/detail", { state: { jobId: jobData.id } })
  }

  const formatDate = (dateString) => {
    if (!dateString) return ""

    const date = new Date(dateString)
    return `${format(date, "MMM d, yyyy h:mm a")} (${formatDistanceToNow(date, { addSuffix: true })})`
  }

  const getMatchColor = (distance) => {
    const match = (1 - distance) * 100
    if (match >= 80) return "bg-green-50 text-green-700 border-green-200"
    if (match >= 60) return "bg-yellow-50 text-yellow-700 border-yellow-200"
    if (match >= 50) return "bg-violet-50 text-violet-700 border-violet-200"
    return "bg-gray-50 text-gray-700 border-gray-200"
  }

  const generateRandomRecommandation = () => {
    return Math.floor(Math.random() * 31) + 50
  }

  return (
    <>
      <Card
        className={`group hover:shadow-lg transition-all duration-300 border-l-4 ${cardBorderColor} ${!currentApplied && !showError ? "hover:border-l-primary" : ""} relative overflow-hidden`}
      >
        <div className="bg-gradient-to-bl h-32 w-32 -z-10 absolute dark:from-gray-900 from-gray-50 opacity-50 right-0 to-transparent top-0" />

        <CardHeader className="pb-2">
          <div className="flex justify-between gap-3 items-start">
            <div className="flex-1">
              <CardTitle
                className={`text-2xl flex text-center justify-between font-bold mb-3 ${
                  showError
                    ? "text-red-500"
                    : "text-gray-900 dark:text-gray-100 group-hover:text-primary"
                } transition-colors duration-200`}
              >
                {jobData.title}
                <div className="flex gap-5 items-center">
                  <Badge
                    className={`${PROVIDER_COLORS[jobData.provider] || "bg-gray-50 text-gray-700 border-gray-200"} px-2 py-0.5`}
                  >
                    {PROVIDER_MAP[jobData.provider] || "Unknown"}
                  </Badge>
                  {userRole === USER_ROLES.STUDENT &&
                    showMatch &&
                    jobData.distance &&
                    selectedFilter === "recommended" && (
                      <Badge className={`${getMatchColor(jobData.distance)} px-2 py-0.5`}>
                        {((1 - jobData.distance) * 100).toFixed(0)}% Match
                      </Badge>
                    )}
                </div>
              </CardTitle>

              <div className="flex flex-wrap text-gray-600 text-sm gap-4 items-center">
                <div className="flex gap-2 items-center">
                  <Building2 className="h-4 text-gray-400 w-4" />
                  <span className="font-medium">{jobData.company_name}</span>
                </div>
                <div className="flex gap-2 items-center">
                  <MapPin className="h-4 text-gray-400 w-4" />
                  <span>{jobData.location}</span>
                </div>
                <div className="flex gap-2 items-center">
                  <Calendar className="h-4 text-gray-400 w-4" />
                  <span>
                    {jobData.applied_at
                      ? `Applied : ${formatDate(jobData.applied_at)}`
                      : `Posted : ${formatDate(jobData.posted_at || jobData.modified_at)}`}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex -mt-1 gap-5 items-start">
              {(selectedFilter === "recommended" || selectedFilter === "applied") && (
                <div className="flex items-center">
                  {showApplied && !currentApplied && !showError && (
                    <Button
                      variant="ghost"
                      onClick={() => onToggleApply(jobData.id, jobData.resume_id)}
                      className="flex text-sm focus:bg-gray-50 focus:text-primary gap-2 hover:bg-gray-50 hover:text-primary items-center px-3 py-1 transition-colors"
                      aria-label="Mark as applied"
                    >
                      <CheckSquare className="h-5 text-gray-400 w-5" />
                      <span className="text-gray-500 font-medium group-hover:text-primary">
                        Mark Applied
                      </span>
                    </Button>
                  )}
                  {showError && (
                    <Badge
                      variant="outline"
                      className="flex bg-red-50 border border-red-200 text-red-700 gap-1.5 items-center mt-1 px-3 py-1"
                      aria-label="Application error"
                    >
                      <AlertCircle className="h-4 w-4" />
                      <span className="text-sm font-medium">Duplicate Apply</span>
                    </Badge>
                  )}
                  {currentApplied && !showError && (
                    <Badge
                      variant="outline"
                      className="flex bg-green-50 border border-green-200 text-green-700 gap-1.5 items-center mt-1 px-3 py-1"
                      aria-label="Application status: Applied"
                    >
                      <CheckCircle className="h-4 w-4" />
                      <span className="text-sm font-medium">Application Status: Applied</span>
                    </Badge>
                  )}
                </div>
              )}

              {selectedFilter !== "all" && selectedFilter !== "recommended" && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 p-0 rounded-full w-8 dark:hover:bg-gray-800 hover:bg-gray-50"
                    >
                      <MoreVertical className="h-5 w-5" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    {allowFeedback && currentApplied && (
                      <DropdownMenuItem onClick={handleFeedback}>Provide Feedback</DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="">
          <div className="grid grid-cols-3 gap-6">
            <div className="flex items-start gap-3">
              <div className="mt-0.5">
                <Briefcase className="h-5 w-5 text-gray-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-200">Experience</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {jobData.min_work_exp} - {jobData.max_work_exp} years
                </p>
              </div>
            </div>

            {jobData.salary && (
              <div className="flex gap-3 items-start">
                <div className="mt-0.5">
                  <IndianRupee className="h-5 text-gray-400 w-5" />
                </div>
                <div>
                  <p className="text-gray-700 text-sm dark:text-gray-200 font-medium">Salary</p>
                  <p className="text-gray-600 text-sm dark:text-gray-400">
                    {jobData.salary.length > 20 ? "Confidential" : jobData.salary}
                  </p>
                </div>
              </div>
            )}
            <div>
              <p className="text-gray-700 text-sm dark:text-gray-200 font-medium">Recommandation</p>
              <p className="text-gray-600 text-sm dark:text-gray-400">
                {generateRandomRecommandation()}%
              </p>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex gap-2 items-center">
              <Award className="h-5 text-gray-400 w-5" />
              <p className="text-gray-700 text-sm dark:text-gray-200 font-medium">
                Required Skills
              </p>
            </div>
            <div className="flex flex-wrap gap-2 pl-7">
              {jobData.skills
                ?.split(",")
                .slice(0, 10)
                .map((skill) => (
                  <Badge
                    key={skill.trim()}
                    variant="secondary"
                    className="bg-blue-50 border border-violet-200 text-primary dark:bg-gray-800 dark:text-violet-400 px-2 py-0.5"
                  >
                    {skill.trim()}
                  </Badge>
                ))}
            </div>
          </div>
        </CardContent>

        <CardFooter className="flex border-t justify-between items-center pt-4">
          <div className="flex text-gray-500 text-sm gap-2 items-center">
            <Clock className="h-4 w-4" />
            <span>
              {isApplied ? "Applied" : "Modified"}: {formatDate(jobData.modified_at)}
            </span>
          </div>

          <div className="flex gap-3">
            <Button
              variant="outline"
              className="dark:hover:text-gray-100 dark:text-gray-200"
              onClick={handleViewDetails}
            >
              View Details
            </Button>
            <Button
              variant="primary"
              className="gap-2"
              onClick={
                jobData.post_link
                  ? () => window.open(jobData.post_link, "_blank")
                  : () => onToggleApply(jobData.id, jobData.resume_id)
              }
            >
              Apply Now
              {jobData.post_link && <ExternalLink className="h-4 w-4" />}
            </Button>
          </div>
        </CardFooter>
      </Card>
      {allowFeedback && (
        <FeedbackDialog
          jobId={jobData.id}
          isOpen={isFeedbackDialogOpen}
          onClose={() => setIsFeedbackDialogOpen(false)}
          onSubmit={submitFeedback}
        />
      )}
    </>
  )
}

JDCard.propTypes = {
  jobData: PropTypes.shape({
    id: PropTypes.number.isRequired,
    title: PropTypes.string.isRequired,
    company_name: PropTypes.string.isRequired,
    min_work_exp: PropTypes.number.isRequired,
    max_work_exp: PropTypes.number.isRequired,
    skills: PropTypes.string.isRequired,
    salary: PropTypes.string.isRequired,
    location: PropTypes.string.isRequired,
    post_link: PropTypes.string,
    provider: PropTypes.number,
    modified_at: PropTypes.string,
    posted_at: PropTypes.string,
    distance: PropTypes.number,
    applied_at: PropTypes.string,
    resume_id: PropTypes.number,
  }).isRequired,
  showApplied: PropTypes.bool,
  isApplied: PropTypes.bool,
  handleJDAppliedClicked: PropTypes.func,
  onArchiveClicked: PropTypes.func.isRequired,
  showMatch: PropTypes.bool,
  allowFeedback: PropTypes.bool,
  handleFeedback: PropTypes.func,
  showArchive: PropTypes.bool,
  userRole: PropTypes.string,
  selectedFilter: PropTypes.string,
  error: PropTypes.string,
}
