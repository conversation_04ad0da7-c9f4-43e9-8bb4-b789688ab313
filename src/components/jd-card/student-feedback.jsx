import { useState } from "react"
import PropTypes from "prop-types"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@radix-ui/react-dropdown-menu"
import { successToast, failureToast } from "../custom/toasts/tosters"

export function FeedbackDialog({ jobId, isOpen, onClose, onSubmit }) {
  const [feedback, setFeedback] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async () => {
    if (!feedback.trim()) {
      failureToast("Feedback Required", "Please provide feedback before submitting")
    }

    setIsSubmitting(true)
    try {
      await onSubmit(jobId, feedback)
      successToast("Feedback Submitted", "Thank you for your feedback")
      setFeedback("")
      onClose()
    } catch (error) {
      failureToast("Submission Failed", error.message || "Failed to submit feedback")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-gray-900 text-xl dark:text-gray-100 font-semibold">
            Provide Feedback
          </DialogTitle>
        </DialogHeader>

        <div className="  ">
          <Label
            htmlFor="student_feedback"
            className="text-gray-700 text-sm block dark:text-gray-300 font-medium mb-2"
          >
            Feedback
          </Label>
          <Textarea
            placeholder="Please share your feedback about this job application..."
            className="w-full min-h-[120px]"
            value={feedback}
            onChange={(e) => setFeedback(e.target.value)}
            id="student_feedback"
            name="student_feedback"
          />
        </div>

        <DialogFooter className="flex justify-end mt-4 space-x-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? "Submitting..." : "Submit Feedback"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

FeedbackDialog.propTypes = {
  jobId: PropTypes.string.isRequired,
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
}
