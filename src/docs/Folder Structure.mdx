## Project Folder Structure Documentation

This document outlines the folder structure of the project and provides guidance on the type of code that should be placed within each folder.

**Root Directory:**

- **public:** Contains static files that are served directly to the browser, including:
  - **locales:** Houses localized content, like language files.
- **src:** Contains all source code files.

**src Directory:**

- **assets:** Holds static assets like images, fonts, and audio files.
- **components:** Houses reusable React components.
  - **ui:** Contains shared UI components specific to China.
  - **layout:** Holds layout components for different pages.
  - **comments:** Contains general comment components.
  - **[other components]:** Add additional component folders as needed based on project requirements.
- **docs:** Contains documentation files, including README files, API documentation, and design specifications.
- **lib:** Houses common utility functions, libraries, and reusable logic.
  - **constant:** Contains project-wide constants.
  - **context:** Holds React context aware components.
  - **hooks:** Contains custom React hooks.
  - **utils:** Houses general utility functions.
- **pages:** Contains page components for different routes.
  - **auth:** Contains authentication related pages (login, signup).
  - **dashboard:** Contains the home page component.
  - **misc:** Contains miscellaneous pages like 404 Not Found, unauthorized error page, etc.
  - **[other pages]:** Add additional page folders based on project requirements.
- **route:** Handles routing and navigation within the application.
  - **blank.routes.jsx:** Defines routes that do not require authentication.
  - **main.routes.jsx:** Defines routes that require authentication.
- **services:** Contains services that interact with APIs, data stores, and other services.
  - **api:** Holds Axios API clients for making network requests.
  - **mock:** Contains API mock data and implementations using MSW (Mock Service Worker).
  - **query:** Houses TanStack Query clients for managing data fetching and caching.
  - **store:** Contains Redux store configuration and reducers.

**Code Organization Guidance:**

- **Components:** Place components that serve a specific purpose or are reusable in multiple parts of the application within the respective folder. For example, a 'Header' component would belong in `src/components/layout`.
- **Lib:** Utilize the `lib` folder to organize reusable logic, helpers, and constants that are used across different parts of the application. For example, a `formatDate` utility function would belong in `src/lib/utils`.
- **Pages:** Create separate folders for different pages of the application. Each folder should contain all the necessary components, data fetching logic, and routing for that page.
- **Services:** Group services that interact with the same API, database, or other external service within the relevant folder. For example, an API service for fetching user data would belong in `src/services/api`.

This folder structure promotes code organization and maintainability. It helps ensure that related files are grouped together, making it easier to navigate and understand the project's codebase.
