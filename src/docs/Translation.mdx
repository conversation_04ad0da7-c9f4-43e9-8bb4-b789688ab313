## Working with Translations in your React Application

This documentation explains how to use translation keys and the `react-i18next` library within your React project, assuming your i18n setup is already in place.

**1. Centralized Translation Keys:**

-   **Dedicated File:** Create a file (`src/constants/translation.constant.js` in this case) to store all your translation keys. This ensures a central location for managing and organizing keys, promoting consistency across your project.

    ```javascript
    const translate = {
        // about page details
        about: {
            title: 'aboutTitle',
        },
        // ... other translation keys
    }

    export default translate
    ```

    **Best Practice:** Organize your translation keys into logical categories and maintain a consistent naming convention for readability and maintainability. For example, you could group keys by component, feature, or screen.

**2. Using Translations in Components:**

-   **Import Necessary Hooks:** Import the `useTranslation` hook from `react-i18next` and your translation key file.

    ```javascript
    import { useTranslation } from 'react-i18next'
    import ct from '@constants/'
    ```

-   **Translate Text:** Utilize the `t` function provided by `useTranslation` to translate strings.

    ```javascript
    export function AboutPage() {
        const { t } = useTranslation()
        return (
            <div className="p-2">
                <h3>{t(ct.translate.about.title)}</h3>
            </div>
        )
    }
    ```

    **Explanation:**

    -   `useTranslation`: Provides the `t` function for translating strings.
    -   `t(key)`: Translates the specified key from your translation files (e.g., `en.json`, `fr.json`).

**3. Handling Placeholders (Dynamic Content):**

Sometimes you need to dynamically insert values into your translations.

-   **Define Placeholders:** In your translation files, use double curly braces (e.g., `{{userName}}`) to mark placeholders.

    ```json
    // locales/en.json
    {
        "welcomeMessage": "Welcome, {{userName}}!"
    }
    ```

-   **Pass Values:** Provide the values for these placeholders as an object to the `t` function.

    ```javascript
    const userName = 'John Doe'
    const { t } = useTranslation()
    return <p>{t('welcomeMessage', { userName })}</p>
    ```

**4. Best Practices:**

-   **Maintain Consistency:** Strive for consistency in how you define and utilize translation keys. This makes your code easier to read and maintain.
-   **Use Descriptive Keys:** Choose translation keys that clearly reflect the content they represent. This makes it easier to understand the purpose of each translation.
-   **Test Translations Thoroughly:** Ensure your translations are accurate and consistent across all languages. Use different devices, browsers, and screen sizes to check for potential issues.

**Example:**

```javascript
// locales/en.json
{
  "about": {
    "title": "About Page"
  }
}

// locales/fr.json
{
  "about": {
    "title": "Page À Propos"
  }
}

// src/components/AboutPage.js
import { useTranslation } from 'react-i18next';
import tc from '@constants/translation-constant';

export function AboutPage() {
  const { t } = useTranslation();
  return (
    <div className="p-2">
      <h3>{t(tc.about.title)}</h3>
    </div>
  );
}
```
