import moment from "moment-timezone"
import React from "react"
import ReactDOM from "react-dom/client"
import "./main.css"

import App from "./App"

import "./lib/i18n"
// import { enableMocking } from "./mock"

// setup timezone
// eslint-disable-next-line new-cap
const zone = Intl.DateTimeFormat().resolvedOptions().timeZone
moment.tz.setDefault(zone)

// Enable mocking in development

// enableMocking().then(() => {
//   ReactDOM.createRoot(document.getElementById("root")).render(
//     <React.StrictMode>
//       <App />
//     </React.StrictMode>
//   )
// })

ReactDOM.createRoot(document.getElementById("root")).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
)
