/* eslint-disable sonarjs/prefer-immediate-return */
import { logoutAC } from "@/services/store/slices/user.slice"
import { useQueryClient } from "@tanstack/react-query"
import { useDispatch, useSelector } from "react-redux"
import { useNavigate } from "react-router-dom"
import ct from "@constants/"
import { useMutateLogOut } from "@/services/query/auth.query"
import { toast } from "@/components/ui/use-toast"
import { getDeviceInfo } from "@/pages/auth/login/utils/device-info"

const useLogoutUser = () => {
  // Get Curent user & Device Info
  const { id: userID, userRole } = useSelector((state) => state[ct.store.USER_STORE])
  const { device_info } = getDeviceInfo()

  const { mutate: mutateLogout, isPending } = useMutateLogOut()
  const queryClient = useQueryClient()
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const handleLogout = () => {
    // Mutate API
    if (!userID || !userRole) {
      navigate(ct.route.AUTH.LOGIN, { replace: true }) // Just redirect to login
    }

    mutateLogout(
      { userID, userRole, device_info },
      {
        onSuccess: () => {
          dispatch(logoutAC()) // Reset Redux state
          queryClient.clear() // Clear Query cache
          navigate(ct.route.AUTH.LOGIN, { replace: true })
          localStorage.clear() // Clear Local Storage
        },
        onError: (error) => {
          console.log("error", error)
          toast({
            title: error?.response?.data?.error?.message ?? "Logout Failed. Please try again later",
            variant: "destructive",
          })
        },
      }
    )
  }

  return [handleLogout, isPending]
}

export default useLogoutUser
