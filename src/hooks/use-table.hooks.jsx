import { useState, useEffect, useMemo } from "react"
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
} from "@tanstack/react-table"

const useTableConfig = (data, columns, found, setPagination, pagination) => {
  console.log("__Data", data)
  const [sorting, setSorting] = useState([])
  const [columnFilters, setColumnFilters] = useState([])
  const [columnVisibility, setColumnVisibility] = useState({})
  const [rowSelection, setRowSelection] = useState({})
  const [pageCount, setPageCount] = useState(0)

  useEffect(() => {
    if (found !== undefined && pagination?.pageSize) {
      setPageCount(Math.ceil(found / pagination.pageSize))
    }
  }, [found, pagination?.pageSize])

  const table = useReactTable({
    data: Array.isArray(data) ? data : [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
      rowSelection,
      pagination,
    },
    initialState: {
      sorting,
      columnFilters,
      pagination,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: setPagination,
    onRowSelectionChange: setRowSelection,
    pageCount,
    manualPagination: true,
    enableRowSelection: true,
    enableMultiRowSelection: true,
    getRowId: (row, index) => row?.id ?? row?.student_id ?? index, // Ensure unique row ID
  })

  return useMemo(
    () => ({
      table,
      setSorting,
      sorting,
      columnFilters,
      setColumnFilters,
      columnVisibility,
      setColumnVisibility,
      rowSelection,
      setRowSelection,
      setPageCount,
      pageCount,
      found,
    }),
    [table, sorting, columnFilters, columnVisibility, rowSelection, pageCount, found]
  )
}

export default useTableConfig
