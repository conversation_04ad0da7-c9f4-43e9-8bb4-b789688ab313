import {
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { useState } from "react"

const useEditableTableConfig = (data, columns, pageCount, autoResetPageIndex) => {
  console.log(pageCount, "metacount")
  // console.log("____listOfAttendance",initialData)
  // const [data, setData] = useState(initialData || [])
  const [sorting, setSorting] = useState([])
  const [columnFilters, setColumnFilters] = useState([])
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 })
  const [rowSelection, setRowSelection] = useState({})

  // const updateData = useCallback((rowIndex, columnId, value) => {
  //   autoResetPageIndex?.()
  //   setData((prevData) =>
  //     prevData.map((row, index) => (index === rowIndex ? { ...row, [columnId]: value } : row))
  //   )
  // }, [])

  const table = useReactTable({
    data: Array.isArray(data) ? data : [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    manualPagination: true,
    enableRowSelection: true,
    enableMultiRowSelection: true,
    getRowId: (row) => row?.id,
    autoResetPageIndex,
    // meta: { updateData },
    state: { sorting, columnFilters, pagination, rowSelection },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: setPagination,
    onRowSelectionChange: setRowSelection,
    pageCount,
    debugTable: true,
  })

  return {
    table,
    data,
    // setData,
    sorting,
    setSorting,
    columnFilters,
    setColumnFilters,
    pagination,
    setPagination,
    rowSelection,
    setRowSelection,
    found: pageCount,
  }
}

export default useEditableTableConfig
