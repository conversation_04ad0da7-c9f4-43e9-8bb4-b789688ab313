import { debounce } from "lodash"
import { useCallback, useEffect, useState } from "react"

export const useOpenCloseHooks = () => {
  const [open, setOpen] = useState(false)
  const handleOpen = () => setOpen(true)
  const handleClose = () => setOpen(false)
  return { open, handleOpen, handleClose, setOpen }
}

export const useDeleteDialogHooks = () => {
  const [isDelete, setIsDelete] = useState(false)
  const handleDelete = () => setIsDelete(true)
  const handleCancel = () => setIsDelete(false)
  return { isDelete, handleDelete, handleCancel, setIsDelete }
}

export const useIsUpdateHook = () => {
  const [isUpdate, setIsUpdate] = useState(false)
  const handleUpdate = () => setIsUpdate(true)
  const handleResetUpdate = () => setIsUpdate(false)
  return { isUpdate, setIsUpdate, handleResetUpdate, handleUpdate }
}

export const useIsEditDialogHook = () => {
  const [isEditDialog, setisEditDialog] = useState(false)
  const [isModuleDialog, setIsModuleDialog] = useState(false)
  const handleOpenEditDialog = () => setisEditDialog(true)
  const handleModuleDialog = () => setIsModuleDialog(true)
  const handleResetModuleDialog = () => setIsModuleDialog(false)
  const handleResetEditDialog = () => {
    handleResetModuleDialog()
    setisEditDialog(false)
  }

  return {
    isEditDialog,
    setisEditDialog,
    handleResetEditDialog,
    handleOpenEditDialog,
    handleModuleDialog,
    handleResetModuleDialog,
    isModuleDialog,
  }
}

export const useSearchHook = (delay = 500) => {
  const [searchValue, setSearchValue] = useState("")

  // Debounced function
  const debouncedSearch = useCallback(
    debounce((query) => {
      setSearchValue(query)
    }, delay),
    []
  )

  const handleSearch = (e) => {
    debouncedSearch(e.target.value)
  }

  return { searchValue, handleSearch, setSearchValue }
}

export const useFilterHooks = () => {
  const [filterValue, setFilterValue] = useState("")
  const [sortBy, setSortBy] = useState("DESC")
  const [sortByField, setSortByField] = useState("modified_at")
  const handleFilter = (e) => setFilterValue(e.target.value)
  return { filterValue, handleFilter, sortBy, setSortBy, sortByField, setSortByField }
}

export const usePaginationHooks = (initialPage = 0, pageSize = 10) => {
  const [offset, setOffset] = useState(initialPage)
  const limit = pageSize
  return { offset, setOffset, pageSize, limit }
}

// const useDebounce = (callback, delay) => {
//   const [timer, setTimer] = useState(null)

//   return (...args) => {
//     if (timer) clearTimeout(timer)
//     setTimer(setTimeout(() => callback(...args), delay))
//   }
// }

const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

export default useDebounce

export const useSelectedIds = () => {
  const [selectedIDs, setSelectedIDs] = useState({
    announceMentID: "",
    moduleID: "",
    courseID: "",
    quizID: "",
    notes: "",
    project_id: "",
  })

  return { selectedIDs, setSelectedIDs }
}

export const useIsLoading = () => {
  const [isLoading, setIsLoading] = useState(false)

  const handleResetLoading = () => setIsLoading(false)
  const handleSetLoading = () => setIsLoading(true)

  return { isLoading, setIsLoading, handleResetLoading, handleSetLoading }
}

export const usePublishStatus = (defaultValue = "DRAFT") => {
  const [publishedStatus, setPublishedStatus] = useState(defaultValue)
  const handleSetPublishStatus = (val) => setPublishedStatus(val)
  const handleResetPublishStatus = (val) => setPublishedStatus(val)
  return { publishedStatus, setPublishedStatus, handleSetPublishStatus, handleResetPublishStatus }
}

export const useUploadDocs = () => {
  const [documentData, setDocumentData] = useState(null)
  const handleChangeFile = (e) => setDocumentData(e)

  const handleResetFile = () => setDocumentData(null)

  return { documentData, setDocumentData, handleChangeFile, handleResetFile }
}

export const useSelectedModule = (intialModule = "", intialTopic = "") => {
  const [selectedModule, setSelectedModule] = useState(intialModule)
  const [selectedTopic, setSelectedTopic] = useState(intialTopic)

  return { selectedModule, setSelectedModule, selectedTopic, setSelectedTopic }
}

export const useModuleAndTopicsList = (intialModule = [], intialTopic = []) => {
  const [listOfModules, setListOfModules] = useState(intialModule)
  const [listOfTopics, setListOfTopics] = useState(intialTopic)

  return { listOfModules, setListOfModules, listOfTopics, setListOfTopics }
}

export const useDateHook = (initialData = {}) => {
  const [date, setDate] = useState(initialData)
  return { date, setDate }
}
