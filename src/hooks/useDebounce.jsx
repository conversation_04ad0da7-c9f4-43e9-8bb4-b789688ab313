import { debounce } from "lodash"
import { useEffect, useState } from "react"

export function useDebounce(searchingValue, delay = 300) {
  const [searchedvalue, setSearchedvalue] = useState(searchingValue ?? null)

  useEffect(() => {
    const debouncedSearch = debounce(setSearchedvalue, delay)

    debouncedSearch(searchingValue)

    return () => {
      debouncedSearch.cancel() // Cancel pending execution on unmount
    }
  }, [searchingValue, delay]) // ✅ Add `delay` as a dependency

  return searchedvalue
}
