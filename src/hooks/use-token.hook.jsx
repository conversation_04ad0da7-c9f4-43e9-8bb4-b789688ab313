import ct from "@constants/"
import { useSelector, useDispatch } from "react-redux"
import { useEffect, useState } from "react"
import { loginAC, logoutAC } from "@/services/store/slices/user.slice" // Import logout action
import { auth } from "../firebase/firebase"

const useToken = () => {
  const dispatch = useDispatch()
  const token = useSelector((state) => state[ct.store.USER_STORE]?.sessionData?.access_token)
  const [currentToken, setCurrentToken] = useState(token)

  useEffect(() => {
    const unsubscribe = auth.onIdTokenChanged(async (user) => {
      if (user) {
        try {
          const newToken = await user.getIdToken()
          dispatch(
            loginAC({
              userData: { summary: "Updated User" }, // Or fetch fresh user data
              sessionData: {
                access_token: newToken,
                type: "Bearer",
              },
            })
          )
          setCurrentToken(newToken)
        } catch (error) {
          console.error("Error getting token from auth change:", error)
          // Handle error appropriately (e.g., display error message, retry)
        }
      } else {
        // User is signed out
        setCurrentToken(null)
        dispatch(logoutAC()) // Dispatch a logout action to clear user data
      }
    })

    // Initial token load (check if user is already logged in)
    const loadInitialToken = async () => {
      const user = auth.currentUser
      if (user) {
        try {
          const initialToken = await user.getIdToken()
          dispatch(
            loginAC({
              userData: { summary: "Existing User" }, // Or fetch fresh user data
              sessionData: {
                access_token: initialToken,
                type: "Bearer",
              },
            })
          )
          setCurrentToken(initialToken)
        } catch (error) {
          console.error("Error getting initial token:", error)
          // Handle error
        }
      }
    }

    loadInitialToken()

    return () => {
      unsubscribe()
    }
  }, [dispatch])

  return currentToken
}

export default useToken
