import CourseAttendance from "@/pages/course-attendence"
import CourseContent from "@/pages/course-content"
import CourseDescription from "@/pages/courses/course-description"
import CourseList from "@/pages/courses/course-list"
import LiveCourses from "@/pages/courses/live-courses"
import LiveCourseDetailsPage from "@/pages/courses/live-courses/course-details/live-course-details-page"
import Feedback from "@/pages/courses/live-courses/feedback"
// import MyCoursesPage from "@/pages/courses/my-courses"
import CvPageTable from "@/pages/cv-page"
import AdminDashboard from "@/pages/dashboard/admin-dashboard"
import MarketingDashboard from "@/pages/dashboard/marketing-dashboard"
import StudentDashboard from "@/pages/dashboard/student-dashboard"
import StudentMarketingDashboard from "@/pages/dashboard/student-marketing-dashboard"
import TrainerDashboard from "@/pages/dashboard/trainer-dashboard"
import VendorDashboard from "@/pages/dashboard/vendor-dashboard"
import JDPage from "@/pages/jd"
import JDDetailsPage from "@/pages/jd/details"

import CourseCreationForm from "@/pages/courses/create-course"
import AssignmentSubmission from "@/pages/courses/live-courses/assignments/assignment-submissions"
import FileView from "@/pages/courses/live-courses/project-submission/file-view"
import QuizViewUI from "@/pages/courses/live-courses/quiz/quiz-view.ui"
import QuizUI from "@/pages/courses/live-courses/quiz/quiz.ui"
import ModuleCreation from "@/pages/courses/module-setup"
import DocumentViewer from "@/pages/cv-page/document-viewer"
import ApplicationsPage from "@/pages/jd/applications"
import ManageJD from "@/pages/jd/manage-jd"
import RelationshipPage from "@/pages/jd/relationship"
import RelationshipDetailsPage from "@/pages/jd/relationship/details"
import ManageCourseBatchPage from "@/pages/manage-batch"
import ManageCourseBatchAssigneePage from "@/pages/manage-batch/manage-page"
import ManageCoursePage from "@/pages/manage-course/manage"
import MockInterview from "@/pages/mock-interview"
import NotificationContent from "@/pages/notification/notification-content"
import PaymentPages from "@/pages/payments"
import Profile from "@/pages/profile"
import StudentPerformancePage from "@/pages/student/performance"
import TrainerEarningPage from "@/pages/trainer/earnings"
import TrainerPaymentPage from "@/pages/trainer/payment"
import UserManagementPage from "@/pages/users"
import ct from "@constants/"
import ProtectedRoute from "./protected-route"

const mainRoutes = [
  {
    path: ct.route.FILE_VIEW,
    element: (
      <ProtectedRoute>
        <FileView />
      </ProtectedRoute>
    ),
  },
  {
    path: ct.route.COURSE_OVERVIEW,
    element: (
      <ProtectedRoute>
        <LiveCourses />
      </ProtectedRoute>
    ),
    children: [
      {
        path: ct.route.FEEDBACK,
        element: (
          <ProtectedRoute>
            <Feedback />
          </ProtectedRoute>
        ),
      },
      {
        path: ct.route.QUIZ_VIEW,
        element: (
          <ProtectedRoute>
            <QuizUI />
          </ProtectedRoute>
        ),
      },
      {
        path: `${ct.route.QUIZ_VIEW}/:id`,
        element: (
          <ProtectedRoute>
            <QuizViewUI />
          </ProtectedRoute>
        ),
      },
    ],
  },

  {
    path: `${ct.route.BATCH_COURSE}/:id${ct.route.COURSE_OVERVIEW}`,
    element: (
      <ProtectedRoute>
        <LiveCourses />
      </ProtectedRoute>
    ),
    children: [
      {
        path: ct.route.FEEDBACK,
        element: (
          <ProtectedRoute>
            <Feedback />
          </ProtectedRoute>
        ),
      },
      {
        path: ct.route.QUIZ_VIEW,
        element: (
          <ProtectedRoute>
            <QuizUI />
          </ProtectedRoute>
        ),
      },
      {
        path: `${ct.route.QUIZ_VIEW}/:id`,
        element: (
          <ProtectedRoute>
            <QuizViewUI />
          </ProtectedRoute>
        ),
      },
    ],
  },
  {
    path: `${ct.route.ASSIGNMENT_VIEW}/:id`,
    element: (
      <ProtectedRoute>
        <AssignmentSubmission />
      </ProtectedRoute>
    ),
  },

  {
    path: ct.route.COURSE_CONTENT,
    element: (
      <ProtectedRoute>
        <CourseContent />
      </ProtectedRoute>
    ),
  },
  {
    path: ct.route.COURSE_OVERVIEW,
    element: (
      <ProtectedRoute>
        <LiveCourses />
      </ProtectedRoute>
    ),
    children: [
      {
        path: `${ct.route.QUIZ_VIEW}/:id`,
        element: (
          <ProtectedRoute>
            <QuizViewUI />
          </ProtectedRoute>
        ),
      },
      {
        path: `${ct.route.ASSIGNMENT_VIEW}/:id`,
        element: (
          <ProtectedRoute>
            <AssignmentSubmission />
          </ProtectedRoute>
        ),
      },
    ],
  },
  {
    path: ct.route.MY_COURSES,
    element: (
      <ProtectedRoute>
        <CourseList name="My Courses" />
      </ProtectedRoute>
    ),
  },
  {
    path: ct.route.LIVE_COURSES_DETAILS,
    element: (
      <ProtectedRoute>
        <LiveCourseDetailsPage />
      </ProtectedRoute>
    ),
  },
  {
    path: ct.route.MOCK_INTERVIEW,
    element: (
      <ProtectedRoute>
        <MockInterview />
      </ProtectedRoute>
    ),
  },
  {
    path: `${ct.route.COURSE}/:id`,
    element: (
      <ProtectedRoute>
        <CourseContent />
      </ProtectedRoute>
    ),
  },
  {
    path: ct.route.RESUME,
    element: (
      <ProtectedRoute>
        <CvPageTable />
      </ProtectedRoute>
    ),
  },
  {
    path: ct.route.PROFILE,
    element: (
      <ProtectedRoute>
        <Profile />
      </ProtectedRoute>
    ),
  },
  {
    path: ct.route.NOTIFICATION,
    element: (
      <ProtectedRoute>
        <NotificationContent />
      </ProtectedRoute>
    ),
  },
  {
    path: ct.route.FEEDBACK,
    element: (
      <ProtectedRoute>
        <Feedback />
      </ProtectedRoute>
    ),
  },
  {
    path: ct.route.SHORT_COURSES_LIST,
    element: (
      <ProtectedRoute>
        <CourseList name="Short courses" />
      </ProtectedRoute>
    ),
  },
  {
    path: ct.route.NANO_COURSES,
    element: (
      <ProtectedRoute>
        <CourseList name="Nano courses" />
      </ProtectedRoute>
    ),
    children: [
      {
        path: ":id",
        element: (
          <ProtectedRoute>
            <LiveCourseDetailsPage />
          </ProtectedRoute>
        ),
        children: [
          {
            path: "course-overview",
            element: (
              <ProtectedRoute>
                <LiveCourses />
              </ProtectedRoute>
            ),
          },
        ],
      },
    ],
  },
  {
    path: ct.route.LIVE_COURSES,
    element: (
      <ProtectedRoute>
        <CourseList name="Live courses" />
      </ProtectedRoute>
    ),
    children: [
      {
        path: ":id",
        element: (
          <ProtectedRoute>
            <LiveCourseDetailsPage />
          </ProtectedRoute>
        ),
        children: [
          {
            path: "course-overview", // ✅ Use a relative path, NOT "/course-overview"
            element: (
              <ProtectedRoute>
                <LiveCourses />
              </ProtectedRoute>
            ),
          },
        ],
      },
    ],
  },

  {
    path: ct.route.COURSE_DETAILS,
    element: (
      <ProtectedRoute>
        <CourseDescription />
      </ProtectedRoute>
    ),
  },
  {
    path: "/student-tracking",
    element: (
      <ProtectedRoute>
        <StudentPerformancePage />
      </ProtectedRoute>
    ),
  },
  {
    path: ct.route.TRAINER_EARNING,
    element: (
      <ProtectedRoute>
        <TrainerEarningPage />
      </ProtectedRoute>
    ),
  },
  {
    path: ct.route.COURSE_ATTENDANCE,
    element: (
      <ProtectedRoute>
        <CourseAttendance />
      </ProtectedRoute>
    ),
  },
  {
    path: ct.route.TRAINER_PAYMENT,
    element: (
      <ProtectedRoute>
        <TrainerPaymentPage />
      </ProtectedRoute>
    ),
  },

  {
    path: `${ct.route.MODULE_CONTENT}/:id`,
    element: (
      <ProtectedRoute>
        <ModuleCreation />
      </ProtectedRoute>
    ),
  },

  {
    path: "/student-dashboard",
    element: (
      <ProtectedRoute>
        <StudentDashboard />
      </ProtectedRoute>
    ),
  },
  {
    path: "/marketing-dashboard",
    element: (
      <ProtectedRoute>
        <MarketingDashboard />
      </ProtectedRoute>
    ),
  },
  {
    path: "/vendor-dashboard",
    element: (
      <ProtectedRoute>
        <VendorDashboard />
      </ProtectedRoute>
    ),
  },
  {
    path: "/admin-dashboard",
    element: (
      <ProtectedRoute>
        <AdminDashboard />
      </ProtectedRoute>
    ),
  },
  {
    path: "/trainer-dashboard",
    element: (
      <ProtectedRoute>
        <TrainerDashboard />
      </ProtectedRoute>
    ),
  },
  {
    path: "/student-marketing-dashboard",
    element: (
      <ProtectedRoute>
        <StudentMarketingDashboard />
      </ProtectedRoute>
    ),
  },
  {
    path: ct.route.JD_PAGE,
    element: (
      <ProtectedRoute>
        <JDPage />
      </ProtectedRoute>
    ),
  },
  {
    path: "/jd/detail",
    element: (
      <ProtectedRoute>
        <JDDetailsPage />
      </ProtectedRoute>
    ),
  },
  {
    path: "/jd/add-JD",
    element: (
      <ProtectedRoute>
        <ManageJD />
      </ProtectedRoute>
    ),
  },
  {
    path: "/course/trainer",
    element: (
      <ProtectedRoute>
        <ManageCoursePage />
      </ProtectedRoute>
    ),
  },
  {
    path: ct.route.MANAGE_BATCH,
    element: (
      <ProtectedRoute>
        <ManageCourseBatchPage />
      </ProtectedRoute>
    ),
  },
  {
    path: "/manage/course/assignee",
    element: (
      <ProtectedRoute>
        <ManageCourseBatchAssigneePage />
      </ProtectedRoute>
    ),
  },
  {
    path: "/user-management",
    element: (
      <ProtectedRoute>
        <UserManagementPage />
      </ProtectedRoute>
    ),
  },
  {
    path: ct.route.PAYMENTS,
    element: (
      <ProtectedRoute>
        <PaymentPages />
      </ProtectedRoute>
    ),
  },
  {
    path: "/applications",
    element: (
      <ProtectedRoute>
        <ApplicationsPage />
      </ProtectedRoute>
    ),
  },
  {
    path: "/leads-management",
    element: (
      <ProtectedRoute>
        <RelationshipPage />
      </ProtectedRoute>
    ),
  },
  {
    path: "/leads-management/relationship",
    element: (
      <ProtectedRoute>
        <RelationshipDetailsPage />
      </ProtectedRoute>
    ),
  },
  {
    path: ct.route.COURSE_CREATION,
    element: (
      <ProtectedRoute>
        <CourseCreationForm />
      </ProtectedRoute>
    ),
  },
  {
    path: ct.route.EDIT_COURSE,
    element: (
      <ProtectedRoute>
        <CourseCreationForm />
      </ProtectedRoute>
    ),
  },
  {
    path: ct.route.PUBLIC_URL,
    element: (
      <ProtectedRoute>
        <DocumentViewer />
      </ProtectedRoute>
    ),
  },
]

export default mainRoutes
