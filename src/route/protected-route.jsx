import PropTypes from "prop-types"
import { useSelector } from "react-redux"
import ct from "@constants/"
import { Navigate } from "react-router-dom"

const ProtectedRoute = ({ children }) => {
  const { id: userID } = useSelector((state) => state[ct.store.USER_STORE])

  return userID ? children : <Navigate to={ct.route.AUTH.LOGIN} replace />
}

ProtectedRoute.propTypes = {
  children: PropTypes.node,
}

export default ProtectedRoute
