import { createBrowserRouter, Navigate } from "react-router-dom"
import MainLayout from "@/components/layout/main-layout"
import BlankLayout from "@/components/layout/blank-layout"
import ct from "@constants/"
import mainRoutes from "./main.routes"
import blankRoutes from "./blank.routes"

const router = createBrowserRouter([
  {
    path: ct.route.ROOT,
    element: <Navigate to={ct.route.AUTH.LOGIN} replace />,
  },
  {
    element: <BlankLayout />,
    children: blankRoutes,
  },
  {
    element: <MainLayout />,
    children: mainRoutes,
  },
])

export default router
