/* ==========================================================================
   Base Imports
   ========================================================================== */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Fira+Code:wght@400;600&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

/* ==========================================================================
      Custom Properties - Light Theme
      ========================================================================== */
@layer base {
  :root {
    --background: 221.2 61% 95%;
    --foreground: 221.2 5% 10%;
    --card: 221.2 50% 90%;
    --card-foreground: 221.2 5% 15%;
    --popover: 221.2 61% 95%;
    --popover-foreground: 221.2 95% 10%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 0 0% 100%;
    --secondary: 221.2 30% 79%;
    --secondary-foreground: 0 0% 0%;
    --muted: 183.2 30% 85%;
    --muted-foreground: 221.2 5% 40%;
    --accent: 183.2 30% 80%;
    --accent-foreground: 221.2 5% 15%;
    --destructive: 0 61% 50%;
    --destructive-foreground: 221.2 5% 90%;
    --border: 221.2 30% 79%;
    --input: 221.2 30% 50%;
    --ring: 221.2 83.2% 53.3%;
    --radius: .6rem;



  }

  .dark {
    --background: 221.2 50% 10%;
    --foreground: 221.2 5% 90%;
    --card: 221.2 50% 10%;
    --card-foreground: 221.2 5% 90%;
    --popover: 221.2 50% 5%;
    --popover-foreground: 221.2 5% 90%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 0 0% 100%;
    --secondary: 221.2 30% 20%;
    --secondary-foreground: 0 0% 100%;
    --muted: 183.2 30% 25%;
    --muted-foreground: 221.2 5% 65%;
    --accent: 183.2 30% 25%;
    --accent-foreground: 221.2 5% 90%;
    --destructive: 0 61% 50%;
    --destructive-foreground: 221.2 5% 90%;
    --border: 221.2 30% 50%;
    --input: 221.2 30% 50%;
    --ring: 221.2 83.2% 53.3%;
    --radius: .6rem;


  }
  
}

/* ==========================================================================
      Custom Properties - Dark Theme
      ========================================================================== */





/* ==========================================================================
      Base Styles
      ========================================================================== */
@layer base {
  * {
    @apply border-border;
  }

  html {
    @apply scroll-smooth font-semibold;
  }

  body {
    @apply text-base sm:text-lg md:text-xl lg:text-2xl font-normal bg-[hsl(var(--background))]  text-foreground;
    color: hsl(var(--foreground));
    font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
      "Segoe UI Symbol", "Noto Color Emoji";
    font-synthesis-weight: none;
    text-rendering: optimizeLegibility;
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4;
    font-feature-settings: normal;
    font-variation-settings: normal;
    -webkit-tap-highlight-color: transparent;
    transition: background-color 0.3s ease, color 0.3s ease; 
  }
}

/* ==========================================================================
      Typography
      ========================================================================== */
@layer utilities {

  /* Headings */
  h1 {
    @apply text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-4;
    line-height: 1.2;
  }

  h2 {
    @apply text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-semibold text-gray-800 mb-4;
    line-height: 1.3;
  }

  h3 {
    @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-semibold text-gray-700 mb-3;
    line-height: 1.4;
  }

  h4 {
    @apply text-xl sm:text-2xl md:text-3xl lg:text-4xl font-medium text-gray-600 mb-3;
    line-height: 1.4;
  }

  h5 {
    @apply text-lg sm:text-xl md:text-2xl lg:text-xl font-medium text-gray-500 mb-2;
    line-height: 1.5;
  }

  h6 {
    @apply text-base sm:text-lg md:text-base lg:text-lg font-medium text-gray-400 mb-2;
    line-height: 1.6;
  }

  /* Text elements */
  p,
  span,
  div {
    @apply text-base sm:text-lg md:text-base font-normal;
    line-height: 1.6;
    color: var(--primary);
  }
}

/* ==========================================================================
      Components
      ========================================================================== */
/* Card */
.card {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
  border: 1px solid hsl(var(--border)) ;
  border-radius: var(--radius) ;
  padding: 1rem ; /* Add some padding to the card */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) ; /* Add a subtle shadow */
  transition: background-color 0.3s ease, color 0.3s ease ; /* Smooth transitions */
}

/* Button Base */
.button {
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
}

/* Button Variants */
.button-primary {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
}

.button-secondary {
  background-color: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
}

.button-danger {
  background-color: hsl(var(--destructive));
  color: hsl(var(--destructive-foreground));
}

/* Form Elements */
.input {
  color: hsl(var(--foreground));
  border: 1px solid hsl(var(--border));
  padding: 8px;
  border-radius: 0.4rem;
}

/* Utility Classes */
.text-muted {
  color: hsl(var(--muted-foreground));
}

.accent {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.navLink{
  background-color: #0050ff30 !important;
  color: hsl(var(--primary)) !important;  
}